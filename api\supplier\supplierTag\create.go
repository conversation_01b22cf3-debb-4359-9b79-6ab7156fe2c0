package supplierTag

import (
	"base/core/xhttp"
	"base/service/supplierTagService"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		Title string `json:"title" validate:"required"`
		Color string `json:"color" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = supplierTagService.NewSupplierTagService().Create(ctx, req.Title, req.Color)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
