package util

import (
	"base/model"
	"github.com/shopspring/decimal"
)

func CalcDeliverFee(pointLongitude, pointLatitude float64, location model.Location) (float64, int, int, int) {
	personDe := decimal.NewFromFloat(2.5)
	trackDe := decimal.NewFromFloat(4)
	deliverDe := decimal.NewFromFloat(1.7)

	distance := LatitudeLongitudeDistance(pointLongitude, pointLatitude, location.Longitude, location.Latitude)
	km, exact := decimal.NewFromInt(int64(distance / 1000)).Round(1).Float64()
	_ = exact

	kmDe := decimal.NewFromFloat(km)

	var deliverFee int
	var person int
	var track int
	if km <= 16 {
		// 跑腿
		person = int(kmDe.Mul(personDe).Floor().IntPart()) * 100
		// 货拉拉
		track = int(kmDe.Mul(trackDe).Floor().IntPart()) * 100
		// 配送
		deliverFee = int(kmDe.Mul(deliverDe).Floor().IntPart()) * 100

		if person < 1000 {
			person = 1000
		}
		if track < 2500 {
			track = 2500
		}

		if deliverFee < 800 {
			deliverFee = 800
		}
	}

	// 距离 配送费 跑腿 货拉拉
	return km, deliverFee, person, track
}
