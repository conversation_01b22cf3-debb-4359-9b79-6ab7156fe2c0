package orderAdjustSettle

import (
	"base/core/xhttp"
	"base/global"
	"base/service/orderAdjustSettleService"
	"base/types"

	"github.com/gin-gonic/gin"
)

// Create 创建调整结算记录
func Create(ctx *gin.Context) {
	global.OrderAdjustLock.Lock()
	defer global.OrderAdjustLock.Unlock()

	var req types.OrderAdjustSettleCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 获取用户ID
	userID, err := xhttp.GetUserID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 创建调整结算记录
	err = orderAdjustSettleService.NewService().Create(ctx, req, userID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
