package deliverScopeService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/deliverScopeDao"
	"base/global"
	"base/model"
	"context"
	"errors"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, servicePointID primitive.ObjectID, points []model.PointLocation, centerPoint model.PointLocation) error
	InitScope(ctx context.Context, servicePointID primitive.ObjectID, centerPoint model.PointLocation) error
	GetByServicePointID(ctx context.Context, servicePointID primitive.ObjectID) (model.DeliverScope, error)
}

type deliverScopeService struct {
	rdb             *redis.Client
	deliverScopeDao deliverScopeDao.DaoInt
}

func NewDeliverScopeService() ServiceInterface {
	return deliverScopeService{
		rdb:             global.RDBDefault,
		deliverScopeDao: dao.DeliverScopeDao,
	}
}

func (s deliverScopeService) Upsert(ctx context.Context, servicePointID primitive.ObjectID, points []model.PointLocation, centerPoint model.PointLocation) error {
	data, err := s.GetByServicePointID(ctx, servicePointID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		data.ID = primitive.NewObjectID()
		data.ServicePointID = servicePointID
		data.PointList = points
		data.CenterPoint = centerPoint
		data.CreatedAt = time.Now().UnixMilli()
		err := s.deliverScopeDao.Create(ctx, data)
		if err != nil {
			return err
		}
		return nil
	}

	// 更新
	update := bson.M{
		"point_list":   points,
		"center_point": centerPoint,
	}
	err = s.deliverScopeDao.UpdateOne(ctx, bson.M{"service_point_id": servicePointID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, data.ServicePointID)
	return nil
}

func (s deliverScopeService) InitScope(ctx context.Context, servicePointID primitive.ObjectID, centerPoint model.PointLocation) error {
	data, err := s.GetByServicePointID(ctx, servicePointID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		data.ID = primitive.NewObjectID()
		data.ServicePointID = servicePointID
		data.CenterPoint = centerPoint
		data.CreatedAt = time.Now().UnixMilli()

		points := []model.PointLocation{
			{
				Longitude: centerPoint.Longitude + 0.0015,
				Latitude:  centerPoint.Latitude,
			},
			{
				Longitude: centerPoint.Longitude,
				Latitude:  centerPoint.Latitude + 0.0015,
			},
			{
				Longitude: centerPoint.Longitude - 0.0015,
				Latitude:  centerPoint.Latitude - 0.0015,
			},
		}

		data.PointList = points
		err := s.deliverScopeDao.Create(ctx, data)
		if err != nil {
			return err
		}
		return nil
	}
	return xerr.NewErr(xerr.ErrParamError, nil, "已存在")
}

func (s deliverScopeService) GetByServicePointID(ctx context.Context, servicePointID primitive.ObjectID) (model.DeliverScope, error) {
	m := get(s.rdb, servicePointID)
	if m.ID == primitive.NilObjectID {
		filter := bson.M{
			"service_point_id": servicePointID,
		}
		data, err := s.deliverScopeDao.Get(ctx, filter)
		if err != nil {
			return model.DeliverScope{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}
