package productCollect

import (
	"base/core/xhttp"
	"base/service/productCollectService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		IDs []string `json:"ids" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, i := range req.IDs {
		id, err := util.ConvertToObjectWithNote(i, "收藏信息ID")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	err = productCollectService.NewProductCollectService().Delete(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
