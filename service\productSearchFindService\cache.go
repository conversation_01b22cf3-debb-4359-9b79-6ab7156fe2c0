package productSearchFindService

import (
	"context"
	"github.com/go-redis/redis/v8"
	"time"
)

var cache = "search-find:"

func get(r *redis.Client) []redis.Z {
	key := cache
	ctx := context.Background()

	//	查询所有
	list := r.Z<PERSON>angeWithScores(ctx, key, 0, -1).Val()

	return list
}

func set(r *redis.Client, list []string) {
	key := cache
	ctx := context.Background()
	r.Del(ctx, key)
	for i, s := range list {
		r.ZAdd(ctx, key, &redis.Z{
			Score:  float64(i),
			Member: s,
		})
	}

	r.Expire(ctx, key, time.Hour*24*60)
}

//
//func del(r *redis.Client, id primitive.ObjectID) {
//	r.Del(context.Background(), cache+id.Hex())
//}
