package purchaseCartService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/purchaseCartDao"
	"base/global"
	"base/model"
	"base/service/productService"
	"base/util"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, supplierID, productID primitive.ObjectID, num int) error
	List(ctx context.Context, supplierID primitive.ObjectID) ([]model.PurchaseCart, error)
	GetNum(ctx context.Context, supplierID primitive.ObjectID) (int, error)
	Delete(ctx context.Context, supplierID primitive.ObjectID, productID primitive.ObjectID) error
	//UpdateCount(ctx context.Context, id primitive.ObjectID, num int) error
	RemoveCartProduct(ctx context.Context, content string) error
}

type purchaseCartService struct {
	mdb             *mongo.Database
	purchaseCartDao purchaseCartDao.DaoInt
	productS        productService.ServiceInterface
}

func NewPurchaseCartService() ServiceInterface {
	return purchaseCartService{
		mdb:             global.MDB,
		purchaseCartDao: dao.PurchaseCartDao,
		productS:        productService.NewProductService(),
	}
}

func (s purchaseCartService) Upsert(ctx context.Context, supplierID, productID primitive.ObjectID, num int) error {
	filter := bson.M{"supplier_id": supplierID, "product_id": productID}

	product, err := s.productS.Get(ctx, productID)
	if err != nil {
		return err
	}

	if product.Stock < num {
		return xerr.NewErr(xerr.ErrParamError, nil, "数量不能大于库存")
	}

	cart, err := s.purchaseCartDao.GetByFilter(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	// 不存在
	if errors.Is(err, mongo.ErrNoDocuments) {
		data := model.PurchaseCart{
			ID:                primitive.NewObjectID(),
			SupplierID:        supplierID,
			ProductID:         productID,
			ProductSupplierID: product.SupplierID,
			Count:             num,
		}
		err = s.purchaseCartDao.Insert(ctx, data)
		if err != nil {
			return err
		}
		return nil
	}
	// 存在
	err = s.purchaseCartDao.Update(ctx, bson.M{"_id": cart.ID}, bson.M{
		"$set": bson.M{
			"count": num,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s purchaseCartService) GetNum(ctx context.Context, supplierID primitive.ObjectID) (int, error) {
	filter := bson.M{
		"supplier_id": supplierID,
	}
	//i, err := s.db.Count(ctx, filter)
	//if err != nil {
	//	return 0, err
	//}

	carts, err := s.purchaseCartDao.List(ctx, filter)
	if err != nil {
		return 0, err
	}
	var i int
	for _, cart := range carts {
		i += cart.Count
	}

	return i, nil
}

//
//func (s purchaseCartService) UpdateCount(ctx context.Context, id primitive.ObjectID, num int) error {
//	filter := bson.M{
//		"_id": id,
//	}
//
//	update := bson.M{
//		"$set": bson.M{
//			"count": num,
//		},
//	}
//
//	err := s.purchaseCartDao.Update(ctx, filter, update)
//	if err != nil {
//		return err
//	}
//
//	return nil
//}

func (s purchaseCartService) List(ctx context.Context, supplierID primitive.ObjectID) ([]model.PurchaseCart, error) {
	filter := bson.M{
		"supplier_id": supplierID,
	}

	list, err := s.purchaseCartDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s purchaseCartService) Delete(ctx context.Context, supplierID primitive.ObjectID, productID primitive.ObjectID) error {

	filter := bson.M{
		"supplier_id": supplierID,
		"product_id":  productID,
	}
	err := s.purchaseCartDao.Delete(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

type MNSRemoveForSupplier struct {
	SupplierID    string   `json:"supplier_id"`
	ProductIDList []string `json:"product_id_list"`
}

func (s purchaseCartService) RemoveCartProduct(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("RemoveCartProduct error:%v", err)
			return
		}
	}()

	var data MNSRemoveForSupplier
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	if len(data.ProductIDList) < 1 {
		return nil
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, data.SupplierID)
	if err != nil {
		return err
	}

	var productIDs []primitive.ObjectID
	for _, s2 := range data.ProductIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s2)
		if err != nil {
			return err
		}
		productIDs = append(productIDs, id)
	}

	filter := bson.M{
		"supplier_id": supplierID,
		"product_id": bson.M{
			"$in": productIDs,
		},
	}
	err = s.purchaseCartDao.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}
