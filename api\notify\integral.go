package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/service/integralOrderService"
	"base/service/messageService"
	"encoding/json"
	"errors"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func PayNotifyIntegralAgentCollect(ctx *gin.Context) {
	ctx.Set("rid", "agent collect integral notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := integralOrderService.NewIntegralOrderService().NotifyPayStatus(ctx, res)
		if errors.Is(err, mongo.ErrNoDocuments) {
			l.<PERSON>rro<PERSON>("bizOrderNo：%s支付成功,但是无相关订单", res.BizOrderNo)
			messageService.NewMessageService().SendWarning("13518757974", "debt agentCollect:"+res.BizOrderNo, "支付成功，无订单")
			xhttp.NotifySuccess(ctx)
			return
		}
		if err != nil {
			l.Errorf("积分订单代收-回调更新失败")
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("托管代收-积分订单-回调未对接：", string(bytes))
	}
}

func PayNotifyIntegralCancel(ctx *gin.Context) {
	ctx.Set("rid", "integral cancel notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := integralOrderService.NewIntegralOrderService().NotifyCancelStatus(ctx, res)
		if err != nil {
			l.Errorf("取消订单-积分-回调更新失败")
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("取消订单-积分-回调未对接：", string(bytes))
	}

}

func PayNotifyIntegralSignalAgentPay(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := integralOrderService.NewIntegralOrderService().NotifyPaySignalAgentPayStatus(ctx, res)
		if err != nil {
			l.Errorf("单笔代付-回调更新失败")
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("单笔代付-回调未对接：", string(bytes))
	}

	/*
			代金券
			{"buyerBizUserId":"b6120808-60c2-4111-b21d-e0f75c0c11b7",
		"amount":1,"orderNo":"1655848777845579776",
		"extendInfo":"微信支付，支付者b6120808-60c2-4111-b21d-e0f75c0c11b7",
		"payDatetime":"2023-05-09 16:14:47",
		"bizOrderNo":"586b78be-1325-4254-872c-2429b320c261","status":"OK"}
	*/

}
