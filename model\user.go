package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type User struct {
	ID             primitive.ObjectID `bson:"_id"  json:"id"`
	Mobile         string             `bson:"mobile" json:"mobile"` // 手机号
	OpenID         string             `bson:"open_id" json:"open_id"`
	UnionID        string             `bson:"union_id" json:"union_id"`
	Note           string             `bson:"note" json:"note"`                         // 备注
	Password       string             `bson:"password" json:"password"`                 // 密码
	ObjectTypeList []ObjectType       `json:"object_type_list" bson:"object_type_list"` // 对象列表
	CreatedAt      int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt      int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt      int64              `bson:"deleted_at" json:"deleted_at"`
}

//type AddressType int

// Address 用户收获地址
type Address struct {
	ID                      primitive.ObjectID `bson:"_id"  json:"id"`
	UserID                  primitive.ObjectID `bson:"user_id" json:"user_id"`
	BuyerID                 primitive.ObjectID `bson:"buyer_id" json:"buyer_id"`
	IsAssignServicePoint    bool               `bson:"is_assign_service_point" json:"is_assign_service_point"` // 分配服务仓情况
	ServicePointID          primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`
	ServicePointName        string             `bson:"service_point_name" json:"service_point_name"` // 服务仓名称
	StationID               primitive.ObjectID `bson:"station_id" json:"station_id"`
	StationName             string             `bson:"station_name" json:"station_name"`                 // 服务仓名称
	Address                 string             `bson:"address" json:"address"`                           // 具体地址
	Contact                 Contact            `bson:"contact" json:"contact"`                           // 联系人信息
	Location                Location           `bson:"location" json:"location"`                         // 地址经纬度
	IsDefault               bool               `bson:"is_default" json:"is_default"`                     // 默认地址
	ShopHeadImg             FileInfo           `json:"shop_head_img" bson:"shop_head_img"`               // 收获地址门头照-图片
	BusinessLicenseImg      FileInfo           `json:"business_license_img" bson:"business_license_img"` // 收获营业执照-图片
	Entity                  int                `json:"entity" bson:"entity"`                             // 1 有 2 无
	ApplyReason             string             `json:"apply_reason" bson:"apply_reason"`                 // 申请说明
	DeliverType             []DeliverType      `json:"deliver_type" bson:"deliver_type"`                 // 配送方式
	DeliverFee              int                `json:"deliver_fee" bson:"deliver_fee"`                   // 配送费
	SubsidyAmount           int                `json:"subsidy_amount" bson:"subsidy_amount"`             // 配送费补贴规则-门槛
	SubsidyPercent          int                `json:"subsidy_percent" bson:"subsidy_percent"`           // 配送费补贴规则-比例
	Note                    string             `json:"note" bson:"note"`                                 // 备注
	AuditStatus             AuditStatusType    `bson:"audit_status" json:"audit_status"`                 // 认证审核状态
	AuditFailReason         string             `bson:"audit_fail_reason" json:"audit_fail_reason"`
	DeliverFreeBegin        int64              `json:"deliver_free_begin" bson:"deliver_free_begin"`                 // 配送限免期-开始
	DeliverFreeEnd          int64              `json:"deliver_free_end" bson:"deliver_free_end"`                     // 配送限免期-结束
	LogisticsNote           string             `json:"logistics_note" bson:"logistics_note"`                         // 物流备注
	LogisticsUnitFee        int                `json:"logistics_unit_fee" bson:"logistics_unit_fee"`                 // 物流费单价
	InstantDeliver          []InstantDeliver   `json:"instant_deliver" bson:"instant_deliver"`                       // 即时配送
	PriceLevel              int                `json:"price_level" bson:"price_level"`                               // 价格等级（加价） +0 +5 +10
	ServiceFee              int                `json:"service_fee" bson:"service_fee"`                               // 服务费
	ServiceFeeRebatePercent int                `json:"service_fee_rebate_percent" bson:"service_fee_rebate_percent"` // 服务费-返利
	UserType                string             `json:"user_type" bson:"user_type"`                                   // normal  YHT
	CreatedAt               int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt               int64              `bson:"updated_at" json:"updated_at"`
}

// DeliverSubsidyAmount  int                `json:"deliver_subsidy_amount" bson:"deliver_subsidy_amount"`   // 配送费补贴规则-门槛
// DeliverSubsidyPercent int                `json:"deliver_subsidy_percent" bson:"deliver_subsidy_percent"` // 配送费补贴规则-比例

// BuyerType             BuyerType          `json:"buyer_type" bson:"buyer_type"`

type DeliverSubsidyRule struct {
	Amount  int `json:"amount" bson:"amount"`   // 金额
	Percent int `json:"percent" bson:"percent"` // 比例
}

type GeoCoder struct {
	Status  int            `json:"status"`
	Message string         `json:"message"`
	Result  GeoCoderResult `json:"result"`
}

type GeoCoderResult struct {
	AdInfo struct {
		AdCode       string `json:"adcode"`
		Province     string `json:"province"`
		ProvinceCode string `json:"province_code"`
		City         string `json:"city"`
		CityCode     string `json:"city_code"`
		District     string `json:"district"`
		DistrictCode string `json:"district_code"`
	} `json:"ad_info"`
}
