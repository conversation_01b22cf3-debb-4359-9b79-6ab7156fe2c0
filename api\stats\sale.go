package stats

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/supplierService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sort"
)

// SaleSupplier 供应商销售
func SaleSupplier(ctx *gin.Context) {
	var req = struct {
		TimeBegin int64 `json:"time_begin"`
		TimeEnd   int64 `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{}
	filter["order_status"] = model.OrderStatusTypeFinish
	filter["pay_status"] = model.PayStatusTypePaid
	if req.TimeBegin == 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "时间不能为0"))
		return
	}

	filter["created_at"] = bson.M{
		"$gte": req.TimeBegin,
		"$lte": req.TimeEnd,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
	}

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mDebt := make(map[primitive.ObjectID][]model.OrderDebt)
	for _, v := range debts {
		mDebt[v.SupplierID] = append(mDebt[v.SupplierID], v)
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)
	for _, re := range refunds {
		mRefund[re.SupplierID] = append(mRefund[re.SupplierID], re)
	}

	mSupplier := make(map[primitive.ObjectID][]model.Order)
	for _, order := range orders {
		mSupplier[order.SupplierID] = append(mSupplier[order.SupplierID], order)
	}

	var l []PerSupplier
	for id, oList := range mSupplier {
		supplier, err := supplierService.NewSupplierService().Get(ctx, id)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		var totalAmount int
		for _, order := range oList {
			totalAmount += order.PaidAmount
		}

		var refundAmount int
		var shipRefundAmount int
		var afterSaleRefundAmount int
		var afterSaleRefundPaidAmount int
		for _, refund := range mRefund[id] {
			// 加运费
			amount := refund.Amount + refund.TotalTransportFee
			refundAmount += amount
			if refund.RefundType == model.RefundTypeQuality {
				shipRefundAmount += amount
				continue
			}
			if refund.RefundType == model.RefundTypeAfterSale {
				afterSaleRefundAmount += amount
				if refund.RefundResult.PayStatus == "success" {
					afterSaleRefundPaidAmount += amount
				}
				continue
			}
		}

		// 补差
		var debtAmount int
		var debtPaidAmount int
		for _, v := range mDebt[id] {
			// 加运费
			amount := v.TotalProductAmount
			//amount := v.TotalProductAmount + v.TotalTransportFee
			debtAmount += amount
			if v.PayStatus == model.PayStatusTypePaid {
				debtPaidAmount += amount
			}
		}

		var finalAmount int

		finalAmount = totalAmount - shipRefundAmount - afterSaleRefundPaidAmount + debtPaidAmount

		item := PerSupplier{
			SupplierID:                id,
			SupplierName:              supplier.ShopSimpleName,
			TotalAmount:               totalAmount,
			RefundAmount:              refundAmount,
			ShipRefundAmount:          shipRefundAmount,
			AfterSaleRefundAmount:     afterSaleRefundAmount,
			AfterSaleRefundPaidAmount: afterSaleRefundPaidAmount,
			DebtAmount:                debtAmount,
			DebtPaidAmount:            debtPaidAmount,
			FinalAmount:               finalAmount,
		}
		l = append(l, item)
	}

	sort.Sort(supplierMonthSort(l))

	xhttp.RespSuccess(ctx, l)
}
