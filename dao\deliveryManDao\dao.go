package deliveryManDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.DeliveryMan) error
	Delete(ctx context.Context, filter bson.M) error
	Update(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.DeliveryMan, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliveryMan, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.DeliveryMan, error)
}

type deliveryManDao struct {
	db *mongo.Collection
}

func (s deliveryManDao) Create(ctx context.Context, data model.DeliveryMan) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s deliveryManDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s deliveryManDao) Delete(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s deliveryManDao) Get(ctx context.Context, filter bson.M) (model.DeliveryMan, error) {
	var data model.DeliveryMan
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.DeliveryMan{}, err
	}
	return data, nil
}

// ListByPage 查询
func (s deliveryManDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliveryMan, int64, error) {
	var list []model.DeliveryMan
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// List 查询
func (s deliveryManDao) List(ctx context.Context, filter bson.M) ([]model.DeliveryMan, error) {
	var list []model.DeliveryMan

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewDeliveryManDao(collect string) DaoInt {
	return deliveryManDao{
		db: global.MDB.Collection(collect),
	}
}
