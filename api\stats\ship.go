package stats

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderQualityService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetWarehouseShipDataTemp 仓库发货统计 临时备货
func GetWarehouseShipDataTemp(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		StationID      string `json:"station_id"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}


	upList, err := orderQualityService.NewOrderQualityService().ListQualityAll(ctx, ts, servicePointID)
	var ids []primitive.ObjectID
	for _, i := range upList {
		for _, v := range i.OrderList {
			ids = append(ids, v.OrderID)
		}
	}

	list, err := orderService.NewOrderService().ListByOrderIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var amount int
	var w int
	mSingle := make(map[primitive.ObjectID]int)
	mBuyer := make(map[primitive.ObjectID]int)
	mSupplier := make(map[primitive.ObjectID]int)
	var countProduct int
	for _, v := range list {
		amount += v.TotalAmount
		for _, i := range v.ProductList {
			mSingle[i.ProductID]++
			w += i.RoughWeight * i.Num
			countProduct += i.Num
		}
		mSupplier[v.SupplierID]++
		mBuyer[v.BuyerID] = 0
	}

	r := shipData{
		TotalOrder:    len(list),
		TotalSupplier: len(mSupplier),
		TotalBuyer:    len(mBuyer),
		TotalProduct:  countProduct,
		TotalSingle:   len(mSingle),
		TotalWeight:   w,
		TotalAmount:   amount,
	}

	xhttp.RespSuccess(ctx, r)
}

func backAmount(orders []model.Order) int {
	var amount int
	for _, order := range orders {
		amount += order.TotalAmount
	}
	return amount
}

func backWeight(orders []model.Order) int {
	var w int
	for _, order := range orders {
		for _, p := range order.ProductList {
			w += p.RoughWeight * p.Num
		}
	}
	return w
}

func backProduct(orders []model.Order) int {
	var sum int
	for _, order := range orders {
		for _, p := range order.ProductList {
			sum += p.Num
		}
	}
	return sum
}

func backSingle(orders []model.Order) int {
	m := make(map[primitive.ObjectID]int)
	for _, order := range orders {
		for _, p := range order.ProductList {
			m[p.ProductID] = 0
		}
	}
	return len(m)
}

func backBuyer(orders []model.Order) int {
	m := make(map[primitive.ObjectID]int)
	for _, order := range orders {
		m[order.BuyerID] = 0
	}
	return len(m)
}

type shipData struct {
	PointID       primitive.ObjectID `json:"point_id"`
	PointName     string             `json:"point_name"`
	TotalOrder    int                `json:"total_order"`
	TotalAmount   int                `json:"total_amount"`
	TotalWeight   int                `json:"total_weight"`
	TotalBuyer    int                `json:"total_buyer"`
	TotalSupplier int                `json:"total_supplier"`
	TotalProduct  int                `json:"total_product"`
	TotalSingle   int                `json:"total_single"`
}
