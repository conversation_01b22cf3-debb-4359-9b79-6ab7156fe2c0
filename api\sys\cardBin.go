package sys

import (
	"base/core/xhttp"
	"base/service/payAccountService"
	"github.com/gin-gonic/gin"
)

// CardBin 支付卡
func CardBin(ctx *gin.Context) {
	var req struct {
		CardNumber string `json:"card_number" validate:"required"`
	}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	res, err := payAccountService.NewPayAccountService().CardBin(ctx, req.CardNumber)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//byCode, err := bankInfoService.NewBankInfoService().GetByCode(ctx, res.CardBinInfo.BankCode)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	xhttp.RespSuccess(ctx, res.CardBinInfo)
}
