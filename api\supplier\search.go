package supplier

import (
	"base/core/xhttp"
	"base/service/supplierService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func Search(ctx *gin.Context) {
	var req = struct {
		Content string `json:"content" validate:"required"`
		Page    int64  `uri:"page" validate:"min=1"`
		Limit   int64  `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"$or": bson.A{
			bson.M{
				"shop_name": bson.M{
					"$regex": req.Content,
				},
			},
			bson.M{
				"shop_simple_name": bson.M{
					"$regex": req.Content,
				},
			},
			bson.M{
				"contact_user": bson.M{
					"$regex": req.Content,
				},
			},
		},
		"deleted_at": 0,
	}

	list, count, err := supplierService.NewSupplierService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}
