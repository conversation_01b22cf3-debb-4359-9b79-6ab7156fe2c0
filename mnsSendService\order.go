package mnsSendService

import (
	"base/model"
	"base/util"
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SendNormalOrderDivide 订单分账
func (s *MnsClient) SendNormalOrderDivide(ctx context.Context, orderID primitive.ObjectID, seconds int64) {
	_ = ctx

	number := util.RangeRandom(1, 120)

	addSeconds := (time.Second * time.Duration(number)).Seconds()

	var t int64

	t = seconds + int64(addSeconds)

	content := encodeContentStruct(model.MNSOrder{
		OrderID: orderID.Hex(),
	})

	msg := model.MNSOrderDivideNormal + "@" + content

	s.send(msg, t)

}

// SendDivideFlatCheck 分账检查
func (s *MnsClient) SendDivideFlatCheck(ctx context.Context, agentPayID primitive.ObjectID) {
	_ = ctx

	var t int64

	addSeconds := (time.Second * 10).Seconds()

	t = int64(addSeconds)

	content := encodeContentStruct(model.MNSAgentPay{
		AgentPayID: agentPayID.Hex(),
	})

	msg := model.MNSOrderDivideFlatCheck + "@" + content

	s.send(msg, t)
}

// SendDivideEndCheck 分账检查
func (s *MnsClient) SendDivideEndCheck(ctx context.Context, agentPayID primitive.ObjectID) {
	_ = ctx

	var t int64

	addSeconds := (time.Second * 10).Seconds()

	t = int64(addSeconds)

	content := encodeContentStruct(model.MNSAgentPay{
		AgentPayID: agentPayID.Hex(),
	})

	msg := model.MNSOrderDivideEndCheck + "@" + content

	s.send(msg, t)
}

// SendOrderCloseParent 订单检查父单
func (s *MnsClient) SendOrderCloseParent(ctx context.Context, parentOrderID primitive.ObjectID, delaySecond int64) {
	_ = ctx

	var t int64

	t = delaySecond

	content := encodeContentStruct(model.MNSOrder{
		OrderID: parentOrderID.Hex(),
	})

	msg := model.MNSOrderCloseParent + "@" + content

	s.send(msg, t)
}

// SendShipSettle 发货结算
func (s *MnsClient) SendShipSettle(ctx context.Context, orderIDList []primitive.ObjectID, delaySecond int64) {
	_ = ctx

	var t int64

	t = delaySecond

	orderIDListStr := make([]string, 0, len(orderIDList))
	for _, orderID := range orderIDList {
		orderIDListStr = append(orderIDListStr, orderID.Hex())
	}

	content := encodeContentStruct(model.MNSOrderList{
		OrderIDList: orderIDListStr,
	})

	msg := model.MNSOrderShipSettle + "@" + content

	s.send(msg, t)
}

// SendDeliverFeeDivide 配送费-分账
func (s *MnsClient) SendDeliverFeeDivide(ctx context.Context, parentID primitive.ObjectID) {
	_ = ctx

	var t int64

	seconds := (time.Hour * time.Duration(4)).Seconds()

	number := util.RangeRandom(1, 60)

	addSeconds := (time.Minute * time.Duration(number)).Seconds()

	t = int64(seconds + addSeconds)

	content := encodeContentStruct(model.MNSParentOrder{
		ParentOrderID: parentID.Hex(),
	})

	msg := model.MNSOrderDivideDeliver + "@" + content

	s.send(msg, t)
}

// SendDebtOrderDivide 补差-订单分账
func (s *MnsClient) SendDebtOrderDivide(ctx context.Context, debtID primitive.ObjectID) {
	_ = ctx

	var t int64

	addSeconds := (time.Minute * time.Duration(120)).Seconds()

	t = int64(addSeconds)

	content := encodeContentStruct(model.MNSDebtOrder{
		DebtOrderID: debtID.Hex(),
	})

	msg := model.MNSOrderDivideDebt + "@" + content

	s.send(msg, t)
}

// SendDeliverFeeDetailGenerate 发送配送费明细生成消息
func (s *MnsClient) SendDeliverFeeDetailGenerate(ctx context.Context, parentOrderID primitive.ObjectID) {
	_ = ctx

	content := encodeContentStruct(model.MNSDeliverFeeDetail{
		ParentOrderID: parentOrderID.Hex(),
	})

	msg := model.MNSDeliverFeeDetailGenerate + "@" + content

	// 立即发送
	s.send(msg, 10)
}

// SendOrderFinalSettle 发送订单最终结算消息
func (s *MnsClient) SendOrderFinalSettle(ctx context.Context, orderID primitive.ObjectID) {
	_ = ctx

	content := encodeContentStruct(model.MNSOrder{
		OrderID: orderID.Hex(),
	})

	msg := model.MNSOrderFinalSettle + "@" + content

	var t int64
	t = 5

	s.send(msg, t)
}
