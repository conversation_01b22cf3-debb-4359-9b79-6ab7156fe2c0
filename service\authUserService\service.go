package authUserService

import (
	"base/dao"
	"base/dao/authUserDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.AuthUser) error
	UpdateUserName(ctx context.Context, id primitive.ObjectID, userName string) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.AuthUser, error)
	List(ctx context.Context, filter bson.M) ([]model.AuthUser, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type authUserService struct {
	authUserDao authUserDao.DaoInt
}

func NewAuthUserService() ServiceInterface {
	return authUserService{
		authUserDao: dao.AuthUserDao,
	}
}

func (s authUserService) Create(ctx context.Context, data model.AuthUser) error {
	err := s.authUserDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s authUserService) UpdateUserName(ctx context.Context, id primitive.ObjectID, userName string) error {
	err := s.authUserDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{"user_name": userName}})
	if err != nil {
		return err
	}
	return nil
}

func (s authUserService) Delete(ctx context.Context, id primitive.ObjectID) error {
	err := s.authUserDao.DeleteOne(ctx, bson.M{
		"buyer_id": id,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s authUserService) List(ctx context.Context, filter bson.M) ([]model.AuthUser, error) {
	list, err := s.authUserDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s authUserService) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.authUserDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s authUserService) Get(ctx context.Context, id primitive.ObjectID) (model.AuthUser, error) {
	data, err := s.authUserDao.Get(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return model.AuthUser{}, err
	}

	return data, nil
}
