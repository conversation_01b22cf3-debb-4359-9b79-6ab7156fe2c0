package station

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/stationService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func UpdateOpen(ctx *gin.Context) {
	var req = struct {
		ID         string           `json:"id"`
		OpenStatus model.OpenStatus `json:"open_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByStation(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if auth.IdentityNo == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请先实名认证"))
		return
	}
	if auth.IndividualBankcardResult != "ok" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请先绑定银行卡"))
		return
	}

	if !auth.IsMobileVerify {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请先验证手机号"))
		return
	}

	err = stationService.NewStationService().UpdateOpen(ctx, id, req.OpenStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func UpdateCommissionRate(ctx *gin.Context) {
	var req = struct {
		ID             string `json:"id"`
		CommissionRate int    `json:"commission_rate" bson:"commission_rate"` // 佣金
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = stationService.NewStationService().UpdateCommissionRate(ctx, id, req.CommissionRate)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func Identity(ctx *gin.Context) {
	var req = struct {
		ID           string `json:"id"`
		IDCardName   string `json:"id_card_name"`
		IDCardNumber string `json:"id_card_number"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = stationService.NewStationService().Identity(ctx, id, req.IDCardName, req.IDCardNumber)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func BankBind(ctx *gin.Context) {
	var req = struct {
		ID     string `json:"id"`
		Mobile string `json:"mobile"`
		CardNo string `json:"card_no"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = stationService.NewStationService().BankBind(ctx, id, req.Mobile, req.CardNo)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func SignProtocol(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	_ = id

	xhttp.RespSuccess(ctx, nil)
}
