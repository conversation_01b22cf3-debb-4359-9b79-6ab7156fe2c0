package mnsSendService

import (
	"base/model"
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SendProductOffLineCheck 商品下架通知发送
func (s *MnsClient) SendProductOffLineCheck(productID primitive.ObjectID, offlineAt int64) {
	content := encodeContentStruct(model.MNSProduct{
		ProductID: productID,
		OfflineAt: offlineAt,
	})

	msg := model.MNSProductOfflineCheck + "@" + content
	var t int64

	duration := time.Minute * 30
	seconds := duration.Seconds()
	t = int64(seconds)

	s.send(msg, t)
}

// SendProductSaleStatsExport 商品销售统计导出
func (s *MnsClient) SendProductSaleStatsExport(beginTime int64, endTime int64) {
	content := encodeContentStruct(model.MNSProductSaleStatsExportTime{
		BeginTime: beginTime,
		EndTime:   endTime,
	})

	msg := model.MNSProductSaleStatsExport + "@" + content

	s.send(msg, 0)
}

func (s *MnsClient) SendProductUp(ctx context.Context, productID primitive.ObjectID) {
	_ = ctx
	content := encodeContentStruct(model.MNSProduct{
		ProductID: productID,
	})

	msg := model.MNSProductUp + "@" + content

	s.send(msg, 5)
}

func (s *MnsClient) SendProductDown(ctx context.Context, productID primitive.ObjectID) {
	_ = ctx

	content := encodeContentStruct(model.MNSProduct{
		ProductID: productID,
	})

	msg := model.MNSProductDown + "@" + content

	s.send(msg, 5)
}
