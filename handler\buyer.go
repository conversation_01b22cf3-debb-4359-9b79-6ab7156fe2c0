package handler

import (
	"base/api/buyer"
	"base/api/buyerGroup"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 采购商
func buyerRouter(r *gin.RouterGroup) {
	r = r.Group("/buyer")

	r.POST("/login/by/wechat", buyer.LoginByWechat)
	r.POST("/login/by/mobile", buyer.LoginByMobile)
	r.POST("/login/send/captcha", buyer.SendLoginCaptcha)
	r.POST("/login/by/pwd", buyer.LoginByPWD)

	r.Use(middleware.CheckToken)

	r.POST("/", buyer.Apply)
	r.POST("/apply/update", buyer.UpdateApply)
	r.POST("/update", buyer.UpdateBuyer)
	r.POST("/avatar/update", buyer.UpdateAvatar)
	r.POST("/license/status/update", buyer.UpdateLicenseStatus)
	r.POST("/service/fee/type/update", buyer.UpdateServiceFee)
	r.POST("/user/type/update", buyer.UpdateUserType)
	r.POST("/user", buyer.GetByUser)
	r.POST("/search", buyer.Search)
	r.POST("/search/address", buyer.SearchByAddress)
	r.POST("/search/mobile", buyer.SearchByLoginMobile)
	r.POST("/search/mobile/v2", buyer.SearchByLoginMobileV2)
	r.POST("/list/latest/active", buyer.LatestActive)
	r.POST("/list/by/station", buyer.ListByStation)
	r.GET("/:id", buyer.Get)
	r.POST("/get", buyer.GetByPost)
	r.POST("/stats/get", buyer.GetBuyerStats)
	r.POST("/export", buyer.ExportBuyer)

	r.POST("/set/real/name", buyer.SetRealName)

	// 银行卡

	//	 交流群
	r.POST("/group/upsert", buyerGroup.Upsert)
	r.POST("/group/list", buyerGroup.List)
	r.POST("/group/list/all", buyerGroup.ListAll)
	r.POST("/group/delete", buyerGroup.Delete)
}
