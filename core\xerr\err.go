package xerr

type Err struct {
	Code int    `json:"code"`
	Err  error  `json:"err"`
	Msg  string `json:"msg"`
}

func (e *Err) Error() string {
	return e.Msg
}

func NewErr(code int, err error, msg ...string) error {
	//pc, file, line, ok := runtime.Caller(1)
	//f := runtime.FuncForPC(pc)
	//if !ok {
	//	return errors.New("WrapError 方法获取堆栈失败")
	//}
	//errMsg := fmt.Sprintf("at %s:%d (Method %s)\nMessage is: %s Cause by: %v", file, line, f.Name(), msg, err)
	//zap.S().Errorf("code: %d,msg: %s", code, errMsg)

	var s string
	if len(msg) > 0 {
		s = msg[0]
	}
	return &Err{
		Code: code,
		Err:  err,
		Msg:  s,
	}
}
