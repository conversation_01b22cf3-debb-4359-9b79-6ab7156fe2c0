package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// ContactMessage 客服消息推送
type ContactMessage struct {
	ID           primitive.ObjectID `json:"id" bson:"_id"`
	UserID       primitive.ObjectID `json:"user_id" bson:"user_id"`
	BuyerID      primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	BuyerName    string             `json:"buyer_name" bson:"buyer_name"`
	ToUserName   string             `json:"to_user_name" bson:"to_user_name"`     // 小程序的原始ID
	FromUserName string             `json:"from_user_name" bson:"from_user_name"` // 发送者的openid
	CreateTime   int64              `json:"create_time" bson:"create_time"`       // 消息创建时间(整型）
	MsgType      string             `json:"msg_type" bson:"msg_type"`             // text
	Content      string             `json:"content" bson:"content"`               // 文本消息内容
	MsgId        int64              `json:"msg_id" bson:"msg_id"`                 // 消息id，64位整型
	CreatedAt    int64              `json:"created_at" bson:"created_at"`
}
