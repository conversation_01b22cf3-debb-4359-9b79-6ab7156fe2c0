package authenticationPersonService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/authenticationPersonDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/util"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// ServiceInterface 银行卡
type ServiceInterface interface {
	//Create(ctx context.Context, mobile string, userID, objectID primitive.ObjectID, objectType model.ObjectType) (primitive.ObjectID, error)
	//UpdateBindMobileStatus(ctx context.Context, id primitive.ObjectID, mobile string, verify bool) error
	//BindMobile(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType, mobile, captcha string) error
	//GetByID(ctx context.Context, id primitive.ObjectID) (model.AuthenticationPerson, error)
	// GetMember 会员信息
	//GetMember(ctx context.Context, id primitive.ObjectID) (interface{}, error)
	GetByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) (model.AuthenticationPerson, error)
}

type authenticationPersonService struct {
	rdb                     *redis.Client
	authenticationPersonDao authenticationPersonDao.DaoInt
	// 支付 会员
	AllInPayS payModule.MemberService
}

func (s authenticationPersonService) GetMember(ctx context.Context, id primitive.ObjectID) (interface{}, error) {
	authentication, err := s.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if authentication.MemberType == pays.MemberTypeCompany {
		res, err := s.AllInPayS.GetMemberInfoForIndividualS(pays.GetMemberInfoReq{
			BizUserId: authentication.PayBizUserId,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	}

	return nil, xerr.NewErr(xerr.ErrParamError, nil, "authentication member type错误")
}

func (s authenticationPersonService) GetByID(ctx context.Context, id primitive.ObjectID) (model.AuthenticationPerson, error) {
	get, err := s.authenticationPersonDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.AuthenticationPerson{}, err
	}
	return get, nil
}

func (s authenticationPersonService) UpdateBindMobileStatus(ctx context.Context, id primitive.ObjectID, mobile string, verify bool) error {
	filter := bson.M{"_id": id}

	update := bson.M{
		"mobile":           mobile,
		"is_mobile_verify": verify,
	}

	err := s.authenticationPersonDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}
func (s authenticationPersonService) BindMobile(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType, mobile, captcha string) error {
	person, err := s.GetByObject(ctx, objectID, objectType)
	if err != nil {
		return err
	}

	res, err := s.AllInPayS.BindPhoneS(pays.BindPhoneReq{
		BizUserId:        person.PayBizUserId,
		Phone:            mobile,
		VerificationCode: captcha,
	})
	if err != nil {
		return err
	}

	_ = res

	err = s.UpdateBindMobileStatus(ctx, person.ID, mobile, true)
	if err != nil {
		return err
	}

	return nil
}

func (s authenticationPersonService) GetByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) (model.AuthenticationPerson, error) {
	filter := bson.M{
		"object_type": objectType,
		"object_id":   objectID,
	}
	authentication, err := s.authenticationPersonDao.Get(ctx, filter)
	if err != nil {
		return model.AuthenticationPerson{}, err
	}
	return authentication, nil
}

func (s authenticationPersonService) Create(ctx context.Context, mobile string, userID, objectID primitive.ObjectID, objectType model.ObjectType) (primitive.ObjectID, error) {
	now := time.Now().UnixMilli()
	mType := pays.MemberTypeIndividual

	// 创建会员
	createMember, err := s.AllInPayS.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType,
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"object_id":   objectID.Hex(),
			"object_type": objectType,
		}})
	if err != nil {
		return primitive.NilObjectID, err
	}

	data := model.AuthenticationPerson{
		ID:             primitive.NewObjectID(),
		UserID:         userID,
		ObjectType:     objectType,
		ObjectID:       objectID,
		Mobile:         mobile,
		IsMobileVerify: false,
		MemberType:     mType,
		PayUserID:      createMember.UserID,
		PayBizUserId:   createMember.BizUserId,
		CreatedAt:      now,
	}

	err = s.authenticationPersonDao.Create(ctx, data)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return data.ID, nil
}

func NewAuthenticationPersonService() ServiceInterface {
	return authenticationPersonService{
		rdb:                     global.RDBDefault,
		authenticationPersonDao: dao.AuthenticationPersonDao,
		AllInPayS:               payModule.NewMember(),
	}
}
