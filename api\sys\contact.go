package sys

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/contactMessageService"
	"base/service/userService"
	"crypto/sha1"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"sort"
	"time"
)

func Contact(ctx *gin.Context) {
	var req ContactTextMessage

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	user, err := userService.NewUserService().GetByOpenID(ctx, model.ObjectTypeBuyer, req.FromUserName)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Errorf("保存客服消息,查询用户信息异常%s", err.Error())
		ctx.Writer.WriteString("success")
		ctx.Abort()
		return
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Warnf("保存客服消息,查询用户信息不存在:%s，openID:%s", err.Error(), req.FromUserName)
		ctx.Writer.WriteString("success")
		ctx.Abort()
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, user.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Errorf("保存客服消息,查询会员信息异常%s", err.Error())
		ctx.Writer.WriteString("success")
		ctx.Abort()
		return
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Warnf("保存客服消息,查询会员信息不存在:%s，openID:%s", err.Error(), req.FromUserName)
		ctx.Writer.WriteString("success")
		ctx.Abort()
		return
	}

	data := model.ContactMessage{
		ID:           primitive.NewObjectID(),
		UserID:       user.ID,
		BuyerID:      buyer.ID,
		BuyerName:    buyer.BuyerName,
		ToUserName:   req.ToUserName,
		FromUserName: req.FromUserName,
		CreateTime:   req.CreateTime,
		MsgType:      req.MsgType,
		Content:      req.Content,
		MsgId:        req.MsgId,
		CreatedAt:    time.Now().UnixMilli(),
	}

	err = contactMessageService.NewContactMessageService().Create(ctx, data)
	if err != nil {
		zap.S().Errorf("保存客服消息异常%s", err.Error())
	}

	ctx.Writer.WriteString("success")
	ctx.Abort()
	return
}

// ContactTextMessage 文本消息
type ContactTextMessage struct {
	ToUserName   string `json:"ToUserName"`   // 小程序的原始ID
	FromUserName string `json:"FromUserName"` // 发送者的openid
	CreateTime   int64  `json:"CreateTime"`   // 消息创建时间(整型）
	MsgType      string `json:"MsgType"`      // text
	Content      string `json:"Content"`      // 文本消息内容
	MsgId        int64  `json:"MsgId"`        // 消息id，64位整型
}

func ContactVerify(ctx *gin.Context) {
	signature := ctx.Query("signature")
	timestamp := ctx.Query("timestamp")
	nonce := ctx.Query("nonce")
	echostr := ctx.Query("echostr")

	token := "xajtcsh6e6lol3mf"
	strs := sort.StringSlice{token, timestamp, nonce}
	sort.Strings(strs)
	str := ""
	for _, s := range strs {
		str += s
	}

	zap.S().Infof("signature：%s", signature)
	zap.S().Infof("timestamp：%s", timestamp)
	zap.S().Infof("nonce：%s", nonce)
	zap.S().Infof("echostr：%s", echostr)

	h := sha1.New()
	h.Write([]byte(str))
	hashCode := fmt.Sprintf("%x", h.Sum(nil))

	if hashCode == signature {
		zap.S().Infof("校验token正确")
		ctx.Writer.WriteString(echostr)
		ctx.Abort()
		return
	}
}
