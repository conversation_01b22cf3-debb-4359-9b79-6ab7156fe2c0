package types

//bind

type BindProductTagReq struct {
	UpdateType string   `json:"update_Type" validate:"oneof=add remove"`
	ProductIDs []string `json:"product_ids" validate:"min=1"`
	TagID      string   `json:"tag_id" validate:"len=24"`
}

// BindSupplierTagReq 供应商
type BindSupplierTagReq struct {
	UpdateType  string   `json:"update_Type" validate:"oneof=add remove"`
	SupplierIDs []string `json:"supplier_ids" validate:"min=1"`
	TagID       string   `json:"tag_id" validate:"len=24"`
}
