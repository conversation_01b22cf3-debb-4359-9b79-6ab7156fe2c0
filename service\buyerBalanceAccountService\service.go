package buyerBalanceAccountService

import (
	"base/dao"
	"base/dao/buyerBalanceAccountDao"
	"base/global"
	"base/model"
	"base/service/aesService"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 会员余额
type ServiceInterface interface {
	UpdateDeposit(ctx context.Context, buyerID primitive.ObjectID, amount int) error
	List(ctx context.Context, filter bson.M) ([]model.BuyerBalanceAccount, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerBalanceAccount, int64, error)
	Get(ctx context.Context, buyerID primitive.ObjectID) (model.BuyerBalanceAccount, error)
}

type buyerBalanceAccountService struct {
	mdb *mongo.Database
	rdb *redis.Client
	l   *zap.SugaredLogger

	aesS aesService.ServiceInterface

	buyerBalanceAccountDao buyerBalanceAccountDao.DaoInt
}

func NewBuyerBalanceAccountService() ServiceInterface {
	return buyerBalanceAccountService{
		mdb:  global.MDB,
		rdb:  global.RDBDefault,
		l:    global.OrderLogger.Sugar(),
		aesS: aesService.NewAesService(),

		buyerBalanceAccountDao: dao.BuyerBalanceAccountDao,
	}
}

func (s buyerBalanceAccountService) List(ctx context.Context, filter bson.M) ([]model.BuyerBalanceAccount, error) {
	list, err := s.buyerBalanceAccountDao.List(ctx, filter)
	return list, err
}

func (s buyerBalanceAccountService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerBalanceAccount, int64, error) {
	list, count, err := s.buyerBalanceAccountDao.ListByPage(ctx, filter, page, limit)
	return list, count, err
}

func (s buyerBalanceAccountService) UpdateDeposit(ctx context.Context, buyerID primitive.ObjectID, amount int) error {
	data, err := s.Get(ctx, buyerID)
	if err != nil {
		return err
	}

	finalAmount := data.Amount + amount
	update := bson.M{
		"amount": finalAmount,
	}
	err = s.buyerBalanceAccountDao.UpdateOne(ctx, bson.M{
		"buyer_id": buyerID,
	}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func (s buyerBalanceAccountService) Get(ctx context.Context, buyerID primitive.ObjectID) (model.BuyerBalanceAccount, error) {
	data, err := s.buyerBalanceAccountDao.Get(ctx, bson.M{"buyer_id": buyerID})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.BuyerBalanceAccount{}, err
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		// 新建
		now := time.Now().UnixMilli()
		newData := model.BuyerBalanceAccount{
			ID:        primitive.NewObjectID(),
			BuyerID:   buyerID,
			Amount:    0,
			CreatedAt: now,
			UpdatedAt: now,
		}
		err = s.buyerBalanceAccountDao.Create(ctx, newData)
		if err != nil {
			return model.BuyerBalanceAccount{}, err
		}

		return newData, err
	}
	return data, nil
}
