package user

//
//func GenTokenByUserID(ctx *gin.Context) {
//	var req = struct {
//		UserID string `json:"user_id"  validate:"required"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	objectID, err := util.ConvertToObject(req.UserID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	user, err := userService.NewUserService().Get(ctx, objectID)
//	if errors.Is(err, mongo.ErrNoDocuments) {
//		xhttp.RespNoExist(ctx)
//		return
//	}
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeToken(req.UserID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	xhttp.RespSuccess(ctx, types.LoginRes{
//		UserID:       user.ID.Hex(),
//		User:         user,
//		AccessToken:  token,
//		RefreshToken: refreshToken,
//		Expires:      expireAt,
//	})
//}
//
//func GenTokenSSO(ctx *gin.Context) {
//	var req = struct {
//		UserID string `json:"user_id"  validate:"required"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	objectID, err := util.ConvertToObject(req.UserID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	user, err := userService.NewUserService().Get(ctx, objectID)
//	if errors.Is(err, mongo.ErrNoDocuments) {
//		xhttp.RespNoExist(ctx)
//		return
//	}
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeToken(req.UserID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	r := types.LoginRes{
//		UserID:       user.ID.Hex(),
//		User:         user,
//		AccessToken:  token,
//		RefreshToken: refreshToken,
//		Expires:      expireAt,
//	}
//
//	admin, err := adminService.NewAdminService().GetByUser(ctx, user.ID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	r.RoleList = admin.RoleList
//	r.AuthList = admin.AuthList
//	if admin.ID == primitive.NilObjectID {
//		//	 非管理员
//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
//		return
//	}
//
//	xhttp.RespSuccess(ctx, r)
//}
