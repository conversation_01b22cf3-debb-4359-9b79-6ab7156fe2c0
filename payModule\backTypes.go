package payModule

// SignAcctProtocol 提现协议签约
type SignAcctProtocol struct {
	BizUserId      string `json:"bizUserId"`      // 必填 商户系统用户标识，商户系统中唯一编号
	AcctProtocolNo string `json:"acctProtocolNo"` // 必填 账户提现协议编号	商户端需保存
	Result         string `json:"result"`         // 必填 签订结果	成功：ok，失败：error
}

// VerifyResult 企业信息审核结果通
type VerifyResult struct {
	BizUserId        string `json:"bizUserId"`        // 必填  商户系统用户标识，商户系统中唯一编号
	Result           int    `json:"result"`           // 必填  2：审核成功。 3：审核失败。
	AccountSetResult int    `json:"accountSetResult"` //       对私银行账户认证结果 2：认证成功。 3：认证失败。  注：个体工商户的对私银行账户四要素认证结果
	CheckTime        string `json:"checkTime"`        // 必填  审核时间
	FailReason       string `json:"failReason"`       //       失败原因  支持返回对私银行账户四要素认证结果
	Remark           string `json:"remark"`           //       备注
}

// UpdateResult 企业会员信息修改结果通知
type UpdateResult struct {
	BizUserId                 string `json:"bizUserId"`                 // 必填  商户系统用户标识，商户系统中唯一编号
	Reqsn                     string `json:"reqsn"`                     // 必填  商户请求流水号
	Result                    int    `json:"result"`                    //       修改结果	2：修改成功。 3：修改失败。
	FailReason                string `json:"failReason"`                //       失败原因
	OcrRegnumComparisonResult int    `json:"ocrRegnumComparisonResult"` //       OCR识别与企业工商认证信息是否一致    0-否 1-是 注：调用【企业会员信息修改】接口，涉及“企业营业执照”OCR识别及比对时，有值
	OcrIdcardComparisonResult int    `json:"ocrIdcardComparisonResult"` //       OCR识别与企业法人实名信息是否一致   0-否 1-是 注：调用【企业会员信息修改】接口，涉及“法人正面、法人反面”OCR识别及比对时，有值
	ResultInfo                string `json:"resultInfo"`                //       比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
}

// OcrComparisonResult 影印件核对结果异步通知
type OcrComparisonResult struct {
	BizUserId                 string             `json:"bizUserId"`                 // 必填  商户系统用户标识，商户系统中唯一编号
	Reqsn                     string             `json:"reqsn"`                     // 必填  商户请求流水号
	OcrRegnumComparisonResult int                `json:"ocrRegnumComparisonResult"` //       OCR识别与企业工商认证信息是否一致 0-否 1-是 该字段与“OCR识别与企业法人实名信息是否一致”字段有一方发生变更即返值  若营业执照未进行识别该字段不返
	OcrIdcardComparisonResult int                `json:"ocrIdcardComparisonResult"` //       OCR识别与企业法人实名信息是否一致  0-否 1-是 该字段与“OCR识别与企业工商认证信息是否一致”字段有一方发生变更即返值 若法人身份证未进行识别该字段不返
	ResultInfo                string             `json:"resultInfo"`                //  必填 比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
	OcrBusLicenseInfo         OcrBusLicenseInfo  `json:"ocrBusLicenseInfo"`         //  必填 比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
	OcrIdCardFrontInfo        OcrIdCardFrontInfo `json:"ocrIdCardFrontInfo"`        //  必填 比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
	OcrIdCardBackInfo         OcrIdCardBackInfo  `json:"ocrIdCardBackInfo"`         //  必填 比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
}

type OcrBusLicenseInfo struct {
	Code       string `json:"code"`       // 必填 000000，则表示渠道查询成功
	Msg        string `json:"msg"`        // 必填 code不为000000，“msg”字段会返回具体的错误原因
	Result     int    `json:"result"`     //     查询结果 0-查询到数据 1-未查到数据
	CreditCode string `json:"creditCode"` //     一证时统一社会信用 三证营业执照号 result为0时有值
	CorpName   string `json:"corpName"`   //     企业名称 result为0时有值
	Forever    string `json:"forever"`    //     true/false,有效期是否永久
	Validate   string `json:"validate"`   //     yyyyMMdd,有效期, 该字段当forever为false时有效
}

type OcrIdCardFrontInfo struct {
	Code     string `json:"code"`     // 必填  000000，则表示渠道查询成功
	Msg      string `json:"msg"`      // 必填  code不为000000，“msg”字段会返回具体的错误原因
	Result   int    `json:"result"`   //      查询结果 0-查询到数据 1-未查到数据
	Name     string `json:"name"`     //      姓名 result为0时有值
	Idcard   string `json:"idcard"`   //      身份证号 result为0时有值
	Gender   string `json:"gender"`   //      性别 取值：男/女，result为0时有值；
	Nation   string `json:"nation"`   //      民族 result为0时有值；
	Birthday string `json:"birthday"` //      生日,格式YYYY/MM/DD result为0时有值；
	Address  string `json:"address"`  //      地址,result为0时有值；
}

type OcrIdCardBackInfo struct {
	Code      string `json:"code"`      // 必填  000000，则表示渠道查询成功
	Msg       string `json:"msg"`       // 必填  code不为000000，“msg”字段会返回具体的错误原因
	Result    int    `json:"result"`    //      查询结果 0-查询到数据 1-未查到数据
	ValidDate string `json:"validDate"` //     有效期，yyyy/MM/dd-yyyy/MM/dd，如果长期则显示中文 result为0时有值
	UnitName  int    `json:"unitName"`  //      所属公安单位
	IsValid   int    `json:"isValid"`   //      是否过期（1未过期，0已过期） result为0时有值
}
