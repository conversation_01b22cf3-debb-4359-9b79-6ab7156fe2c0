package global

import (
	"base/core/config"
	"encoding/json"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"github.com/yop-platform/yop-go-sdk/yop/response"
)

var YeePay *YeePayInfo

type YeePayInfo struct {
	AppID      string            `json:"app_id"`
	PrivateKey string            `json:"private_key"`
	PublicKey  string            `json:"PublicKey"`
	Platform   string            `json:"platform"`
	ReqPriKey  request.IsvPriKey `json:"req_pri_key"`
}

func (*YeePayInfo) DoRequest(yopRequest *request.YopRequest) (*response.YopResponse, error) {
	yopRequest.AppId = YeePay.AppID
	yopRequest.IsvPriKey = YeePay.ReqPriKey

	yopResponse, err := client.DefaultClient.Request(yopRequest)
	if err != nil {
		return nil, err
	}

	yopRequest.RequestId = yopResponse.Metadata.YopRequestId
	yopRequest.IsvPriKey.Value = ""
	yopRequest.PlatformPubKey.Value = ""

	marshal, err := json.Marshal(yopRequest)
	if err != nil {
		return nil, err
	}

	PayLogger.Sugar().Infof("yeePay req:%s", string(marshal))

	bytes, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return nil, err
	}

	if gin.Mode() == gin.DebugMode {
		m := make(map[string]any)

		err = json.Unmarshal(yopResponse.Content, &m)
		if err != nil {
			return nil, err
		}

		delete(m, "sign")
		mm, err := json.Marshal(m)
		if err != nil {
			return nil, err
		}

		PayLogger.Sugar().Infof("yeePay content:%v", string(mm))
	} else {
		PayLogger.Sugar().Infof("yeePay res:%s", string(bytes))
	}

	return yopResponse, nil
}

func initYeePay(pay config.YeePay) {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	var err error

	readFile, err := os.ReadFile(pay.Cer)
	if err != nil {
		log.Fatalf("load merchant private key error:%s", err.Error())
		return
	}

	public, err := os.ReadFile(pay.Public)
	if err != nil {
		log.Fatalf("load merchant private key error:%s", err.Error())
		return
	}

	platform, err := os.ReadFile(pay.Platform)
	if err != nil {
		log.Fatalf("load merchant private key error:%s", err.Error())
		return
	}

	YeePay = &YeePayInfo{
		AppID:      pay.AppID,
		PrivateKey: string(readFile),
		PublicKey:  string(public),
		Platform:   string(platform),
		ReqPriKey:  request.IsvPriKey{Value: string(readFile), CertType: request.RSA2048},
	}

	if gin.Mode() == gin.DebugMode {
		BackHost = backHostDev
	}

	logrus.SetLevel(logrus.ErrorLevel)
}

const NotifyUrlYeePayMerchantRegister = "/api/pay/back/notify/yee/merchant/register" // 入网商户回调
const NotifyUrlYeePayMerchantBusiness = "/api/pay/back/notify/yee/merchant/business" // 业务结果通知

const NotifyUrlYeePayTradeOrder = "/api/pay/back/notify/yee/trade/order"                   // 交易下单
const NotifyUrlYeePayTradeOrderDebt = "/api/pay/back/notify/yee/trade/order/debt"          // 交易下单
const NotifyUrlYeePayAggTutelagePrePay = "/api/pay/back/notify/yee/tutelage/pay"           // 聚合托管下单
const NotifyUrlYeePayAggTutelagePrePayDebt = "/api/pay/back/notify/yee/tutelage/pay/debt"  // 聚合托管下单
const NotifyUrlYeePayAccountBookPay = "/api/pay/back/notify/yee/account/book/pay"          // 记账簿支付
const NotifyUrlYeePayAccountBookPayDebt = "/api/pay/back/notify/yee/account/book/pay/debt" // 记账簿支付
const NotifyUrlYeePayAccountBookDeposit = "/api/pay/back/notify/yee/account/book/deposit"  // 记账簿支付

const NotifyUrlYeePayTradeRefundCancel = "/api/pay/back/notify/yee/trade/refund/cancel"                     // 子单退款
const NotifyUrlYeePayTradeRefundDeliverFee = "/api/pay/back/notify/yee/trade/refund/deliver/fee"            //
const NotifyUrlYeePayTradeRefundAfterSale = "/api/pay/back/notify/yee/trade/refund/after/sale"              // 售后退款
const NotifyUrlYeePayTradeRefundQuality = "/api/pay/back/notify/yee/trade/refund/quality"                   // 品控退款
const NotifyUrlYeePayTradeRefundAdjustSettle = "/api/pay/back/notify/yee/trade/refund/adjust/settle"        // 调整结算退款
const NotifyUrlYeePayDebtPaidRefund = "/api/pay/back/notify/yee/debt/paid/refund"                           // 品控退款
const NotifyUrlYeePayRefundAccountBookCharge = "/api/pay/back/notify/yee/account/book/refund/charge"        // 记账簿-汇款-退款
const NotifyUrlYeePayRefundAccountBookPayCancel = "/api/pay/back/notify/yee/account/book/refund/pay/cancel" // 记账簿-支付-退款

const NotifyUrlYeePayTradeRefundManual = "/api/pay/back/notify/yee/trade/refund/manual" // 手动退款

const NotifyUrlYeePayWithdraw = "/api/pay/back/notify/yee/withdraw" // 提现

const NotifyUrlYeePayTransfer = "/api/pay/back/notify/yee/transfer" // 转账

const NotifyUrlYeeProductModify = "/api/pay/back/notify/yee/product/modify"
