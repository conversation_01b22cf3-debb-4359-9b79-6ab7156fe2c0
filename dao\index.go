package dao

import (
	warehouseLoadFeeDao "base/dao/WarehouseLoadFeeDao"
	"base/dao/adminDao"
	"base/dao/allInPayUserDao"
	"base/dao/auditDao"
	"base/dao/authUserDao"
	"base/dao/authenticationDao"
	"base/dao/authenticationPersonDao"
	"base/dao/bankAccountDao"
	"base/dao/bankInfoDao"
	"base/dao/billDao"
	"base/dao/brandDao"
	"base/dao/browseDao"
	"base/dao/buyerActiveDao"
	"base/dao/buyerBalanceAccountDao"
	"base/dao/buyerBalanceOrderDao"
	"base/dao/buyerBalanceRecordDao"
	"base/dao/buyerDao"
	"base/dao/buyerGroupDao"
	"base/dao/buyerManagerDao"
	"base/dao/cartDao"
	"base/dao/categoryDao"
	"base/dao/commentDao"
	"base/dao/contactMessageDao"
	"base/dao/couponStockDao"
	"base/dao/couponUserDao"
	"base/dao/deliverAssignDao"
	"base/dao/deliverFeeDetailDao"
	"base/dao/deliverFeeRuleDao"
	"base/dao/deliverNoteDao"
	"base/dao/deliverScopeDao"
	"base/dao/deliveryManDao"
	"base/dao/depositAccountDao"
	"base/dao/depositApplyOrderDao"
	"base/dao/depositRecordDao"
	"base/dao/depositSetDao"
	"base/dao/entityDao"
	"base/dao/fruitClassDao"
	"base/dao/generalImgDao"
	"base/dao/indexPartDao"
	"base/dao/indexPartProductApplyDao"
	"base/dao/indexPartProductDao"
	"base/dao/integralAccountDao"
	"base/dao/integralOrderDao"
	"base/dao/integralProductDao"
	"base/dao/integralRecordDao"
	"base/dao/inviteDao"
	"base/dao/invoiceDao"
	"base/dao/invoiceTitleDao"
	"base/dao/marketingAccountDao"
	"base/dao/multiUserDao"
	"base/dao/officialAccountUserDao"
	"base/dao/orderAdjustSettleDao"
	"base/dao/orderAgentPayDao"
	"base/dao/orderDao"
	"base/dao/orderDebtDao"
	"base/dao/orderFinalSettleDao"
	"base/dao/orderQualityDao"
	"base/dao/orderRefundDao"
	"base/dao/orderStockUpDao"
	"base/dao/parentOrderDao"
	"base/dao/payOrderDao"
	"base/dao/productAuditDao"
	"base/dao/productBuyPriceDao"
	"base/dao/productCollectDao"
	"base/dao/productCommissionDao"
	"base/dao/productDao"
	"base/dao/productHistoryDao"
	"base/dao/productImageDao"
	"base/dao/productTagDao"
	"base/dao/productUnitDao"
	"base/dao/profitDao"
	"base/dao/profitSettlementDao"
	"base/dao/promoteDao"
	"base/dao/promoteNewDao"
	"base/dao/promotionsubsidydao"
	"base/dao/protocolDao"
	"base/dao/purchaseCartDao"
	"base/dao/purchaseOrderDao"
	"base/dao/purchaseParentOrderDao"
	"base/dao/regionDao"
	"base/dao/routeDao"
	"base/dao/servicePointCommissionDao"
	"base/dao/servicePointDao"
	"base/dao/shortcutDao"
	"base/dao/signProtocolDao"
	"base/dao/stationDao"
	"base/dao/supplierBillDao"
	"base/dao/supplierCollectDao"
	"base/dao/supplierDao"
	"base/dao/supplierTagDao"
	"base/dao/swipeDao"
	"base/dao/tagDao"
	"base/dao/topicDao"
	"base/dao/trackDao"
	"base/dao/userAddrDao"
	"base/dao/versionDao"
	"base/dao/warehouseDao"
	"base/dao/withdrawApplyOrderDao"
	"base/dao/yeeMerchantDao"
)

var (
	UserAddrDao userAddrDao.UserAddrDaoInt
	CategoryDao categoryDao.CategoryDaoInt
	// TagDao 分类标签
	TagDao     tagDao.DaoInt
	ProductDao productDao.DaoInt

	// ProductTagDao 供应商标签
	ProductTagDao productTagDao.DaoInt

	ProductUnitDao productUnitDao.DaoInt // 单位

	ProductCollectDao productCollectDao.DaoInt // 商品收藏

	ProductImageDao productImageDao.DaoInt // ProductImageDao 商品镜像

	ProductBuyPriceDao productBuyPriceDao.DaoInt

	// FruitClassDao 水果标签
	FruitClassDao fruitClassDao.DaoInt

	//ProductSkuDao productDao.ProductSkuDaoInt
	CartDao cartDao.DaoInt

	BankInfoDao bankInfoDao.DaoInt

	SwipeDao    swipeDao.DaoInt //	首页
	ShortcutDao shortcutDao.DaoInt
	TopicDao    topicDao.DaoInt

	IndexPartDao             indexPartDao.DaoInt // 专区
	IndexPartProductDao      indexPartProductDao.DaoInt
	IndexPartProductApplyDao indexPartProductApplyDao.DaoInt

	RegionDao regionDao.DaoInt // 区域

	AuthenticationDao       authenticationDao.DaoInt // 认证信息
	AuthenticationPersonDao authenticationPersonDao.DaoInt

	YeeMerchantDao yeeMerchantDao.DaoInt

	AuditDao auditDao.DaoInt // AuditDao 通用审核

	ServicePointDao servicePointDao.DaoInt // ServicePointDao 服务点

	StationDao stationDao.DaoInt

	SupplierDao        supplierDao.DaoInt        // SupplierDao 供应商
	SupplierCollectDao supplierCollectDao.DaoInt // 供应商收藏

	SupplierTagDao supplierTagDao.DaoInt // SupplierTagDao 供应商标签

	BankAccountDao bankAccountDao.DaoInt // BankAccountDao 银行账户

	EntityDao entityDao.DaoInt // EntityDao 主体信息

	SignProtocolDao signProtocolDao.DaoInt // SignProtocolDao 签约协议信息

	WarehouseDao warehouseDao.DaoInt // WarehouseDao 集中仓

	RouteDao routeDao.DaoInt // RouteDao 路线

	ServicePointCommissionDao servicePointCommissionDao.DaoInt // ServicePointCommissionDao 服务点佣金

	ProductCommissionDao productCommissionDao.DaoInt // ProductCommissionDao 产品佣金

	AdminDao adminDao.DaoInt // AdminDao 管理员

	PayOrderDao payOrderDao.DaoInt // PayOrderDao 支付单

	OrderDao             orderDao.DaoInt             // OrderDao 订单
	ParentOrderDao       parentOrderDao.DaoInt       // 父单
	OrderDebtDao         orderDebtDao.DaoInt         // 补差订单
	OrderRefundDao       orderRefundDao.DaoInt       // 退款订单
	OrderStockUpDao      orderStockUpDao.DaoInt      // 备货
	OrderQualityDao      orderQualityDao.DaoInt      // 品控
	OrderAdjustSettleDao orderAdjustSettleDao.DaoInt // 订单调整结算记录

	CommentDao commentDao.DaoInt // CommentDao 评论

	DepositSetDao        depositSetDao.DaoInt        // 保证金设置
	DepositAccountDao    depositAccountDao.DaoInt    // 保证金账户
	DepositRecordDao     depositRecordDao.DaoInt     // 保证金记录明细
	DepositApplyOrderDao depositApplyOrderDao.DaoInt // 保证金充值订单

	AllInPayUserDao allInPayUserDao.DaoInt // 支付用户

	CouponStockDao couponStockDao.DaoInt // 优惠券批次
	CouponUserDao  couponUserDao.DaoInt  // 优惠券用户
	BrowseDao      browseDao.DaoInt      // 浏览记录

	BuyerActiveDao buyerActiveDao.DaoInt // 采购商活跃

	BuyerDao buyerDao.BuyerDao // 采购商

	BuyerLinkUserDao buyerManagerDao.DaoInt // 客户经理

	WithdrawApplyOrderDao withdrawApplyOrderDao.DaoInt // 提现

	GeneralImgDao generalImgDao.DaoInt // 通用图片
	ProtocolDao   protocolDao.DaoInt   // 协议

	VersionDao versionDao.DaoInt // 版本

	OrderAgentPayDao  orderAgentPayDao.DaoInt
	ProductHistoryDao productHistoryDao.DaoInt

	MultiUserDao   multiUserDao.DaoInt
	DeliveryManDao deliveryManDao.DaoInt // 配送员

	WarehouseLoadDao warehouseLoadFeeDao.DaoInt // 仓配费

	DeliverFeeRuleDao deliverFeeRuleDao.DaoInt // 配送费规则

	BuyerGroupDao buyerGroupDao.DaoInt // 配送费规则

	InvoiceTitleDao invoiceTitleDao.DaoInt // 发票抬头
	InvoiceDao      invoiceDao.DaoInt      // 发票

	DeliverNoteDao deliverNoteDao.DaoInt // 配送单

	DeliverScopeDao deliverScopeDao.DaoInt

	BillDao         billDao.DaoInt
	SupplierBillDao supplierBillDao.DaoInt

	InviteDao inviteDao.DaoInt

	MarketingAccountDao marketingAccountDao.DaoInt

	IntegralAccountDao integralAccountDao.DaoInt

	IntegralRecordDao integralRecordDao.DaoInt

	IntegralProductDao integralProductDao.DaoInt
	IntegralOrderDao   integralOrderDao.DaoInt

	DeliverAssignDao deliverAssignDao.DaoInt

	OfficialAccountUserDao officialAccountUserDao.DaoInt

	PromoteDao promoteDao.DaoInt

	PromoteNewDao promoteNewDao.DaoInt

	AuthUserDao authUserDao.DaoInt

	ContactMessageDao contactMessageDao.DaoInt

	BuyerBalanceOrderDao buyerBalanceOrderDao.DaoInt

	BuyerBalanceAccountDao buyerBalanceAccountDao.DaoInt

	BuyerBalanceRecordDao buyerBalanceRecordDao.DaoInt

	BrandDao brandDao.DaoInt

	PurchaseCartDao  purchaseCartDao.DaoInt
	PurchaseOrderDao purchaseOrderDao.DaoInt

	PurchaseParentOrderDao purchaseParentOrderDao.DaoInt

	TrackDao trackDao.DaoInt

	PromotionSubsidyDao promotionsubsidydao.DaoInt

	ProfitDao profitDao.DaoInt

	ProfitSettlementDao profitSettlementDao.DaoInt

	ProductAuditDao productAuditDao.DaoInt

	DeliverFeeDetailDao deliverFeeDetailDao.DaoInt // 配送费明细

	OrderFinalSettleDao orderFinalSettleDao.DaoInt
)

// NewOrderAdjustSettleDao 创建调整结算记录DAO实例（不使用服务层）
func NewOrderAdjustSettleDao() orderAdjustSettleDao.DaoInt {
	return orderAdjustSettleDao.NewDao()
}

func Init() {
	RegionDao = regionDao.NewRegionDao("region")

	AuthenticationDao = authenticationDao.NewAuthenticationDao("authentication")
	AuthenticationPersonDao = authenticationPersonDao.NewAuthenticationPersonDao("authentication_person")

	YeeMerchantDao = yeeMerchantDao.NewYeeMerchantDao("yee_merchant")

	//审核
	AuditDao = auditDao.NewAuditDao("audit")

	UserAddrDao = userAddrDao.NewUserAddrDao("user_addr")
	CategoryDao = categoryDao.NewCategoryDao("category")
	TagDao = tagDao.NewTagDao("tag")
	ProductDao = productDao.NewProductDao("product")
	// 产品图片
	ProductTagDao = productTagDao.NewProductTagDao("product_tag")
	ProductUnitDao = productUnitDao.NewProductUnitDao("product_unit")
	ProductCollectDao = productCollectDao.NewProductCollectDao("product_collect")

	FruitClassDao = fruitClassDao.NewFruitClassDao("fruit_class")

	//ProductSkuDao = productDao.NewProductSkuDao("product_sku")
	CartDao = cartDao.NewCartDao("cart")

	BankInfoDao = bankInfoDao.NewBankInfoDao("bank_info")

	//首页
	SwipeDao = swipeDao.NewSwipeDao("swipe")
	ShortcutDao = shortcutDao.NewShortcutDao("shortcut")
	TopicDao = topicDao.NewTopicDao("topic")

	IndexPartDao = indexPartDao.NewIndexPartDao("index_part") // 专区
	IndexPartProductDao = indexPartProductDao.NewIndexPartProductDao("index_part_product")
	IndexPartProductApplyDao = indexPartProductApplyDao.NewIndexPartProductApplyDao("index_part_product_apply")

	// 集中仓
	WarehouseDao = warehouseDao.NewWarehouseDao("warehouse")

	//	服务点
	ServicePointDao = servicePointDao.NewServicePointDao("service_point")

	StationDao = stationDao.NewStationDao("station")

	//	供应商
	SupplierDao = supplierDao.NewSupplierDao("supplier")
	SupplierTagDao = supplierTagDao.NewSupplierTagDao("supplier_tag")
	SupplierCollectDao = supplierCollectDao.NewSupplierCollectDao("supplier_collect")
	//	管理员
	AdminDao = adminDao.NewAdminDao("admin")

	//	运费
	RouteDao = routeDao.NewRouteDao("route")
	// 服务点佣金
	ServicePointCommissionDao = servicePointCommissionDao.NewServicePointCommissionDao("service_point_commission")
	// 产品佣金
	ProductCommissionDao = productCommissionDao.NewProductCommissionDao("product_commission")

	// 支付单
	PayOrderDao = payOrderDao.NewPayOrderDao("pay_order")

	// 订单
	OrderDao = orderDao.NewOrderDao("order")
	OrderDebtDao = orderDebtDao.NewOrderDebtDao("order_debt")              // 补差订单
	OrderRefundDao = orderRefundDao.NewOrderRefundDao("order_refund")      // 退款订单
	OrderStockUpDao = orderStockUpDao.NewOrderStockUpDao("order_stock_up") // 备货
	OrderQualityDao = orderQualityDao.NewOrderQualityDao("order_quality")  // 备货
	OrderAdjustSettleDao = orderAdjustSettleDao.NewDao()                   // 订单调整结算记录
	// 评论
	CommentDao = commentDao.NewCommentDao("comment")
	// 父单（支付单
	ParentOrderDao = parentOrderDao.NewParentOrderDao("parent_order")

	ProductImageDao = productImageDao.NewProductImageDao("product_image")

	ProductBuyPriceDao = productBuyPriceDao.NewProductBuyPriceDao("product_buy_price")

	// 资料相关
	BankAccountDao = bankAccountDao.NewBankCardDao("bank_account")
	EntityDao = entityDao.NewEntityDao("entity")
	SignProtocolDao = signProtocolDao.NewSignProtocolDao("sign_protocol")

	//	保证金
	DepositSetDao = depositSetDao.NewDepositSetDao("deposit_set")
	DepositAccountDao = depositAccountDao.NewDepositAccountDao("deposit_account")
	DepositRecordDao = depositRecordDao.NewDepositRecordDao("deposit_record")
	DepositApplyOrderDao = depositApplyOrderDao.NewDepositApplyOrderDao("deposit_apply_order")
	//	支付
	AllInPayUserDao = allInPayUserDao.NewAllInPayUserDao("all_in_pay_user")

	// 优惠券
	CouponStockDao = couponStockDao.NewDao("coupon_stock")
	CouponUserDao = couponUserDao.NewCouponUserDao("coupon_user")

	BrowseDao = browseDao.NewBrowseDao("browse")

	BuyerActiveDao = buyerActiveDao.NewBuyerActiveDao("buyer_active")

	BuyerDao = buyerDao.NewBuyerDao()

	BuyerLinkUserDao = buyerManagerDao.NewBuyerManagerDao("buyer_manager")

	WithdrawApplyOrderDao = withdrawApplyOrderDao.NewWithdrawApplyOrderDao("withdraw_apply_order")

	GeneralImgDao = generalImgDao.NewGeneralImgDao("general_img")
	ProtocolDao = protocolDao.NewProtocolDao("protocol")

	VersionDao = versionDao.NewVersionDao("version")

	OrderAgentPayDao = orderAgentPayDao.NewOrderAgentPayDao("order_agent_pay")
	ProductHistoryDao = productHistoryDao.NewProductHistoryDao("product_history")

	MultiUserDao = multiUserDao.NewMultiUserDao("multi_user")

	DeliveryManDao = deliveryManDao.NewDeliveryManDao("delivery_man")

	WarehouseLoadDao = warehouseLoadFeeDao.NewWarehouseLoadFeeDao("warehouse_load_fee")

	DeliverFeeRuleDao = deliverFeeRuleDao.NewDeliverFeeRuleDao("deliver_fee_rule")

	BuyerGroupDao = buyerGroupDao.NewBuyerGroupDao("buyer_group")

	//	 发票抬头
	InvoiceTitleDao = invoiceTitleDao.NewInvoiceTitleDao("invoice_title")
	InvoiceDao = invoiceDao.NewInvoiceDao("invoice")

	DeliverNoteDao = deliverNoteDao.NewDeliverNoteDao("deliver_note")

	DeliverScopeDao = deliverScopeDao.NewDeliverScopeDao("deliver_scope")

	BillDao = billDao.NewBillDao("bill")

	SupplierBillDao = supplierBillDao.NewSupplierBillDao("supplier_bill")

	InviteDao = inviteDao.NewInviteDao("invite")

	MarketingAccountDao = marketingAccountDao.NewMarketingAccountDao("marketing_account")

	IntegralAccountDao = integralAccountDao.NewIntegralAccountDao("integral_account")

	IntegralRecordDao = integralRecordDao.NewIntegralRecordDao("integral_record")

	IntegralProductDao = integralProductDao.NewIntegralProductDao("integral_product")
	IntegralOrderDao = integralOrderDao.NewIntegralOrderDao("integral_order")

	DeliverAssignDao = deliverAssignDao.NewDeliverAssignDao("deliver_assign")

	OfficialAccountUserDao = officialAccountUserDao.NewOfficialAccountUserDao("official_account_user")

	PromoteDao = promoteDao.NewPromoteDao("promote")

	PromoteNewDao = promoteNewDao.NewPromoteNewDao("promote_new")

	AuthUserDao = authUserDao.NewAuthUserDao("auth_user")

	ContactMessageDao = contactMessageDao.NewContactMessageDao("contact_message")

	BuyerBalanceOrderDao = buyerBalanceOrderDao.NewBuyerBalanceOrderDao("buyer_balance_order")

	BuyerBalanceAccountDao = buyerBalanceAccountDao.NewBuyerBalanceAccountDao("buyer_balance_account")

	BuyerBalanceRecordDao = buyerBalanceRecordDao.NewBuyerBalanceRecordDao("buyer_balance_record")

	BrandDao = brandDao.NewBrandDao("brand")

	PurchaseCartDao = purchaseCartDao.NewPurchaseCartDao("purchase_cart")
	PurchaseOrderDao = purchaseOrderDao.NewPurchaseOrderDao("purchase_order")
	PurchaseParentOrderDao = purchaseParentOrderDao.NewPurchaseParentOrderDao("purchase_parent_order")

	TrackDao = trackDao.NewTrackDao("track")

	PromotionSubsidyDao = promotionsubsidydao.NewPromotionSubsidyDao("promotion_subsidy")

	ProfitDao = profitDao.NewProfitDao("profit")

	ProfitSettlementDao = profitSettlementDao.NewProfitSettlementDao("profit_settlement")

	ProductAuditDao = productAuditDao.NewProductAuditDao("product_audit")

	DeliverFeeDetailDao = deliverFeeDetailDao.NewDeliverFeeDetailDao("deliver_fee_detail")

	OrderFinalSettleDao = orderFinalSettleDao.NewDao()
}
