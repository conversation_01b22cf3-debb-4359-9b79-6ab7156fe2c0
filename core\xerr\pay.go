package xerr

// 支付
var (
	XerrPayNoOrder         = NewErr(ErrPayOrderNotExist, nil, "订单不存在")
	XerrPayOrderProcessing = NewErr(ErrPayOrderProcessing, nil, "该订单已经在处理中")
	XerrPayAccHasBind      = NewErr(ErrPayAccHasBind, nil, "支付账户用户标识已绑定")

	XerrMobileHasBind    = NewErr(ErrPayMobileHasBind, nil, "手机已绑定")
	XerrVerificationCode = NewErr(ErrPayMobileHasBind, nil, "验证码错误")

	//XerrPayCheckNext = NewErr(ErrPayCheckNext, nil, "进行下次订单支付检查")

	//PayOrderClosed   = NewErr(ErrPayOrderClosed, nil, "订单已关闭")
	//PayOrderNotExist = NewErr(ErrPayOrderNotExist, nil, "订单不存在")
)

const (
// ErrPayOrderClosed = 400
)

const (
	ErrPayOrderNotExist   = 40000
	ErrPayOrderProcessing = 40033
	ErrPayAccHasBind      = 9000
	ErrPayMobileHasBind   = 30024
)

const (
// ErrPayCheckNext = 1
)

var (
	XerrCategoryExistProduct     = NewErr(ErrCategoryExistProduct, nil, "该分类下存在商品，不能删除")
	XerrCategoryExistNextLevel   = NewErr(ErrCategoryExistNextLevel, nil, "该分类下存在下级分类，不能删除")
	XerrDistanceOverScope        = NewErr(ErrOrderOverScope, nil, "超出城市服务仓配送范围")
	ErrBillExportLimit           = NewErr(ErrParamError, nil, "每日只能执行导出10次")
	ErrInviteExist               = NewErr(ErrHasInvited, nil, "已被邀请")
	ErrExistNewUserCouponAccount = NewErr(ErrHasInvited, nil, "存在新人券")
)
