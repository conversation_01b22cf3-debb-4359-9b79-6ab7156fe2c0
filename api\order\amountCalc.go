package order

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/serviceFeeService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AmountCalc 订单费用计算
func AmountCalc(ctx *gin.Context) {
	var req = struct {
		ProductList   []types.PerProductForCalc `json:"product_list" validate:"min=1"`
		BuyerID       string                    `json:"buyer_id"`
		SecondPointID string                    `json:"second_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByID(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = buyer

	if buyer.UserType == "YHT" {
		//	YHT 处理  服务费

	}

	var productAmount int
	var totalWeight int
	var countProduct int
	var totalServiceFee int

	for _, p := range req.ProductList {
		countProduct += p.Num

		price := p.Price

		productAmountPer := price * p.Num

		productAmount += productAmountPer
		totalWeight += p.RoughWeight * p.Num

		// 服务费
		priceServiceFee, err := serviceFeeService.NewServiceFeeService().CalcServiceFeeForProduct(ctx, buyer.ServiceFeeType, price)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		totalServiceFee += priceServiceFee * p.Num

	}

	totalAmount := productAmount + totalServiceFee

	r := CalcRes{
		TotalAmount:        totalAmount,
		TotalProductAmount: productAmount,
		TotalServiceFee:    totalServiceFee,
		TotalWeight:        totalWeight,
	}

	xhttp.RespSuccess(ctx, r)
}

type CalcRes struct {
	TotalAmount        int `json:"total_amount"`
	TotalProductAmount int `json:"total_product_amount"`
	TotalWeight        int `json:"total_weight"`
	TotalServiceFee    int `json:"total_service_fee"` // 服务费
}

type DiscountAmountPer struct {
	ProductID primitive.ObjectID `json:"product_id"`
	Amount    int                `json:"amount"`
}

// func backValidCoupon(list []model.CouponForOrder) model.CouponAccount {
// 	for _, c := range list {
// 		//	金额符合的
// 		if c.Optional {
// 			return c.CouponAccount
// 		}
// 	}
// 	return model.CouponAccount{}
// }
