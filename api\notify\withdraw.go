package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/service/buyerBalanceOrderService"
	"base/service/withdrawApplyOrderService"
	"encoding/json"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PayNotifyWithdraw 提现-供应商
func PayNotifyWithdraw(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := withdrawApplyOrderService.NewWithdrawApplyOrderService().NotifyPayStatus(ctx, res)
		if err != nil {
			l.<PERSON><PERSON><PERSON>("提现-回调更新失败")
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("提现-回调未对接：", string(bytes))
	}
}

// PayNotifyWithdrawBuyer 提现-会员
func PayNotifyWithdrawBuyer(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := buyerBalanceOrderService.NewBuyerBalanceOrderService().NotifyWithdrawPayStatus(ctx, res)
		if err != nil {
			l.Errorf("会员提现-回调更新失败")
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("会员提现-回调未对接：", string(bytes))
	}
}
