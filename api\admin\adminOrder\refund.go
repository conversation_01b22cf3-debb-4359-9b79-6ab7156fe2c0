package adminOrder

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderRefundService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func ListRefundOrder(ctx *gin.Context) {
	var req = struct {
		//ServicePointID string `json:"service_point_id"`
		RefundType     int   `json:"refund_type" validate:"required"`
		AuditStatus    int   `json:"audit_status" validate:"required"`
		WithdrawStatus int   `json:"withdraw_status"` // 撤销状态   0 所有 1 未撤销 2 已撤销
		Page           int64 `json:"page" validate:"min=1"`
		Limit          int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	statusType, err := model.BackAuditStatusType(req.AuditStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	refundType, err := model.BackRefundType(req.RefundType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	list, i, err := orderRefundService.NewOrderRefundService().ListByPage(ctx, refundType, statusType, req.Page, req.Limit, req.WithdrawStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func ListRefundByProduct(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
		Page      int64  `json:"page"`
		Limit     int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"product_id":  productID,
		"refund_type": model.RefundTypeAfterSale,
	}

	list, i, err := orderRefundService.NewOrderRefundService().ListByPageCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func AuditRefundOrder(ctx *gin.Context) {
	var req = struct {
		RefundOrderID string            `json:"refund_order_id" `
		AuditStatus   int               `json:"audit_status"`
		AuditAmount   int               `json:"audit_amount"`
		AuditNote     string            `json:"audit_note"`
		AuditorType   model.AuditorType `json:"auditor_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.RefundOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	statusType, err := model.BackAuditStatusType(req.AuditStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	byID, err := orderRefundService.NewOrderRefundService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	if byID.AuditStatus != model.AuditStatusTypeDoing {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请勿重复审核"))
		return
	}

	if byID.IsWithdraw {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该申请已撤销，请刷新"))
		return
	}

	err = orderRefundService.NewOrderRefundService().Audit(ctx, byID, statusType, req.AuditAmount, req.AuditNote, req.AuditorType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func CountAudit(ctx *gin.Context) {
	var req = struct {
		//ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	filter := bson.M{
		//"service_point_id": pointID,
		"audit_status": model.AuditStatusTypeDoing,
		"auditor_type": model.AuditorTypePlatform,
		"is_withdraw":  false,
	}

	count, err := orderRefundService.NewOrderRefundService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, count)
}
