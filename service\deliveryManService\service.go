package deliveryManService

import (
	"base/dao"
	"base/dao/deliveryManDao"
	"base/global"
	"base/model"
	"base/service/multiUserService"
	"base/service/userService"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, userID, servicePointID primitive.ObjectID, userName, desc string) error
	Update(ctx context.Context, id primitive.ObjectID, userName, desc string) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	ListByServicePoint(ctx context.Context, servicePointID primitive.ObjectID) ([]model.DeliveryMan, error)
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.DeliveryMan, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.DeliveryMan, error)
}

type deliveryManService struct {
	mdb            *mongo.Database
	deliveryManDao deliveryManDao.DaoInt
	UserS          userService.ServiceInterface
	multiUserS     multiUserService.ServiceInterface
}

func NewDeliveryManService() ServiceInterface {
	return deliveryManService{
		mdb:            global.MDB,
		deliveryManDao: dao.DeliveryManDao,
		UserS:          userService.NewUserService(),
		multiUserS:     multiUserService.NewMultiUserService(),
	}
}

func (s deliveryManService) Create(ctx context.Context, userID, servicePointID primitive.ObjectID, userName, desc string) error {
	//filter := bson.M{"user_id": userID}
	//man, err := s.deliveryManDao.Get(ctx, filter)
	//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//	return err
	//}
	//if man.ID != primitive.NilObjectID {
	//	return xerr.NewErr(xerr.ErrParamError, nil, "已存在，请勿重复添加")
	//}

	now := time.Now().UnixMilli()

	data := model.DeliveryMan{
		ID:             primitive.NewObjectID(),
		UserID:         userID,
		ServicePointID: servicePointID,
		UserName:       userName,
		Desc:           desc,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	err := s.deliveryManDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s deliveryManService) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.DeliveryMan, error) {
	filter := bson.M{"user_id": userID}
	man, err := s.deliveryManDao.Get(ctx, filter)
	if err != nil {
		return model.DeliveryMan{}, err
	}

	return man, nil
}

func (s deliveryManService) Get(ctx context.Context, id primitive.ObjectID) (model.DeliveryMan, error) {
	filter := bson.M{"_id": id}
	man, err := s.deliveryManDao.Get(ctx, filter)
	if err != nil {
		return model.DeliveryMan{}, err
	}

	return man, nil
}

func (s deliveryManService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.deliveryManDao.Delete(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s deliveryManService) ListByServicePoint(ctx context.Context, servicePointID primitive.ObjectID) ([]model.DeliveryMan, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
	}
	list, err := s.deliveryManDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s deliveryManService) Update(ctx context.Context, id primitive.ObjectID, userName, desc string) error {
	filter := bson.M{
		"_id": id,
	}
	now := time.Now().UnixMilli()

	err := s.deliveryManDao.Update(ctx, filter, bson.M{
		"$set": bson.M{
			"user_name":  userName,
			"desc":       desc,
			"updated_at": now,
		},
	})
	if err != nil {
		return err
	}
	return nil
}
