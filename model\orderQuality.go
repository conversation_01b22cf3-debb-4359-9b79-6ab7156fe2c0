package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// OrderQuality 品控单
type OrderQuality struct {
	ID                  primitive.ObjectID   `json:"id" bson:"_id"`
	SupplierID          primitive.ObjectID   `json:"supplier_id" bson:"supplier_id"`               // 供应商ID
	SupplierName        string               `json:"supplier_name" bson:"supplier_name"`           // 供应商名称
	ProductID           primitive.ObjectID   `json:"product_id" bson:"product_id"`                 // 商品ID
	CategoryIDs         []primitive.ObjectID `bson:"category_ids" json:"category_ids"`             // 商品分类信息
	PerRoughWeight      int                  `json:"per_rough_weight" bson:"per_rough_weight"`     // 商品毛重
	IsCheckWeight       bool                 `json:"is_check_weight" bson:"is_check_weight"`       // 分拣检查重量  是/否
	HasParam            bool                 `json:"has_param" bson:"has_param"`                   // 规格参数 有/无
	ProductParamType    ProductParamType     `json:"product_param_type" bson:"product_param_type"` //
	StandardAttr        StandardAttr         `json:"standard_attr" bson:"standard_attr"`           // 标品参数
	NonStandardAttr     NonStandardAttr      `json:"non_standard_attr" bson:"non_standard_attr"`   // 非标品参数
	ProductTitle        string               `json:"product_title" bson:"product_title"`           // 商品标题
	ProductCover        FileInfo             `json:"product_cover" bson:"product_cover"`           // 商品封面
	SkuIDCode           string               `json:"sku_id_code" bson:"sku_id_code"`               // 商品SKU编码
	SkuName             string               `json:"sku_name" bson:"sku_name"`                     // 商品SKU名称
	OrderList           []PerOrderTemp       `json:"order_list" bson:"order_list"`                 // 订单列表
	ServicePointID      primitive.ObjectID   `json:"service_point_id" bson:"service_point_id"`     // 中心仓ID
	ServicePointName    string               `json:"service_point_name" bson:"service_point_name"` // 中心仓名称
	SupplierLevel       SupplierLevel        `json:"supplier_level" bson:"supplier_level"`
	StationID           primitive.ObjectID   `json:"station_id" bson:"station_id"`                         // 城市仓ID
	StationName         string               `json:"station_name" bson:"station_name"`                     // 城市仓名称
	QualityDueNum       int                  `json:"quality_due_num" bson:"quality_due_num"`               // 应品控入库数
	QualityDueChangeNum int                  `json:"quality_due_change_num" bson:"quality_due_change_num"` // 应品控变化数
	QualityNum          int                  `json:"quality_num" bson:"quality_num"`                       // 品控入库数
	Amount              int                  `json:"amount" bson:"amount"`                                 // 采购总价
	QualityHas          bool                 `json:"quality_has" bson:"quality_has"`                       // 是否品控
	SortHas             bool                 `json:"sort_has" bson:"sort_has"`                             // 是否分拣
	SortNewOrderNum     int                  `json:"sort_new_order_num" bson:"sort_new_order_num"`         // 分拣-新分拣订单-合单
	StockUpNo           int                  `json:"stock_up_no" bson:"stock_up_no"`                       // 备货 批次
	StockUpDayTime      int64                `json:"stock_up_day_time" bson:"stock_up_day_time"`           // 备货 日期
	ReasonType          int                  `json:"reason_type" bson:"reason_type"`                       // 原因-类型   1  质量 2 缺货
	ReasonImg           FileInfo             `json:"reason_img" bson:"reason_img"`                         // 原因-图片
	PurchaseNote        string               `json:"purchase_note" bson:"purchase_note"`                   // 采购说明-采购门店
	LinkBrandStatus     int                  `json:"link_brand_status" bson:"link_brand_status"`           // 关联品牌状态  2 关联
	LinkBrandID         primitive.ObjectID   `json:"link_brand_id" bson:"link_brand_id"`                   // 关联品牌ID
	LinkBrandName       string               `json:"link_brand_name" bson:"link_brand_name"`               // 关联品牌名称
	CreatedAt           int64                `bson:"created_at" json:"created_at"`
	UpdatedAt           int64                `bson:"updated_at" json:"updated_at"`
	DeletedAt           int64                `bson:"deleted_at" json:"deleted_at"`
}

//QualityHasNum           int                `json:"quality_has_num" bson:"quality_has_num"`                       // 品控入库数

// PerOrderTemp 订单列表
type PerOrderTemp struct {
	OrderID            primitive.ObjectID `json:"order_id" bson:"order_id"`
	SecondPointID      primitive.ObjectID `json:"second_point_id" bson:"second_point_id"`
	SecondPointName    string             `json:"second_point_name" bson:"second_point_name"`
	BuyerID            primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	BuyerName          string             `json:"buyer_name" bson:"buyer_name"`
	DeliverType        DeliverType        `json:"deliver_type" bson:"deliver_type"`                 // 配送方式
	InstantDeliverType int                `json:"instant_deliver_type" bson:"instant_deliver_type"` // 配送方式
	InstantDeliverName string             `json:"instant_deliver_name" bson:"instant_deliver_name"` // 配送方式
	Address            OrderAddress       `json:"address" bson:"address"`                           // 地址
	DueNum             int                `json:"due_num" bson:"due_num"`                           // 应有数量
	DueWeight          int                `json:"due_weight" bson:"due_weight"`                     // 应有重量
	SortNum            int                `json:"sort_num" bson:"sort_num"`                         // 分拣数量
	SortWeight         int                `json:"sort_weight" bson:"sort_weight"`                   // 分拣总重
	HasShip            bool               `json:"has_ship" bson:"has_ship"`                         // 是否已经发货-发货后，不能再更新
	HasQuality         bool               `json:"has_quality" bson:"has_quality"`                   // 是否已品控
	HasSort            bool               `json:"has_sort" bson:"has_sort"`                         // 是否已分拣
	Photo              FileInfo           `json:"photo" bson:"photo"`                               // 分拣图
	PhotoList          []FileInfo         `json:"photo_list" bson:"photo_list"`                     // 品控图列表
	SortUserID         primitive.ObjectID `json:"sort_user_id" bson:"sort_user_id"`                 // 分拣人ID
	SortUserName       string             `json:"sort_user_name" bson:"sort_user_name"`             //
	SortUserMobile     string             `json:"sort_user_mobile" bson:"sort_user_mobile"`         //
}

/*
	ServicePointName string             `json:"service_point_name" bson:"service_point_name"` // 服务点名称
	WarehouseName    string             `json:"warehouse_name" bson:"warehouse_name"`         // 集中仓名称
	WarehouseID      primitive.ObjectID `json:"warehouse_id" bson:"warehouse_id"`             // 集中仓ID
	ServicePointID   primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`     // 服务点ID
*/
//
//type PerServicePoint struct {
//	ServicePointID   primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`     // 服务点ID
//	ServicePointName string             `json:"service_point_name" bson:"service_point_name"` // 服务点名称
//	QualityDueNum    int                `json:"quality_due_num" bson:"quality_due_num"`       // 应品控入库数
//	QualityNum       int                `json:"quality_num" bson:"quality_num"`               // 品控入库数
//}

//type StockUpToDo struct {
//	OrderID          primitive.ObjectID `json:"order_id"`               // 订单ID
//	Address          OrderAddress       `json:"address" bson:"address"` // 地址
//	ServicePointID   primitive.ObjectID `json:"service_point_id"`       // 服务点ID
//	ServicePointName string             `json:"service_point_name"`     // 服务点名称
//	OrderCreateTime  int64              `json:"order_create_time"`      // 下单时间
//	ProductList      []PerProduct       `json:"product_list"`           // 商品列表
//}
//
//type PerProduct struct {
//	ProductID       primitive.ObjectID `json:"product_id"`
//	ProductTitle    string             `json:"product_title"`                              // 商品标题
//	ProductCover    FileInfo           `json:"product_cover"`                              // 商品封面
//	StandardAttr    StandardAttr       `json:"standard_attr" bson:"standard_attr"`         // 标品参数
//	NonStandardAttr NonStandardAttr    `json:"non_standard_attr" bson:"non_standard_attr"` // 非标品参数
//	Num             int                `json:"num"`                                        // 数量
//}
//
//type StockUpInfo struct {
//	ProductID     primitive.ObjectID `json:"product_id"`       // 商品ID
//	OrderIDList   []PerOrder         `json:"order_id_list"`    // 订单ID列表
//	ProductTitle  string             `json:"product_title"`    // 商品标题
//	ProductCover  FileInfo           `json:"product_cover"`    // 商品封面
//	StockUpDueNum int                `json:"stock_up_due_num"` // 应有备货
//	StockUpHasNum int                `json:"stock_up_has_num"` // 已经备货
//}
