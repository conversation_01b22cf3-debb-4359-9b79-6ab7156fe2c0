package adminProduct

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/service/productStatsService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Stats 商品统计查询
func Stats(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	product, err := productService.NewProductService().Get(ctx, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	stats, err := productStatsService.NewProductStatsService().Get(ctx, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := productRes{
		ProductID:          product.ID,
		SupplierSimpleName: product.SupplierSimpleName,
		Stats:              stats,
	}

	xhttp.RespSuccess(ctx, r)
}

type productRes struct {
	ProductID          primitive.ObjectID `json:"product_id"`
	SupplierSimpleName string             `json:"supplier_simple_name"`
	Stats              model.ProductStats `json:"stats"`
}
