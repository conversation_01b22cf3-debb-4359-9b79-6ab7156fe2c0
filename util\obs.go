package util

import (
	"base/core/xerr"
	"github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/global"
	iam "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/region"
	"go.uber.org/zap"
)

// ObsUtil 对象存储
type ObsUtil struct {
	ak     string
	sk     string
	re     string
	point  string
	auth   *global.Credentials
	client *iam.IamClient
}

func NewObsUtil(ak, sk, re, point string) *ObsUtil {
	auth := global.NewCredentialsBuilder().
		WithAk(ak).
		WithSk(sk).
		Build()
	return &ObsUtil{
		ak:    ak,
		sk:    sk,
		re:    re,
		point: point,
		auth:  auth,
		client: iam.NewIamClient(
			iam.IamClientBuilder().
				WithRegion(region.ValueOf(re)).
				WithCredential(auth).
				Build()),
	}
}

func (s ObsUtil) GetSign(bucket, objName string, expire int) (string, error) {
	var obsClient, _ = obs.New(s.ak, s.sk, s.point)

	input := &obs.CreateSignedUrlInput{}
	input.Expires = expire

	input.Method = obs.HttpMethodGet
	input.Bucket = bucket
	input.Key = objName
	urlOutput, err := obsClient.CreateSignedUrl(input)
	if err != nil {
		return "", err
	}

	return urlOutput.SignedUrl, nil
}

func (s ObsUtil) GetTemporaryAccessKeyByToken(action []string, duration int32, resource []string) (*model.Credential, error) {
	request := &model.CreateTemporaryAccessKeyByTokenRequest{}

	var listStatementPolicy = []model.ServiceStatement{
		{
			Effect:   model.GetServiceStatementEffectEnum().ALLOW,
			Action:   action,
			Resource: &resource,
		},
	}

	policyIdentity := &model.ServicePolicy{
		Version:   "1.1",
		Statement: listStatementPolicy,
	}
	durationSecondsToken := duration
	tokenIdentity := &model.IdentityToken{
		DurationSeconds: &durationSecondsToken,
	}

	var listMethodsIdentity = []model.TokenAuthIdentityMethods{
		model.GetTokenAuthIdentityMethodsEnum().TOKEN,
	}
	identityAuth := &model.TokenAuthIdentity{
		Methods: listMethodsIdentity,
		Token:   tokenIdentity,
		Policy:  policyIdentity,
	}
	authbody := &model.TokenAuth{
		Identity: identityAuth,
	}
	request.Body = &model.CreateTemporaryAccessKeyByTokenRequestBody{
		Auth: authbody,
	}
	response, err := s.client.CreateTemporaryAccessKeyByToken(request)
	if err != nil {
		return nil, err
	}
	switch response.HttpStatusCode {
	case 201:
		return response.Credential, nil
	case 400:
		zap.S().Error("obs 错误，参数无效。")
		return nil, xerr.NewErr(xerr.ErrSysBusy, nil)
	case 401:
		zap.S().Error("obs 错误，认证失败。")
		return nil, xerr.NewErr(xerr.ErrSysBusy, nil)
	case 403:
		zap.S().Error("obs 错误，没有操作权限。")
		return nil, xerr.NewErr(xerr.ErrSysBusy, nil)
	case 500:
		zap.S().Error("obs 错误，内部服务错误。")
		return nil, xerr.NewErr(xerr.ErrSysBusy, nil)
	}

	return nil, xerr.NewErr(xerr.ErrSysBusy, nil)
}
