package multiUserService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/multiUserDao"
	"base/global"
	"base/model"
	"base/service/userService"
	"context"
	"errors"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	GetByUserAndObject(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.MultiUser, error)
	Create(ctx context.Context, userID, objectID primitive.ObjectID, note string, objectType model.ObjectType) error
	//Delete(ctx context.Context, id primitive.ObjectID) error
	//List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Admin, int64, error)
	//CheckAdmin(ctx context.Context, userID primitive.ObjectID) (bool, error)
	//CheckSuperAdmin(ctx context.Context, serID primitive.ObjectID) (bool, error)
}

type multiUserService struct {
	rdb          *redis.Client
	multiUserDao multiUserDao.DaoInt
	UserS        userService.ServiceInterface
}

func NewMultiUserService() ServiceInterface {
	return multiUserService{
		rdb:          global.RDBDefault,
		multiUserDao: dao.MultiUserDao,
		UserS:        userService.NewUserService(),
	}
}

func (s multiUserService) Create(ctx context.Context, userID, objectID primitive.ObjectID, note string, objectType model.ObjectType) error {
	now := time.Now().UnixMilli()
	data := model.MultiUser{
		ID:         primitive.NewObjectID(),
		UserID:     userID,
		Note:       note,
		ObjectType: objectType,
		ObjectID:   objectID,
		CreatedAt:  now,
	}

	multiUser, err := s.GetByUserAndObject(ctx, userID, objectType)
	if err != nil {
		return err
	}
	if multiUser.ID == primitive.NilObjectID {
		// 新建
		err = s.multiUserDao.Create(ctx, data)
		if err != nil {
			return err
		}
		return nil
	}

	return xerr.NewErr(xerr.ErrParamError, nil, "用户已存在该类型角色，请勿重复添加")
}

//	func (s multiUserService) Delete(ctx context.Context, userID primitive.ObjectID) error {
//		checkSuperAdmin, err := s.CheckSuperAdmin(ctx, userID)
//		if err != nil {
//			return err
//		}
//		if checkSuperAdmin {
//			return xerr.NewErr(xerr.ErrParamError, nil, "操作错误，不能删除超级管理员")
//		}
//
//		filter := bson.M{
//			"user_id": userID,
//		}
//		err = s.adminDao.Delete(ctx, filter)
//		if err != nil {
//			return err
//		}
//
//		del(s.rdb, userID)
//		return nil
//	}
func (s multiUserService) GetByUserAndObject(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.MultiUser, error) {
	filter := bson.M{
		"object_type": objectType,
		"user_id":     userID,
	}
	data, err := s.multiUserDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.MultiUser{}, err
	}

	return data, nil
}

//
//func (s multiUserService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Admin, int64, error) {
//	list, i, err := s.adminDao.List(context.Background(), filter, page, limit)
//	if err != nil {
//		return nil, 0, err
//	}
//	return list, i, nil
//}
//
//func (s multiUserService) CheckAdmin(ctx context.Context, userID primitive.ObjectID) (bool, error) {
//	user, err := s.GetByUser(ctx, userID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	var f bool
//
//	for _, roleType := range user.RoleList {
//		if roleType == model.RoleTypeNormalAdmin || roleType == model.RoleTypeSuperAdmin {
//			f = true
//			break
//		}
//	}
//	if !f {
//		//zap.S().Warn("非普通管理员：", userID.Hex())
//	}
//
//	return f, nil
//}
