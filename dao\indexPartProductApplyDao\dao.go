package indexPartProductApplyDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 专区-商品
type DaoInt interface {
	Create(ctx context.Context, data model.IndexPartProductApply) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.IndexPartProductApply, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	List(ctx context.Context, filter bson.M) ([]model.IndexPartProductApply, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IndexPartProductApply, int64, error)
	Delete(ctx context.Context, filter bson.M) error
}

type indexPartProductApplyDao struct {
	db *mongo.Collection
}

func (s indexPartProductApplyDao) Create(ctx context.Context, data model.IndexPartProductApply) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductApplyDao) Get(ctx context.Context, filter bson.M) (model.IndexPartProductApply, error) {
	var data model.IndexPartProductApply
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IndexPartProductApply{}, err
	}
	return data, nil
}

func (s indexPartProductApplyDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s indexPartProductApplyDao) List(ctx context.Context, filter bson.M) ([]model.IndexPartProductApply, error) {
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	var list []model.IndexPartProductApply
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartProductApplyDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IndexPartProductApply, int64, error) {
	var list []model.IndexPartProductApply
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s indexPartProductApplyDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductApplyDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func NewIndexPartProductApplyDao(collect string) DaoInt {
	return indexPartProductApplyDao{
		db: global.MDB.Collection(collect),
	}
}
