package handler

import (
	"base/api/user"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func userRouter(r *gin.RouterGroup) {
	r = r.Group("/user")
	//r.POST("/login", user.Login)
	//r.POST("/login/mobile", user.LoginByMobile)
	r.POST("/login/pwd", user.LoginByPWD)
	//r.POST("/login/test", user.LoginTest)

	//r.POST("/sso/token", user.GenTokenSSO)

	l := r.Use(middleware.CheckToken)
	l.GET("/:user_id", user.Get)
	l.POST("/get", user.GetByPost)
	//l.GET("/qr/code", user.GetQrCode)
	l.POST("/search/mobile", user.ListRegexMobile)
	l.POST("/pwd/init", user.InitPWD)
	l.POST("/pwd/reset", user.ResetPWD)
	l.POST("/asset/stat", user.GetAssetStat)

	//l.POST("/gen/token", user.GenTokenByUserID)

	// 绑定手机号
	l.POST("/bind/mobile", user.BindMobile)
}
