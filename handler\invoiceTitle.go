package handler

import (
	"base/api/invoiceTitle"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 发票抬头
func invoiceTitleRouter(r *gin.RouterGroup) {
	r = r.Group("/invoice")
	r.Use(middleware.CheckToken)

	//	 发票抬头
	r2 := r.Group("/title")
	r2.POST("/upsert", invoiceTitle.Upsert)
	r2.POST("/delete", invoiceTitle.Delete)
	r2.POST("/get", invoiceTitle.Get)
	r2.POST("/list", invoiceTitle.List)
}
