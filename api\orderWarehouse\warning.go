package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// WarningShip 告警-发货
func WarningShip(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToQuality, model.OrderStatusTypeToSort, model.OrderStatusTypeToShip},
		},
		"service_point_id": servicePointID,
		"stock_up_day_time": bson.M{
			"$ne": 0,
			"$lt": ts,
		},
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var dayList []int64

	for _, order := range orders {
		var f bool
		for _, i := range dayList {
			if i == order.StockUpDayTime {
				f = true
				break
			}
		}
		if !f {
			dayList = append(dayList, order.StockUpDayTime)
		}
	}

	xhttp.RespSuccess(ctx, dayList)
}
