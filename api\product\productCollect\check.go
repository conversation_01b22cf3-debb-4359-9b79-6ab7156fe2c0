package productCollect

import (
	"base/core/xhttp"
	"base/service/productCollectService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Check(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	f, err := productCollectService.NewProductCollectService().Check(ctx, id, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, f)
}
