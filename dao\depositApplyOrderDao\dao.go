package depositApplyOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.DepositApplyOrder) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.DepositApplyOrder, error)
	Get(ctx context.Context, filter bson.M) (model.DepositApplyOrder, error)
	Delete(ctx context.Context, filter bson.M) error
}

type depositApplyOrderDao struct {
	db *mongo.Collection
}

func (s depositApplyOrderDao) Get(ctx context.Context, filter bson.M) (model.DepositApplyOrder, error) {
	var data model.DepositApplyOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s depositApplyOrderDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s depositApplyOrderDao) List(ctx context.Context, filter bson.M) ([]model.DepositApplyOrder, error) {
	var list []model.DepositApplyOrder
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s depositApplyOrderDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s depositApplyOrderDao) Create(ctx context.Context, data model.DepositApplyOrder) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func NewDepositApplyOrderDao(collect string) DaoInt {
	return depositApplyOrderDao{
		db: global.MDB.Collection(collect),
	}
}
