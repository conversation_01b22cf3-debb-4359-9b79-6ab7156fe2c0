package handler

import (
	"base/api/station"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func stationRouter(r *gin.RouterGroup) {
	r = r.Group("/station")

	user := r.Group("/")

	_ = user

	r.Use(middleware.CheckToken)

	r.POST("/create", station.Create)

	r.POST("/get/by/user", station.GetByUser)
	r.POST("/get", station.Get)
	r.POST("/list/by/web", station.ListByWeb)
	r.POST("/list/by/point", station.ListByPoint)
	r.POST("/list", station.List)
	r.POST("/update/open/status", station.UpdateOpen)
	r.POST("/update/commission/rate", station.UpdateCommissionRate)
	r.POST("/identity", station.Identity)
	r.POST("/bank/bind", station.BankBind)
	r.POST("/withdraw/sign/protocol", station.SignProtocol)
}
