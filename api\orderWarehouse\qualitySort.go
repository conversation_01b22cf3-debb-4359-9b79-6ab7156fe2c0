package orderWarehouse

import "base/model"

// 发货页面排序

type qualityList []model.OrderQuality

func (array qualityList) Len() int {
	return len(array)
}

func (array qualityList) Less(i, j int) bool {
	if array[i].ProductTitle == array[j].ProductTitle {
		return array[i].StockUpNo < array[j].StockUpNo
	}
	if array[i].ProductTitle < array[j].ProductTitle {
		return true
	}
	return false //从小到大， 若为大于号，则从大到小
}

func (array qualityList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

type sortList []model.OrderQuality

func (array sortList) Len() int {
	return len(array)
}

func (array sortList) Less(i, j int) bool {
	if array[i].UpdatedAt == array[j].UpdatedAt {
		return array[i].ProductTitle < array[j].ProductTitle
	}
	if array[i].UpdatedAt > array[j].UpdatedAt {
		return true
	}
	return false //从小到大， 若为大于号，则从大到小
}

func (array sortList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}
