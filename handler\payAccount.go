package handler

import (
	"base/api/payAccount"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 支付账户

func payAccountRouter(r *gin.RouterGroup) {
	r = r.Group("/pay/account")

	r.Use(middleware.CheckToken)

	r.POST("/user/balance", payAccount.GetNormalUserBalance)
	r.POST("/user/balance/by/buyer", payAccount.GetBuyerBalance)
	//r.POST("/user/balance/by/buyer/cache", payAccount.GetBuyerBalanceCache)
	r.POST("/user/balance/by/station", payAccount.GetStation)
	r.POST("/platform/balance/market", payAccount.GetPlatformMarketNo)
	r.POST("/platform/balance/standard", payAccount.GetPlatformStandardBalanceNo)
	r.POST("/platform/balance/reserve", payAccount.GetPlatformReserveLimitNo)
	r.POST("/platform/balance/deposit", payAccount.GetPlatformStandardDepositNo)

	r.POST("/platform/account/a", payAccount.GetPlatformAccountA)
	r.POST("/platform/account/b", payAccount.GetPlatformAccountB)
	r.POST("/platform/account/reserve", payAccount.GetReserveFundBalance)
}
