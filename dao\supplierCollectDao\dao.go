package supplierCollectDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.SupplierCollect) error
	Get(ctx context.Context, filter bson.M) (model.SupplierCollect, error)
	Delete(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.SupplierCollect, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type supplierCollectDao struct {
	db *mongo.Collection
}

func NewSupplierCollectDao(collect string) DaoInt {
	return supplierCollectDao{
		db: global.MDB.Collection(collect),
	}
}

func (s supplierCollectDao) Get(ctx context.Context, filter bson.M) (model.SupplierCollect, error) {
	var data model.SupplierCollect
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.SupplierCollect{}, err
	}
	return data, nil
}

func (s supplierCollectDao) Create(ctx context.Context, data model.SupplierCollect) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierCollectDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierCollectDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierCollectDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.SupplierCollect, int64, error) {
	var list []model.SupplierCollect
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s supplierCollectDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}
