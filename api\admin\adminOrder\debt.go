package adminOrder

import (
	"base/core/xhttp"
	"base/service/orderDebtService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func FreeDebt(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderDebtService.NewOrderDebtService().FreeDebt(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
	return
}
