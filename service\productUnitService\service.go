package productUnitService

import (
	"base/dao"
	"base/dao/productUnitDao"
	"base/global"
	"base/model"
	"base/service/productService"
	"context"
	"errors"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Upsert(ctx context.Context, id primitive.ObjectID, name string) error
	Get(ctx context.Context, id primitive.ObjectID) (model.ProductUnit, error)
	List(ctx context.Context) ([]model.ProductUnit, error)
}

type productUnitService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	pUnitDao productUnitDao.DaoInt
	productS productService.ServiceInterface
}

func NewProductUnitService() ServiceInterface {
	return productUnitService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		pUnitDao: dao.ProductUnitDao,
		productS: productService.NewProductService(),
	}
}

func (s productUnitService) Get(ctx context.Context, id primitive.ObjectID) (model.ProductUnit, error) {
	m := get(s.rdb, id)
	if m == "" {
		i, err := s.pUnitDao.Get(context.Background(), bson.M{"_id": id})
		if err != nil {
			return model.ProductUnit{}, err
		}
		set(s.rdb, i)
		return i, nil
	}
	return model.ProductUnit{ID: id, Name: m}, nil
}

func (s productUnitService) Upsert(ctx context.Context, id primitive.ObjectID, name string) error {
	data := model.ProductUnit{
		ID:   primitive.NewObjectID(),
		Name: name,
	}
	unit, err := s.Get(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		if unit.ID != primitive.NilObjectID {
			data.ID = unit.ID
		}
		err = s.pUnitDao.Upsert(ctx, data)
		if err != nil {
			return nil, err
		}

		err = s.productS.UpdateUnit(ctx, id, name)
		if err != nil {
			return nil, err
		}

		return nil, nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (s productUnitService) List(ctx context.Context) ([]model.ProductUnit, error) {
	filter := bson.M{}
	list, err := s.pUnitDao.List(ctx, filter)
	return list, err
}
