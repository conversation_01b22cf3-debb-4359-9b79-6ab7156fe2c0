package mapService

import (
	"base/model"
	"base/util"
	"encoding/json"
	"fmt"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.uber.org/zap"
	"log"
)

var key = "UHDBZ-KIBCT-RKSXH-VBQUD-YSEU6-ANF2H"

type ServiceInterface interface {
	Geocoder(lat, lng float64) (model.Location, error)
}

type mapService struct {
	key string
}

func NewMapService() ServiceInterface {
	return mapService{
		key: key,
	}
}

// Geocoder 逆解析
func (s mapService) Geocoder(lat, lng float64) (model.Location, error) {
	latS := fmt.Sprintf("%.7f", lat)
	lngS := fmt.Sprintf("%.7f", lng)
	location := latS + "," + lngS
	u := fmt.Sprintf("https://apis.map.qq.com/ws/geocoder/v1/?location=%s&key=%s", location, s.key)
	resp, err := util.DoHttp(u, "GET", nil, nil)
	if err != nil {
		log.Println(err)
		return model.Location{}, err
	}
	var r GeoResp
	err = json.Unmarshal(resp, &r)
	if err != nil {
		return model.Location{}, err
	}
	if r.Status != 0 {
		zap.S().Errorf("请求地址逆解析错误%v,请求%v", r.Message, u)
	}
	var data model.Location
	adInfo := r.Result.AdInfo
	adCode := adInfo.AdCode
	if len(adCode) == 6 {
		data.ProvinceCode = adCode[:2]
		data.CityCode = adCode[0:4]
		data.DistrictCode = adCode
	}
	if len(adCode) != 6 {
		zap.S().Errorf("地址逆解析信息不匹配，请求%v,返回%v", u, string(resp))
	}

	data.Province = adInfo.Province
	data.City = adInfo.City
	data.District = adInfo.District

	return data, nil
}

type GeoResp struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Result  Result `json:"result"`
}
type Result struct {
	AdInfo AdInfo `json:"ad_info"`
}
type AdInfo struct {
	AdCode   string `json:"adcode"` // 530111
	Province string `json:"province"`
	City     string `json:"city"`
	District string `json:"district"`
}
