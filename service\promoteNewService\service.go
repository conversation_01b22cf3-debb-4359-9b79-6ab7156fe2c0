package promoteNewService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/promoteNewDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strings"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, pointID primitive.ObjectID, title string, fileType string, imgList []model.FileInfo, video model.FileInfo, productID primitive.ObjectID) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.PromoteNew, error)
	List(ctx context.Context, begin, end int64, pointID primitive.ObjectID) ([]model.PromoteNew, error)
}

type promoteNewService struct {
	promoteNewDao promoteNewDao.DaoInt
}

func NewPromoteNewService() ServiceInterface {
	return promoteNewService{
		promoteNewDao: dao.PromoteNewDao,
	}
}

func (s promoteNewService) Create(ctx context.Context, pointID primitive.ObjectID, title string, fileType string, imgList []model.FileInfo, video model.FileInfo, productID primitive.ObjectID) error {
	if strings.TrimSpace(title) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请输入标题")
	}

	if fileType == "img" && len(imgList) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请上传图片")
	}

	if fileType == "video" && video.Name == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请上传视频")
	}

	milli := time.Now().UnixMilli()
	data := model.PromoteNew{
		ID:             primitive.NewObjectID(),
		ServicePointID: pointID,
		Title:          title,
		LinkProductID:  productID,
		Video:          video,
		ImgFileList:    imgList,
		FileType:       fileType,
		CreatedAt:      milli,
	}

	err := s.promoteNewDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s promoteNewService) Update(ctx context.Context, filter, update bson.M) error {
	err := s.promoteNewDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s promoteNewService) List(ctx context.Context, begin, end int64, pointID primitive.ObjectID) ([]model.PromoteNew, error) {
	list, err := s.promoteNewDao.List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"service_point_id": pointID,
	})
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s promoteNewService) Get(ctx context.Context, id primitive.ObjectID) (model.PromoteNew, error) {
	data, err := s.promoteNewDao.Get(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return model.PromoteNew{}, err
	}

	return data, nil
}

func (s promoteNewService) Delete(ctx context.Context, id primitive.ObjectID) error {
	err := s.promoteNewDao.DeleteOne(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return err
	}

	return nil
}
