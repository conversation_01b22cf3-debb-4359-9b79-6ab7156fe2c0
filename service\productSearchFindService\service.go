package productSearchFindService

import (
	"base/global"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"
)

type ServiceInterface interface {
	List(ctx context.Context) ([]redis.Z, error)
}

type productSearchFindService struct {
	mdb *mongo.Database
	rdb *redis.Client
}

func (s productSearchFindService) Upsert(ctx context.Context, list []string) error {
	set(s.rdb, list)

	return nil
}

func (s productSearchFindService) List(ctx context.Context) ([]redis.Z, error) {
	list := get(s.rdb)

	return list, nil
}

func NewProductSearchFindService() ServiceInterface {
	return productSearchFindService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
	}
}
