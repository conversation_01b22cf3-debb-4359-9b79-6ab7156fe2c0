package adminService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/adminDao"
	"base/model"
	"base/service/aesService"
	"base/util"
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 管理员服务接口
type ServiceInterface interface {
	LoginByPWD(ctx context.Context, mobile, pwd string) (model.Admin, error)
	Create(ctx context.Context, userID primitive.ObjectID, note string, roleList []model.RoleInfo) error
	Update(ctx context.Context, id primitive.ObjectID, note string, roleList []model.RoleInfo) error
	Delete(ctx context.Context, userID primitive.ObjectID) error
	List(ctx context.Context, filter bson.M) ([]model.Admin, error)
	GetByUser(ctx context.Context, userID primitive.ObjectID) (model.Admin, error)
	GetByID(ctx context.Context, userID primitive.ObjectID) (model.Admin, error)
	CheckVisitor(ctx context.Context, userID primitive.ObjectID) (bool, error)
	CheckAfterSale(ctx context.Context, userID primitive.ObjectID) (bool, error)
	CheckFinance(ctx context.Context, userID primitive.ObjectID) (bool, error)
	CheckUI(ctx context.Context, userID primitive.ObjectID) (bool, error)
	CheckSuperAdmin(ctx context.Context, serID primitive.ObjectID) (bool, error)
}

// adminService 管理员服务
type adminService struct {
	AesSvr   aesService.ServiceInterface
	adminDao adminDao.DaoInt
}

// NewAdminService 创建管理员服务
func NewAdminService() ServiceInterface {
	return adminService{
		AesSvr:   aesService.NewAesService(),
		adminDao: dao.AdminDao,
	}
}

func (s adminService) LoginByPWD(ctx context.Context, mobile, pwd string) (model.Admin, error) {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return model.Admin{}, err
	}

	if pwd == "" {
		return model.Admin{}, xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	if byMobile.Password != pwd {
		return model.Admin{}, xerr.NewErr(xerr.ErrParamError, nil, "密码错误")
	}

	return byMobile, nil
}

func (s adminService) GetByMobile(ctx context.Context, mobile string) (model.Admin, error) {
	filter := bson.M{
		"mobile": mobile,
	}
	user, err := s.adminDao.Get(ctx, filter)
	if err != nil {
		return model.Admin{}, err
	}
	return user, nil
}

func (s adminService) Create(ctx context.Context, userID primitive.ObjectID, note string, roleList []model.RoleInfo) error {
	if len(roleList) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "至少需要一个角色")
	}

	for _, i := range roleList {
		if len(i.AuthList) == 0 {
			return xerr.NewErr(xerr.ErrParamError, nil, "每个角色下至少需要一个权限")
		}
	}

	note = util.DealWrap(note)
	if note == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "备注不能为空")
	}

	now := time.Now().UnixMilli()

	data := model.Admin{
		ID:        primitive.NewObjectID(),
		UserID:    userID,
		Note:      note,
		RoleInfo:  roleList,
		CreatedAt: now,
	}

	admin, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if admin.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "该请勿重复添加")
	}

	err = s.adminDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s adminService) Update(ctx context.Context, id primitive.ObjectID, note string, roleList []model.RoleInfo) error {
	if len(roleList) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "至少需要一个角色")
	}

	for _, i := range roleList {
		if len(i.AuthList) == 0 {
			return xerr.NewErr(xerr.ErrParamError, nil, "每个角色下至少需要一个权限")
		}
	}

	note = util.DealWrap(note)
	if note == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "备注不能为空")
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"role_info":  roleList,
		"updated_at": now,
		"note":       note,
	}

	err := s.adminDao.UpdateOne(ctx, bson.M{
		"_id": id,
	}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func (s adminService) Delete(ctx context.Context, userID primitive.ObjectID) error {
	admin, err := s.GetByUser(ctx, userID)
	if err != nil {
		return err
	}

	for _, v := range admin.RoleList {
		if v == model.RoleTypeSuperAdmin {
			return xerr.NewErr(xerr.ErrParamError, nil, "操作错误，不能删除超级管理员")
		}
	}

	filter := bson.M{
		"user_id": userID,
	}
	err = s.adminDao.Delete(ctx, filter)
	if err != nil {
		return err
	}

	return nil
}

func (s adminService) CheckSuperAdmin(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	user, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	var f bool

	for _, roleType := range user.RoleList {
		if roleType == model.RoleTypeSuperAdmin {
			f = true
			break
		}
	}
	if !f {
		zap.S().Warn("非超级管理员：", userID.Hex())
	}

	return f, nil
}

func (s adminService) GetByUser(ctx context.Context, userID primitive.ObjectID) (model.Admin, error) {
	admin, err := s.adminDao.GetByUserID(ctx, userID)
	if err != nil {
		return model.Admin{}, err
	}

	return admin, nil
}

func (s adminService) GetByID(ctx context.Context, userID primitive.ObjectID) (model.Admin, error) {
	admin, err := s.adminDao.GetByID(ctx, userID)
	if err != nil {
		return model.Admin{}, err
	}
	return admin, nil
}

func (s adminService) List(ctx context.Context, filter bson.M) ([]model.Admin, error) {
	list, err := s.adminDao.List(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s adminService) CheckVisitor(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	user, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	var f bool

	for _, roleType := range user.RoleList {
		if roleType == model.RoleTypeSuperAdmin {
			f = true
			break
		}
	}

	return f, nil
}

func (s adminService) CheckFinance(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	user, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	var f bool

	for _, roleType := range user.RoleList {
		if roleType == model.RoleTypeSuperAdmin {
			f = true
			break
		}
	}
	return f, nil
}

func (s adminService) CheckAfterSale(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	user, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	var f bool

	for _, auth := range user.AuthList {
		if auth == model.AuthTypeNormalAfterSaleAudit {
			f = true
			break
		}
	}
	return f, nil
}
func (s adminService) CheckUI(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	user, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	var f bool

	for _, roleType := range user.RoleList {
		if roleType == model.RoleTypeSuperAdmin {
			f = true
			break
		}
	}
	return f, nil
}
