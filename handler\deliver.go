package handler

import (
	"base/api/deliverAssign"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 配送指派
func deliverAssignRouter(r *gin.RouterGroup) {
	r = r.Group("/order/deliver/assign")
	r.Use(middleware.CheckToken)

	//r.POST("/qr/get", deliverAssign.GetDeliverAssignQr)
	r.POST("/create", deliverAssign.Create)
	r.POST("/delete", deliverAssign.Delete)
	r.POST("/buyer/list", deliverAssign.ListBuyer)
	r.POST("/order/list", deliverAssign.ListBuyerOrder)
	//r.POST("/get/by/key", deliverAssign.GetDeliverInfoByKey)

}
