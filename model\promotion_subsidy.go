package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// PromotionSubsidyStatus 营销补贴状态
type PromotionSubsidyStatus string

const (

	// PromotionSubsidyStatusPending 待处理
	PromotionSubsidyStatusPending PromotionSubsidyStatus = "PENDING"
	// PromotionSubsidyStatusSuccess 成功
	PromotionSubsidyStatusSuccess PromotionSubsidyStatus = "SUCCESS"
	// PromotionSubsidyStatusFailed 失败
	PromotionSubsidyStatusFailed PromotionSubsidyStatus = "FAIL"
)

// PromotionSubsidy 营销补贴
type PromotionSubsidy struct {
	ID               primitive.ObjectID     `json:"id" bson:"_id"`
	OrderID          primitive.ObjectID     `json:"order_id" bson:"order_id"`                     // 业务订单ID
	YeeOrderID       string                 `json:"yee_order_id" bson:"yee_order_id"`             // 易宝订单号
	UniqueOrderNo    string                 `json:"unique_order_no" bson:"unique_order_no"`       // 易宝交易流水号
	SubsidyRequestID string                 `json:"subsidy_request_id" bson:"subsidy_request_id"` // 补贴请求号
	AssumeMerchantNo string                 `json:"assume_merchant_no" bson:"assume_merchant_no"` // 出资方商编
	ParentMerchantNo string                 `json:"parent_merchant_no" bson:"parent_merchant_no"` // 发起方商编
	MerchantNo       string                 `json:"merchant_no" bson:"merchant_no"`               // 收款商户编号
	SubsidyAmount    int                    `json:"subsidy_amount" bson:"subsidy_amount"`         // 补贴金额
	Memo             string                 `json:"memo" bson:"memo"`                             // 描述
	Status           PromotionSubsidyStatus `json:"status" bson:"status"`                         // 状态
	FailReason       string                 `json:"fail_reason" bson:"fail_reason"`               // 失败原因
	CreatedAt        int64                  `json:"created_at" bson:"created_at"`                 // 创建时间
	UpdatedAt        int64                  `json:"updated_at" bson:"updated_at"`                 // 更新时间
}
