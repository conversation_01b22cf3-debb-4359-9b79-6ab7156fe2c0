package util

import (
	"base/model"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/aliyun/credentials-go/credentials/utils"
	"go.uber.org/zap"
	"hash"
	"io"
	"time"
)

// AliOssClient 阿里云对象存储
type AliOssClient struct {
	accessKeyID     string
	accessKeySecret string
	endPoint        string
	host            string
	client          *oss.Client
}

func (s AliOssClient) GetSign(uploadDir string, expireTime int64) (model.PolicyToken, error) {
	now := time.Now().Unix()
	expireEnd := now + expireTime
	var tokenExpire = getGmtIso8601(expireEnd)

	//create post policy json
	var config model.ConfigStruct
	config.Expiration = tokenExpire
	var condition []string
	condition = append(condition, "starts-with")
	condition = append(condition, "$key")
	condition = append(condition, uploadDir)
	config.Conditions = append(config.Conditions, condition)

	// signature
	result, err := json.Marshal(config)
	if err != nil {
		return model.PolicyToken{}, err
	}
	encodeToString := base64.StdEncoding.EncodeToString(result)
	h := hmac.New(func() hash.Hash { return sha1.New() }, []byte(s.accessKeySecret))
	io.WriteString(h, encodeToString)
	signedStr := base64.StdEncoding.EncodeToString(h.Sum(nil))

	var policyToken model.PolicyToken
	policyToken.AccessKeyId = s.accessKeyID
	policyToken.Host = s.host
	policyToken.Expire = expireEnd
	policyToken.Signature = string(signedStr)
	policyToken.Directory = uploadDir
	policyToken.Policy = string(encodeToString)
	return policyToken, nil
}

func (s AliOssClient) Upload(bucket, path string, f io.Reader) error {
	b, err := s.client.Bucket(bucket)
	if err != nil {
		return err
	}

	err = b.PutObject(path, f)
	if err != nil {
		return err
	}

	return nil
}

// VideoSnapshot 视频截帧
func (s AliOssClient) VideoSnapshot(bucket, dir, path string) (string, error) {
	b, err := s.client.Bucket(bucket)
	if err != nil {
		return "", err
	}

	// 指定原图名称。如果图片不在Bucket根目录，需携带文件完整路径，例如example/example.jpg。
	sourceImageName := path
	// 指定用于存放处理后图片的Bucket名称，该Bucket需与原图所在Bucket在同一地域。
	targetBucketName := bucket

	// 指定处理后图片名称。如果图片不在Bucket根目录，需携带文件完整访问路径，例如exampledir/example.jpg。
	targetImageName := fmt.Sprintf("%s/%s.jpg", dir, utils.GetUUID())
	// 将图片缩放为固定宽高100 px后转存到指定存储空间。
	style := "video/snapshot,t_0,f_jpg,w_750"
	process := fmt.Sprintf("%s|sys/saveas,o_%v,b_%v", style, base64.URLEncoding.EncodeToString([]byte(targetImageName)), base64.URLEncoding.EncodeToString([]byte(targetBucketName)))

	result, err := b.ProcessObject(sourceImageName, process)
	if err != nil {
		zap.S().Errorf("ProcessObject error: %v", err.Error())
		return "", err
	} else {
		zap.S().Infof("	ProcessObject %v", result)
	}

	return targetImageName, nil
}

func (s AliOssClient) UploadLocalFile(bucket, objectName, path string) error {
	b, err := s.client.Bucket(bucket)
	if err != nil {
		return err
	}

	err = b.PutObjectFromFile(objectName, path)
	if err != nil {
		return err
	}

	return nil
}

func (s AliOssClient) Download(bucket, url string) ([]byte, error) {
	b, err := s.client.Bucket(bucket)
	if err != nil {
		return nil, err
	}
	body, err := b.GetObject(url)
	if err != nil {
		return nil, err
	}
	defer body.Close()
	data, err := io.ReadAll(body)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func NewOssClient(endPoint, accessKeyID, accessKeySecret, host string, cli *oss.Client) *AliOssClient {
	return &AliOssClient{
		client:          cli,
		endPoint:        endPoint,
		accessKeyID:     accessKeyID,
		accessKeySecret: accessKeySecret,
		host:            host,
	}
}

func getGmtIso8601(expireEnd int64) string {
	var tokenExpire = time.Unix(expireEnd, 0).UTC().Format("2006-01-02T15:04:05Z")
	return tokenExpire
}
