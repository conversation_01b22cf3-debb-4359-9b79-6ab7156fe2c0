package allinpay

import "encoding/json"

// CommonResponse 公共响应参数
type CommonResponse struct {
	Code    string          `json:"code"`    // 网关返回码
	Msg     string          `json:"msg"`     // 网关返回码描述
	SubCode string          `json:"subCode"` // 业务返回码
	SubMsg  string          `json:"subMsg"`  // 业务返回码描述
	Sign    string          `json:"sign"`    // 签名
	Data    json.RawMessage `json:"data"`    // 返回参数的集合，最大长度不限，除公共参数外所有返回参数都必须放在这个参数中传递
}
