package yeeMerchant

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/types"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	var req types.YeeMerchantUpdateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateSecondPoint(ctx *gin.Context) {
	var req types.YeeMerchantUpdateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().UpdateBySecondPoint(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
