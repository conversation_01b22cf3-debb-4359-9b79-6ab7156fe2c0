package order

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func ListToShipByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().Get(buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orders, i, err := orderService.NewOrderService().ListToShipByBuyer(ctx, buyer.ID, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, orders, i)

}
