package ossService

import (
	"base/core/config"
	"base/global"
	"base/model"
	"base/util"
	"fmt"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"io"
	"os"
	"strings"
)

// OssService 阿里云oss
type OssService interface {
	GetSignUpload(dir string) (model.PolicyToken, error)
	UploadDeliverNote(objectName string, f io.Reader) error
	UploadDeliverNoteLocal(objectName, path string) error
	DownLoad(url string) ([]byte, error)
	VideoSnap(dir, url string) (string, error)
}

type ossService struct {
	l                *zap.SugaredLogger
	rdb              *redis.Client
	bucket           string
	SignUploadExpire int64
	ossCli           *util.AliOssClient
}

func (s ossService) GetSignUpload(dir string) (model.PolicyToken, error) {
	t, err := s.ossCli.GetSign(dir, s.SignUploadExpire)
	if err != nil {
		return model.PolicyToken{}, err
	}

	uuid := util.NewUUID()
	trim := strings.ReplaceAll(uuid, "-", "")

	//formatInt := strconv.FormatInt(time.Now().UnixMilli(), 10)

	t.FileNamePrefix = trim
	//+ formatInt

	return t, nil
}

func (s ossService) UploadDeliverNote(objectName string, f io.Reader) error {
	err := s.ossCli.Upload("guoshut", objectName, f)
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}

	return nil
}
func (s ossService) UploadDeliverNoteLocal(objectName, path string) error {
	err := s.ossCli.UploadLocalFile("guoshut", objectName, path)
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}

	return nil
}

func (s ossService) DownLoad(url string) ([]byte, error) {
	bytes, err := s.ossCli.Download(s.bucket, url)
	if err != nil {
		return nil, err
	}
	return bytes, nil
}

func (s ossService) VideoSnap(dir, url string) (string, error) {
	snapshotUrl, err := s.ossCli.VideoSnapshot(s.bucket, dir, url)
	if err != nil {
		return "", err
	}
	return snapshotUrl, nil
}

func NewOssService() OssService {
	c := config.Conf.AliOss
	return ossService{
		l:                global.SysLogger.Sugar(),
		rdb:              global.RDBDefault,
		bucket:           c.Bucket,
		SignUploadExpire: c.SignUploadExpire,
		ossCli:           util.NewOssClient(c.EndPoint, c.AccessKeyID, c.AccessKeySecret, c.Host, global.AliOss),
	}
}
