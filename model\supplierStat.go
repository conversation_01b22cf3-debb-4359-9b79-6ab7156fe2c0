package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SupplierStat 供应商简易统计
type SupplierStat struct {
	//ID             primitive.ObjectID `bson:"_id" json:"id"`
	SupplierID     primitive.ObjectID `bson:"supplier_id" json:"supplier_id"`           // 供应商ID
	SaleNumMonthly int                `json:"sale_num_monthly" bson:"sale_num_monthly"` // 月销-最近30天
	FansNum        int                `json:"fans_num" bson:"fans_num"`                 // 粉丝数
	Star           int                `json:"star" bson:"star"`                         // 星级-最近30天
	ReBuyRate      int                `json:"re_buy_rate" bson:"re_buy_rate"`           // 回头率-最近30天
	AfterSaleRate  float64            `json:"after_sale_rate" bson:"after_sale_rate"`
	OrderNum       int                `json:"order_num" bson:"order_num"`
	CreatedAt      int64              `bson:"created_at" json:"created_at"`
	UpdatedAt      int64              `bson:"updated_at" json:"updated_at"`
}
