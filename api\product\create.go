package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/service/productUnitService"
	"base/service/supplierService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Create 商品创建
func Create(ctx *gin.Context) {
	var req types.ProductCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	supplier, err := supplierService.NewSupplierService().Get(ctx, supplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	unitID, err := util.ConvertToObjectWithCtx(ctx, req.ProductUnitID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	unit, err := productUnitService.NewProductUnitService().Get(ctx, unitID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().Create(ctx, req, supplier, unit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// Update 商品更新
func Update(ctx *gin.Context) {
	var req types.ProductUpdateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	unitID, err := util.ConvertToObjectWithCtx(ctx, req.ProductUnitID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	unit, err := productUnitService.NewProductUnitService().Get(ctx, unitID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().Update(ctx, req, unit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
