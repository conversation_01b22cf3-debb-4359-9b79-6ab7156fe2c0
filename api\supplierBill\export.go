package supplierBill

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/billService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/ossService"
	"base/service/parentOrderService"
	"base/util"
	"bytes"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func Export(ctx *gin.Context) {
	var req = struct {
		BuyerID     string   `json:"buyer_id"`
		TimeBegin   int64    `json:"time_begin"`
		TimeEnd     int64    `json:"time_end"`
		OrderIDList []string `json:"order_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = billService.NewBillService().CheckExportLimit(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	now := time.Now()

	var orderIDs []primitive.ObjectID
	for _, v := range req.OrderIDList {
		id, err := util.ConvertToObjectWithNote(v, "")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		orderIDs = append(orderIDs, id)
	}

	filter := bson.M{}
	filter["_id"] = bson.M{"$in": orderIDs}
	if len(req.OrderIDList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单列表不能为空"))
		return
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
			//"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	var parentOrderIDs []primitive.ObjectID
	for _, order := range orders {
		parentOrderIDs = append(parentOrderIDs, order.ParentOrderID)
	}

	parentOrders, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{"_id": bson.M{"$in": parentOrderIDs}})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	beginAt, endAt, orderLists, calcRes := CalcFunc(orders, refunds, debts)

	bufferFile, err := toExcel(orderLists, parentOrders, calcRes, req.TimeBegin, req.TimeEnd)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//xhttp.RespSuccess(ctx, nil)
	//return

	_ = beginAt
	_ = endAt
	_ = bufferFile

	dir := "bill"
	pathSuffix := now.Format("20060102T150405")

	fileName := pathSuffix + ".xlsx"
	objectName := dir + "/" + fileName

	err = ossService.NewOssService().UploadDeliverNote(objectName, bufferFile)
	if err != nil {
		zap.S().Errorf("上传oss失败：%v", err.Error())
		xhttp.RespErr(ctx, err)
		return
	}

	_ = buyerID

	createdAt := now.UnixMilli()
	generatedAt := time.Now().UnixMilli()
	data := model.Bill{
		ID:            primitive.NewObjectID(),
		FilePath:      objectName,
		Remark:        "",
		OrderBeginAt:  beginAt,
		OrderEndAt:    endAt,
		ExportBeginAt: req.TimeBegin,
		ExportEndAt:   req.TimeEnd,
		GeneratedAt:   generatedAt,
		BuyerID:       buyerID,
		CreatedAt:     createdAt,
		UpdatedAt:     createdAt,
	}
	err = billService.NewBillService().Create(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

type CalcRes struct {
	TotalAmount              int   `json:"total_amount"`
	TotalFinalAmount         int   `json:"total_final_amount"`
	TotalDebtPaidAmount      int   `json:"total_debt_paid_amount"`
	TotalDebtNotPaidAmount   int   `json:"total_debt_not_paid_amount"`
	TotalAfterSalePassAmount int   `json:"total_after_sale_pass_amount"`
	TotalShipRefundAmount    int   `json:"total_ship_refund_amount"`
	OrderTimeBegin           int64 `json:"order_time_begin"`
	OrderTimeEnd             int64 `json:"order_time_end"`
	ExistAfterSaleAuditing   bool  `json:"exist_after_sale_auditing"`
	ExistDebtNotPaid         bool  `json:"exist_debt_not_paid"`
}

type OrderList struct {
	Order      model.Order         `json:"order"`
	Debt       model.OrderDebt     `json:"debt"`
	RefundList []model.OrderRefund `json:"refund_list"`
}

func CalcFunc(orders []model.Order, refunds []model.OrderRefund, debts []model.OrderDebt) (int64, int64, []OrderList, CalcRes) {
	var orderTimeBegin int64
	var orderTimeEnd int64

	var totalAmount int
	var totalDebtPaidAmount int
	var totalDebtNotPaidAmount int
	var totalAfterSalePassAmount int
	var totalShipRefundAmount int

	var existAfterSaleAuditing bool
	var existDebtNotPaid bool

	for _, order := range orders {
		var hasRefund int
		for _, productOrder := range order.ProductList {
			if productOrder.IsShipRefundAll {
				hasRefund += productOrder.TotalAmount
			} else {
				// TODO
			}
		}

		perTotalAmount := order.TotalAmount - hasRefund
		_ = perTotalAmount
		totalAmount += order.PaidAmount

		if orderTimeBegin == 0 {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd == 0 {
			orderTimeEnd = order.CreatedAt
		}

		if orderTimeBegin > order.CreatedAt {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd < order.CreatedAt {
			orderTimeEnd = order.CreatedAt
		}
	}

	mDebt := make(map[primitive.ObjectID]model.OrderDebt)

	for _, debt := range debts {
		if debt.PayStatus == model.PayStatusTypePaid {
			totalDebtPaidAmount += debt.TotalProductAmount
		} else {
			totalDebtNotPaidAmount += debt.TotalProductAmount
		}
		mDebt[debt.OrderID] = debt
		if debt.PayStatus != model.PayStatusTypePaid {
			existDebtNotPaid = true
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)

	for _, re := range refunds {
		if re.RefundType == model.RefundTypeQuality {
			totalShipRefundAmount += re.AuditAmount + re.TotalTransportFee + re.TotalWarehouseLoadFee
		}
		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypePass {
			totalAfterSalePassAmount += re.AuditAmount
		}
		mRefund[re.OrderID] = append(mRefund[re.OrderID], re)

		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypeDoing {
			existAfterSaleAuditing = true
		}
	}

	resList := make([]OrderList, 0)
	for _, order := range orders {
		item := OrderList{
			Order:      order,
			Debt:       mDebt[order.ID],
			RefundList: mRefund[order.ID],
		}
		resList = append(resList, item)
	}

	//totalFinalAmount := totalAmount - totalShipRefundAmount - totalAfterSalePassAmount + totalDebtPaidAmount + totalDebtNotPaidAmount
	totalFinalAmount := totalAmount - totalShipRefundAmount + totalDebtPaidAmount + totalDebtNotPaidAmount

	res := CalcRes{
		TotalAmount:              totalAmount,
		TotalDebtPaidAmount:      totalDebtPaidAmount,
		TotalDebtNotPaidAmount:   totalDebtNotPaidAmount,
		TotalAfterSalePassAmount: totalAfterSalePassAmount,
		TotalShipRefundAmount:    totalShipRefundAmount,
		OrderTimeBegin:           orderTimeBegin,
		OrderTimeEnd:             orderTimeEnd,
		ExistAfterSaleAuditing:   existAfterSaleAuditing,
		ExistDebtNotPaid:         existDebtNotPaid,
		TotalFinalAmount:         totalFinalAmount,
	}
	return orderTimeBegin, orderTimeEnd, resList, res

}

type parentOrderSort []model.ParentOrder

func (array parentOrderSort) Len() int {
	return len(array)
}

func (array parentOrderSort) Less(i, j int) bool {
	return array[i].CreatedAt < array[j].CreatedAt //从小到大， 若为大于号，则从大到小
}

func (array parentOrderSort) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

func setColorStyle(f *excelize.File, sheetName string, cellList []string) {
	var err error
	_ = err
	for _, s := range cellList {
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold:   true,
				Family: "宋体",
				Size:   10,
				Color:  "#ff0000",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "000000", Style: 1},
				{Type: "top", Color: "000000", Style: 1},
				{Type: "bottom", Color: "000000", Style: 1},
				{Type: "right", Color: "000000", Style: 1},
			},
			Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		})
		_ = f.SetCellStyle(sheetName, s, s, style)
	}
}

func toExcel(list []OrderList, parentOrders []model.ParentOrder, res CalcRes, exportBegin, exportEnd int64) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	title(f, sheetName, i)

	buyer(f, sheetName, list[0].Order.BuyerName, i+1)

	orderTime(f, sheetName, i+2, res.OrderTimeBegin, res.OrderTimeEnd, exportBegin, exportEnd)
	createTime(f, sheetName, i+3)

	category(f, sheetName, i+5)

	i = 8

	mParent := make(map[primitive.ObjectID][]OrderList)
	for _, orderList := range list {
		mParent[orderList.Order.ParentOrderID] = append(mParent[orderList.Order.ParentOrderID], orderList)
	}

	sort.Sort(parentOrderSort(parentOrders))

	var totalProductAmount int
	var pAllFinalAmount int
	var totalDeliverFeeAll int
	var totalDeliverFeeSubsidy int
	var totalDeliverFee int
	var totalServiceFee int
	var totalLoadFee int

	var setColorList []string

	for pI, pOrder := range parentOrders {
		if allList, ok := mParent[pOrder.ID]; ok {
			deliverMergeBegin := i
			var paidAmountPerOrder int
			for k, v := range allList {
				productNum := len(v.Order.ProductList)
				if productNum > 1 {
					//err = f.MergeCell(sheetName, "A"+strconv.Itoa(i), "A"+strconv.Itoa(i+productNum-1))
					err = f.MergeCell(sheetName, "C"+strconv.Itoa(i), "C"+strconv.Itoa(i+productNum-1))
				}

				_ = k
				//createTime1 := time.UnixMilli(v.Order.CreatedAt).Format("2006-01-02 15:04:05")
				//no := fmt.Sprintf("%s\n[%s]", v.Order.SupplierName, createTime1)
				err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i+productNum-1), v.Order.SupplierName)

				for j, p := range v.Order.ProductList {

					productTitle := fmt.Sprintf("%s", p.ProductTitle)
					err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i+j), productTitle)
					// 单价
					err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+j), dealMoney(p.Price))
					err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+j), p.Num)
					err = f.SetCellValue(sheetName, "K"+strconv.Itoa(i+j), p.SortNum)

					runeLen := utf8.RuneCountInString(productTitle)
					//zap.S().Info(runeLen)
					//l := float64(runeLen) / 10
					if runeLen < 30 {
						runeLen = 30
					}

					err = f.SetRowHeight(sheetName, i+j, float64(runeLen))
					//if productNum > 1 {
					//
					//
					//} else {
					//	//err = f.SetRowHeight(sheetName, i+j, 34)
					//}
					err = f.SetCellValue(sheetName, "H"+strconv.Itoa(i+j), dealMoney(p.ProductAmount))
					if p.IsCheckWeight {
						err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i+j), dealWeight(p.DueWeight))
						err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i+j), dealWeight(p.SortWeight))
						// 重量误差
						weightOffset := p.SortWeight - p.DueWeight
						err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i+j), dealWeight(weightOffset))
					}

					err = f.SetCellValue(sheetName, "J"+strconv.Itoa(i+j), dealMoney(p.TotalWarehouseLoadFee))

					saleWay := "按件"
					if p.IsCheckWeight {
						saleWay = "称重"
					}
					err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i+j), saleWay)

					var afterSaleRefund int
					var auditStatus model.AuditStatusType
					for _, r1 := range v.RefundList {
						if r1.ProductID == p.ProductID {

							if r1.RefundType == model.RefundTypeAfterSale && (r1.AuditStatus == model.AuditStatusTypeDoing || r1.AuditStatus == model.AuditStatusTypePass) {
								//	 售后退款
								afterSaleRefund += r1.AuditAmount
								auditStatus = r1.AuditStatus
							}
						}
					}
					if afterSaleRefund != 0 {
						var afterSaleNote string
						if auditStatus == model.AuditStatusTypeDoing {
							afterSaleNote = "[审核中]"
							setColorList = append(setColorList, "O"+strconv.Itoa(i+j))
						}
						err = f.SetCellValue(sheetName, "O"+strconv.Itoa(i+j), fmt.Sprintf("%.2f%s", dealMoney(afterSaleRefund), afterSaleNote))
					}

					// 品控退款
					var sortRefund int

					for _, d1 := range v.Debt.RefundProductList {
						if d1.ProductID == p.ProductID {
							sortRefund += d1.ProductAmount + d1.TotalTransportFee + d1.TotalServiceFee + d1.TotalWarehouseLoadFee
							continue
						}
					}
					if sortRefund > 0 {
						err = f.SetCellValue(sheetName, "N"+strconv.Itoa(i+j), dealMoney(sortRefund))
					}

					var debtPaidAmount int
					var debtNotPaidAmount int
					for _, d1 := range v.Debt.ProductList {
						if d1.ProductID == p.ProductID && v.Debt.PayStatus == 4 {
							debtPaidAmount += d1.Amount
							continue
						}
						if d1.ProductID == p.ProductID && v.Debt.PayStatus != 4 {
							debtNotPaidAmount += d1.Amount
						}
					}

					//err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i+j), dealMoney(debtPaidAmount))
					debtAmount := debtPaidAmount + debtNotPaidAmount
					if debtAmount != 0 {
						var debtPayNote string
						if debtNotPaidAmount != 0 {
							debtPayNote = "[未支付]"
							setColorList = append(setColorList, "P"+strconv.Itoa(i+j))
						}
						err = f.SetCellValue(sheetName, "P"+strconv.Itoa(i+j), fmt.Sprintf("%.2f%s", dealMoney(debtAmount), debtPayNote))
					}

					// 金额小计 商品金额+仓配费-品控退款+补差金额
					pFinalAmount := p.ProductAmount + p.TotalWarehouseLoadFee - sortRefund + debtPaidAmount
					if afterSaleRefund != 0 && auditStatus == model.AuditStatusTypePass {
						pFinalAmount -= afterSaleRefund
					}

					pAllFinalAmount += pFinalAmount

					err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(i+j), dealMoney(pFinalAmount))

				}
				i += productNum

				totalServiceFee += v.Order.TotalServiceFee
				totalLoadFee += v.Order.TotalWarehouseLoadFee
				totalProductAmount += v.Order.ProductTotalAmount

				paidAmountPerOrder += v.Order.PaidAmount
			}

			//createTime1 := time.UnixMilli(pOrder.CreatedAt).Format("2006-01-02 15:04:05")
			//no := fmt.Sprintf("%s\n[%s]", pOrder..Order.SupplierName, createTime1)
			//err = f.SetSheetRow(sheetName, "A"+strconv.Itoa(i+productNum-1), &[]interface{}{k + 1, no})
			mergeNum := strconv.Itoa(deliverMergeBegin)
			err = f.MergeCell(sheetName, "A"+strconv.Itoa(deliverMergeBegin), "A"+strconv.Itoa(i-1))
			err = f.SetCellValue(sheetName, "A"+mergeNum, pI+1)

			totalDeliverFee += pOrder.DeliverFeeRes.FinalDeliverFee
			totalDeliverFeeAll += pOrder.DeliverFeeRes.TotalDeliverFee
			if pOrder.DeliverFeeRes.SubsidyDeliverFee > 0 {
				totalDeliverFeeSubsidy += pOrder.DeliverFeeRes.SubsidyDeliverFee
			}

			// 下单时间
			err = f.MergeCell(sheetName, "B"+strconv.Itoa(deliverMergeBegin), "B"+strconv.Itoa(i-1))
			format := time.UnixMilli(pOrder.CreatedAt).Format("2006-01-02 15:04:05")
			err = f.SetCellValue(sheetName, "B"+strconv.Itoa(deliverMergeBegin), format)

			//err = f.MergeCell(sheetName, "I"+strconv.Itoa(deliverMergeBegin), "I"+strconv.Itoa(i-1))
			//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(deliverMergeBegin), dealMoney(pOrder.DeliverFeeRes.FinalDeliverFee))

			//	 下单金额
			//paidAmount := paidAmountPerOrder + pOrder.DeliverFeeRes.FinalDeliverFee
			//err = f.MergeCell(sheetName, "K"+strconv.Itoa(deliverMergeBegin), "K"+strconv.Itoa(i-1))
			//err = f.SetCellValue(sheetName, "K"+strconv.Itoa(deliverMergeBegin), dealMoney(paidAmount))
		}

	}

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	content2 := strconv.Itoa(i)
	err = f.SetCellStyle(sheetName, "A8", "Q"+content2, style)
	//err = f.SetRowStyle(sheetName, 6, i, style)

	setProductStyle(f, sheetName, 8, i)
	setColorStyle(f, sheetName, setColorList)

	// 小计
	resForCol(f, sheetName, i, res, totalProductAmount, totalLoadFee, pAllFinalAmount)

	setOther(f, sheetName, i+1, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount)

	//err = f.SetRowHeight(sheetName, i, 26)
	//err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), "合计")
	////err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(totalProductAmount))
	////err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i), dealMoney(totalLoadFee))
	//err = f.SetCellValue(sheetName, "J"+strconv.Itoa(i), dealMoney(totalDeliverFee))
	//err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i), dealMoney(res.TotalShipRefundAmount))
	////err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i), dealMoney(res.TotalAfterSalePassAmount))
	//err = f.SetCellValue(sheetName, "N"+strconv.Itoa(i), dealMoney(pAllFinalAmount))
	//err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i), dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))

	// 总计
	//total(f, sheetName, i+1, res, totalProductAmount, totalLoadFee, totalDeliverFee)

	// 汇总表
	i += 11
	//summary(f, sheetName, i, res, pAllFinalAmount, totalDeliverFee)

	//i += 5

	note(f, sheetName, i)

	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	//f.SaveAs("./api/bill/test.xlsx")

	return toBuffer, nil
}

// 其他
func setOther(f *excelize.File, sheetName string, index, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount int) {
	originIndex := index
	row := strconv.Itoa(index)
	var err error

	err = f.SetCellValue(sheetName, "B"+row, "其他")

	colNum := 3
	colValueNum := 17
	// 配送费
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "配送费")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "配送费平台补贴")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "配送费实付")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalDeliverFeeAll))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalDeliverFeeSubsidy))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), dealMoney(totalDeliverFee))

	// 服务费
	index += 3
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "服务费")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "服务费平台补贴")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "服务费实付")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalServiceFee))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalServiceFee))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), 0)

	// 优惠券
	index += 3
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "优惠券使用")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), 0)

	//index += 1
	err = f.MergeCell(sheetName, "B"+strconv.Itoa(originIndex), "B"+strconv.Itoa(index))
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	//err = f.MergeCell(sheetName, "B"+strconv.Itoa(index+1), "B"+strconv.Itoa(index))
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}

	index += 1
	err = f.SetCellValue(sheetName, "B"+strconv.Itoa(index), "合计")
	finalAmount := pAllFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(finalAmount))
	//
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(originIndex), convertToCol(colValueNum)+strconv.Itoa(index), style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(index), convertToCol(colValueNum)+strconv.Itoa(index), style2)
	//zap.S().Info("originIndex::", originIndex)
	//err = f.SetRowHeight(sheetName, originIndex, 20)
	//err = f.SetRowHeight(sheetName, originIndex+1, 20)
	//err = f.SetRowHeight(sheetName, originIndex+2, 20)
	//err = f.SetRowHeight(sheetName, originIndex+3, 20)
	//err = f.SetRowHeight(sheetName, originIndex+4, 20)
	//err = f.SetRowHeight(sheetName, originIndex+5, 20)
	//err = f.SetRowHeight(sheetName, originIndex+6, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+7, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+8, 26)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "D"+cell1, "D"+cell2, style)
}

// 小计
func resForCol(f *excelize.File, sheetName string, index int, res CalcRes, totalProductAmount, totalLoadFee, pAllFinalAmount int) {
	row := strconv.Itoa(index)
	//row2 := strconv.Itoa(index + 1)

	var err error
	_ = err

	err = f.SetRowHeight(sheetName, index, 26)
	err = f.SetCellValue(sheetName, "B"+row, "小计")
	err = f.SetCellValue(sheetName, "H"+row, dealMoney(totalProductAmount))
	err = f.SetCellValue(sheetName, "J"+row, dealMoney(totalLoadFee))
	err = f.SetCellValue(sheetName, "N"+row, dealMoney(res.TotalShipRefundAmount))
	err = f.SetCellValue(sheetName, "O"+row, dealMoney(res.TotalAfterSalePassAmount))
	err = f.SetCellValue(sheetName, "P"+row, dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))
	err = f.SetCellValue(sheetName, "Q"+row, dealMoney(pAllFinalAmount))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, "Q"+row, style)

	err = f.SetRowHeight(sheetName, index, 20)

}

// 汇总
func summary(f *excelize.File, sheetName string, i int, res CalcRes, pAllFinalAmount, totalDeliverFee int) {
	var err error
	_ = err
	err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), "汇总：")

	j := i + 1
	err = f.SetCellValue(sheetName, "G"+strconv.Itoa(j), "支付金额")
	err = f.SetCellValue(sheetName, "G"+strconv.Itoa(j+1), dealMoney(res.TotalAmount+totalDeliverFee))
	err = f.SetCellValue(sheetName, "H"+strconv.Itoa(j), "退款金额")
	err = f.SetCellValue(sheetName, "H"+strconv.Itoa(j+1), dealMoney(res.TotalShipRefundAmount))
	//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j), "商品合计")
	//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j+1), dealMoney(pAllFinalAmount))
	//err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j), "配送费")
	//err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j+1), dealMoney(totalDeliverFee))
	err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j), "补差金额")
	err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j+1), dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))
	err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j), "实际金额")

	final := res.TotalFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j+1), dealMoney(final))

	err = f.SetRowHeight(sheetName, i, 30)
	err = f.SetRowHeight(sheetName, j, 30)
	err = f.SetRowHeight(sheetName, j+1, 30)

	style1, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "F"+strconv.Itoa(i), "J"+strconv.Itoa(j+1), style1)

}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.14),
		Right:  float64Ptr(0.14),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func note(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "备注说明：")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+1), "M"+strconv.Itoa(index+1))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+1), "补差未支付时，补差金额不计入金额小计；")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+2), "M"+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+2), "售后存在审核中时，售后金额不计入金额小计；")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "C"+strconv.Itoa(index+2), style)

}

func orderTime(f *excelize.File, sheetName string, index int, min, max, exportBegin, exportEnd int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "Q"+row)
	if err != nil {

	}

	exportBeginFormat := time.UnixMilli(exportBegin).Format("2006/01/02 15:04:05")
	exportEndFormat := time.UnixMilli(exportEnd).Format("2006/01/02 15:04:05")

	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ], 导出区间：[ %s , %s ]", minT, MaxT, exportBeginFormat, exportEndFormat))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

// 导出区间
func exportTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("导出区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func total(f *excelize.File, sheetName string, index int, res CalcRes, totalProductAmount, totalLoadFee, totalDeliverFee int) {
	err := f.SetRowHeight(sheetName, index, 26)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	row := strconv.Itoa(index)
	name := "总计"
	//if res.TotalDebtNotPaidAmount != 0 {
	//	name = "总计（含未支付补差）"
	//}

	err = f.SetCellValue(sheetName, "B"+row, name)

	amount := res.TotalFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, "M"+row, dealMoney(amount))

}

func title(f *excelize.File, sheetName string, index int) {
	_ = index
	err := f.MergeCell(sheetName, "A1", "Q1")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetRowHeight(sheetName, 1, 30)

	err = f.SetCellValue(sheetName, "A1", "账单")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   24,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {

	}
	err = f.SetCellStyle(sheetName, "A1", "A1", titleStyle)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

}

func buyer(f *excelize.File, sheetName, buyerName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "会员："+buyerName)
	err = f.SetRowHeight(sheetName, 2, 24)
	timeStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   18,
		},
	})
	_ = err

	err = f.SetCellStyle(sheetName, "B"+row, "C"+row, timeStyle)

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	err = f.MergeCell(sheetName, "A"+row, "A"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "A"+row, "")
	err = f.SetColWidth(sheetName, "A", "A", 4)

	colNum := 2
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 14)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "下单时间")

	// 供应商
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "供应商")

	// 商品
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 32)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "商品")

	// 计价方式
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "计价\n方式")

	// 订单信息
	colNum += 1
	colBegin := colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+4)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "订单信息")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "单价\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "订单数量\n（件）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "商品金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+3)+row2, "订单重量\n（kg）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+4)+row2, "仓配费\n（元）")

	err = f.SetColWidth(sheetName, convertToCol(colBegin+2), convertToCol(colBegin+2), 14)
	err = f.SetColWidth(sheetName, convertToCol(colBegin+4), convertToCol(colBegin+4), 14)
	//err = f.SetCellValue(sheetName, "I"+row2, "配送费\n（元）")

	// 发货信息
	colNum += 5
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+2)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "发货信息")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "发货数量\n（件）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "发货重量\n（kg）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "重量误差\n（kg）")

	// 退款补差
	colNum += 3
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+2)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "退款补差")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "发货退款\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "售后金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "补差金额\n（元）")

	// 金额小计
	colNum += 3
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetColWidth(sheetName, convertToCol(colBegin), convertToCol(colBegin), 14)
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "金额小计\n（元）")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, convertToCol(colBegin)+row2, style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "F"+row2, "P"+row2, style2)

	err = f.SetRowHeight(sheetName, index, 20)
	err = f.SetRowHeight(sheetName, index+1, 26)
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

func convertToCol(columnNumber int) string {
	var res []byte
	for columnNumber > 0 {
		a := columnNumber % 26
		if a == 0 {
			a = 26
		}
		res = append(res, 'A'+byte(a-1))
		columnNumber = (columnNumber - a) / 26
	}
	// 上面输出的res是反着的，前后交换
	for i, n := 0, len(res); i < n/2; i++ {
		res[i], res[n-1-i] = res[n-1-i], res[i]
	}
	return string(res)
}
