package adminUser

import (
	"base/core/xhttp"
	"base/service/adminService"
	"base/util"

	"github.com/gin-gonic/gin"
)

func GetByUser(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	objectID, err := util.ConvertToObject(req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	admin, err := adminService.NewAdminService().GetByUser(ctx, objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, admin)
}
