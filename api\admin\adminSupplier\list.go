package adminSupplier

import (
	"base/core/xhttp"
	"base/service/supplierService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
		AccountStatus int   `json:"account_status"`
		Page          int64 `json:"page" validate:"min=1"`
		Limit         int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{}

	if req.AccountStatus != 0 {
		filter["account_status"] = req.AccountStatus
	}

	list, count, err := supplierService.NewSupplierService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, count)
}

// ListByTag 查询
func ListByTag(ctx *gin.Context) {
	var req = struct {
		TagID string `json:"tag_id" validate:"len=24"`
		Page  int64  `json:"page" validate:"min=1"`
		Limit int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.TagID, "ListByTag tag_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"tag_list._id": id,
		"deleted_at":   0,
	}

	list, count, err := supplierService.NewSupplierService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, count)
}
