package tag

import (
	"base/core/xhttp"
	"base/service/tagService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Create  标签添加
func Create(ctx *gin.Context) {
	var req = struct {
		CategoryID string `json:"category_id" validate:"len=24"`
		Title      string `json:"title"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	categoryID, err := util.ConvertToObject(req.CategoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = tagService.NewTagService().Create(ctx, categoryID, req.Title)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
