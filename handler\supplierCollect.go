package handler

import (
	"base/api/supplier/supplierCollect"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 供应商收藏
func supplierCollectRouter(r *gin.RouterGroup) {
	r = r.Group("/supplier/collect")

	r2 := r.Group("/").Use(middleware.CheckToken)
	r2.POST("/upsert", supplierCollect.Upsert)
	r2.POST("/exist", supplierCollect.Exist)
	r2.POST("/delete", supplierCollect.Delete)
	r2.POST("/list", supplierCollect.List)
}
