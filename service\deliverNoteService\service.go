package deliverNoteService

import (
	"base/dao"
	"base/dao/deliverNoteDao"
	"base/global"
	"base/model"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, now time.Time, buyerID primitive.ObjectID, filePath, remark string, beginAt, endAt, timestamp int64, deliverType model.DeliverType) error
	Generate(ctx context.Context, content string) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.DeliveryNote, error)
	GetByBuyer(ctx context.Context, buyerID primitive.ObjectID, dayAt int64, deliverType model.DeliverType) (model.DeliveryNote, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliveryNote, int64, error)
}

type deliverNoteService struct {
	rdb            *redis.Client
	deliverNoteDao deliverNoteDao.DaoInt
}

func NewDeliverNoteService() ServiceInterface {
	return deliverNoteService{
		rdb:            global.RDBDefault,
		deliverNoteDao: dao.DeliverNoteDao,
	}
}

func (s deliverNoteService) Upsert(ctx context.Context, now time.Time, buyerID primitive.ObjectID, filePath, remark string, beginAt, endAt, timestamp int64, deliverType model.DeliverType) error {
	n := now.UnixMilli()

	data := model.DeliveryNote{
		ID:          primitive.NewObjectID(),
		BuyerID:     buyerID,
		FilePath:    filePath,
		Remark:      remark,
		BeginAt:     beginAt,
		EndAt:       endAt,
		DayAt:       timestamp,
		DeliverType: deliverType,
		CreatedAt:   n,
		UpdatedAt:   n,
	}

	byDayAt, err := s.GetByBuyer(ctx, buyerID, timestamp, deliverType)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		err := s.deliverNoteDao.Create(ctx, data)
		if err != nil {
			return err
		}
		return nil
	}

	// 更新
	data.ID = byDayAt.ID
	err = s.deliverNoteDao.UpdateOne(ctx, bson.M{"_id": byDayAt.ID}, bson.M{"$set": data})
	if err != nil {
		return err
	}
	return nil
}

func (s deliverNoteService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"deleted_at": time.Now().UnixMilli(),
	}
	err := s.deliverNoteDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s deliverNoteService) GetByID(ctx context.Context, id primitive.ObjectID) (model.DeliveryNote, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.deliverNoteDao.Get(ctx, filter)
	if err != nil {
		return model.DeliveryNote{}, err
	}
	return fee, nil
}

func (s deliverNoteService) GetByBuyer(ctx context.Context, buyerID primitive.ObjectID, dayAt int64, deliverType model.DeliverType) (model.DeliveryNote, error) {
	filter := bson.M{
		"buyer_id":     buyerID,
		"day_at":       dayAt,
		"deliver_type": deliverType,
	}
	fee, err := s.deliverNoteDao.Get(ctx, filter)
	if err != nil {
		return model.DeliveryNote{}, err
	}
	return fee, nil
}

func (s deliverNoteService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliveryNote, int64, error) {
	list, count, err := s.deliverNoteDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s deliverNoteService) Generate(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("deliverNoteService Generate error:%v", err)
			return
		}
	}()

	var data model.MNSGenDeliverNote

	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}
	marshal, _ := json.Marshal(data)

	reqID := util.NewUUID()
	withValue := context.WithValue(ctx, "req_id", reqID)

	zap.S().Infof("deliverNoteService req:%s, Generate：%s", reqID, string(marshal))

	//var ids []primitive.ObjectID
	for _, buyer := range data.BuyerList {
		id, err := util.ConvertToObjectWithCtx(ctx, buyer.BuyerID)
		if err != nil {
			return err
		}
		//ids = append(ids, id)
		err = s.gen(withValue, id, data.Timestamp, buyer.DeliverType)
		if err != nil {
			return err
		}
	}

	return nil
}
