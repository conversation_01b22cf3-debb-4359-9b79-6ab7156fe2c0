package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// RoleType 角色类型
type RoleType string

const (
	// RoleTypeSuperAdmin 超级管理员
	RoleTypeSuperAdmin RoleType = "superAdmin"
	// RoleTypeNormalAdmin 普通管理员
	RoleTypeNormalAdmin RoleType = "normalAdmin"
	// RoleTypeYhtAdmin YHT管理员
	RoleTypeYhtAdmin RoleType = "yhtAdmin"
)

// AuthType 权限类型
type AuthType string

const (
	// AuthTypeNormalBaseRead 普通管理员基础查看
	AuthTypeNormalBaseRead AuthType = "normalAdmin:base:read"
	// AuthTypeNormalAfterSaleAudit 普通管理员售后审核
	AuthTypeNormalAfterSaleAudit AuthType = "normalAdmin:afterSale:audit"
	// AuthTypeNormalProductAudit 普通管理员产品审核
	AuthTypeNormalProductAudit AuthType = "normalAdmin:product:audit"
)

const (
	// AuthTypeSuperAdminBaseRead 超级管理员基础查看
	AuthTypeSuperAdminBaseRead AuthType = "superAdmin:base:read"
)

const (
	// AuthTypeYhtAdminBaseRead YHT管理员基础查看
	AuthTypeYhtAdminBaseRead AuthType = "yhtAdmin:base:read"
)

// SuperRoleInfo 超级管理员角色信息
var SuperRoleInfo = RoleInfo{
	Value: RoleTypeSuperAdmin,
	Name:  "超级管理员",
	AuthList: []AuthInfo{
		{
			Value: AuthTypeSuperAdminBaseRead,
			Name:  "基础查看",
		},
	},
}

// NormalRoleInfo 普通管理员角色信息
var NormalRoleInfo = RoleInfo{
	Value: RoleTypeNormalAdmin,
	Name:  "普通管理员",
	AuthList: []AuthInfo{
		{
			Value: AuthTypeNormalBaseRead,
			Name:  "基础查看",
		},
		{
			Value: AuthTypeNormalAfterSaleAudit,
			Name:  "售后审核",
		},
		{
			Value: AuthTypeNormalProductAudit,
			Name:  "商品审核",
		},
	},
}

// YhtRoleInfo YHT管理员角色信息
var YhtRoleInfo = RoleInfo{
	Value: RoleTypeYhtAdmin,
	Name:  "YHT管理员",
	AuthList: []AuthInfo{
		{
			Value: AuthTypeYhtAdminBaseRead,
			Name:  "基础查看",
		},
	},
}

// Admin 管理员
type Admin struct {
	ID        primitive.ObjectID `bson:"_id"  json:"id"`
	UserID    primitive.ObjectID `bson:"user_id" json:"user_id"`
	Note      string             `bson:"note" json:"note"`
	Mobile    string             `bson:"mobile" json:"mobile"`
	Password  string             `bson:"password" json:"password"`
	RoleList  []RoleType         `bson:"role_list" json:"role_list"`
	AuthList  []AuthType         `bson:"auth_list" json:"auth_list"`
	RoleInfo  []RoleInfo         `bson:"role_info" json:"role_info"`
	CreatedAt int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt int64              `bson:"updated_at"  json:"updated_at"`
}

// RoleInfo 角色信息
type RoleInfo struct {
	Value    RoleType   `json:"value"`
	Name     string     `json:"name"`
	AuthList []AuthInfo `json:"auth_list"`
}

// AuthInfo 权限信息
type AuthInfo struct {
	Value AuthType `json:"value"`
	Name  string   `json:"name"`
}
