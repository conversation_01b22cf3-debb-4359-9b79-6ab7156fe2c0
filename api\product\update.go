package product

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/service/productTagService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func UpdateStock(ctx *gin.Context) {
	var req = struct {
		ProductId string `json:"product_id"`
		Stock     int    `json:"stock"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObjectWithCtx(ctx, req.ProductId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateStock(ctx, productId, req.Stock)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, "修改成功")
}

func UpdateCustomTag(ctx *gin.Context) {
	var req = struct {
		ProductId     string            `json:"product_id" validate:"required"`
		CustomTagList []model.CustomTag `json:"custom_tag_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObject(req.ProductId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateCustomTag(ctx, productId, req.CustomTagList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, "修改成功")
}

// UpdateBuyLimit 限购
func UpdateBuyLimit(ctx *gin.Context) {
	var req = struct {
		ProductID   string `json:"product_id"`
		BuyMinLimit int    `json:"buy_min_limit"`
		BuyMaxLimit int    `json:"buy_max_limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateBuyLimit(ctx, productID, req.BuyMinLimit, req.BuyMaxLimit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

//
//func UpdatePrice(ctx *gin.Context) {
//	var req = struct {
//		ProductID string           `json:"product_id" validate:"required"`
//		PriceList []model.PerPrice `json:"price_list" validate:"min=1"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	productId, err := util.ConvertToObject(req.ProductID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	err = productService.NewProductService().UpdatePrice(ctx, productId, req.PriceList)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, "修改成功")
//}

func UpdatePriceSingle(ctx *gin.Context) {
	var req = struct {
		ProductID             string `json:"product_id"`
		Price                 int    `json:"price"`
		MarketWholesalePrice  int    `json:"market_wholesale_price"`  // 市场批发价
		EstimatePurchasePrice int    `json:"estimate_purchase_price"` // 预估采购价
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Price < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "价格设置不合理"))
		return
	}

	if req.MarketWholesalePrice <= 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "预估采购价不能为0"))
		return
	}

	if req.EstimatePurchasePrice <= 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "预估采购价不能为0"))
		return
	}

	err = productService.NewProductService().UpdatePriceSingle(ctx, productID, req.Price, req.MarketWholesalePrice, req.EstimatePurchasePrice)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func UpdateProductUserType(ctx *gin.Context) {
	var req = struct {
		ProductID string         `json:"product_id"`
		UserType  model.UserType `json:"user_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateUserType(ctx, productID, req.UserType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

//
//func UpdatePricePart(ctx *gin.Context) {
//	var req = struct {
//		ProductID   string `json:"product_id" validate:"required"`
//		Price       int    `json:"price"`
//		OriginPrice int    `json:"origin_price"`
//		CostPrice   int    `json:"cost_price"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	productId, err := util.ConvertToObject(req.ProductID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	if req.OriginPrice < req.Price {
//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "市场价不能小于活动价"))
//		return
//	}
//
//	if req.CostPrice < req.Price {
//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "成本价不能小于活动价"))
//		return
//	}
//
//	err = productService.NewProductService().UpdatePricePart(ctx, productId, req.Price, req.OriginPrice, req.CostPrice)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, "修改成功")
//}

func UpdateDesc(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"required"`
		Desc      string `json:"desc"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateDesc(ctx, productId, req.Desc)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateTitle(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"required"`
		Title     string `json:"title"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateTitle(ctx, productId, req.Title)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdatePurchaseNote(ctx *gin.Context) {
	var req = struct {
		ProductID    string `json:"product_id" validate:"required"`
		PurchaseNote string `json:"purchase_note"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdatePurchaseNote(ctx, productId, req.PurchaseNote)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateAttr(ctx *gin.Context) {
	var req = struct {
		ProductID string            `json:"product_id" validate:"required"`
		Attr      []model.FieldInfo `json:"attr"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateAttr(ctx, productId, req.Attr)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// BindProductTag 标签绑定
func BindProductTag(ctx *gin.Context) {
	var req types.BindProductTagReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	tagID, err := util.ConvertToObjectWithNote(req.TagID, "BindProductTag")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	tag, err := productTagService.NewProductTagService().Get(ctx, tagID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	if tag.TagType != 2 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "只能设置普通标签"))
		return
	}
	var ids []primitive.ObjectID
	for _, i := range req.ProductIDs {
		id, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	var productIDs []primitive.ObjectID
	for _, i := range req.ProductIDs {
		id, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		productIDs = append(productIDs, id)
	}

	err = productService.NewProductService().BindProductTag(ctx, tag, productIDs, req.UpdateType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// BindCoverProductTag 封面标签绑定
func BindCoverProductTag(ctx *gin.Context) {
	var req types.BindProductTagReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	tagID, err := util.ConvertToObjectWithNote(req.TagID, "BindProductTag")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	tag, err := productTagService.NewProductTagService().Get(ctx, tagID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	if tag.TagType != 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "只能设置封面标签"))
		return
	}

	var ids []primitive.ObjectID
	for _, i := range req.ProductIDs {
		id, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	var productIDs []primitive.ObjectID
	for _, i := range req.ProductIDs {
		id, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		productIDs = append(productIDs, id)
	}

	err = productService.NewProductService().BindCoverProductTag(ctx, tag, productIDs, req.UpdateType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
