package buyerService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

var cacheByUser = "buyerByUser:"

func getByUser(r *redis.Client, id primitive.ObjectID) model.Buyer {
	key := cacheByUser + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.Buyer{}
		}
		var i model.Buyer
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.Buyer{}
		}
		return i
	}
	return model.Buyer{}
}

func setByUser(r *redis.Client, info model.Buyer) {
	key := cacheByUser + info.UserID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().<PERSON>rror("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, time.Hour*24*30)
}

func delByUser(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cacheByUser+id.Hex())
}
