package invoiceTitleDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.InvoiceTitle) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.InvoiceTitle, error)
	List(ctx context.Context, filter bson.M) ([]model.InvoiceTitle, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.InvoiceTitle, int64, error)
}

type invoiceTitleDao struct {
	db *mongo.Collection
}

// List 查询
func (s invoiceTitleDao) List(ctx context.Context, filter bson.M) ([]model.InvoiceTitle, error) {
	var list []model.InvoiceTitle
	//skip := (page - 1) * limit
	opts := options.Find()
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s invoiceTitleDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.InvoiceTitle, int64, error) {
	var list []model.InvoiceTitle
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s invoiceTitleDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceTitleDao) Create(ctx context.Context, data model.InvoiceTitle) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceTitleDao) Get(ctx context.Context, filter bson.M) (model.InvoiceTitle, error) {
	var data model.InvoiceTitle
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.InvoiceTitle{}, err
	}
	return data, nil
}

func NewInvoiceTitleDao(collect string) DaoInt {
	return invoiceTitleDao{
		db: global.MDB.Collection(collect),
	}
}
