package adminPromote

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/ossService"
	"base/service/promoteService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strings"
	"time"
)

func Create(ctx *gin.Context) {
	var req = struct {
		ServicePointID string         `json:"service_point_id"`
		Title          string         `json:"title"`
		Desc           string         `json:"desc"`
		Cover          model.FileInfo `json:"cover"` // 封面
		Video          model.FileInfo `json:"video"` // 视频
		ProductList    []string       `json:"product_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if strings.TrimSpace(req.Title) == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入标题"))
		return
	}
	if strings.TrimSpace(req.Desc) == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入描述"))
		return
	}
	if req.Cover.Name == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请上传封面"))
		return
	}
	if req.Video.Name == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请上传视频"))
		return
	}

	productList := make([]primitive.ObjectID, 0)
	for _, s := range req.ProductList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		productList = append(productList, id)
	}

	if req.Video.Name != "" {
		snap, err := ossService.NewOssService().VideoSnap("promote", req.Video.Name)
		if err != nil {
			return
		}

		req.Video.Poster = snap
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	now := time.Now().UnixMilli()
	data := model.Promote{
		ID:             primitive.NewObjectID(),
		ServicePointID: pointID,
		Title:          req.Title,
		Desc:           req.Desc,
		Cover:          req.Cover,
		Video:          req.Video,
		ProductList:    productList,
		Status:         model.PromoteStatusVisible,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	err = promoteService.NewPromoteService().Create(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
