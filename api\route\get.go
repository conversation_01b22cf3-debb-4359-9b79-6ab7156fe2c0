package route

import (
	"base/core/xhttp"
	"base/model"
	"base/service/routeService"
	"base/util"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// GetServicePoint 服务点路线查询
func GetServicePoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	info, err := routeService.NewTransportFeeService().GetServicePoint(ctx, id)
	if err == mongo.ErrNoDocuments {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, info)
}

// GetArriveTime 到达时间
func GetArriveTime(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	info, err := routeService.NewTransportFeeService().GetServicePoint(ctx, id)
	if err == mongo.ErrNoDocuments {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res := deal(info)
	xhttp.RespSuccess(ctx, res)
}

func deal(route model.Route) ArriveTime {
	t := carbon.PRC
	var res ArriveTime
	now := carbon.Now(t)

	for _, i := range route.DeliverTime {
		begin := carbon.CreateFromTimeMilli(int(i.OrderBeginTime), 0, 0, 0)
		end := carbon.CreateFromTimeMilli(int(i.OrderEndTime), 59, 59, 999)
		if now.BetweenIncludedBoth(begin, end) {
			reachTime := carbon.CreateFromTimeMilli(int(i.ReachTime), 0, 0, 0)
			targetTime := reachTime.AddDays(int(i.OffsetDay))
			cost := now.DiffInHours(targetTime)
			res.CostHour = cost
			//res.ArriveTime = targetTime.TimestampMilli()
			res.ArriveTime = targetTime.Carbon2Time().Format("01月02日")
			res.EndOrderTime = end.Carbon2Time().Format("15:04")

			res.Prefix = "今日"
			if i.OffsetDay == 1 {
				res.Prefix = "明日"
			}
			res.Now = now.Carbon2Time().Format(time.RFC3339Nano)
		}
	}

	return res
}

type ArriveTime struct {
	CostHour     int64  `json:"cost_hour"` // 消耗小时
	ArriveTime   string `json:"arrive_time"`
	Prefix       string `json:"prefix"`
	EndOrderTime string `json:"end_order_time"` // 截单时间
	Now          string `json:"now"`
}
