package xhttp

import (
	"base/core/xerr"
	"base/model"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func UserID(ctx *gin.Context) (primitive.ObjectID, error) {
	i := ctx.GetString("user_id")
	objectID, err := util.ConvertToObject(i)
	if err != nil {
		zap.S().Error("未登录，直接获取userID转换错误")
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrLoginExpire, nil)
	}
	return objectID, nil
}

func GetUserID(ctx *gin.Context) (primitive.ObjectID, error) {
	i := ctx.GetString("user_id")
	objectID, err := util.ConvertToObjectWithCtx(ctx, i)
	if err != nil {
		zap.S().Error("未登录，直接获取userID转换错误")
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrLoginExpire, nil)
	}
	return objectID, nil
}

func IP(ctx *gin.Context) string {
	ip := ctx.Request.Header.Get("X-Real-IP")
	if ip == "" {
		ip = ctx.Request.Header.Get("X-Forwarded-For")
	}
	if ip == "" {
		ip = ctx.Request.RemoteAddr
	}
	return ip
}

// GetEnvID 获取环境信息
//func GetEnvID(ctx *gin.Context, s *svc.ServiceContext) (model.ShopEnv, error) {
//	envID := ctx.GetInt("env_id")
//	env, err := s.ShopEnvSvr.GetEnv(envID)
//	if err != nil {
//		return model.ShopEnv{}, err
//	}
//	return env, nil
//}

// GetEnv 获取环境信息
func GetEnv(ctx *gin.Context) model.ObjectType {
	env := ctx.GetInt("X-Env")
	if env == 0 {
		return model.ObjectTypeBuyer
	}

	return model.ObjectType(env)
}

func CheckSelfAuth(ctx *gin.Context, userID primitive.ObjectID) error {
	id, err := UserID(ctx)
	if err != nil {
		return err
	}

	if id != userID {
		return errors.New("无操作权限")
	}
	return nil
}

func GetLoginUserID(ctx *gin.Context) primitive.ObjectID {
	idStr := ctx.GetHeader("X-Login")
	if idStr == "" {
		return primitive.NilObjectID
	}
	id, err := util.ConvertToObjectWithCtx(ctx, idStr)
	if err != nil {
		return primitive.NilObjectID
	}

	return id
}

// GetPointID 获取header中心仓id
func GetPointID(ctx *gin.Context) (primitive.ObjectID, error) {
	idStr := ctx.GetHeader("X-Point")
	id, err := util.ConvertToObjectWithCtx(ctx, idStr)
	if err != nil {
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrLoginExpire, nil, "登录失效")
	}

	return id, nil
}

//
//// GetStationID 服务仓ID
//func GetStationID(ctx *gin.Context) (primitive.ObjectID, primitive.ObjectID) {
//	defaultID, _ := primitive.ObjectIDFromHex("66d7fee0e6aa983280068840")
//
//	idStr := ctx.GetHeader("X-Station")
//	if idStr == "" {
//		return primitive.NilObjectID, defaultID
//	}
//	id, err := util.ConvertToObjectWithCtx(ctx, idStr)
//	if err != nil {
//		return primitive.NilObjectID, defaultID
//	}
//
//	return id, defaultID
//}
