package util

import (
	"base/core/xerr"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	ocr "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ocr/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ocr/v1/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ocr/v1/region"
	"go.uber.org/zap"
	"io"
	"mime/multipart"
)

type OcrUtil struct {
	auth   *basic.Credentials
	client *ocr.OcrClient
}

func NewOcrUtil(ak, sk, re string) *OcrUtil {
	auth := basic.NewCredentialsBuilder().WithAk(ak).WithSk(sk).Build()

	return &OcrUtil{
		auth:   auth,
		client: ocr.NewOcrClient(ocr.OcrClientBuilder().WithRegion(region.ValueOf(re)).WithCredential(auth).Build()),
	}
}

// GetFileBase64 获取文件base64编码
func (o *OcrUtil) GetFileBase64(f multipart.File, fh *multipart.FileHeader) (string, error) {
	// 如果文件大小超过10M不进行请求
	if fh.Size>>20 > 10 {
		return "", xerr.NewErr(xerr.ErrParamError, nil, "请求内容大小超过10M, 请对上传文件进行压缩")
	}

	// 读取文件内容
	fcon, err := io.ReadAll(f)
	if err != nil {
		return "", errors.New(fmt.Sprintf("文件内容读取失败:%s", err.Error()))
	}
	return base64.StdEncoding.EncodeToString(fcon), nil
}

// IdCard 身份证识别，国徽一面为true, 身份信息一面为false
func (o *OcrUtil) IdCard(url string, isBack bool) (interface{}, error) {
	side := "front" // 正面
	if isBack {
		side = "back" // 反面
	}

	res, err := o.client.RecognizeIdCard(&model.RecognizeIdCardRequest{
		Body: &model.IdCardRequestBody{
			Url:  &url,
			Side: &side,
		},
	})
	if err != nil {
		zap.S().Error("身份证识别错误:", err)
		return nil, xerr.NewErr(xerr.ErrOcrIdCard, err)
	}
	return res.Result, nil
}

// BankCard 银行卡识别
func (o *OcrUtil) BankCard(url string) (interface{}, error) {
	// 请求银行卡识别
	res, err := o.client.RecognizeBankcard(&model.RecognizeBankcardRequest{
		Body: &model.BankcardRequestBody{
			Url: &url,
		},
	})

	if err != nil {
		zap.S().Error("银行卡识别错误:", err)
		return nil, xerr.NewErr(xerr.ErrOcrBank, err)
	}

	return res.Result, nil
}

// BusinessLicense 营业执照识别
func (o *OcrUtil) BusinessLicense(url string) (interface{}, error) {
	// 请求营业执照识别
	res, err := o.client.RecognizeBusinessLicense(&model.RecognizeBusinessLicenseRequest{
		Body: &model.BusinessLicenseRequestBody{
			Url: &url,
		},
	})

	if err != nil {
		zap.S().Error("营业执照识别错误:", err)
		return nil, xerr.NewErr(xerr.ErrOcrBusinessLicense, err)
	}

	return res.Result, nil
}
