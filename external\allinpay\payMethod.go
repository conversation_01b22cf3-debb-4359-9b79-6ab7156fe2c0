package allinpay

// PayMethodMiniProgramBack  微信小程序支付（收银宝）
func PayMethodMiniProgramBack(amount int, wechatAppID, openID string) map[string]interface{} {
	m := map[string]interface{}{
		//"limitPay": "no_credit",
		"limitPay": "",
		"subAppid": wechatAppID,
		"amount":   amount,
		"acct":     openID,
	}
	return map[string]interface{}{
		"WECHATPAY_MINIPROGRAM": m,
	}
}

// PayMethodMiniProgramBackDeposit 充值
func PayMethodMiniProgramBackDeposit(amount int, wechatAppID, openID string) map[string]interface{} {
	m := map[string]interface{}{
		"limitPay": "no_credit",
		//"limitPay": "",
		"subAppid": wechatAppID,
		"amount":   amount,
		"acct":     openID,
	}
	return map[string]interface{}{
		"WECHATPAY_MINIPROGRAM": m,
	}
}

// PayMethodCoupon 代金券-账户内转账
func PayMethodCoupon(amount int, accountSetNo string) map[string]interface{} {
	m := map[string]interface{}{
		"amount":       amount,
		"accountSetNo": accountSetNo, //  账户集编号。 目前只支持营销专用账户和保证金账户。  如果不传此字段，则默认为营销专用账户。
	}
	return map[string]interface{}{
		"COUPON": m,
	}
}

// PayMethodBalance  余额
func PayMethodBalance(amount int, accountSetNo string) map[string]interface{} {
	m := map[string]interface{}{
		"accountSetNo": accountSetNo,
		"amount":       amount,
	}
	return map[string]interface{}{
		"BALANCE": []interface{}{m},
	}
}
