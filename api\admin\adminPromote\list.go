package adminPromote

import (
	"base/core/xhttp"
	"base/model"
	"base/service/promoteService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		ServicePointID string              `json:"service_point_id"`
		Status         model.PromoteStatus `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := promoteService.NewPromoteService().List(ctx, req.Status, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}
