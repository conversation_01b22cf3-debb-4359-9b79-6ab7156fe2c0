package handler

import (
	"base/api/invoice"
	"base/api/orderStats"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 发票
func invoiceRouter(r *gin.RouterGroup) {
	r = r.Group("/invoice")
	r.Use(middleware.CheckToken)
	r.POST("/order/finish", orderStats.ListFinishByBuyer)
	r.POST("/calc", invoice.Calc)
	r.POST("/down/excel", invoice.DownExcel)
	r.POST("/list", invoice.List)
	r.POST("/get", invoice.Get)
	r.POST("/status/count", invoice.CountStatus)
	r.POST("/create", invoice.Create)
	r.POST("/create/check", invoice.CreateCheck)
	r.POST("/cancel", invoice.Cancel)
	r.POST("/delete", invoice.Delete)
}
