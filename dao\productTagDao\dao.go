package productTagDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.ProductTag) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.ProductTag, error)
	List(ctx context.Context, filter bson.M) ([]model.ProductTag, error)
}

type productTagDao struct {
	db *mongo.Collection
}

func (s productTagDao) Create(ctx context.Context, data model.ProductTag) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s productTagDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s productTagDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s productTagDao) Get(ctx context.Context, filter bson.M) (model.ProductTag, error) {
	var data model.ProductTag
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductTag{}, err
	}
	return data, nil
}

// List 查询
func (s productTagDao) List(ctx context.Context, filter bson.M) ([]model.ProductTag, error) {
	var list []model.ProductTag

	// 根据tag_type排序
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "tag_type", Value: 1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewProductTagDao(collect string) DaoInt {
	return productTagDao{
		db: global.MDB.Collection(collect),
	}
}
