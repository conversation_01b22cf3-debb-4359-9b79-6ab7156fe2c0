package qrCodeService

import (
	"base/global"
	"base/service/miniService"
	"context"
	"encoding/base64"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

var cache = "user-qr:"

type ServiceInterface interface {
	Get(ctx context.Context, userID primitive.ObjectID) (string, error)
}

type qrCodeService struct {
	rdb   *redis.Client
	miniS miniService.ServiceInterface
}

func NewQrCodeService() ServiceInterface {
	return qrCodeService{
		rdb:   global.RDBDefault,
		miniS: miniService.NewMiniService(),
	}
}

func (s qrCodeService) Get(ctx context.Context, userID primitive.ObjectID) (string, error) {
	key := cache + userID.Hex()
	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		//	exist
		s2 := s.rdb.Get(ctx, key).Val()
		return s2, nil
	}
	// generate
	qrCodeBytes, err := s.miniS.GetUnlimitedQRCode(userID.Hex())
	if err != nil {
		return "", err
	}
	toString := base64.StdEncoding.EncodeToString(qrCodeBytes)
	s.rdb.Set(ctx, key, toString, time.Minute*10)

	return toString, nil
}
