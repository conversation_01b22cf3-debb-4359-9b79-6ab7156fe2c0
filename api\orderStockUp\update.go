package orderStockUp

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/global"
	"base/service/orderQualityService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// UpdateOrderToStockUp 加入备货
func UpdateOrderToStockUp(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()

	var req = struct {
		ServicePointID  string   `json:"service_point_id"`
		OrderIDList     []string `json:"order_id_list" validate:"-"`
		TargetTimestamp int64    `json:"target_timestamp" validate:"required"` // 目标时间
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.OrderIDList) < 1 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "订单缺失,请刷新重试")
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, s := range req.OrderIDList {
		orderID, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		var f bool
		for _, id := range ids {
			if id == orderID {
				zap.S().Infof("跳过")
				f = true
				break
			}
		}
		if !f {
			ids = append(ids, orderID)
		}
	}

	ts, err := util.DayStartTimestamp(req.TargetTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"order_list.order_id": bson.M{
			"$in": ids,
		},
	}

	count, err := orderQualityService.NewOrderQualityService().Count(ctx, filter)
	if err != nil {
		return
	}

	if count > 0 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "存在部分订单已加入备货,请刷新重试")
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderQualityService.NewOrderQualityService().UpdateOrderToStockUp(ctx, ids, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
