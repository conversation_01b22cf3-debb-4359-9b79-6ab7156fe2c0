package product

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/indexPartProductService"
	"base/service/productService"
	"base/service/shortcutService"
	"base/util"
	"context"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func DownSaleBatch(ctx *gin.Context) {
	var req = struct {
		ProductIDList []string `json:"product_id_list"`
		//Sale          bool   `json:"sale"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.ProductIDList) < 1 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "参数缺失")
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, s := range req.ProductIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	for _, id := range ids {
		err = productService.NewProductService().UpdateSale(ctx, id, false)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccess(ctx, nil)
}

// UpdateSale 更新商品上下架状态
func UpdateSale(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
		Reason    string `json:"reason"`
		Sale      bool   `json:"sale"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if !req.Sale {
		// 下架审核
		err = productService.NewProductService().CreateOffLineAudit(ctx, productID, req.Reason)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	} else {
		// 上架
		err = productService.NewProductService().UpdateSale(ctx, productID, true)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccess(ctx, "修改成功")
}

func DownProduct(ctx context.Context, id primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("异步下架商品 error:%v", err)
			return
		}
	}()
	//快捷栏下架
	err := shortcutService.NewShortcutService().DownProduct(ctx, id, false)
	if err != nil {
		return err
	}

	// 主题
	//err = topicService.NewTopicService().DownProduct(ctx, id, false)
	//if err != nil {
	//	return err
	//}

	// 专区
	err = indexPartProductService.NewIndexPartProductService().DownProduct(ctx, id, false)
	if err != nil {
		return err
	}

	// 推广
	//err = promoteService.NewPromoteService().DownProduct(ctx, id)
	//if err != nil {
	//	return err
	//}

	return nil
}

func UpProduct(ctx context.Context, id primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("异步上架商品 error:%v", err)
			return
		}
	}()

	//快捷栏
	err := shortcutService.NewShortcutService().UpProduct(ctx, id)
	if err != nil {
		return err
	}

	//// 主题
	//err = topicService.NewTopicService().UpProduct(ctx, id)
	//if err != nil {
	//	return err
	//}

	// 专区
	err = indexPartProductService.NewIndexPartProductService().UpProduct(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

// ListOffLineAudit 下架审核-查询
func ListOffLineAudit(ctx *gin.Context) {
	products, err := productService.NewProductService().ListOffLineAudit(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, products)
}

// OffLineAudit 下架审核
func OffLineAudit(ctx *gin.Context) {
	var req = struct {
		ProductID   string                `json:"product_id"`
		AuditStatus model.AuditStatusType `json:"audit_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().OffLineAudit(ctx, productID, req.AuditStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
