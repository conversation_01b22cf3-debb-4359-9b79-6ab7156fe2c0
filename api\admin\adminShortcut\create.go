package adminShortcut

import (
	"base/core/xhttp"
	"base/service/shortcutService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	req := types.ShortcutCreate{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	res, err := shortcutService.NewShortcutService().Create(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res.Hex())
}

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = shortcutService.NewShortcutService().Del(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
