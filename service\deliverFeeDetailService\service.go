package deliverFeeDetailService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/deliverFeeDetailDao"
	"base/global"
	"base/model"
	"base/service/parentOrderService"
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 配送费明细服务接口
type ServiceInterface interface {
	// CreateFromParentOrder 从父订单创建配送费明细记录
	CreateFromParentOrder(ctx context.Context, parentOrderID primitive.ObjectID) error
	// List 按月获取配送费明细汇总
	List(ctx context.Context, monthStart, monthEnd int64) ([]model.DeliverFeeDetail, error)
	// Get 获取单个配送费明细记录
	Get(ctx context.Context, id primitive.ObjectID) (model.DeliverFeeDetail, error)
	// GetByParentOrderID 获取单个配送费明细记录
	GetByParentOrderID(ctx context.Context, parentOrderID primitive.ObjectID) (model.DeliverFeeDetail, error)
}

type deliverFeeDetailService struct {
	mdb                 *mongo.Database
	parentOrderS        parentOrderService.ServiceInterface
	deliverFeeDetailDao deliverFeeDetailDao.DaoInt
}

// NewDeliverFeeDetailService 创建配送费明细服务实例
func NewDeliverFeeDetailService() ServiceInterface {
	return &deliverFeeDetailService{
		mdb:                 global.MDB,
		parentOrderS:        parentOrderService.NewParentOrderService(),
		deliverFeeDetailDao: dao.DeliverFeeDetailDao,
	}
}

// CreateFromParentOrder 从父订单创建配送费明细记录
func (s *deliverFeeDetailService) CreateFromParentOrder(ctx context.Context, parentOrderID primitive.ObjectID) error {
	detail, err := s.GetByParentOrderID(ctx, parentOrderID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if detail.ID != primitive.NilObjectID {
		zap.S().Infof("已存在配送费明细记录，父订单ID：%s", parentOrderID.Hex())
		return nil
	}

	// 获取父订单信息
	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
	if err != nil {
		return err
	}

	if parentOrder.DeliverFeeRes.FinalDeliverFee == 0 || (parentOrder.DeliverType != model.DeliverTypeDoor && parentOrder.DeliverType != model.DeliverTypeInstantDeliver) {
		return nil
	}

	// 构建配送费明细记录
	deliverFeeDetail := model.DeliverFeeDetail{
		ID:                 primitive.NewObjectID(),
		ParentOrderID:      parentOrder.ID,
		BuyerID:            parentOrder.BuyerID,
		DeliverType:        parentOrder.DeliverType,
		InstantDeliverType: parentOrder.InstantDeliverType,
		InstantDeliverName: parentOrder.InstantDeliverName,
		TotalDeliverFee:    parentOrder.DeliverFeeRes.TotalDeliverFee,
		SubsidyDeliverFee:  parentOrder.DeliverFeeRes.SubsidyDeliverFee,
		FinalDeliverFee:    parentOrder.DeliverFeeRes.FinalDeliverFee,
		OrderCreatedAt:     parentOrder.CreatedAt,
		CreatedAt:          time.Now().UnixMilli(),
	}

	// 保存记录
	err = s.deliverFeeDetailDao.Create(ctx, deliverFeeDetail)
	if err != nil {
		return err
	}

	return nil
}

// ListByMonth 按月获取配送费明细汇总
func (s *deliverFeeDetailService) List(ctx context.Context, monthStart, monthEnd int64) ([]model.DeliverFeeDetail, error) {
	// 参数验证
	if monthStart == 0 || monthEnd == 0 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "月份时间戳不能为空")
	}

	// 构建查询条件
	filter := bson.M{
		"order_created_at": bson.M{
			"$gte": monthStart,
			"$lt":  monthEnd,
		},
	}

	// 查询数据
	list, err := s.deliverFeeDetailDao.List(ctx, filter)
	if err != nil {
		zap.L().Error("查询配送费明细列表失败", zap.Error(err), zap.Any("filter", filter))
		return nil, err
	}

	return list, nil
}

// Get 获取单个配送费明细记录
func (s *deliverFeeDetailService) Get(ctx context.Context, id primitive.ObjectID) (model.DeliverFeeDetail, error) {
	// 参数验证
	if id == primitive.NilObjectID {
		return model.DeliverFeeDetail{}, xerr.NewErr(xerr.ErrParamError, nil, "记录ID不能为空")
	}

	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}

	detail, err := s.deliverFeeDetailDao.Get(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return model.DeliverFeeDetail{}, xerr.NewErr(xerr.ErrNoDocument, nil, "配送费明细记录不存在")
		}
		zap.L().Error("获取配送费明细记录失败", zap.Error(err), zap.String("id", id.Hex()))
		return model.DeliverFeeDetail{}, err
	}

	return detail, nil
}

// GetByParentOrderID 获取单个配送费明细记录
func (s *deliverFeeDetailService) GetByParentOrderID(ctx context.Context, parentOrderID primitive.ObjectID) (model.DeliverFeeDetail, error) {
	filter := bson.M{
		"parent_order_id": parentOrderID,
	}

	detail, err := s.deliverFeeDetailDao.Get(ctx, filter)
	if err != nil {
		return model.DeliverFeeDetail{}, err
	}

	return detail, nil
}
