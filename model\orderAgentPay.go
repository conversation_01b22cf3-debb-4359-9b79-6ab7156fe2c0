package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type AgentPayType int

const (
	AgentPayTypeNormal  AgentPayType = 1
	AgentPayTypeDebt    AgentPayType = 2
	AgentPayTypeCoupon  AgentPayType = 3
	AgentPayTypeAdjust  AgentPayType = 4 // 调账
	AgentPayTypeDeliver AgentPayType = 5 // 配送费
)

// OrderAgentPay 代付单
type OrderAgentPay struct {
	ID                               primitive.ObjectID `json:"id" bson:"_id"`
	OrderID                          primitive.ObjectID `json:"order_id" bson:"order_id"`               //     订单ID
	ParentOrderID                    primitive.ObjectID `json:"parent_order_id" bson:"parent_order_id"` //     父单ID
	BuyerID                          primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`               // 采购商ID
	UserID                           primitive.ObjectID `json:"user_id" bson:"user_id"`                 // 采购商ID
	BuyerName                        string             `json:"buyer_name" bson:"buyer_name"`           // 采购商名称
	PayMethod                        PayMethodType      `json:"pay_method" bson:"pay_method"`
	AgentPayType                     AgentPayType       `json:"agent_pay_type" bson:"agent_pay_type"`                                 //     代付订单类型  1 普通订单 2 补差订单 3 代金券
	OrderPaidAmount                  int                `json:"order_paid_amount" bson:"order_paid_amount"`                           //     订单实付
	OrderSplitAmount                 int                `json:"order_split_amount" bson:"order_split_amount"`                         //     最终分账金额
	BizOrderNo                       string             `json:"biz_order_no" bson:"biz_order_no"`                                     //     支付订单号（云商通
	ReceiverBizUserID                string             `json:"receiver_biz_user_id" bson:"receiver_biz_user_id"`                     // 	   收款方-供应商
	OriPayBizUserID                  string             `json:"ori_pay_biz_user_id" bson:"ori_pay_biz_user_id"`                       //     原付款方
	OriBizOrderNo                    string             `json:"ori_biz_order_no" bson:"ori_biz_order_no"`                             //     需要退款的原交易订单号
	OriOrderNo                       string             `json:"ori_order_no" bson:"ori_order_no"`                                     //     原云商通订单号
	AccountSetNo                     string             `json:"account_set_no" bson:"account_set_no"`                                 //     必填 收款人的账户集编号。	云商通分配的托管专用账户集的编号
	SupplierID                       primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`                                       // 供应商ID
	SupplierName                     string             `json:"supplier_name" bson:"supplier_name"`                                   // 供应商名称
	SupplierAmount                   int                `json:"supplier_amount" bson:"supplier_amount"`                               // 	   供应商金额
	SupplierFeeAmount                int                `json:"supplier_fee_amount" bson:"supplier_fee_amount"`                       // 	   供应商手续费
	WarehouseID                      primitive.ObjectID `json:"warehouse_id" bson:"warehouse_id"`                                     // 集中仓ID
	WarehouseName                    string             `json:"warehouse_name" bson:"warehouse_name"`                                 // 集中仓名称
	WarehouseAmount                  int                `json:"warehouse_amount" bson:"warehouse_amount"`                             // 	   集中仓总金额
	WarehouseTransportAmount         int                `json:"warehouse_transport_amount" bson:"warehouse_transport_amount"`         // 	   集中仓运费金额
	WarehouseTransportFeeAmount      int                `json:"warehouse_transport_fee_amount" bson:"warehouse_transport_fee_amount"` // 	   集中仓运费手续费
	WarehouseLoadAmount              int                `json:"warehouse_load_amount" bson:"warehouse_load_amount"`                   // 	   仓配费
	WarehouseLoadFeeAmount           int                `json:"warehouse_load_fee_amount" bson:"warehouse_load_fee_amount"`           // 	   仓配费-手续费
	DeliverAmount                    int                `json:"deliver_amount" bson:"deliver_amount"`                                 // 	   配送费
	DeliverFeeAmount                 int                `json:"deliver_fee_amount" bson:"deliver_fee_amount"`                         // 	   配送费-手续费
	ServiceAmount                    int                `json:"service_amount" bson:"service_amount"`                                 // 	   服务费
	ServiceFeeAmount                 int                `json:"service_fee_amount" bson:"service_fee_amount"`
	StationServiceAmount             int                `json:"station_service_amount" bson:"station_service_amount"`         // 	   服务费
	StationServiceFeeAmount          int                `json:"station_service_fee_amount" bson:"station_service_fee_amount"` // 	   服务费-手续费
	SupplierLevel                    SupplierLevel      `json:"supplier_level" bson:"supplier_level"`
	StationID                        primitive.ObjectID `json:"station_id" bson:"station_id"`                                                       // 城市仓ID
	StationName                      string             `json:"station_name" bson:"station_name"`                                                   // 城市仓名称
	ServicePointID                   primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`                                           // 服务点ID
	ServicePointName                 string             `json:"service_point_name" bson:"service_point_name"`                                       // 服务点名称
	RebateServiceFeeAmount           int                `json:"rebate_service_fee_amount" bson:"rebate_service_fee_amount"`                         //     服务费返现
	AgentPayResult                   AgentPayResult     `json:"agent_pay_result" bson:"agent_pay_result"`                                           //     代付结果
	YeeDivideResult                  YeeDivideResult    `json:"yee_divide_result" bson:"yee_divide_result"`                                         //     分账结果
	Note                             string             `json:"note" bson:"note"`                                                                   // 备注
	TotalQualityRefundCouponAmount   int                `json:"total_quality_refund_coupon_amount" bson:"total_quality_refund_coupon_amount"`       //     售后退款券金额
	TotalAfterSaleRefundCouponAmount int                `json:"total_after_sale_refund_coupon_amount" bson:"total_after_sale_refund_coupon_amount"` //     售后退款券金额
	TotalPromotionSubsidyAmount      int                `json:"total_promotion_subsidy_amount" bson:"total_promotion_subsidy_amount"`               //   优惠券补贴金额
	CreatedAt                        int64              `bson:"created_at" json:"created_at"`
	UpdatedAt                        int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt                        int64              `bson:"deleted_at" json:"deleted_at"`
}

type AgentPayResult struct {
	PayStatus      string `json:"pay_status" bson:"pay_status"`             //     	 支付状态	成功：success 进行中：pending 失败：fail 退款到银行卡/微信/支付宝成功、失败时会发订单结果通知商户。
	Status         string `json:"status" bson:"status"`                     //     	 支付状态	成功: OK
	PayFailMessage string `json:"pay_fail_message" bson:"pay_fail_message"` //         支付失败信息	只有payStatus为fail时有效
	OrderNo        string `json:"order_no" bson:"order_no"`                 // 必填     云商通订单编号
	BizOrderNo     string `json:"biz_order_no" bson:"biz_order_no"`         // 必填     商户订单号（支付订单）
	PayWhereabouts int    `json:"pay_whereabouts" bson:"pay_whereabouts"`   // 必填     1：到账户余额
	ExtendInfo     string `json:"extend_info" bson:"extend_info"`           //         扩展信息
}

type YeeDivideResult struct {
	OriOrderId                string            `json:"ori_order_id" bson:"ori_order_id"`                                 //
	OriUniqueOrderNo          string            `json:"ori_unique_order_no" bson:"ori_unique_order_no"`                   //
	ParentMerchantNo          string            `json:"parent_merchant_no" bson:"parent_merchant_no"`                     //
	MerchantNo                string            `json:"merchant_no" bson:"merchant_no"`                                   //
	ServicePointMerchantNo    string            `json:"service_point_merchant_no" bson:"service_point_merchant_no"`       //
	DivideRequestId           string            `json:"divide_request_id" bson:"divide_request_id"`                       // 商户分账请求号
	Status                    string            `json:"status" bson:"status"`                                             //
	DivideDetail              []YeeDivideDetail `json:"divide_detail" bson:"divide_detail"`                               //
	CreateDate                string            `json:"create_date" bson:"create_date"`                                   //
	DivideSuccessDate         string            `json:"divide_success_date" bson:"divide_success_date"`                   //
	CompleteDivideRequestId   string            `json:"complete_divide_request_id" bson:"complete_divide_request_id"`     //
	CompleteDivideStatus      string            `json:"complete_divide_status" bson:"complete_divide_status"`             //
	CompleteDivideDetail      []YeeDivideDetail `json:"complete_divide_detail" bson:"complete_divide_detail"`             //
	CompleteCreateDate        string            `json:"complete_create_date" bson:"complete_create_date"`                 //
	CompleteDivideSuccessDate string            `json:"complete_divide_success_date" bson:"complete_divide_success_date"` //
}

type YeeDivideDetail struct {
	DivideDetailNo   string `json:"divide_detail_no" bson:"divide_detail_no"`     // 易宝分账明细单号
	LedgerNo         string `json:"ledger_no" bson:"ledger_no"`                   // 分账接收方编号
	Amount           int    `json:"amount" bson:"amount"`                         // 分账金额
	DivideDetailDesc string `json:"divide_detail_desc" bson:"divide_detail_desc"` // 分账说明
}
