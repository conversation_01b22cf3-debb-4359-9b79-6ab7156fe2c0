package categoryService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/categoryDao"
	"base/mnsSendService"
	"base/model"
	"base/types"
	"base/util"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Add(ctx context.Context, req *types.CategoryCreate) (primitive.ObjectID, error)
	ListFirst(ctx context.Context, visibleType int) ([]model.Category, error)
	ListFirstAll(ctx context.Context) ([]model.Category, error)
	List(ctx context.Context, parentId string, env model.ObjectType, visibleType int) ([]model.Category, error)
	ListNextAll(ctx context.Context, parentID primitive.ObjectID) ([]model.Category, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Category, error)
	ListSecondSpecial(ctx context.Context, parentID primitive.ObjectID) ([]model.Category, error)
	ListSecondByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Category, error)
	Tree(ctx context.Context) ([]*types.CategoryRes, error)
	Update(ctx context.Context, req *types.CategoryUpdate) error
	UpdateSort(ctx context.Context, req *types.CategoryUpdateSort) error
	Del(ctx context.Context, id primitive.ObjectID) error
	GetDetail(ctx context.Context, id primitive.ObjectID) ([]model.Category, error)
	AddProducts(ctx context.Context, req *types.CategoryProduct) error
	CheckExistByThird(ctx context.Context, id primitive.ObjectID) (model.Category, error)

	//RefreshVisible(ctx context.Context, ids []primitive.ObjectID) error
}

type categoryService struct {
	db categoryDao.CategoryDaoInt
}

func NewCategoryService() ServiceInterface {
	return categoryService{
		db: dao.CategoryDao,
	}
}

func (s categoryService) GetDetail(ctx context.Context, id primitive.ObjectID) ([]model.Category, error) {
	get3, err := s.db.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return nil, err
	}
	if get3.Level != 3 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "参数需要第三级分类ID")
	}
	if len(get3.Path) != 2 {
		zap.S().Error("查询第三及分类错误")
		return nil, nil
	}
	get1, err := s.db.Get(ctx, bson.M{"_id": get3.Path[0]})
	if err != nil {
		return nil, err
	}
	get2, err := s.db.Get(ctx, bson.M{"_id": get3.Path[1]})
	if err != nil {
		return nil, err
	}
	var list []model.Category

	list = append(list, get1)
	list = append(list, get2)
	list = append(list, get3)

	return list, nil
}

func (s categoryService) CheckExistByThird(ctx context.Context, id primitive.ObjectID) (model.Category, error) {
	filter := bson.M{
		"_id": id,
	}
	info, err := s.db.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.Category{}, err
	}
	if info.ID == primitive.NilObjectID {
		return model.Category{}, xerr.NewErr(xerr.ErrParamError, nil, "分类不存在或失效")
	}
	return info, nil
}

func (s categoryService) ListFirst(ctx context.Context, visibleType int) ([]model.Category, error) {
	filter := bson.M{
		"level": 1,
	}
	switch visibleType {
	case 1:
	case 2:
		//	可视
		filter["visible"] = true
	case 3:
		filter["visible"] = false
	}

	list, err := s.db.List(ctx, filter)
	return list, err
}

func (s categoryService) ListFirstAll(ctx context.Context) ([]model.Category, error) {
	filter := bson.M{
		"level": 1,
	}
	list, err := s.db.List(ctx, filter)
	return list, err
}

func (s categoryService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Category, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	list, err := s.db.List(ctx, filter)
	return list, err
}

// Add 分类添加
func (s categoryService) Add(ctx context.Context, req *types.CategoryCreate) (primitive.ObjectID, error) {
	category := model.Category{
		ID:        primitive.NewObjectID(),
		Name:      req.Name,
		Img:       req.Img,
		Level:     1,
		Sort:      99,
		Visible:   true,
		IsSpecial: req.IsSpecial,
	}

	// 查询父节点是否存在
	pCategory := model.Category{}
	if len(req.ParentID) > 0 {
		parentId, err := primitive.ObjectIDFromHex(req.ParentID)
		if err != nil {
			return primitive.NilObjectID, xerr.NewErr(xerr.ErrNoDocument, nil, "分类所选父节点不存在，请刷新页面后重试")
		}

		pCategory, err = s.db.Find(ctx, parentId)
		if err != nil {
			return primitive.NilObjectID, err
		}

		if pCategory.Level >= 2 && req.IsSpecial {
			return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil, "特殊分类不能为三级分类")
		}

		if pCategory.IsSpecial {
			return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil, "特殊分类不能添加字类")
		}

		if pCategory.Level >= 3 {
			return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil, "分类层级不能超过3层")
		}

		category.Level = pCategory.Level + 1
		category.ParentID = pCategory.ID
		category.Path = append(pCategory.Path, pCategory.ID)
	}

	res, err := s.db.Create(ctx, category)
	if err != nil {
		return primitive.NilObjectID, err
	}
	// 创建等级
	if category.Level == 2 && category.ParentID.Hex() == "6450d00498426603acf0a074" {
		mnsSendService.NewMNSClient().SendCreateFruitClass(category.ID.Hex())
	}

	return res, nil
}

func (s categoryService) List(ctx context.Context, parentId string, env model.ObjectType, visibleType int) ([]model.Category, error) {
	var err error
	p := primitive.NilObjectID
	if parentId != "all" {
		if p, err = primitive.ObjectIDFromHex(parentId); err != nil {
			return []model.Category{}, xerr.NewErr(xerr.ErrNoDocument, nil, "所选父节点不存在，请刷新页面后重试")
		}
	}

	filter := bson.M{
		"parent_id": p,
	}

	if env == model.ObjectTypeBuyer {
		expressID, _ := primitive.ObjectIDFromHex("66727385d948593db3eee799")
		yhtID, _ := primitive.ObjectIDFromHex("66792215e7ea14140bec6cba")
		stationID, _ := primitive.ObjectIDFromHex("66ea8e010bf5034b411ea72f")

		excludeIDList := []primitive.ObjectID{expressID, yhtID, stationID}

		filter["_id"] = bson.M{
			"$nin": excludeIDList,
		}

	}

	switch visibleType {
	case 1:
	case 2:
		//	可视
		filter["visible"] = true
	case 3:
		filter["visible"] = false
	}

	return s.db.List(ctx, filter)
}

func (s categoryService) ListNextAll(ctx context.Context, parentID primitive.ObjectID) ([]model.Category, error) {
	filter := bson.M{
		"parent_id": parentID,
	}
	return s.db.List(ctx, filter)
}

func (s categoryService) ListSecondSpecial(ctx context.Context, parentID primitive.ObjectID) ([]model.Category, error) {
	filter := bson.M{
		"parent_id":  parentID,
		"is_special": true,
	}
	return s.db.List(ctx, filter)
}

func (s categoryService) ListSecondByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Category, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"level": 2,
	}
	return s.db.List(ctx, filter)
}

func (s categoryService) Update(ctx context.Context, req *types.CategoryUpdate) error {
	id, err := primitive.ObjectIDFromHex(req.ID)
	if err != nil {
		return xerr.NewErr(xerr.ErrNoDocument, nil, "分类不存在，请刷新页面后重试")
	}

	category, err := s.db.Find(ctx, id)
	if err != nil {
		return err
	}

	category.Img = req.Img
	category.Name = req.Name
	category.Visible = req.Visible

	err = s.db.Update(ctx, category)
	if err != nil {
		return err
	}
	return nil
}

//
//func (s categoryService) RefreshVisible(ctx context.Context, ids []primitive.ObjectID) error {
//	if len(ids) < 1 {
//		return nil
//	}
//
//	filter := bson.M{"_id": bson.M{
//		"$in": ids,
//	}}
//	err := s.db.UpdateMany(ctx, filter, bson.M{"$set": bson.M{
//		"visible": true,
//	}})
//	if err != nil {
//		return err
//	}
//
//	filterFalse := bson.M{"_id": bson.M{
//		"$nin": ids,
//	}}
//	err = s.db.UpdateMany(ctx, filterFalse, bson.M{"$set": bson.M{
//		"visible": false,
//	}})
//	if err != nil {
//		return err
//	}
//	return nil
//}

func (s categoryService) UpdateSort(ctx context.Context, req *types.CategoryUpdateSort) error {
	for _, sort := range req.List {
		id, err := util.ConvertToObject(sort.ID)
		if err != nil {
			return err
		}
		err = s.db.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"sort": sort.Sort,
		}})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s categoryService) Del(ctx context.Context, id primitive.ObjectID) error {
	// 获取分类信息
	nextAll, err := s.ListNextAll(ctx, id)
	if err != nil {
		return err
	}
	if len(nextAll) > 0 {
		return xerr.XerrCategoryExistNextLevel
	}

	err = s.db.Del(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (s categoryService) Tree(ctx context.Context) ([]*types.CategoryRes, error) {
	list, err := s.db.List(ctx, bson.M{"visible": true})
	if err != nil {
		return []*types.CategoryRes{}, err
	}

	listMap := make(map[string]*types.CategoryRes)
	res := make([]*types.CategoryRes, 0)
	for _, val := range list {
		listMap[val.ID.Hex()] = &types.CategoryRes{
			ID:       val.ID.Hex(),
			ParentID: val.ParentID.Hex(),
			Name:     val.Name,
			Sort:     val.Sort,
		}

		if val.ParentID == primitive.NilObjectID {
			res = append(res, listMap[val.ID.Hex()])
		}
	}

	// 列表组装成树
	for _, val := range listMap {
		parent, ok := listMap[val.ParentID]
		if !ok {
			continue
		}

		if parent.Children == nil {
			parent.Children = make([]*types.CategoryRes, 0)
		}
		parent.Children = append(parent.Children, val)
	}

	return res, nil
}

// AddProducts 特殊分类添加商品id信息
func (s categoryService) AddProducts(ctx context.Context, req *types.CategoryProduct) error {
	id, err := primitive.ObjectIDFromHex(req.ID)
	if err != nil {
		return xerr.NewErr(xerr.ErrNoDocument, nil, "分类不存在，请刷新页面后重试")
	}

	category, err := s.db.Find(ctx, id)
	if err != nil {
		return err
	}
	if !category.IsSpecial {
		return xerr.NewErr(xerr.ErrParamError, nil, "当前分类不是特殊分类，不能选择商品")
	}

	// 添加商品id
	category.ProductIDs = make([]primitive.ObjectID, len(req.ProductIds))
	for i, productId := range req.ProductIds {
		category.ProductIDs[i], err = primitive.ObjectIDFromHex(productId)
		if err != nil {
			return err
		}
	}

	err = s.db.Update(ctx, category)
	if err != nil {
		return err
	}
	return nil
}
