package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CouponStatusType 状态类型
type CouponStatusType string

const (
	// CouponStatusTypeValid 有效
	CouponStatusTypeValid CouponStatusType = "valid"
	// CouponStatusTypeExpired 已过期
	CouponStatusTypeExpired CouponStatusType = "expired"
	// CouponStatusTypeUsed 已使用
	CouponStatusTypeUsed CouponStatusType = "used"
)

// CouponUser 优惠券用户
type CouponUser struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id"`
	SerialNo           string             `json:"serial_no" bson:"serial_no"`                       // 流水号
	BuyerID            primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                         // 采购商ID
	BuyerName          string             `json:"buyer_name" bson:"buyer_name"`                     // 采购商名称
	CouponStockID      primitive.ObjectID `json:"coupon_stock_id" bson:"coupon_stock_id"`           // 优惠券批次ID
	CouponStockType    CouponStockType    `json:"coupon_stock_type" bson:"coupon_stock_type"`       // 优惠券批次类型
	AvailableBeginTime int64              `json:"available_begin_time" bson:"available_begin_time"` // 可用时间
	AvailableEndTime   int64              `json:"available_end_time" bson:"available_end_time"`     // 可用时间
	Title              string             `json:"title" bson:"title"`                               // 标题
	Description        string             `json:"description" bson:"description"`                   // 描述
	CouponAmount       int                `json:"coupon_amount" bson:"coupon_amount"`               // 优惠券面额
	MinAmount          int                `json:"min_amount" bson:"min_amount"`                     // 门槛金额
	CouponStatus       CouponStatusType   `json:"coupon_status" bson:"coupon_status"`               // 状态
	Remark             string             `json:"remark" bson:"remark"`                             // 备注
	ParentOrderID      primitive.ObjectID `json:"parent_order_id" bson:"parent_order_id"`           // 父订单ID
	CreatedAt          int64              `bson:"created_at" json:"created_at"`
	UpdatedAt          int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt          int64              `bson:"deleted_at" json:"deleted_at"`
}
