package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/types"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListByOrigin	商品来源
func ListByOrigin(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"sale":                true,
		"product_origin_type": model.ProductOriginTypeForeign,
		"deleted_at":          0,
	}

	expressID, _ := primitive.ObjectIDFromHex("66727385d948593db3eee799")
	yhtID, _ := primitive.ObjectIDFromHex("66792215e7ea14140bec6cba")
	stationID, _ := primitive.ObjectIDFromHex("66ea8e010bf5034b411ea72f")

	excludeIDList := []primitive.ObjectID{expressID, yhtID, stationID}

	filter["category_ids.1"] = bson.M{
		"$nin": excludeIDList,
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range list {
			list[j].Price = 0
			list[j].OriginPrice = 0
		}
	}

	xhttp.RespSuccessList(ctx, list, count)

}
