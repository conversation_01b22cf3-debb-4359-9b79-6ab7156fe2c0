package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// MultiUser 多用户
type MultiUser struct {
	ID         primitive.ObjectID `bson:"_id"  json:"id"`
	ObjectID   primitive.ObjectID `bson:"object_id" json:"object_id"`
	UserID     primitive.ObjectID `bson:"user_id" json:"user_id"`
	ObjectType ObjectType         `json:"object_type" bson:"object_type"`
	Note       string             `bson:"note" json:"note"`
	CreatedAt  int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt  int64              `bson:"updated_at"  json:"updated_at"`
}
