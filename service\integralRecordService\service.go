package integralRecordService

import (
	"base/dao"
	"base/dao/integralRecordDao"
	"base/global"
	"base/model"
	"base/service/integralAccountService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	CreateMany(ctx context.Context, list []model.IntegralRecord) error
	DelMany(ctx context.Context, filter bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralRecord, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.IntegralRecord, error)
	Generate(ctx context.Context, content string) error
	Consume(ctx context.Context, content string) error
}

type integralRecordService struct {
	mdb               *mongo.Database
	rdb               *redis.Client
	integralRecordDao integralRecordDao.DaoInt
	integralAccountS  integralAccountService.ServiceInterface
	orderS            orderService.ServiceInterface
	orderRefundS      orderRefundService.ServiceInterface
	parentOrderS      parentOrderService.ServiceInterface
}

func NewIntegralRecordService() ServiceInterface {
	return integralRecordService{
		mdb:               global.MDB,
		rdb:               global.RDBDefault,
		integralRecordDao: dao.IntegralRecordDao,
		integralAccountS:  integralAccountService.NewIntegralAccountService(),
		orderS:            orderService.NewOrderService(),
		orderRefundS:      orderRefundService.NewOrderRefundService(),
		parentOrderS:      parentOrderService.NewParentOrderService(),
	}
}

func (s integralRecordService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralRecord, int64, error) {
	list, i, err := s.integralRecordDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s integralRecordService) List(ctx context.Context, filter bson.M) ([]model.IntegralRecord, error) {
	list, err := s.integralRecordDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s integralRecordService) CreateMany(ctx context.Context, list []model.IntegralRecord) error {
	err := s.integralRecordDao.CreateMany(ctx, list)
	if err != nil {
		return err
	}
	return nil
}

func (s integralRecordService) DelMany(ctx context.Context, filter bson.M) error {
	err := s.integralRecordDao.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s integralRecordService) Generate(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("integralAccountService Generate error:%v", err)
			return
		}
	}()

	// 下单 补差 评论 积分兑换恢复

	var data = struct {
		BuyerID    string           `json:"buyer_id"`
		ObjectID   string           `json:"object_id"`
		RecordType model.RecordType `json:"record_type"`
		BasicNum   int              `json:"basic_num"`
	}{}
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	id, err := util.ConvertToObjectWithCtx(ctx, data.ObjectID)
	if err != nil {
		return err
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, data.BuyerID)
	if err != nil {
		return err
	}

	integralAccount, err := s.integralAccountS.GetByBuyer(ctx, buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		createID, err := s.integralAccountS.Create(ctx, buyerID)
		if err != nil {
			return err
		}
		integralAccount.ID = createID
	}
	if err != nil {
		return err
	}

	num := data.BasicNum

	now := time.Now().UnixMilli()
	currentNum := integralAccount.Num + num

	var note string
	if data.RecordType == model.RecordTypeExchange {
		note = "积分兑换取消后恢复"
	}
	info := model.IntegralRecord{
		ID:                primitive.NewObjectID(),
		IntegralAccountID: integralAccount.ID,
		RecordType:        data.RecordType,
		ObjectID:          id,
		ChangeNum:         num,
		ChangeType:        1, // 加
		Note:              note,
		CreatedAt:         now,
		UpdatedAt:         now,
	}

	err = s.integralRecordDao.Create(ctx, info)
	if err != nil {
		zap.S().Errorf("更新代金券账户有效错误，err:%s", err.Error())
		return err
	}
	err = s.integralAccountS.UpdateNum(ctx, integralAccount.ID, currentNum)
	if err != nil {
		return err
	}

	return nil
}

// Exchange 兑换
func (s integralRecordService) Exchange(ctx context.Context, buyerID, id primitive.ObjectID, basicNum int) error {
	integralAccount, err := s.integralAccountS.GetByBuyer(ctx, buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		createID, err := s.integralAccountS.Create(ctx, buyerID)
		if err != nil {
			return err
		}
		integralAccount.ID = createID
	}
	if err != nil {
		return err
	}

	recordType := model.RecordTypeExchange

	num := basicNum

	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Errorf("integralRecordService Consume Exchange ErrNoDocuments:%s", err.Error())
		return nil
	}
	if err != nil {
		zap.S().Errorf("integralRecordService Consume err:%s", err.Error())
		return err
	}
	if num == 0 {
		return nil
	}

	if integralAccount.Num == 0 {
		return nil
	}
	now := time.Now().UnixMilli()
	currentNum := integralAccount.Num - num

	if currentNum < 0 {
		currentNum = 0
	}
	info := model.IntegralRecord{
		ID:                primitive.NewObjectID(),
		IntegralAccountID: integralAccount.ID,
		RecordType:        recordType,
		ObjectID:          id,
		ChangeNum:         num,
		ChangeType:        0, // 减
		Note:              "",
		Status:            model.IntegralRecordStatusValid,
		CreatedAt:         now,
		UpdatedAt:         now,
	}

	err = s.integralRecordDao.Create(ctx, info)
	if err != nil {
		zap.S().Errorf("新建积分记录，err:%s", err.Error())
		return err
	}
	err = s.integralAccountS.UpdateNum(ctx, integralAccount.ID, currentNum)
	if err != nil {
		return err
	}

	return nil
}

// Consume 消费
func (s integralRecordService) Consume(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("integralAccountService Consume error:%v", err)
			return
		}
	}()

	var data = struct {
		BuyerID    string           `json:"buyer_id"`
		ObjectID   string           `json:"object_id"` // 积分商品ID，订单支付单ID
		RecordType model.RecordType `json:"record_type"`
		BasicNum   int              `json:"basic_num"`
	}{}
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}
	marshal, _ := json.Marshal(data)

	zap.S().Infof("integralRecordService Consume：%s", string(marshal))

	id, err := util.ConvertToObjectWithCtx(ctx, data.ObjectID)
	if err != nil {
		return err
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, data.BuyerID)
	if err != nil {
		return err
	}

	//if data.RecordType == model.RecordTypeExchange {
	//	//
	//	err = s.Exchange(ctx, userID, id, data.BasicNum)
	//	if err != nil {
	//		return err
	//	}
	//	return nil
	//}

	// 关闭订单、取消订单、品控退款，售后退款--更新

	rate := 1

	var note string

	if data.RecordType == model.RecordTypeOrderClose {
		note = "关闭订单"
	}
	if data.RecordType == model.RecordTypeOrderCancel {
		note = "取消订单"
	}
	if data.RecordType == model.RecordTypeOrderQuality {
		note = "品控退款"
	}
	if data.RecordType == model.RecordTypeOrderAfterSale {
		note = "售后退款"
	}
	if data.RecordType == model.RecordTypeExchange {
		note = "积分兑换"
		s.Exchange(ctx, buyerID, id, data.BasicNum)
		return nil
	}

	integralAccount, err := s.integralAccountS.GetByBuyer(ctx, buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		createID, err := s.integralAccountS.Create(ctx, buyerID)
		if err != nil {
			return err
		}
		integralAccount.ID = createID
	}

	if err != nil {
		return err
	}

	_ = marshal

	integralRecord, err := s.integralRecordDao.Get(ctx, bson.M{"object_id": id})
	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Errorf("积分消费-查询：%s, 找不到积分记录，err:%s", id.Hex(), err.Error())
		return nil
	}
	if err != nil {
		return err
	}

	originList := integralRecord.ReduceRecordList

	num := data.BasicNum * rate

	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Errorf("integralRecordService Consume ErrNoDocuments:%s", err.Error())
		return nil
	}
	if err != nil {
		zap.S().Errorf("integralRecordService Consume err:%s", err.Error())
		return err
	}
	if num == 0 {
		return nil
	}

	if integralAccount.Num == 0 {
		return nil
	}
	now := time.Now().UnixMilli()
	currentNum := integralAccount.Num - num

	if currentNum < 0 {
		currentNum = 0
	}

	item := model.ReduceRecord{
		ID:        primitive.NewObjectID(),
		ChangeNum: num,
		Note:      note,
		CreatedAt: now,
	}

	originList = append(originList, item)

	recordNum := integralRecord.ChangeNum - num
	if recordNum < 0 {
		recordNum = 0
	}
	update := bson.M{
		"reduce_record_list": originList,
		"change_num":         recordNum,
		"updated_at":         now,
	}

	err = s.integralRecordDao.UpdateOne(ctx, bson.M{"_id": integralRecord.ID}, bson.M{"$set": update})
	if err != nil {
		zap.S().Errorf("更新积分记录，err:%s", err.Error())
		return err
	}

	err = s.integralAccountS.UpdateNum(ctx, integralAccount.ID, currentNum)
	if err != nil {
		return err
	}

	return nil
}
