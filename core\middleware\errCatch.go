package middleware

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

func CatchErr(ctx *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			zap.L().Error("error ", zap.Any("", err))
			if ctx.IsAborted() {
				return
			}
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, "系统忙")
			return
		}
	}()
	ctx.Next()
}

type Error struct {
	StatusCode int    `json:"-"`
	Code       int    `json:"code"`
	Msg        string `json:"captcha"`
}

func HandleNotFound(ctx *gin.Context) {
	ctx.JSON(http.StatusMethodNotAllowed, "请求方法错误")
	return
}
