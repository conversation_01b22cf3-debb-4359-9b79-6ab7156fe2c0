package fruitClassDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.FruitClass) error
	Get(ctx context.Context, filter bson.M) (model.FruitClass, error)
	Delete(ctx context.Context, filter bson.M) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.FruitClass, error)
}

type fruitClassDao struct {
	db *mongo.Collection
}

func (s fruitClassDao) Create(ctx context.Context, data model.FruitClass) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

func (s fruitClassDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}

func (s fruitClassDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s fruitClassDao) List(ctx context.Context, filter bson.M) ([]model.FruitClass, error) {
	var list []model.FruitClass
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s fruitClassDao) Get(ctx context.Context, filter bson.M) (model.FruitClass, error) {
	var data model.FruitClass
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func NewFruitClassDao(collect string) DaoInt {
	return fruitClassDao{
		db: global.MDB.Collection(collect),
	}
}
