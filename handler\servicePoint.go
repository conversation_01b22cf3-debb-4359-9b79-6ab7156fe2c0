package handler

import (
	"base/api/deliverFeeRule"
	"base/api/servicePoint"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 服务点
func servicePointRouter(r *gin.RouterGroup) {
	r = r.Group("/service/point")

	user := r.Group("/")

	user.POST("/list/second/by/web", servicePoint.ListSecondByWeb)
	user.POST("/list/second/by/index", servicePoint.ListSecondByIndex)

	user.POST("/list/location", servicePoint.ListByLocation)

	r.Use(middleware.CheckToken)

	user.POST("/second/create", servicePoint.CreateSecond)

	r.POST("/update/head/img", servicePoint.UpdateHeadImg)
	r.POST("/update/scope", servicePoint.UpdateScope)
	r.POST("/update/center/location", servicePoint.UpdateCenterLocation)

	//r.GET("/user/:user_id", servicePoint.GetApplyInfo)
	r.POST("/get/by/user", servicePoint.GetByUser)
	//r.POST("/get/by/user/only", servicePoint.GetByUser)
	r.POST("/get", servicePoint.Get)
	r.POST("/info/get", servicePoint.GetInfo)
	r.POST("/list/warehouse", servicePoint.ListByWarehouse)

	//	 配送诶
	r.POST("/delivery/fee/rule/upsert", deliverFeeRule.Upsert)
	r.POST("/delivery/fee/rule/get/by/point", deliverFeeRule.GetByServicePoint)
	r.POST("/delivery/fee/rule/list", deliverFeeRule.List)

	//	配送员
	r.POST("/delivery/man/create", servicePoint.CreateDeliveryMan)
	r.POST("/delivery/man/list", servicePoint.ListDeliveryMan)
	r.POST("/delivery/man/delete", servicePoint.DeleteDeliveryMan)
	r.POST("/delivery/man/update", servicePoint.UpdateDeliveryMan)
	r.POST("/delivery/man/get/by/user", servicePoint.GetDeliveryManByUser)

}
