package buyerDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// BuyerDao 采购商
type BuyerDao interface {
	Create(ctx context.Context, data model.Buyer) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Buyer, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Buyer, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.Buyer, error)
}

type buyerDao struct {
	db *mongo.Collection
}

// List 查询
func (s buyerDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Buyer, int64, error) {
	var list []model.Buyer
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s buyerDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// ListByCus 自定义查询
func (s buyerDao) ListByCus(ctx context.Context, filter bson.M) ([]model.Buyer, error) {
	var list []model.Buyer
	opts := options.Find()

	sort := bson.D{
		bson.E{Key: "updated_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s buyerDao) Update(ctx context.Context, filter, update bson.M) error {
	one, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = one
	return nil
}

func (s buyerDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	one, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = one
	return nil
}

func (s buyerDao) Create(ctx context.Context, data model.Buyer) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}
func (s buyerDao) Get(ctx context.Context, filter bson.M) (model.Buyer, error) {
	var u model.Buyer
	err := s.db.FindOne(ctx, filter).Decode(&u)
	if err != nil {
		return model.Buyer{}, err
	}
	return u, nil
}

func NewBuyerDao() BuyerDao {
	return buyerDao{
		db: global.MDB.Collection("buyer"),
	}
}
