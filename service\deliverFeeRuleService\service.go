package deliverFeeRuleService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/deliverFeeRuleDao"
	"base/global"
	"base/model"
	"base/types"
	"base/util"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, req types.DeliverFeeRuleUpsert) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.DeliverFeeRule, error)
	List(ctx context.Context) ([]model.DeliverFeeRule, error)
	GetByServicePoint(ctx context.Context, servicePointID primitive.ObjectID) (model.DeliverFeeRule, error)
	BackDueProductNum(ctx context.Context, Rules []model.DistanceWithNumRule, distance int) (int, error)
	CalcDistanceFee(ctx context.Context, address model.Address, deliverType model.DeliverType, instantDeliverType, productAmount, totalWeight int, point model.ServicePoint) (model.DeliverFeeRes, error)
	DeliverRecord(ctx context.Context, addressID, orderID primitive.ObjectID) error
	ExistDeliverRecord(ctx context.Context, addressID primitive.ObjectID) (bool, error)
	RemoveDeliverRecord(ctx context.Context, addressID, orderID primitive.ObjectID) error
}

type deliverFeeRuleService struct {
	rdb               *redis.Client
	deliverFeeRuleDao deliverFeeRuleDao.DaoInt
}

func NewDeliverFeeRuleService() ServiceInterface {
	return deliverFeeRuleService{
		rdb:               global.RDBDefault,
		deliverFeeRuleDao: dao.DeliverFeeRuleDao,
	}
}

// DeliverFeeRule 下单配送费收取规则
type DeliverFeeRule struct {
	ID                   primitive.ObjectID    `bson:"_id"  json:"id"`
	ServicePointID       primitive.ObjectID    `bson:"service_point_id" json:"service_point_id"`
	Rules                []DistanceWithNumRule `json:"rules" bson:"rules"`                                       // 规则
	BaseDeliverFee       int                   `json:"base_deliver_fee" bson:"base_deliver_fee"`                 // 基础配送费
	BaseDeliverDistance  int                   `json:"base_deliver_distance" bson:"base_deliver_distance"`       // 基础配送距离
	FeePerKm             int                   `json:"fee_per_km" bson:"fee_per_km"`                             // 里程价每km
	BaseDeliverWeight    int                   `json:"base_deliver_weight" bson:"base_deliver_weight"`           // 基础配送重量
	OverWeightFeePerUnit int                   `json:"over_weight_fee_per_unit" bson:"over_weight_fee_per_unit"` // 超重价格每单位
	OverWeightKGPerUnit  int                   `json:"over_weight_kg_per_unit" bson:"over_weight_kg_per_unit"`   // 超重单位
	CreatedAt            int64                 `bson:"created_at"  json:"created_at"`
	UpdatedAt            int64                 `bson:"updated_at"  json:"updated_at"`
}

// DistanceWithNumRule 距离对应的商品件数
type DistanceWithNumRule struct {
	Distance int `json:"distance" bson:"distance"` // 距离
	Num      int `json:"num" bson:"num"`           // 件数
}

func checkRules(list []model.DistanceWithNumRule) error {
	if len(list) < 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "配送规则列表为空")
	}
	n := len(list) - 1
	for i := 0; i < n; i++ {
		if list[i].Distance < 1000 {
			return xerr.NewErr(xerr.ErrParamError, nil, "配送规则【距离】最小为1km")
		}
		if list[i].Num < 1 {
			return xerr.NewErr(xerr.ErrParamError, nil, "配送规则【件数】最小为1件")
		}

		if list[i].Distance >= list[i+1].Distance {
			return xerr.NewErr(xerr.ErrParamError, nil, "配送规则【距离】需递增且不相等")
		}
		if list[i].Num >= list[i+1].Num {
			return xerr.NewErr(xerr.ErrParamError, nil, "配送规则【件数】需递增且不相等")
		}
	}

	return nil
}

func (s deliverFeeRuleService) Upsert(ctx context.Context, req types.DeliverFeeRuleUpsert) error {
	servicePointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "")
	if err != nil {
		return err
	}

	err = checkRules(req.Rules)
	if err != nil {
		return err
	}

	rule, err := s.GetByServicePoint(ctx, servicePointID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	now := time.Now().UnixMilli()

	rule.Rules = req.Rules
	rule.ServicePointID = servicePointID
	rule.BaseDeliverFee = req.BaseDeliverFee
	rule.BaseDeliverDistance = req.BaseDeliverDistance
	rule.FeePerKm = req.FeePerKm
	rule.SubsidyFeePerKm = req.SubsidyFeePerKm
	//rule.BaseDeliverWeight = req.BaseDeliverWeight
	//rule.OverWeightFeePerUnit = req.OverWeightFeePerUnit
	//rule.OverWeightKGPerUnit = req.OverWeightKGPerUnit
	rule.UpdatedAt = now

	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		rule.ID = primitive.NewObjectID()
		rule.CreatedAt = now
		err = s.deliverFeeRuleDao.Create(ctx, rule)
		if err != nil {
			return err
		}
		del(s.rdb, servicePointID)

		return nil
	}

	// 更新
	err = s.deliverFeeRuleDao.UpdateOne(bson.M{"_id": rule.ID, "service_point_id": rule.ServicePointID}, bson.M{
		"$set": rule,
	})
	if err != nil {
		return err
	}
	del(s.rdb, servicePointID)

	return nil
}

func (s deliverFeeRuleService) GetByID(ctx context.Context, id primitive.ObjectID) (model.DeliverFeeRule, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.deliverFeeRuleDao.Get(ctx, filter)
	if err != nil {
		return model.DeliverFeeRule{}, err
	}
	return fee, nil
}

func (s deliverFeeRuleService) GetByServicePoint(ctx context.Context, servicePointID primitive.ObjectID) (model.DeliverFeeRule, error) {
	m := get(s.rdb, servicePointID)
	if m.ID == primitive.NilObjectID {
		filter := bson.M{
			"service_point_id": servicePointID,
		}
		data, err := s.deliverFeeRuleDao.Get(ctx, filter)
		if err != nil {
			return model.DeliverFeeRule{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s deliverFeeRuleService) List(ctx context.Context) ([]model.DeliverFeeRule, error) {
	filter := bson.M{}
	list, err := s.deliverFeeRuleDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s deliverFeeRuleService) BackDueProductNum(ctx context.Context, list []model.DistanceWithNumRule, distance int) (int, error) {
	n := len(list) - 1
	for i := 0; i <= n; i++ {
		if distance < list[i].Distance {
			zap.S().Infof("item%v", list[i])
			return list[i].Num, nil
		}
		if i == n && list[n].Distance <= distance {
			return list[n].Num, nil
		}
	}
	zap.S().Errorf("BackDueProductNum error,距离%d,规则列表%v", distance, list)
	return 0, xerr.NewErr(xerr.ErrParamError, nil, "计算配送距离规则错误")
}

// RecordDeliverSubsidy 记录地址补贴
func (s deliverFeeRuleService) RecordDeliverSubsidy(ctx context.Context, addressID primitive.ObjectID) error {
	// 获取当前时间和当天23:59:59相差秒数
	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	diff := endTime.Sub(now).Seconds()

	// redis 使用set 记录地址id最新的补贴信息，key是subsidy-deliver-address:[地址id]
	key := fmt.Sprintf("subsidy-deliver-address:%s", addressID.Hex())
	if err := s.rdb.Set(ctx, key, now.UnixMilli(), time.Duration(diff)).Err(); err != nil {
		return err
	}

	return nil
}

// IsRecordSubsidy 判断地址id是否已经记录
func (s deliverFeeRuleService) IsRecordSubsidy(ctx context.Context, addressID primitive.ObjectID) (bool, error) {
	// redis 使用set 记录地址id最新的补贴信息，key是subsidy-deliver-address:[地址id]
	key := fmt.Sprintf("subsidy-deliver-address:%s", addressID.Hex())
	exists := s.rdb.Exists(ctx, key).Val()
	if exists > 0 {
		return true, nil
	}

	return false, nil
}

var recordKey = "exist-deliver-record:"

// DeliverRecord 记录配送费记录
func (s deliverFeeRuleService) DeliverRecord(ctx context.Context, addressID, orderID primitive.ObjectID) error {
	// 获取当前时间和当天23:59:59相差秒数
	now := time.Now()
	morningBegin := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	morningEnd := time.Date(now.Year(), now.Month(), now.Day(), 6, 59, 59, 999, now.Location())
	var diff float64
	var add float64
	if now.After(morningBegin) && now.Before(morningEnd) {
		// 凌晨下单
		diff = morningEnd.Sub(now).Seconds()
	} else {
		endTime := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999, now.Location())
		diff = endTime.Sub(now).Seconds()
		add = (time.Hour * time.Duration(7)).Seconds()
	}

	second := int64(diff + add)

	key := fmt.Sprintf("%s%s", recordKey, addressID.Hex())

	err := s.rdb.ZAdd(ctx, key, &redis.Z{
		Score:  float64(now.UnixMilli()),
		Member: orderID.Hex(),
	}).Err()
	if err != nil {
		zap.S().Errorf("DeliverRecord err: %v", err.Error())
	}
	s.rdb.Expire(ctx, key, time.Second*time.Duration(second))

	return nil
}

// ExistDeliverRecord 存在配送费记录-补贴和未补贴均记录
func (s deliverFeeRuleService) ExistDeliverRecord(ctx context.Context, addressID primitive.ObjectID) (bool, error) {
	// redis 使用set 记录地址id最新的补贴信息，key是subsidy-deliver-address:[地址id]
	key := fmt.Sprintf("%s%s", recordKey, addressID.Hex())
	exists := s.rdb.Exists(ctx, key).Val()
	if exists > 0 {
		return true, nil
	}

	return false, nil
}

// RemoveDeliverRecord 移除配送费记录
func (s deliverFeeRuleService) RemoveDeliverRecord(ctx context.Context, addressID, orderID primitive.ObjectID) error {
	key := fmt.Sprintf("%s%s", recordKey, addressID.Hex())
	err := s.rdb.ZRem(ctx, key, orderID.Hex()).Err()
	if err != nil {
		zap.S().Errorf("RemoveDeliverRecord err: %v", err.Error())
	}
	return nil
}

// DeliverySubsidyRule 补贴规则
var DeliverySubsidyRule = []model.DeliverySubsidyRule{
	{
		Amount:  30000,
		Percent: 50,
	}, {
		Amount:  90000,
		Percent: 100,
	}}

func backSubsidyPercent(amount int) int {
	var percent int
	for _, per := range DeliverySubsidyRule {
		if amount < per.Amount {
			break
		}
		percent = per.Percent
	}

	return percent
}

// CalcDistanceFee 计算距离费用
func (s deliverFeeRuleService) CalcDistanceFee(ctx context.Context, address model.Address, deliverType model.DeliverType, instantDeliverType, productAmount, totalWeight int, point model.ServicePoint) (model.DeliverFeeRes, error) {
	_ = ctx

	data := model.DeliverFeeRes{
		DeliverType: deliverType,
	}

	distance := util.LatitudeLongitudeDistance(point.Location.Longitude, point.Location.Latitude, address.Location.Longitude, address.Location.Latitude)
	distanceCalc := decimal.NewFromInt(int64(distance)).Div(decimal.NewFromInt(1000)).Round(1).InexactFloat64()

	if deliverType == model.DeliverTypeDoor {
		if distance > 18000 {
			data.IsOverScope = true
			return data, nil
		}

		//	 计算费用
		var subsidy int

		subsidyPercent := backSubsidyPercent(productAmount)

		deliveryUnitPrice := 300
		perKmDe := decimal.NewFromInt(int64(deliveryUnitPrice))
		distanceCalcDe := decimal.NewFromFloat(distanceCalc)
		dAmountDe := perKmDe.Mul(distanceCalcDe)

		total := int(dAmountDe.Round(0).IntPart())

		if subsidyPercent > 0 {
			subsidyPercentDe := decimal.NewFromFloat(float64(subsidyPercent)).Div(decimal.NewFromInt(100))
			subsidyAmountDe := dAmountDe.Mul(subsidyPercentDe)
			subsidy = int(subsidyAmountDe.Round(0).IntPart())
		}

		//	 优先费用
		//diff := productAmount - point.DeliverySubsidyAmount
		//
		//if diff >= 0 {
		//	subsidy = total
		//} else {
		//	data.NeedAmountForSubsidy = diff * -1
		//}

		data.TotalDeliverFee = total
		data.SubsidyDeliverFee = subsidy
		data.FinalDeliverFee = total - subsidy

	}

	if deliverType == model.DeliverTypeSelfPickUp || deliverType == model.DeliverTypeLogistics {
		// 自提  物流

	}

	if deliverType == model.DeliverTypeInstantDeliver {
		// 即时配送
		perKm := 2.5
		instantDeliverName := "跑腿"

		if instantDeliverType == 2 {
			// 货拉拉
			perKm = 4
			instantDeliverName = "货拉拉"
		}

		perKmDe := decimal.NewFromFloat(perKm)

		distanceCalcDe := decimal.NewFromFloat(distanceCalc)
		intPart := perKmDe.Mul(distanceCalcDe).Round(0).IntPart()

		amount := int(intPart) * 100
		if amount < 1000 {
			amount = 1000
		}

		data.TotalDeliverFee = amount
		data.FinalDeliverFee = amount
		data.CalcDistance = distance

		data.InstantDeliverType = instantDeliverType
		data.InstantDeliverName = instantDeliverName
	}

	return data, nil
}

func (s deliverFeeRuleService) CheckInArea(lng float64, lat float64, points []model.PointLocation) (bool, error) {
	var pList []model.PointLocation
	for _, point := range points {
		pList = append(pList, model.PointLocation{
			Latitude:  point.Latitude,
			Longitude: point.Longitude,
		})
	}

	//f := util.NewGISArea(pList).PointInArea(model.Point{
	//	Lat: lat,
	//	Lng: lng,
	//})

	f := isPointInsidePolygon(model.PointLocation{
		Latitude:  lat,
		Longitude: lng,
	}, pList)

	return f, nil
}

func isPointInsidePolygon(point model.PointLocation, polygon []model.PointLocation) bool {
	inside := false
	for i, j := 0, len(polygon)-1; i < len(polygon); i++ {
		if (polygon[i].Latitude > point.Latitude) != (polygon[j].Latitude > point.Latitude) &&
			point.Longitude < (polygon[j].Longitude-polygon[i].Longitude)*(point.Latitude-polygon[i].Latitude)/(polygon[j].Latitude-polygon[i].Latitude)+polygon[i].Longitude {
			inside = !inside
		}
		j = i
	}
	return inside
}
