package productCommissionDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 产品佣金
type DaoInt interface {
	Upsert(ctx context.Context, data model.ProductCommission) error
	Find(ctx context.Context, filter bson.M) ([]model.ProductCommission, error)
	Get(ctx context.Context, filter bson.M) (model.ProductCommission, error)
}

type productCommissionDao struct {
	db *mongo.Collection
}

func (s productCommissionDao) Upsert(ctx context.Context, data model.ProductCommission) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}

	return nil
}

func (s productCommissionDao) Get(ctx context.Context, filter bson.M) (model.ProductCommission, error) {
	var data model.ProductCommission
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductCommission{}, err
	}
	return data, nil
}

func (s productCommissionDao) Find(ctx context.Context, filter bson.M) ([]model.ProductCommission, error) {
	var list []model.ProductCommission

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewProductCommissionDao(collect string) DaoInt {
	return productCommissionDao{
		db: global.MDB.Collection(collect),
	}
}
