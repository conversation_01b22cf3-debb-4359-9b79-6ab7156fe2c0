package authenticationService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/authenticationDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, mobile string, req types.AuthenticationReq, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error
	CreateByStation(ctx context.Context, mobile string, userID, objectID primitive.ObjectID) error
	CheckParam(ctx context.Context, req types.AuthenticationReq) error
	// CreateIndividual 个人会员
	CreateIndividual(ctx context.Context, mobile string, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error
	Update(ctx context.Context, req types.AuthenticationUpdateReq) error
	UpdateBuyerCreditCode(ctx context.Context, id primitive.ObjectID, creditCode string) error
	UpdateCus(ctx context.Context, filter, update bson.M) error
	UpdateReApply(ctx context.Context, req types.AuthenticationReq, objectID primitive.ObjectID, objectType model.ObjectType) error
	SetCompany(ctx context.Context, id primitive.ObjectID) error
	BindCompanyPersonalBank(ctx context.Context, id primitive.ObjectID, cardNo, bankReservedMobile string) error
	UpdateHasSubmitPayOcr(ctx context.Context, id primitive.ObjectID, picType pays.PicType) error
	UpdateByBizUserID(ctx context.Context, bizUserID string) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	// GetMember 会员信息
	GetMember(ctx context.Context, id primitive.ObjectID) (interface{}, error)
	GetMemberIndividual(ctx context.Context, id primitive.ObjectID) (pays.IndividualMemberInfoGet, error)
	GetMemberIndividualByPayBizUser(ctx context.Context, payBizUserId string) (pays.IndividualMemberInfoGet, error)
	GetByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error)
	GetByUserAndObject(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error)
	GetByBuyer(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	GetBuyerByUser(ctx context.Context, userID primitive.ObjectID) (model.Authentication, error)
	GetByUserAndEnv(ctx context.Context, userID primitive.ObjectID, env model.ObjectType) (model.Authentication, error)
	GetByBizUserID(ctx context.Context, bizUserID string) (model.Authentication, error)
	GetBySupplier(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	GetByServicePoint(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	GetByStation(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	BindBankByStation(ctx context.Context, id primitive.ObjectID, mobile, cardNo string) error
	SetRealNameByStation(ctx context.Context, id primitive.ObjectID, name, identityNo string) error
	GetByWarehouse(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	CheckBySupplier(ctx context.Context, id primitive.ObjectID) (bool, error)
	CheckByServicePoint(ctx context.Context, id primitive.ObjectID) (bool, error)
	CheckByWarehouse(ctx context.Context, id primitive.ObjectID) (bool, error)
	ListAll(ctx context.Context) ([]model.Authentication, error)
	ListByStation(ctx context.Context, ids []primitive.ObjectID) ([]model.Authentication, error)

	BindMobile(ctx context.Context, id primitive.ObjectID, mobile, captcha string) error
	UnbindMobile(ctx context.Context, id primitive.ObjectID, mobile, captcha string) error
	UpdateBindMobileStatus(ctx context.Context, id primitive.ObjectID, mobile string, verify bool) error
	//	NotifySetCompany 设置企业信息回调
	NotifySetCompany(ctx context.Context, res allinpay.VerifyResult)
	// NotifyOcrComparisonResult 影印件核对结果异步通知
	NotifyOcrComparisonResult(ctx context.Context, res allinpay.OcrComparisonResult)
	// NotifySignAcctProtocol 提现协议
	//NotifySignAcctProtocol(ctx context.Context, res allinpay.SignAcctProtocolResult)

	ApplyBindAcctForWechat(ctx context.Context, bizUserId, openID string) error                       // 绑定小程序支付标识
	QueryApplyBindAcctForWechat(ctx context.Context, bizUserId string) (pays.ApplyBindAcctRes, error) //  绑定小程序支付标识--查询

	UnbindBank(ctx context.Context, objectID primitive.ObjectID) error
	UnbindIndividualBank(ctx context.Context, id primitive.ObjectID) error
	ApplyBindBank(ctx context.Context, id primitive.ObjectID, cardNo, phone string) error
	ListBankCard(ctx context.Context, bizUserID string) ([]pays.BindBankInfo, error)
}

type authenticationService struct {
	rdb               *redis.Client
	authenticationDao authenticationDao.DaoInt

	// 支付 会员
	payMember payModule.MemberService
}

func NewAuthenticationService() ServiceInterface {
	return authenticationService{
		rdb:               global.RDBDefault,
		authenticationDao: dao.AuthenticationDao,
		payMember:         payModule.NewMember(),
	}
}

// ApplyBindAcctForWechat 绑定小程序支付标识
func (s authenticationService) ApplyBindAcctForWechat(ctx context.Context, bizUserId, openID string) error {
	res, err := s.payMember.ApplyBindAcctS(pays.ApplyBindAcctReq{
		BizUserId:     bizUserId,
		OperationType: "set",
		Acct:          openID,
		AcctType:      "weChatMiniProgram",
	})
	if err == xerr.XerrPayAccHasBind {
		//	 更新状态即可
		update := bson.M{
			"$set": bson.M{
				"is_pay_mini_acc": true,
			},
			"$addToSet": bson.M{
				"pay_mini_acc_list": openID,
			},
		}
		err = s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": bizUserId}, update)
		if err != nil {
			zap.S().Errorf("更新绑定微信支付标识错误，%v", err)
			return err
		}
		return nil
	}
	if err != nil {
		return err
	}
	if res.Result == "OK" {
		//	 成功
		update := bson.M{
			"$set": bson.M{
				"is_pay_mini_acc": true,
			},
			"$addToSet": bson.M{
				"pay_mini_acc_list": openID,
			},
		}
		err = s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": bizUserId}, update)
		if err != nil {
			zap.S().Errorf("更新绑定微信支付标识错误，%v", err)
			return err
		}

		return nil
	}
	zap.S().Errorf("绑定微信支付标识错误，%v", err)

	return nil
}

// QueryApplyBindAcctForWechat 绑定小程序支付标识--查询
func (s authenticationService) QueryApplyBindAcctForWechat(ctx context.Context, bizUserId string) (pays.ApplyBindAcctRes, error) {
	// 创建会员
	res, err := s.payMember.ApplyBindAcctS(pays.ApplyBindAcctReq{
		BizUserId:     bizUserId,
		OperationType: "query",
	})
	if err != nil {
		return pays.ApplyBindAcctRes{}, err
	}
	return res, nil
}

func (s authenticationService) CreateIndividual(ctx context.Context, mobile string, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error {
	data := model.Authentication{
		ID:             primitive.NewObjectID(),
		UserID:         userID,
		ObjectType:     objectType,
		ObjectID:       objectID,
		Mobile:         mobile,
		IsMobileVerify: false,
		MemberType:     pays.MemberTypeIndividual, // 个人
		CreatedAt:      time.Now().UnixMilli(),
	}

	// 创建会员
	res, err := s.payMember.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType,
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"buyer_id": objectID.Hex(),
		}})
	if err != nil {
		return err
	}

	data.PayUserID = res.UserID
	data.PayBizUserId = res.BizUserId

	err = s.authenticationDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) NotifySetCompany(ctx context.Context, res allinpay.VerifyResult) {
	update := bson.M{
		"result":             res.Result,
		"account_set_result": res.AccountSetResult,
		"check_time":         res.CheckTime,
		"fail_reason":        res.FailReason,
		"remark":             res.Remark,
	}

	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
	if err != nil {
		zap.S().Error("设置企业信息异步通知更新失败：", err)
	}

}

//
//func (s authenticationService) NotifySignAcctProtocol(ctx context.Context, res allinpay.SignAcctProtocol) {
//	update := bson.M{
//		"sign_result":      res.Result,
//		"acct_protocol_no": res.AcctProtocolNo,
//	}
//
//	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
//	if err != nil {
//		zap.S().Error("更新提现协议编号失败：", err)
//	}
//
//}

func (s authenticationService) NotifyOcrComparisonResult(ctx context.Context, res allinpay.OcrComparisonResult) {
	update := bson.M{
		"result_info":   res.ResultInfo,
		"req_serial_no": res.ReqSerialNo,
	}

	if res.OcrRegnumComparisonResult != 0 {
		update["ocr_regnum_comparison_result"] = res.OcrRegnumComparisonResult
	}

	if res.OcrIdcardComparisonResult != 0 {
		update["ocr_idcard_comparison_result"] = res.OcrIdcardComparisonResult
	}

	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
	if err != nil {
		zap.S().Error("设置企业信息异步通知更新失败：", err)
	}

}

func (s authenticationService) GetMember(ctx context.Context, id primitive.ObjectID) (interface{}, error) {
	authentication, err := s.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if authentication.MemberType == pays.MemberTypeCompany {
		companyS, err := s.payMember.GetMemberInfoForCompanyS(pays.GetMemberInfoReq{
			BizUserId: authentication.PayBizUserId,
		})
		if err != nil {
			return nil, err
		}
		return companyS, nil
	}
	if authentication.MemberType == pays.MemberTypeIndividual {
		res, err := s.payMember.GetMemberInfoForIndividualS(pays.GetMemberInfoReq{
			BizUserId: authentication.PayBizUserId,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	}

	return nil, xerr.NewErr(xerr.ErrParamError, nil, "authentication member type错误")
}

func (s authenticationService) GetMemberIndividual(ctx context.Context, id primitive.ObjectID) (pays.IndividualMemberInfoGet, error) {
	authentication, err := s.GetByID(ctx, id)
	if err != nil {
		return pays.IndividualMemberInfoGet{}, err
	}

	res, err := s.payMember.GetMemberInfoForIndividualS(pays.GetMemberInfoReq{
		BizUserId: authentication.PayBizUserId,
	})
	if err != nil {
		return pays.IndividualMemberInfoGet{}, err
	}
	return res, nil
}
func (s authenticationService) GetMemberIndividualByPayBizUser(ctx context.Context, payBizUserId string) (pays.IndividualMemberInfoGet, error) {
	res, err := s.payMember.GetMemberInfoForIndividualS(pays.GetMemberInfoReq{
		BizUserId: payBizUserId,
	})
	if err != nil {
		return pays.IndividualMemberInfoGet{}, err
	}
	return res, nil
}

func (s authenticationService) GetByID(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	get, err := s.authenticationDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.Authentication{}, err
	}
	return get, nil
}

func (s authenticationService) BindMobile(ctx context.Context, id primitive.ObjectID, mobile, captcha string) error {
	data, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	res, err := s.payMember.BindPhoneS(pays.BindPhoneReq{
		BizUserId:        data.PayBizUserId,
		Phone:            mobile,
		VerificationCode: captcha,
	})
	if err != nil {
		return err
	}
	_ = res

	err = s.UpdateBindMobileStatus(ctx, data.ID, mobile, true)
	if err != nil {
		return err
	}

	return nil
}

func (s authenticationService) UnbindMobile(ctx context.Context, id primitive.ObjectID, mobile, captcha string) error {
	data, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	res, err := s.payMember.UnbindPhoneS(pays.UnbindPhoneReq{
		BizUserId:        data.PayBizUserId,
		Phone:            mobile,
		VerificationCode: captcha,
	})
	if err != nil {
		return err
	}
	if res.Result == "OK" {
		err = s.UpdateUnbindMobileStatus(ctx, data.ID)
		if err != nil {
			return err
		}
		return nil
	}

	return xerr.NewErr(xerr.ErrParamError, nil, "解绑手机号失败")
}

func (s authenticationService) UpdateUnbindMobileStatus(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}

	update := bson.M{
		"is_mobile_verify": false,
		"mobile":           "",
	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) UpdateBindMobileStatus(ctx context.Context, id primitive.ObjectID, mobile string, verify bool) error {
	filter := bson.M{"_id": id}

	update := bson.M{
		"is_mobile_verify": verify,
		"mobile":           mobile,
	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) Update(ctx context.Context, req types.AuthenticationUpdateReq) error {
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		return err
	}
	filter := bson.M{"_id": id}

	byID, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	// 公司
	company := byID.Company
	company.CompanyName = req.Company.CompanyName
	company.CreditCode = req.Company.CreditCode
	company.BusinessLicense = req.Company.BusinessLicense
	company.BusinessLicenseImg = req.Company.BusinessLicenseImg
	company.BusinessLicenseValidTo = req.Company.BusinessLicenseValidTo

	// 法人
	legal := byID.Company.Legal
	legal.LegalName = req.Company.Legal.LegalName
	legal.LegalIds = req.Company.Legal.LegalIds
	legal.IdentityType = 1
	legal.IDCard = req.Company.Legal.IDCard
	legal.IdCardFrontImg = req.Company.Legal.IdCardFrontImg
	legal.IdCardBackImg = req.Company.Legal.IdCardBackImg

	company.Legal = legal
	byID.Company = company

	// 银行账户
	bank := byID.BankAccount
	bank.BankReservedMobile = req.BankAccount.BankReservedMobile
	bank.AccountType = model.AccountType(req.BankAccount.AccountType)
	bank.CardNumber = req.BankAccount.CardNumber
	bank.ParentBankName = req.BankAccount.ParentBankName
	bank.BankName = req.BankAccount.BankName
	bank.BankcardImg = req.BankAccount.BankcardImg
	bank.UnionBank = req.BankAccount.UnionBank
	bank.Bankcard = req.BankAccount.Bankcard

	byID.BankAccount = bank

	byID.UpdatedAt = time.Now().UnixMilli()

	err = s.authenticationDao.Update(ctx, filter, bson.M{"$set": byID})
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) UpdateBuyerCreditCode(ctx context.Context, id primitive.ObjectID, creditCode string) error {
	filter := bson.M{"_id": id}
	now := time.Now().UnixMilli()

	update := bson.M{
		"company.credit_code": creditCode,
		"updated_at":          now,
	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) UpdateByBizUserID(ctx context.Context, bizUserID string) error {

	return nil
}

func (s authenticationService) SetCompany(ctx context.Context, id primitive.ObjectID) error {
	byID, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	legal := byID.Company.Legal
	bank := byID.BankAccount

	cInfo := pays.CompanyBasicInfoSet{
		CompanyName:  byID.Company.CompanyName,
		AuthType:     2, // 一证
		UniCredit:    byID.Company.CreditCode,
		LegalName:    legal.LegalName,
		LegalIds:     legal.LegalIds,
		IdentityType: 1, // 身份证
		LegalPhone:   legal.LegalPhone,
		AcctType:     int(byID.BankAccount.AccountType),
		AccountNo:    byID.BankAccount.CardNumber,
	}
	var comproperty pays.CompropertyType
	if byID.Company.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		comproperty = pays.CompropertyTypeIndividualBusiness

		// 对私
		cInfo.Phone = bank.BankReservedMobile
	} else {
		// 企业
		comproperty = pays.CompropertyTypeCompany

		cInfo.ParentBankName = bank.ParentBankName
		cInfo.BankName = bank.BankName
		cInfo.UnionBank = bank.UnionBank
	}

	res, err := s.payMember.SetCompanyInfoS(pays.SetCompanyInfoReq{
		BizUserId:        byID.PayBizUserId,
		BackUrl:          global.BackHost + global.BackUrl,
		Comproperty:      comproperty,
		CompanyBasicInfo: cInfo,
	})
	if err != nil {
		return err
	}
	_ = res

	if res.Result == 2 {
		//	审核成功
		filter := bson.M{"_id": id}
		update := bson.M{}
		update["has_set_company_info"] = true

		if res.FailReason != "" {
			update["fail_reason"] = res.FailReason
		}
		if res.AccountSetResult != 0 {
			//对私银行账户认证结果
			//	失败
			update["bank_account.account_set_result"] = res.AccountSetResult
		}

		err = s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
		if err != nil {
			return err
		}

	}

	return nil
}

func (s authenticationService) BindCompanyPersonalBank(ctx context.Context, id primitive.ObjectID, cardNo, bankReservedMobile string) error {
	update := bson.M{
		"personal_bank_account.account_type":         0,
		"personal_bank_account.card_number":          cardNo,
		"personal_bank_account.bank_reserved_mobile": bankReservedMobile,
	}
	err := s.authenticationDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	data, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	legal := data.Company.Legal

	res, err := s.payMember.ApplyBindBankCardS(pays.ApplyBindBankCardReq{
		BizUserId:    data.PayBizUserId,
		CardNo:       data.PersonalBankAccount.CardNumber,
		Phone:        data.PersonalBankAccount.BankReservedMobile,
		Name:         legal.LegalName,
		CardCheck:    8,
		IdentityType: 1,
		IdentityNo:   legal.LegalIds,
	})
	if err != nil {
		return err
	}
	_ = res

	marshal, _ := json.Marshal(res)
	zap.S().Infof("绑卡结果：%s", string(marshal))

	return nil
}
func (s authenticationService) UpdateHasSubmitPayOcr(ctx context.Context, id primitive.ObjectID, picType pays.PicType) error {
	filter := bson.M{"_id": id}
	update := bson.M{}

	switch picType {
	case pays.PicTypeBusinessLicense:
		update["company.has_ocr_regnum_comparison"] = true
	case pays.PicTypeIDCardFront:
		update["company.legal.has_ocr_idcard_comparison_front"] = true
	case pays.PicTypeIDCardBack:
		update["company.legal.has_ocr_idcard_comparison_back"] = true
	default:
		return xerr.NewErr(xerr.ErrParamError, nil, "PicType参数错误")

	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) UpdateCus(ctx context.Context, filter, update bson.M) error {
	err := s.authenticationDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) GetByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error) {
	filter := bson.M{
		"object_type": objectType,
		"object_id":   objectID,
	}
	authentication, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return authentication, nil
}

func (s authenticationService) GetByUserAndObject(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error) {
	filter := bson.M{
		"object_type": objectType,
		"user_id":     userID,
	}
	authentication, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return authentication, nil
}

func (s authenticationService) Create(ctx context.Context, mobile string, req types.AuthenticationReq, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error {
	now := time.Now().UnixMilli()
	company := model.Company{
		BusinessLicenseImg: req.BusinessLicenseImg,
		CompanyType:        req.CompanyType,
		Legal: model.Legal{
			LegalPhone:     req.LegalPhone,
			IdCardFrontImg: req.LegalIdCardFrontImg,
			IdCardBackImg:  req.LegalIdCardBackImg,
		},
	}
	aType := model.AccountTypePublic
	if req.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		aType = model.AccountTypeSelf
		if len(req.BankReservedMobile) != 11 {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户需要填写银行账户预留手机号")
		}
	}
	if req.CompanyType == model.CompanyTypeCo {
		//	企业
		if req.BankName == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要填写开户支行名称和支付行号")
		}
		if len(req.UnionBank) != 12 {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要12位支付行号")
		}
	}

	bankAccount := model.BankAccount{
		AccountType: aType,
		CardNumber:  req.CardNumber,
		//对公
		//ParentBankName: "",
		BankName:  req.BankName,
		UnionBank: req.UnionBank,
		//对私
		BankReservedMobile: req.BankReservedMobile,
		//Bankcard:   ,
		BankcardImg: req.BankcardImg,
	}

	// 创建会员
	createMember, err := s.payMember.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType,
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"object_id":   objectID.Hex(),
			"object_type": objectType,
		}})
	if err != nil {
		return err
	}

	data := model.Authentication{
		ID:           primitive.NewObjectID(),
		UserID:       userID,
		ObjectType:   objectType,
		ObjectID:     objectID,
		Mobile:       mobile,
		MemberType:   mType,
		PayUserID:    createMember.UserID,
		PayBizUserId: createMember.BizUserId,
		Company:      company,
		BankAccount:  bankAccount,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.authenticationDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) CreateByStation(ctx context.Context, mobile string, userID, objectID primitive.ObjectID) error {
	byStation, err := s.GetByStation(ctx, objectID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if byStation.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在会员支付信息")
	}

	now := time.Now().UnixMilli()
	// 创建会员
	mType := pays.MemberTypeIndividual
	createMember, err := s.payMember.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType, // 个人
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"object_id":   objectID.Hex(),
			"object_type": model.ObjectTypeStation,
		}})
	if err != nil {
		return err
	}

	data := model.Authentication{
		ID:           primitive.NewObjectID(),
		UserID:       userID,
		ObjectType:   model.ObjectTypeStation,
		ObjectID:     objectID,
		Mobile:       mobile,
		MemberType:   mType,
		PayUserID:    createMember.UserID,
		PayBizUserId: createMember.BizUserId,
		//Company:      company,
		//BankAccount:  bankAccount,
		CreatedAt: now,
		UpdatedAt: now,
	}

	err = s.authenticationDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) CheckParam(ctx context.Context, req types.AuthenticationReq) error {
	if req.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		if len(req.BankReservedMobile) != 11 {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户需要填写银行账户预留手机号")
		}
	}
	if req.CompanyType == model.CompanyTypeCo {
		//	企业
		if req.BankName == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要填写开户支行名称和支付行号")
		}
		if len(req.UnionBank) != 12 {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要12位支付行号")
		}
	}
	return nil
}

func (s authenticationService) UpdateReApply(ctx context.Context, req types.AuthenticationReq, objectID primitive.ObjectID, objectType model.ObjectType) error {
	authentication, err := s.GetByObject(ctx, objectID, objectType)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()

	authentication.UpdatedAt = now

	authentication.Company.BusinessLicenseImg = req.BusinessLicenseImg
	authentication.Company.CompanyType = req.CompanyType
	authentication.Company.Legal.LegalPhone = req.LegalPhone
	authentication.Company.Legal.IdCardFrontImg = req.LegalIdCardFrontImg
	authentication.Company.Legal.IdCardBackImg = req.LegalIdCardBackImg

	aType := model.AccountTypePublic
	if req.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		aType = model.AccountTypeSelf
		if len(req.BankReservedMobile) != 11 {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户需要填写银行账户预留手机号")
		}
	}
	if req.CompanyType == model.CompanyTypeCo {
		//	企业
		if req.BankName == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "企业需要填写开户支行名称和支付行号")
		}
		if len(req.UnionBank) != 12 {
			return xerr.NewErr(xerr.ErrParamError, nil, "企业需要12位支付行号")
		}
	}

	authentication.BankAccount.AccountType = aType
	authentication.BankAccount.CardNumber = req.CardNumber
	authentication.BankAccount.BankName = req.BankName
	authentication.BankAccount.UnionBank = req.UnionBank
	authentication.BankAccount.BankReservedMobile = req.BankReservedMobile
	authentication.BankAccount.BankcardImg = req.BankcardImg

	err = s.authenticationDao.Update(ctx, bson.M{"_id": authentication.ID}, bson.M{"$set": authentication})
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationService) CheckBySupplier(ctx context.Context, id primitive.ObjectID) (bool, error) {
	i, err := s.GetBySupplier(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s authenticationService) CheckByWarehouse(ctx context.Context, id primitive.ObjectID) (bool, error) {
	i, err := s.GetByWarehouse(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s authenticationService) CheckByServicePoint(ctx context.Context, id primitive.ObjectID) (bool, error) {
	i, err := s.GetByServicePoint(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s authenticationService) GetBySupplier(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeSupplier)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s authenticationService) GetByBuyer(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeBuyer)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s authenticationService) GetBuyerByUser(ctx context.Context, userID primitive.ObjectID) (model.Authentication, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeBuyer,
		"user_id":     userID,
	}
	authentication, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return authentication, nil
}

func (s authenticationService) GetByUserAndEnv(ctx context.Context, userID primitive.ObjectID, env model.ObjectType) (model.Authentication, error) {
	filter := bson.M{
		"user_id":     userID,
		"object_type": env,
	}
	object, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s authenticationService) GetByBizUserID(ctx context.Context, bizUserID string) (model.Authentication, error) {
	filter := bson.M{
		"pay_biz_user_id": bizUserID,
	}
	object, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s authenticationService) GetByServicePoint(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeServicePoint)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s authenticationService) GetByStation(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeStation)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

// SetRealNameByStation 实名认证
func (s authenticationService) SetRealNameByStation(ctx context.Context, id primitive.ObjectID, name, identityNo string) error {
	authentication, err := s.GetByStation(ctx, id)
	if err != nil {
		return err
	}

	req := pays.SetRealNameReq{
		BizUserId:  authentication.PayBizUserId,
		Name:       name,
		IdentityNo: identityNo,
	}
	res, err := s.payMember.SetRealNameS(req)
	if err != nil {
		marshal, _ := json.Marshal(req)
		zap.S().Errorf("站点：%s,实名错误：%s,实名信息：%s，", id.Hex(), err.Error(), string(marshal))
		return err
	}
	_ = res

	update := bson.M{
		"real_name":   name,
		"identity_no": identityNo,
	}
	err = s.UpdateCus(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s authenticationService) BindBankByStation(ctx context.Context, id primitive.ObjectID, mobile, cardNo string) error {
	auth, err := s.GetByStation(ctx, id)
	if err != nil {
		return err
	}

	if auth.IndividualBankcardResult == "ok" {
		return xerr.NewErr(xerr.ErrParamError, nil, "已绑定，请勿重复绑定")
	}

	req := pays.ApplyBindBankCardReq{
		BizUserId:    auth.PayBizUserId,
		CardNo:       cardNo,
		Phone:        mobile,
		CardCheck:    8,
		Name:         auth.RealName,
		IdentityType: 1,
		IdentityNo:   auth.IdentityNo,
	}
	res, err := s.payMember.ApplyBindBankCardS(req)
	if err != nil {
		marshal, _ := json.Marshal(req)
		zap.S().Errorf("BindBank 站点：%s,绑定银行卡请求错误：%s,请求信息：%s，", id.Hex(), err.Error(), string(marshal))
		return err
	}
	_ = res

	update := bson.M{
		"individual_bankcard_no":          cardNo,
		"individual_bank_reserved_mobile": mobile,
		"individual_bankcard_result":      "ok",
	}
	err = s.UpdateCus(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s authenticationService) GetByWarehouse(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeWarehouse)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s authenticationService) ListAll(ctx context.Context) ([]model.Authentication, error) {
	list, _, err := s.authenticationDao.List(ctx, bson.M{}, 1, 999)

	return list, err
}

func (s authenticationService) ListByStation(ctx context.Context, ids []primitive.ObjectID) ([]model.Authentication, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeStation,
		"object_id": bson.M{
			"$in": ids,
		},
	}

	list, err := s.authenticationDao.ListAll(ctx, filter)
	if err != nil {
		return nil, err
	}

	return list, err
}

func (s authenticationService) UnbindBank(ctx context.Context, id primitive.ObjectID) error {
	authentication, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	res, err := s.payMember.UnbindBankCard(pays.UnbindBankCardReq{
		BizUserId: authentication.PayBizUserId,
		CardNo:    authentication.BankAccount.CardNumber,
	})
	if err != nil {
		return err
	}

	// 移除银行卡信息
	filter := bson.M{
		"_id":             authentication.ID,
		"pay_biz_user_id": res.BizUserId,
	}

	bank := authentication.BankAccount
	if bank.AccountType == model.AccountTypeSelf {
		// 对私
		bank.CardNumber = ""
		bank.BankReservedMobile = ""
		bank.AccountSetResult = 0
		bank.BankcardImg = model.FileInfo{}
		bank.Bankcard = model.Bankcard{}
	}

	err = s.authenticationDao.Update(ctx, filter, bson.M{"$set": bson.M{
		"bank_account": bank,
		//"acct_protocol_no": "",
		//"sign_acct_name":   "",
		//"sign_result":      "",
		"updated_at": time.Now().UnixMilli(),
	}})
	if err != nil {
		return err
	}

	return nil
}

// UnbindIndividualBank 个人银行卡
func (s authenticationService) UnbindIndividualBank(ctx context.Context, id primitive.ObjectID) error {
	authentication, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	res, err := s.payMember.UnbindBankCard(pays.UnbindBankCardReq{
		BizUserId: authentication.PayBizUserId,
		CardNo:    authentication.IndividualBankcardNo,
	})
	if err != nil {
		return err
	}

	// 移除银行卡信息
	filter := bson.M{
		"_id":             authentication.ID,
		"pay_biz_user_id": res.BizUserId,
	}

	err = s.authenticationDao.Update(ctx, filter, bson.M{"$set": bson.M{
		"individual_bankcard_no":          "",
		"individual_bank_reserved_mobile": "",
		"updated_at":                      time.Now().UnixMilli(),
	}})
	if err != nil {
		return err
	}

	return nil
}

func (s authenticationService) ApplyBindBank(ctx context.Context, id primitive.ObjectID, cardNo, phone string) error {
	authentication, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	res, err := s.payMember.ApplyBindBankCardS(pays.ApplyBindBankCardReq{
		BizUserId:    authentication.PayBizUserId,
		CardNo:       cardNo,
		Phone:        phone,
		Name:         authentication.Company.Legal.LegalName,
		CardCheck:    8, // 绑卡方式 8 银行卡四要素验证
		IdentityType: 1,
		IdentityNo:   authentication.Company.Legal.LegalIds,
	})
	if err != nil {
		return err
	}

	filter := bson.M{
		"_id":             authentication.ID,
		"pay_biz_user_id": res.BizUserId,
	}

	bank := authentication.BankAccount
	if bank.AccountType == model.AccountTypeSelf {
		// 对私
		bank.CardNumber = cardNo
		bank.BankReservedMobile = phone
		bank.AccountSetResult = 2
		bank.BankcardImg = model.FileInfo{}
		bank.Bankcard = model.Bankcard{
			BankName: res.BankName,
			BankCode: res.BankCode,
			CardType: res.CardType,
		}
	}

	err = s.authenticationDao.Update(ctx, filter, bson.M{"$set": bson.M{
		"bank_account": bank,
		"updated_at":   time.Now().UnixMilli(),
	}})
	if err != nil {
		return err
	}

	return nil
}
func (s authenticationService) ListBankCard(ctx context.Context, bizUserID string) ([]pays.BindBankInfo, error) {
	res, err := s.payMember.QueryBankCardS(pays.QueryBankCardReq{
		BizUserId: bizUserID,
	})
	if err != nil {
		return nil, err
	}

	return res.BindCardList, nil
}

// DoBindBank 确认绑定银行卡
func (s authenticationService) DoBindBank() error {
	//// 创建会员
	//res, err := s.payMember.BindBankCard(pays.BindBankCardReq{
	//	BizUserId:        util.NewUUID(),
	//	TranceNum:        cardNo,
	//	Phone:            phone,
	//	VerificationCode: code, // 短信验
	//})
	//if err != nil {
	//	return err
	//}
	//_ = res
	return nil
}
