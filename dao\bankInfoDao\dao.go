package bankInfoDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	GetByName(ctx context.Context, name string) (model.BankInfo, error)
	GetByCode(ctx context.Context, code string) (model.BankInfo, error)
}

type bankInfoDao struct {
	db *mongo.Collection
}

func (s bankInfoDao) GetByName(ctx context.Context, name string) (model.BankInfo, error) {
	var data model.BankInfo
	filter := bson.M{"name": name}
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.BankInfo{}, err
	}
	return data, nil
}

func (s bankInfoDao) GetByCode(ctx context.Context, code string) (model.BankInfo, error) {
	var data model.BankInfo
	filter := bson.M{"code": code}
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.BankInfo{}, err
	}
	return data, nil
}

func NewBankInfoDao(collect string) DaoInt {
	return bankInfoDao{
		db: global.MDB.Collection(collect),
	}
}
