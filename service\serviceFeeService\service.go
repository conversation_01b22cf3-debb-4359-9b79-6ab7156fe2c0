package serviceFeeService

import (
	"base/dao"
	"base/dao/deliverFeeRuleDao"
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
)

// ServiceInterface 服务费
type ServiceInterface interface {
	//CalcServiceFee(ctx context.Context, feeType model.ServiceFeeType, list []types.PerProductForCalc) (int, error)
	CalcServiceFeeForProduct(ctx context.Context, feeType model.ServiceFeeType, price int) (int, error)
}

type serviceFeeService struct {
	rdb               *redis.Client
	deliverFeeRuleDao deliverFeeRuleDao.DaoInt
	//ServiceFee        float64
}

func NewServiceFeeService() ServiceInterface {
	return serviceFeeService{
		rdb:               global.RDBDefault,
		deliverFeeRuleDao: dao.DeliverFeeRuleDao,
		//ServiceFee:        config.Conf.ServiceFee,
	}
}

//func (s serviceFeeService) CalcServiceFee(ctx context.Context, serviceFee int, list []types.PerProduct) (int, map[primitive.ObjectID]int, error) {
//	var total int                         // 总计
//	m := make(map[primitive.ObjectID]int) // 每个商品
//	for _, p := range list {
//		perAmount := p.Price * p.Num
//		perFee := backServiceFee(perAmount, serviceFee)
//		id, err := util.ConvertToObjectWithCtx(ctx, p.ProductID)
//		if err != nil {
//			return 0, nil, err
//		}
//		m[id] = perFee
//		total += perFee
//	}
//
//	return total, m, nil
//}

type Fee struct {
	Begin int `json:"begin"`
	End   int `json:"end"`
	Fee   int `json:"fee"`
}

var feeTypeOne = []Fee{{
	Begin: 0,
	End:   2000, //不含2000
	Fee:   0,
}, {
	Begin: 2000,
	End:   10000,
	Fee:   200,
}, {
	Begin: 10000,
	End:   30000,
	Fee:   400,
}, {
	Begin: 30000,
	End:   100000,
	Fee:   600,
}}

var feeTypeTwo = []Fee{{
	Begin: 0,
	End:   2000, //不含2000
	Fee:   100,
}, {
	Begin: 2000,
	End:   10000,
	Fee:   300,
}, {
	Begin: 10000,
	End:   30000,
	Fee:   500,
}, {
	Begin: 30000,
	End:   100000,
	Fee:   800,
}}

func (s serviceFeeService) CalcServiceFeeForProduct(ctx context.Context, feeType model.ServiceFeeType, price int) (int, error) {
	_ = ctx

	var perAmount int

	if feeType == model.ServiceFeeTypeOne {
		perAmount = backServiceFee(price, feeTypeOne)
	}

	if feeType == model.ServiceFeeTypeTwo {
		perAmount = backServiceFee(price, feeTypeTwo)
	}

	return perAmount, nil
}

//
//func (s serviceFeeService) CalcServiceFeeForProduct(ctx context.Context, fee int, price int) (int, error) {
//	_ = ctx
//
//	feeDe := decimal.NewFromInt(int64(fee)).Div(decimal.NewFromInt(100))
//	priceDe := decimal.NewFromInt(int64(price))
//
//	amountDe := feeDe.Mul(priceDe).Round(0)
//
//	amount := int(amountDe.IntPart())
//
//	// max
//
//	// 最高8元
//	if amount > 8*100 {
//		amount = 800
//	}
//
//	return amount, nil
//}

//func backServiceFee(amount int, serviceFee int) int {
//	percent := decimal.NewFromInt(int64(serviceFee)).Div(decimal.NewFromInt(100))
//	amountNum := percent.Mul(decimal.NewFromInt(int64(amount))).Round(0)
//	intPart := int(amountNum.IntPart())
//	return intPart
//}

func backServiceFee(price int, list []Fee) int {
	var amount int
	for _, per := range list {
		if price >= per.Begin {
			amount = per.Fee
		}
	}

	return amount
}
