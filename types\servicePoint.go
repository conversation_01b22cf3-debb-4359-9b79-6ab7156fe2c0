package types

import (
	"base/model"
)

type ServicePointSecondCreateReq struct {
	Name                 string              `json:"name"`
	UserName             string              `json:"user_name"`
	Mobile               string              `json:"mobile"`
	Location             model.Location      `json:"location"`               // 定位地址
	Address              string              `json:"address"`                // 详细地址
	DeliverType          []model.DeliverType `json:"deliver_type"`           // 配送方式
	ServiceChargePercent int                 `json:"service_charge_percent"` // 服务费分成
	TransportUnitPrice   int                 `json:"transport_unit_price"`   // 干线费单价
	//DeliveryScope        []model.PointLocation `json:"delivery_scope"`         // 配送范围
}

type ServicePointApplyReq struct {
	Name              string              `json:"name"`
	DeliverType       []model.DeliverType `json:"deliver_type"` // 配送方式
	ShopHeadImg       model.FileInfo      `json:"shop_head_img"`
	AuthenticationReq AuthenticationReq   `json:"authentication_req"`
}

type PointRes struct {
	model.ServicePoint
	IsMobileVerify bool   `json:"is_mobile_verify"`
	PayMobile      string `json:"pay_mobile"`
}

type StationCreateReq struct {
	ServicePointID string              `json:"service_point_id" bson:"service_point_id"`               //
	Name           string              `bson:"name" json:"name"`                                       // 名称
	DeliverType    []model.DeliverType `json:"deliver_type" bson:"deliver_type"`                       // 配送方式
	ContactUser    string              `json:"contact_user"  bson:"contact_user"  validate:"required"` // 联系人
	ContactMobile  string              `json:"contact_mobile" bson:"contact_mobile"`                   // 联系人
	Location       model.Location      `bson:"location" json:"location"`                               // 定位地址
	Address        string              `bson:"address" json:"address"`                                 // 详细地址
	CommissionRate int                 `json:"commission_rate"`                                        // 佣金
}
