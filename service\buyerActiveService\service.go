package buyerActiveService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/buyerActiveDao"
	"base/global"
	"base/model"
	"base/service/buyerService"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, buyerID primitive.ObjectID) error
	Audit(ctx context.Context, buyerID primitive.ObjectID, isPass bool) error
	List(ctx context.Context, isPass bool, page, limit int64) ([]model.BuyerActive, int64, error)
	CheckExist(ctx context.Context, buyerID primitive.ObjectID) (model.BuyerActive, error)
}

type buyerActiveService struct {
	mdb            *mongo.Database
	rdb            *redis.Client
	buyerActiveDao buyerActiveDao.DaoInt
	buyerS         buyerService.ServiceInterface
}

func (s buyerActiveService) Create(ctx context.Context, buyerID primitive.ObjectID) error {
	exist, err := s.CheckExist(ctx, buyerID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if exist.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在申请")
	}
	data := model.BuyerActive{
		ID:        primitive.NewObjectID(),
		BuyerID:   buyerID,
		IsPass:    false,
		CreatedAt: time.Now().UnixMilli(),
	}
	err = s.buyerActiveDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerActiveService) Audit(ctx context.Context, buyerID primitive.ObjectID, isPass bool) error {
	filter := bson.M{
		"buyer_id": buyerID,
	}
	update := bson.M{
		"is_pass":  isPass,
		"is_audit": true,
	}
	err := s.buyerActiveDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	if isPass {
		//	通过
		err = s.buyerS.UpdateActiveExpire(ctx, buyerID)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s buyerActiveService) CheckExist(ctx context.Context, buyerID primitive.ObjectID) (model.BuyerActive, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"is_audit": false,
	}
	get, err := s.buyerActiveDao.Get(ctx, filter)
	if err != nil {
		return model.BuyerActive{}, err
	}
	return get, nil
}

func (s buyerActiveService) List(ctx context.Context, isPass bool, page, limit int64) ([]model.BuyerActive, int64, error) {
	filter := bson.M{
		"is_pass": isPass,
	}
	list, i, err := s.buyerActiveDao.List(context.Background(), filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func NewBuyerActiveService() ServiceInterface {
	return buyerActiveService{
		mdb:            global.MDB,
		rdb:            global.RDBDefault,
		buyerActiveDao: dao.BuyerActiveDao,
		buyerS:         buyerService.NewBuyerService(),
	}
}
