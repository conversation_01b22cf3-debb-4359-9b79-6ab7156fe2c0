package productImageService

import (
	"base/dao"
	"base/dao/productImageDao"
	"base/global"
	"base/model"
	"context"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ServiceInterface 商品镜像
type ServiceInterface interface {
	Create(ctx context.Context, orderID primitive.ObjectID, list []model.Product, now int64) error
	GetByVersion(ctx context.Context, productID primitive.ObjectID, version int64) (model.ProductImage, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.ProductImage, error)
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID, productID primitive.ObjectID) (model.ProductImage, error)
}

type productImageService struct {
	rdb             *redis.Client
	productImageDao productImageDao.DaoInt
}

// GetByVersion 根据产品ID和版本获取商品镜像
func (s productImageService) GetByVersion(ctx context.Context, productID primitive.ObjectID, version int64) (model.ProductImage, error) {
	filter := bson.M{
		"product._id":     productID,
		"product.version": version,
	}
	image, err := s.productImageDao.Get(ctx, filter)
	if err != nil {
		return model.ProductImage{}, err
	}

	return image, nil
}

// Get 根据ID获取商品镜像
func (s productImageService) Get(ctx context.Context, id primitive.ObjectID) (model.ProductImage, error) {
	filter := bson.M{
		"_id": id,
	}
	image, err := s.productImageDao.Get(ctx, filter)
	if err != nil {
		return model.ProductImage{}, err
	}

	return image, nil
}

// Create 创建商品镜像
func (s productImageService) Create(ctx context.Context, orderID primitive.ObjectID, list []model.Product, now int64) error {
	var dataList []model.ProductImage
	productIDMap := make(map[primitive.ObjectID]bool) // 用于去重的map

	for _, v := range list {
		// 检查商品ID是否已经存在，如果存在则跳过
		if productIDMap[v.ID] {
			continue
		}

		productIDMap[v.ID] = true // 标记该商品ID已处理

		data := model.ProductImage{
			ID:        primitive.NewObjectID(),
			OrderID:   orderID,
			Product:   v,
			CreatedAt: now,
		}
		dataList = append(dataList, data)
	}

	err := s.productImageDao.CreateMany(ctx, dataList)
	if err != nil {
		return err
	}

	return nil
}

// GetByOrderID 根据订单ID和产品ID获取商品镜像

func (s productImageService) GetByOrderID(ctx context.Context, orderID primitive.ObjectID, productID primitive.ObjectID) (model.ProductImage, error) {
	filter := bson.M{
		"order_id":    orderID,
		"product._id": productID,
	}
	image, err := s.productImageDao.Get(ctx, filter)
	if err != nil {
		return model.ProductImage{}, err
	}

	return image, nil
}

// NewProductImageService 创建商品镜像服务
func NewProductImageService() ServiceInterface {
	return productImageService{
		rdb:             global.RDBDefault,
		productImageDao: dao.ProductImageDao,
	}
}
