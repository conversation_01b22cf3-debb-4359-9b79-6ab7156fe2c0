package officialAccountUserDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.OfficialAccountUser) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.OfficialAccountUser, error)
	List(ctx context.Context, filter bson.M) ([]model.OfficialAccountUser, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OfficialAccountUser, int64, error)
}

type officialAccountUserDao struct {
	db *mongo.Collection
}

// List 查询
func (s officialAccountUserDao) List(ctx context.Context, filter bson.M) ([]model.OfficialAccountUser, error) {
	var list []model.OfficialAccountUser
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s officialAccountUserDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OfficialAccountUser, int64, error) {
	var list []model.OfficialAccountUser
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s officialAccountUserDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s officialAccountUserDao) Create(ctx context.Context, data model.OfficialAccountUser) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s officialAccountUserDao) Get(ctx context.Context, filter bson.M) (model.OfficialAccountUser, error) {
	var data model.OfficialAccountUser
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.OfficialAccountUser{}, err
	}
	return data, nil
}

func NewOfficialAccountUserDao(collect string) DaoInt {
	return officialAccountUserDao{
		db: global.MDB.Collection(collect),
	}
}
