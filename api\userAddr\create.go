package userAddr

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/userAddrService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Add(ctx *gin.Context) {
	req := &types.UserAddrCreate{}
	err := xhttp.Parse(ctx, req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByID(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = userAddrService.NewUserAddrService().Add(ctx, req, buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
