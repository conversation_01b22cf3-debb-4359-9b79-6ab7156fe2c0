package promotionsubsidydao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 认证
type DaoInt interface {
	Create(ctx context.Context, data model.PromotionSubsidy) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.PromotionSubsidy, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.PromotionSubsidy, int64, error)
	ListAll(ctx context.Context, filter bson.M) ([]model.PromotionSubsidy, error)
}

type dao struct {
	db *mongo.Collection
}

func (s dao) Create(ctx context.Context, data model.PromotionSubsidy) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s dao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s dao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s dao) Get(ctx context.Context, filter bson.M) (model.PromotionSubsidy, error) {
	var data model.PromotionSubsidy
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.PromotionSubsidy{}, err
	}
	return data, nil
}

// List 查询
func (s dao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.PromotionSubsidy, int64, error) {
	var list []model.PromotionSubsidy
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s dao) ListAll(ctx context.Context, filter bson.M) ([]model.PromotionSubsidy, error) {
	var list []model.PromotionSubsidy
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewPromotionSubsidyDao(collect string) DaoInt {
	return dao{
		db: global.MDB.Collection(collect),
	}
}
