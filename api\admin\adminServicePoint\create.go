package adminServicePoint

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/servicePointService"
	"base/types"
	"github.com/gin-gonic/gin"
)

// Create 服务点申请
func Create(ctx *gin.Context) {
	var req types.ServicePointApplyReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	for _, v := range req.DeliverType {
		if _, ok := model.DeliverTypeMsg[v]; !ok {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "配送方式参数错误"))
			return
		}
	}

	err = servicePointService.NewServicePointService().Create(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
	return
}
