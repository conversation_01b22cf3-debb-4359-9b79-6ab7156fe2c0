package yeeMerchant

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func AccountOpen(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	return
	//}
	err = yeeMerchantService.NewYeeMerchantService().AccountOpen(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func AccountQuery(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().AccountQuery(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func AccountRechargeQuery(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().AccountBookRechargeQuery(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func AccountRechargeRefund(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().AccountBookRefund(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func AccountWithDrawCardQueryBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().AccountWithDrawCardQuery(ctx, yeeMerchant)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func AccountWithDrawCardQueryByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetYeeByPoint(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().AccountWithDrawCardQuery(ctx, yeeMerchant)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func AccountWithDrawCardBindBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		AccountNo  string `json:"account_no"`
		BankCode   string `json:"bank_code"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().AccountWithDrawCardBind(ctx, yeeMerchant, req.AccountNo, req.BankCode)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}
