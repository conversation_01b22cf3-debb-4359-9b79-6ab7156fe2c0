package payAccountService

import (
	"base/core/config"
	"base/core/xerr"
	"base/dao"
	"base/dao/bankAccountDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/allInPayUserService"
	"base/service/authenticationService"
	"context"
	"encoding/json"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

var cacheBalance = "balance:"

// ServiceInterface 支付账户
type ServiceInterface interface {
	CardBin(ctx context.Context, cardNumber string) (pays.GetBankCardBinRes, error)

	BindPhone(ctx context.Context, id primitive.ObjectID, objectType model.ObjectType, mobile, captcha string) error

	// GetNormalUserBalance 普通会员
	GetNormalUserBalance(ctx context.Context, bizUserID string) (pays.QueryBalanceRes, error)
	GetNormalUserBalanceCache(ctx context.Context, bizUserID string) (pays.QueryBalanceRes, error)

	// GetPlatformBalance 平台-余额
	GetPlatformBalance(ctx context.Context, accountSetNo string) (pays.QueryMerchantBalanceRes, error)
	// GetPlatformStandardBalanceNo 标准余额
	GetPlatformStandardBalanceNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error)
	// GetPlatformMarketNo 标准营销账户集
	GetPlatformMarketNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error)
	// GetPlatformStandardDepositNo 标准保证金账户集
	GetPlatformStandardDepositNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error)

	// GetPlatformReserveLimitNo 准备金额度账户集
	GetPlatformReserveLimitNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error)

	GetPlatformAccountA(ctx context.Context) (pays.QueryMerchantBalanceRes, error)
	GetPlatformAccountB(ctx context.Context) (pays.QueryMerchantBalanceRes, error)
	GetReserveFundBalance(ctx context.Context) (pays.QueryReserveFundBalanceRes, error)

	//	CheckMarketNoEnough 检查营销账户余额
	CheckMarketNoEnough(ctx context.Context, amount int) error
}

type payAccountService struct {
	rdb            *redis.Client
	bankAccountDao bankAccountDao.DaoInt

	AllInPayMemberS   payModule.MemberService
	AllInPayOrderS    payModule.OrderService
	AllInPayMerchantS payModule.MerchantService
	AllInPayUserS     allInPayUserService.ServiceInterface
	authenticationS   authenticationService.ServiceInterface

	AllInPayAccountSetInfo config.AllInPayAccountSetInfo
}

func NewPayAccountService() ServiceInterface {
	return payAccountService{
		rdb:                    global.RDBDefault,
		bankAccountDao:         dao.BankAccountDao,
		AllInPayAccountSetInfo: global.AllInPayAccountSetInfo,

		AllInPayMemberS:   payModule.NewMember(),
		AllInPayOrderS:    payModule.NewOrderS(),
		AllInPayMerchantS: payModule.NewMerchantS(),
		authenticationS:   authenticationService.NewAuthenticationService(),

		AllInPayUserS: allInPayUserService.NewAllInPayUserService(),
	}
}

func (s payAccountService) CheckMarketNoEnough(ctx context.Context, amount int) error {
	res, err := s.GetPlatformMarketNo(ctx)
	if err != nil {
		return err
	}
	if res.AllAmount < amount {
		return xerr.NewErr(xerr.ErrOrder, nil, "优惠券余额不足，请联系平台或移除优惠券再支付")
	}
	return nil
}

func (s payAccountService) CardBin(ctx context.Context, cardNumber string) (pays.GetBankCardBinRes, error) {
	res, err := s.AllInPayMemberS.GetBankCardBinS(pays.GetBankCardBinReq{
		CardNo: cardNumber,
	})
	if err != nil {
		return pays.GetBankCardBinRes{}, err
	}
	return res, nil
}

func (s payAccountService) BindPhone(ctx context.Context, id primitive.ObjectID, objectType model.ObjectType, mobile, captcha string) error {
	payUser, err := s.AllInPayUserS.GetByObjectType(ctx, id, objectType)
	if err != nil {
		return err
	}

	res, err := s.AllInPayMemberS.BindPhoneS(pays.BindPhoneReq{
		BizUserId:        payUser.PayBizUserId,
		Phone:            mobile,
		VerificationCode: captcha,
	})
	if err != nil {
		return err
	}
	if res.Phone == mobile && res.BizUserId == payUser.PayBizUserId {
		//	成功
		err = s.AllInPayUserS.UpdateMobile(ctx, payUser.ID, mobile)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s payAccountService) GetPlatformAccountA(ctx context.Context) (pays.QueryMerchantBalanceRes, error) {
	// 中间账户集A
	res, err := s.AllInPayMerchantS.QueryMerchantBalance(pays.QueryMerchantBalanceReq{
		AccountSetNo: "100004",
	})
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

func (s payAccountService) GetPlatformAccountB(ctx context.Context) (pays.QueryMerchantBalanceRes, error) {
	// 中间账户集B
	res, err := s.AllInPayMerchantS.QueryMerchantBalance(pays.QueryMerchantBalanceReq{
		AccountSetNo: "100005",
	})
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

func (s payAccountService) GetReserveFundBalance(ctx context.Context) (pays.QueryReserveFundBalanceRes, error) {
	_ = ctx
	// 中间账户集B
	res, err := s.AllInPayMerchantS.QueryReserveFundBalance(pays.QueryReserveFundBalanceReq{})
	if err != nil {
		return pays.QueryReserveFundBalanceRes{}, err
	}
	return res, nil
}

func (s payAccountService) GetPlatformBalance(ctx context.Context, accountSetNo string) (pays.QueryMerchantBalanceRes, error) {
	// 查询余额
	res, err := s.AllInPayMerchantS.QueryMerchantBalance(pays.QueryMerchantBalanceReq{
		AccountSetNo: accountSetNo,
	})
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

// GetPlatformStandardBalanceNo 标准余额
func (s payAccountService) GetPlatformStandardBalanceNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error) {
	res, err := s.GetPlatformBalance(ctx, global.AllInPayAccountSetInfo.StandardBalanceNo)
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

// GetPlatformMarketNo 标准营销账户集
func (s payAccountService) GetPlatformMarketNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error) {
	res, err := s.GetPlatformBalance(ctx, global.AllInPayAccountSetInfo.MarketNo)
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

// GetPlatformStandardDepositNo 标准保证金账户集
func (s payAccountService) GetPlatformStandardDepositNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error) {
	res, err := s.GetPlatformBalance(ctx, global.AllInPayAccountSetInfo.StandardDepositNo)
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

// GetPlatformReserveLimitNo 准备金额度账户集
func (s payAccountService) GetPlatformReserveLimitNo(ctx context.Context) (pays.QueryMerchantBalanceRes, error) {
	res, err := s.GetPlatformBalance(ctx, global.AllInPayAccountSetInfo.ReserveLimitNo)
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

func (s payAccountService) GetNormalUserBalance(ctx context.Context, bizUserID string) (pays.QueryBalanceRes, error) {
	// 查询余额
	balanceS, err := s.AllInPayOrderS.QueryBalanceS(pays.QueryBalanceReq{
		BizUserId:    bizUserID,
		AccountSetNo: s.AllInPayAccountSetInfo.EscrowUserNo,
		//BizUserId    string `json:"bizUserId"`    // 必填    商户系统用户标识，商户系统中唯一编号。
		//AccountSetNo string `json:"accountSetNo"` // 必填 	账户集编号	云商通分配的托管专用账户集的编号
	})
	if err != nil {
		return pays.QueryBalanceRes{}, err
	}
	return balanceS, nil
}

func (s payAccountService) GetNormalUserBalanceCache(ctx context.Context, bizUserID string) (pays.QueryBalanceRes, error) {
	key := cacheBalance + bizUserID
	ctx2 := context.Background()
	val := s.rdb.Exists(ctx2, key).Val()
	if val > 0 {
		bytes, err := s.rdb.Get(ctx2, key).Bytes()
		if err != nil {
			zap.S().Errorf("get GetNormalUserBalanceCache err:%s", err.Error())
			return pays.QueryBalanceRes{}, err
		}
		var i pays.QueryBalanceRes
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Errorf("unmarshal GetNormalUserBalanceCache err:%s", err.Error())
			return pays.QueryBalanceRes{}, err
		}
		return i, nil
	}

	queryBalanceRes, err := s.GetNormalUserBalance(ctx, bizUserID)
	if err != nil {
		return pays.QueryBalanceRes{}, err
	}
	bytes, err := json.Marshal(queryBalanceRes)
	if err != nil {
		zap.S().Error("set GetNormalUserBalanceCache marshal,", err)
		return pays.QueryBalanceRes{}, err
	}
	// 30 天过期
	s.rdb.Set(context.Background(), key, bytes, time.Hour*24*7)

	return queryBalanceRes, nil
}

type remove struct {
	BizUserID string `json:"biz_user_id"`
}
