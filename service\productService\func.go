package productService

import (
	"base/core/xerr"
	"base/model"
	"base/types"
	"context"
	"fmt"
	"strings"
)

// BackValidDiscountPrice 返回适用折扣价格
func (s productService) BackValidDiscountPrice(ctx context.Context, num int, list []model.DiscountPrice) (int, error) {

	return 0, nil

	//if num < 1 {
	//	return 0, nil
	//}
	//
	//if len(list) < 1 {
	//	return 0, nil
	//}
	//
	//for i := len(list); i > 0; i-- {
	//	if num >= list[i-1].Num {
	//		return list[i-1].Price, nil
	//	}
	//}
	//
	//return 0, nil
}

// BackPriceByNum 获取查询的阶梯价格
//func (s productService) BackPriceByNum(ctx context.Context, list []model.PerPrice, num int) (int, error) {
//	if len(list) < 1 {
//		return 0, xerr.NewErr(xerr.ErrParamError, nil, "价格列表为空")
//	}
//	if len(list) == 1 {
//		return list[0].Price, nil
//	}
//	n := len(list) - 1
//	for i := 0; i < n; i++ {
//		if list[i].Num <= num && list[i+1].Num > num {
//			return list[i].Price, nil
//		}
//		if i+1 == n && list[n].Num <= num {
//			return list[n].Price, nil
//		}
//	}
//
//	return 0, xerr.NewErr(xerr.ErrParamError, nil, "数量未达到起售数")
//}

func checkAttr(list []model.FieldInfo) error {
	if len(list) < 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写商品产地等参数或返回上一页再重试")
	}

	for _, i := range list {
		if i.Field != "" {
			s := strings.TrimSpace(i.Value)
			if s == "" {
				return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("参数【%s】内容不能为空", i.Field))
			}
		}
	}

	return nil
}

// 检查
func checkCreate(req types.ProductCreateReq) error {
	if req.Title == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请输入标题")
	}
	// if req.Stock < 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "库存最小为0")
	// }
	//err := checkPriceList(req.PriceList)
	//if err != nil {
	//	return err
	//}
	// if req.Price <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "价格不能为0")
	// }

	// if req.MarketWholesalePrice <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "市场批发价不能为0")
	// }

	// if req.EstimatePurchasePrice <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "预估采购价不能为0")
	// }

	err := checkAttr(req.AttrInfo)
	if err != nil {
		return err
	}
	if len(req.CategoryIDs) != 3 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请选择分类")
	}
	if req.Title == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "标题缺失")
	}
	if len(req.DisplayFile) < 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请添加商品轮播图")
	}
	for _, info := range req.DisplayFile {
		if info.Type != "image" {
			return xerr.NewErr(xerr.ErrParamError, nil, "商品轮播图参数类型错误，应为image")
		}
		if info.Name == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "商品轮播图缺失")
		}
	}

	//重量
	// if req.Weight.RoughWeight <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "重量参数错误，毛重不能小于等于0")
	// }
	// if req.ProductParamType == model.ProductParamTypeFruit {
	// 	//if req.Weight.OutWeight <= 0 {
	// 	//	return xerr.NewErr(xerr.ErrParamError, nil, "皮重参数错误")
	// 	//}
	// }

	// if req.HasParam {
	// 	if req.Weight.RoughWeight-req.Weight.OutWeight != req.Weight.NetWeight {
	// 		return xerr.NewErr(xerr.ErrParamError, nil, "重量参数错误，净重等于毛重减去皮重")
	// 	}
	// 	if req.Weight.NetWeight < 0 {
	// 		return xerr.NewErr(xerr.ErrParamError, nil, "重量参数错误，净重不能小于0")
	// 	}
	// }

	//// 价格
	//list := req.PriceList
	//n := len(list)
	//for i := 0; i < n-1; i++ {
	//	if list[i].Num >= list[i+1].Num {
	//		return xerr.NewErr(xerr.ErrParamError, nil, "阶梯价格参数错误，数量需依次递增")
	//	}
	//	if list[i].Price <= list[i+1].Price {
	//		return xerr.NewErr(xerr.ErrParamError, nil, "阶梯价格参数错误，价格需依次递增")
	//	}
	//}
	return nil
}

// 检查
func checkUpdate(req types.ProductUpdateReq) error {
	if req.Title == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请输入标题")
	}
	// if req.Stock < 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "库存最小为0")
	// }
	// if req.Price <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "价格需大于0")
	// }
	// if req.MarketWholesalePrice <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "市场批发价不能为0")
	// }

	// if req.EstimatePurchasePrice <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "预估采购价不能为0")
	// }
	err := checkAttr(req.AttrInfo)
	if err != nil {
		return err
	}
	if len(req.CategoryIDs) != 3 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请选择分类")
	}

	if req.Title == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "标题缺失")
	}
	if len(req.DisplayFile) < 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请添加商品轮播图")
	}
	for _, info := range req.DisplayFile {
		if info.Type != "image" {
			return xerr.NewErr(xerr.ErrParamError, nil, "商品轮播图参数类型错误，应为image")
		}
		if info.Name == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "商品轮播图缺失")
		}
	}

	//重量
	// if req.Weight.RoughWeight <= 0 {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "重量参数错误，毛重不能小于等于0")
	// }

	// if req.HasParam {
	// 	if req.Weight.RoughWeight-req.Weight.OutWeight != req.Weight.NetWeight {
	// 		return xerr.NewErr(xerr.ErrParamError, nil, "重量参数错误，净重等于毛重减去皮重")
	// 	}
	// 	if req.Weight.NetWeight < 0 {
	// 		return xerr.NewErr(xerr.ErrParamError, nil, "重量参数错误，净重不能小于0")
	// 	}
	// }

	return nil
}
