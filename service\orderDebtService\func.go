package orderDebtService

import "github.com/shopspring/decimal"

func backDebtAmount(weight int, productRoughWeightUnitPriceKG int) int {
	fee := decimal.NewFromInt(int64(productRoughWeightUnitPriceKG))
	amount := toDec(weight).Mul(fee).Div(decimal.NewFromInt(1000)).Round(0)
	return int(amount.IntPart())
}

// 补差运费
func backDebtTransport(overWeight int, transportFeePerKG int) int {
	ow := decimal.NewFromInt(int64(overWeight))
	fee := decimal.NewFromInt(int64(transportFeePerKG))
	amount := ow.Div(decimal.NewFromInt(1000)).Mul(fee).Round(0)

	return int(amount.IntPart())
}

func backLoadFee(overWeight int, transportFeePerKG int) int {
	ow := decimal.NewFromInt(int64(overWeight))
	fee := decimal.NewFromInt(int64(transportFeePerKG))
	amount := ow.Div(decimal.NewFromInt(1000)).Mul(fee).Round(0)

	return int(amount.IntPart())
}

func toDec(num int) decimal.Decimal {
	return decimal.NewFromInt(int64(num))
}
