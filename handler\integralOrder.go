package handler

import (
	"base/api/integralOrder"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func integralOrderRouter(r *gin.RouterGroup) {
	r = r.Group("/integral/order")

	r.Use(middleware.CheckToken)

	r.POST("/create", integralOrder.Create)
	r.POST("/get", integralOrder.Get)
	r.POST("/list/by/user", integralOrder.ListByUser)
	r.POST("/list/by/web", integralOrder.ListByWeb)
	r.POST("/count/ship", integralOrder.CountShip)
	r.POST("/cancel", integralOrder.Cancel)
	r.POST("/ship", integralOrder.Ship)
}
