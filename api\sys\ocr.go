package sys

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/aliocrService"
	"github.com/gin-gonic/gin"
)

// Ocr ocr识别
func Ocr(ctx *gin.Context) {
	var req = struct {
		Url         string `json:"url" validate:"required"`
		Type        string `json:"type" validate:"required"`
		Orientation string `json:"orientation"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	switch req.Type {
	case "id_card":
		// 朝向
		//v := req.Orientation
		//if v != "front" && v != "back" {
		//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "身份证朝向字段错误"))
		//	return
		//}
		//var isBack bool
		//if v == "back" {
		//	isBack = true
		//}

		//res, err := ocrService.NewOcrService().IDCard(req.Url, isBack)
		//if err != nil {
		//	xhttp.RespErr(ctx, err)
		//	return
		//}
		//xhttp.RespSuccess(ctx, res)
		r, err := aliocrService.NewAliOcrService().OcrIdCard(req.Url)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		xhttp.RespSuccess(ctx, r)

	case "bank_card":
		//res, err := ocrService.NewOcrService().BankCard(req.Url)
		//if err != nil {
		//	xhttp.RespErr(ctx, err)
		//	return
		//}
		//xhttp.RespSuccess(ctx, res)
		r, err := aliocrService.NewAliOcrService().OcrBankCard(req.Url)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		xhttp.RespSuccess(ctx, r)
	case "business_license":
		//res, err := ocrService.NewOcrService().BusinessLicense(req.Url)
		//if err != nil {
		//	xhttp.RespErr(ctx, err)
		//	return
		//}
		r, err := aliocrService.NewAliOcrService().OcrLicense(req.Url)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		xhttp.RespSuccess(ctx, r)
	default:
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "识别类型错误"))
		return
	}
}
