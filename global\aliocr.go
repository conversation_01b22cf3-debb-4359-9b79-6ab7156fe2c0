package global

import (
	"base/core/config"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	ocr_api20210707 "github.com/alibabacloud-go/ocr-api-20210707/client"
	"github.com/alibabacloud-go/tea/tea"
	"go.uber.org/zap"
)

var AliOcr *ocr_api20210707.Client

func initAliOcr(c config.AliOcr) {
	clientConf := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: tea.String(c.AccessKeyID),
		// 必填，您的 AccessKey Secret
		AccessKeySecret: tea.String(c.AccessKeySecret),
	}
	clientConf.Endpoint = tea.String("ocr-api.cn-hangzhou.aliyuncs.com")
	//_result = &ocr_api20210707.Client{}
	client, err := ocr_api20210707.NewClient(clientConf)
	if err != nil {
		zap.S().Errorf("init ali ocr error:%s", err.Error())
		return
	}
	AliOcr = client
}
