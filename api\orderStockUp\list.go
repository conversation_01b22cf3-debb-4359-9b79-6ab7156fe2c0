package orderStockUp

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/service/orderStockUpService"
	"base/service/supplierService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListStockUp  备货列表-未实际备货
func ListStockUp(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
		//ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp int64 `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.SupplierID, "ListStockUp supplier_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := supplierService.NewSupplierService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var servicePointID primitive.ObjectID

	stockUpInfos, err := orderStockUpService.NewOrderStockUpService().ListStockOrder(ctx, supplier.ID, servicePointID, ts, false)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, stockUpInfos)
}

// ListHasStockUp   备货列表-已实际备货
func ListHasStockUp(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
		//ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp int64 `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.SupplierID, "ListStockUp supplier_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := supplierService.NewSupplierService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var servicePointID primitive.ObjectID

	stockUpInfos, err := orderStockUpService.NewOrderStockUpService().ListStockOrder(ctx, supplier.ID, servicePointID, ts, true)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, stockUpInfos)
}

// ListToDoStockUpOrder  待备货订单
func ListToDoStockUpOrder(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
		Timestamp  int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithNote(req.SupplierID, "ListAllGroupByProduct SupplierID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := supplierService.NewSupplierService().Get(ctx, supplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stockUpInfos, err := orderStockUpService.NewOrderStockUpService().ListStockOrderToDo(ctx, supplier.ID, ts)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, stockUpInfos)
}

// ListDoingOrderForSupplier  供应商进行中订单
func ListDoingOrderForSupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
		Timestamp  int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithNote(req.SupplierID, "ListAllGroupByProduct SupplierID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		"stock_up_day_time": ts,
		"supplier_id":       supplierID,
		"order_status": bson.M{
			//"$gte": model.OrderStatusTypeToQuality,
			"$in": bson.A{model.OrderStatusTypeToQuality, model.OrderStatusTypeToSort, model.OrderStatusTypeToShip, model.OrderStatusTypeToArrive, model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
	})

	xhttp.RespSuccess(ctx, orders)
}

//
//// ListToDoStockUpOrderAll  所有待备货订单
//func ListToDoStockUpOrderAll(ctx *gin.Context) {
//	stockUpInfos, err := orderStockUpService.NewOrderStockUpService().ListStockOrderToDoAll(ctx)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	var list []toStockRes
//	for _, i := range stockUpInfos {
//		supplier, _ := supplierService.NewSupplierService().Get(ctx, i.SupplierID)
//		user, _ := userService.NewUserService().Get(ctx, supplier.UserID)
//
//		list = append(list, toStockRes{
//			Order:          i,
//			SupplierMobile: user.Mobile,
//		})
//	}
//
//	sort.Sort(toStockList(list))
//
//	xhttp.RespSuccess(ctx, list)
//}
//
//type toStockRes struct {
//	model.Order
//	SupplierMobile string `json:"supplier_mobile"`
//}
