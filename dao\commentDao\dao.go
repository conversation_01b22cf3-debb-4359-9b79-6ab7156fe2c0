package commentDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 评论
type DaoInt interface {
	Create(ctx context.Context, data model.Comment) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Comment, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Comment, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Get(ctx context.Context, filter bson.M) (model.Comment, error)
}

type commentDao struct {
	db *mongo.Collection
}

func NewCommentDao(collect string) DaoInt {
	return commentDao{
		db: global.MDB.Collection(collect),
	}
}

func (s commentDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Comment, int64, error) {
	var list []model.Comment
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// List 查询
func (s commentDao) List(ctx context.Context, filter bson.M) ([]model.Comment, error) {
	var list []model.Comment
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s commentDao) Count(ctx context.Context, filter bson.M) (int64, error) {

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s commentDao) Create(ctx context.Context, data model.Comment) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s commentDao) Get(ctx context.Context, filter bson.M) (model.Comment, error) {
	var data model.Comment

	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Comment{}, err
	}

	return data, nil
}

func (s commentDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return nil
	}

	return nil
}

func (s commentDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return nil
	}

	return nil
}
