package payOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 支付单
type DaoInt interface {
	Create(ctx context.Context, data model.PayOrder) error
	Get(ctx context.Context, filter bson.M) (model.PayOrder, error)
	List(ctx context.Context, filter bson.M) ([]model.PayOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PayOrder, int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type payOrderDao struct {
	db *mongo.Collection
}

func (s payOrderDao) Create(ctx context.Context, data model.PayOrder) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

func (s payOrderDao) Get(ctx context.Context, filter bson.M) (model.PayOrder, error) {
	var data model.PayOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s payOrderDao) List(ctx context.Context, filter bson.M) ([]model.PayOrder, error) {
	var list []model.PayOrder
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s payOrderDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PayOrder, int64, error) {
	var list []model.PayOrder
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s payOrderDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s payOrderDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}
func NewPayOrderDao(collect string) DaoInt {
	return payOrderDao{
		db: global.MDB.Collection(collect),
	}
}
