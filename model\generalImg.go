package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// GeneralImg 通用图片
type GeneralImg struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Type      string             `json:"type" bson:"type"`         // product_desc 商品说明
	ImgList   []FileInfo         `json:"img_list" bson:"img_list"` // 图片列表
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
}
