package orderDebt

import (
	"base/core/xhttp"
	"base/service/orderDebtService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func GetByID(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "GetByOrder id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := orderDebtService.NewOrderDebtService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

// GetByOrder 通过订单查询补差单
func GetByOrder(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := orderDebtService.NewOrderDebtService().GetByOrderID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

func ExistDebtOrder(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res := map[string]bool{
		"exist": true,
	}
	f, err := orderDebtService.NewOrderDebtService().CheckByBuyer(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = f
	res["exist"] = f
	xhttp.RespSuccess(ctx, res)
}

func CountNotPaidByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	count, err := orderDebtService.NewOrderDebtService().CountNotPaidByBuyer(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, count)
}
