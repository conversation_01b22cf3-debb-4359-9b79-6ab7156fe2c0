package browseService

import (
	"base/dao"
	"base/dao/browseDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, buyerID, productID primitive.ObjectID) error
	Delete(ctx context.Context, ids []primitive.ObjectID) error
	ListForBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.BrowseStats, int64, error)
}

type browseService struct {
	mdb       *mongo.Database
	rdb       *redis.Client
	browseDao browseDao.DaoInt
}

func (s browseService) Create(ctx context.Context, buyerID, productID primitive.ObjectID) error {
	data := model.Browse{
		ID:        primitive.NilObjectID,
		BuyerID:   buyerID,
		ProductID: productID,
		CreatedAt: time.Now().UnixMilli(),
	}
	err := s.browseDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s browseService) ListForBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.BrowseStats, int64, error) {
	list, i, err := s.browseDao.ListForBuyer(ctx, buyerID, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s browseService) Delete(ctx context.Context, ids []primitive.ObjectID) error {
	if len(ids) < 1 {
		return nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	err := s.browseDao.UpdateMany(ctx, filter, bson.M{"$set": bson.M{"deleted_at": time.Now().UnixMilli()}})
	if err != nil {
		return err
	}
	return nil
}

func NewBrowseService() ServiceInterface {
	return browseService{
		mdb:       global.MDB,
		rdb:       global.RDBDefault,
		browseDao: dao.BrowseDao,
	}
}
