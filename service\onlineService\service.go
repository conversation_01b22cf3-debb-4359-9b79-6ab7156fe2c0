package onlineService

import (
	"base/global"
	"base/util"
	"context"
	"github.com/go-redis/redis/v8"
	"github.com/golang-module/carbon/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"time"
)

var cacheOnline = "online:"

var cacheOnlineNum = "onlineNum"

// ServiceInterface 分类标签
type ServiceInterface interface {
	AddTourVisit(ctx context.Context, t int64, ip string)
	AddUserVisit(ctx context.Context, t int64, userID primitive.ObjectID)
	AddBuyerVisit(ctx context.Context, t int64, buyerID primitive.ObjectID)

	List(ctx context.Context, num int) (interface{}, error)
	RecordEnterMini(userID string, num int)
	GetOnlineNum() int
}

type onlineService struct {
	mdb *mongo.Database
	rdb *redis.Client
}

func NewOnlineService() ServiceInterface {
	return onlineService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
	}
}

func backTs(t int64) string {
	start, err := util.StartOfHourTimestamp(t)
	if err != nil {
		zap.S().Error(err.Error())
		return ""
	}
	ts := strconv.Itoa(int(start))

	return ts
}

// AddTourVisit 游客
func (s onlineService) AddTourVisit(ctx context.Context, t int64, ip string) {
	key := cacheOnline + backTs(t)
	s.rdb.ZAdd(ctx, key, &redis.Z{
		Score:  float64(t),
		Member: "tour" + ip,
	})
	s.rdb.Expire(ctx, key, time.Hour*48)
}

// AddUserVisit 普通用户
func (s onlineService) AddUserVisit(ctx context.Context, t int64, userID primitive.ObjectID) {
	key := cacheOnline + backTs(t)
	s.rdb.ZAdd(ctx, key, &redis.Z{
		Score:  float64(t),
		Member: "user" + userID.Hex(),
	})
	s.rdb.Expire(ctx, key, time.Hour*48)
}

// AddBuyerVisit 会员
func (s onlineService) AddBuyerVisit(ctx context.Context, t int64, buyerID primitive.ObjectID) {
	key := cacheOnline + backTs(t)
	s.rdb.ZAdd(ctx, key, &redis.Z{
		Score:  float64(t),
		Member: "buyer" + buyerID.Hex(),
	})
	s.rdb.Expire(ctx, key, time.Hour*48)
}

func (s onlineService) List(ctx context.Context, num int) (interface{}, error) {
	if num < 1 {
		return []res{}, nil
	}
	list := make([]res, num, num)
	startHour := carbon.Time2Carbon(time.Now()).StartOfHour()
	for i, r := range list {
		_ = r
		h := startHour.SubHours(i).TimestampMilli()
		list[i].Hour = h
		key := cacheOnline + strconv.Itoa(int(h))
		valList := s.rdb.ZRange(ctx, key, 0, -1).Val()
		for _, s2 := range valList {
			if strings.HasPrefix(s2, "tour") {
				list[i].TourNum++
			}
			if strings.HasPrefix(s2, "user") {
				list[i].UserNum++
			}
			if strings.HasPrefix(s2, "buyer") {
				list[i].BuyerNum++
			}
		}

	}
	return list, nil
}

func (s onlineService) RecordEnterMini(userID string, num int) {
	s.rdb.Set(context.Background(), cacheOnlineNum, num, time.Hour)
}

func (s onlineService) GetOnlineNum() int {
	i, err := s.rdb.Get(context.Background(), cacheOnlineNum).Int()
	if err != nil {
		return 0
	}

	return i
}

type res struct {
	Hour     int64 `json:"hour"`
	TourNum  int   `json:"tour_num"`
	UserNum  int   `json:"user_num"`
	BuyerNum int   `json:"buyer_num"`
}
