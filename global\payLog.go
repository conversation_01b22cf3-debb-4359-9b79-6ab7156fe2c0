package global

import (
	"github.com/gin-gonic/gin"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"log"
	"os"
)

// PayLogger 支付日志
var PayLogger *zap.Logger

type LogConfig struct {
	Level      zapcore.Level `json:"level"`       // Level 最低日志等级，DEBUG<INFO<WARN<ERROR<FATAL 例如：info-->收集info等级以上的日志
	FileName   string        `json:"file_name"`   // FileName 日志文件位置
	MaxSize    int           `json:"max_size"`    // MaxSize 进行切割之前，日志文件的最大大小(MB为单位)，默认为100MB
	MaxAge     int           `json:"max_age"`     // MaxAge 是根据文件名中编码的时间戳保留旧日志文件的最大天数。
	MaxBackups int           `json:"max_backups"` // MaxBackups 是要保留的旧日志文件的最大数量。默认是保留所有旧的日志文件（尽管 MaxAge 可能仍会导致它们被删除。）
}

func InitZapLogger(file string, size int) *zap.Logger {
	if size < 10 {
		size = 10
	}
	lCfg := LogConfig{
		Level:      zapcore.InfoLevel,
		FileName:   file,
		MaxSize:    size,
		MaxAge:     7,
		MaxBackups: 0,
	}

	// 获取日志写入位置
	writeSyncer := getLogWriter(lCfg.FileName, lCfg.MaxSize, lCfg.MaxBackups, lCfg.MaxAge)
	// 获取日志编码格式
	encoder := getEncoder()
	var level = lCfg.Level

	// 获取日志最低等级，即>=该等级，才会被写入。
	var core zapcore.Core
	if gin.Mode() == gin.DebugMode {
		// 开发模式，日志输出到终端
		consoleEncoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
		// NewTee创建一个核心，将日志条目复制到两个或多个底层核心中。
		core = zapcore.NewTee(
			zapcore.NewCore(encoder, writeSyncer, zap.DebugLevel),
			zapcore.NewCore(consoleEncoder, zapcore.Lock(os.Stdout), zap.DebugLevel),
		)
	} else {
		// 标准库也输出到文件
		if file == "logs/sys.log" {
			log.SetOutput(writeSyncer)
		}
		core = zapcore.NewCore(encoder, writeSyncer, level)
	}
	return zap.New(core, zap.AddCaller())
}

// 负责设置 encoding 的日志格式
func getEncoder() zapcore.Encoder {
	// 获取一个指定的的EncoderConfig，进行自定义
	encodeConfig := zap.NewProductionEncoderConfig()

	// 设置每个日志条目使用的键。如果有任何键为空，则省略该条目的部分。

	// 序列化时间。eg: 2022-09-01T19:11:35.921+0800
	encodeConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	// "time":"2022-09-01T19:11:35.921+0800"
	encodeConfig.TimeKey = "time"
	//
	//encodeConfig.EncodeLevel = zapcore.LowercaseLevelEncoder
	//// 以 package/file:行 的格式 序列化调用程序，从完整路径中删除除最后一个目录外的所有目录。
	//encodeConfig.EncodeCaller = zapcore.ShortCallerEncoder
	//
	//encodeConfig.LineEnding = zapcore.DefaultLineEnding
	//encodeConfig.EncodeDuration = zapcore.MillisDurationEncoder
	return zapcore.NewJSONEncoder(encodeConfig)
	//return zapcore.NewConsoleEncoder(encodeConfig)
}

// 负责日志写入的位置
func getLogWriter(filename string, maxsize, maxBackup, maxAge int) zapcore.WriteSyncer {
	lumberJackLogger := &lumberjack.Logger{
		Filename:   filename,  // 文件位置
		MaxSize:    maxsize,   // 进行切割之前,日志文件的最大大小(MB为单位)
		MaxAge:     maxAge,    // 保留旧文件的最大天数
		MaxBackups: maxBackup, // 保留旧文件的最大个数
		Compress:   false,     // 是否压缩/归档旧文件
	}
	// AddSync 将 io.Writer 转换为 WriteSyncer。
	// 它试图变得智能：如果 io.Writer 的具体类型实现了 WriteSyncer，我们将使用现有的 Sync 方法。
	// 如果没有，我们将添加一个无操作同步。

	return zapcore.AddSync(lumberJackLogger)
}
