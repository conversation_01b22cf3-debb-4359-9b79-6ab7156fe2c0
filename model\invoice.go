package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type InvoiceStatusType int

const (
	InvoiceStatusTypeCanceled  InvoiceStatusType = 1
	InvoiceStatusTypeAuditing  InvoiceStatusType = 2
	InvoiceStatusTypeInvoicing InvoiceStatusType = 3
	InvoiceStatusTypeAuditFail InvoiceStatusType = 4
	InvoiceStatusTypeIssued    InvoiceStatusType = 5
)

// Invoice 发票
type Invoice struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`                                // 发票ID
	BuyerID          primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                     // 采购商ID
	InvoiceTitleType InvoiceTitleType   `json:"invoice_title_type" bson:"invoice_title_type"` // 发票抬头类型，可以是 "单位" 或 "个人"
	InvoiceTitle     string             `json:"invoice_title" bson:"invoice_title"`           // 发票抬头，如果是个人可以填写个人姓名
	TaxNumber        string             `json:"tax_number" bson:"tax_number"`                 // 纳税人识别号，也称税号或统一社会信用代码
	Address          string             `json:"address" bson:"address"`                       // 注册地址，购买方或销售方的地址
	PhoneNumber      string             `json:"phone_number" bson:"phone_number"`             // 注册电话，购买方或销售方的联系电话号码
	BankName         string             `json:"bank_name" bson:"bank_name"`                   // 开户银行，购买方或销售方的银行名称
	BankAccount      string             `json:"bank_account" bson:"bank_account"`             // 银行账户，购买方或销售方的银行账号
	ApplyNote        string             `json:"apply_note" bson:"apply_note"`                 // 申请备注
	Status           InvoiceStatusType  `json:"status" bson:"status"`                         // 审核状态   // 1 已取消 2 审核中 3 开票中 4 审核不通过 5 开票完成
	FailNote         string             `json:"fail_note" bson:"fail_note"`                   // 失败备注
	IssuedFile       string             `json:"issued_file" bson:"issued_file"`               // 开票文件
	OrderFile        string             `json:"order_file" bson:"order_file"`                 // 开票文件
	IssuedNote       string             `json:"issued_note" bson:"issued_note"`               // 开票备注
	IssuedAt         int64              `json:"issued_at" bson:"issued_at"`                   // 开票时间
	CreatedAt        int64              `json:"created_at" bson:"created_at"`                 // 申请时间
	UpdatedAt        int64              `json:"updated_at" bson:"updated_at"`
	DeletedAt        int64              `json:"deleted_at" bson:"deleted_at"`
}

type InvoiceTitleType int

const (
	InvoiceTitleTypePersonal InvoiceTitleType = 1
	InvoiceTitleTypeCompany  InvoiceTitleType = 2
)

// InvoiceTitle 发票抬头
type InvoiceTitle struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID          primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                     // 采购商ID
	InvoiceTitleType InvoiceTitleType   `json:"invoice_title_type" bson:"invoice_title_type"` // 发票抬头类型，可以是 "单位" 或 "个人"
	InvoiceTitle     string             `json:"invoice_title" bson:"invoice_title"`           // 发票抬头，如果是个人可以填写个人姓名
	TaxNumber        string             `json:"tax_number" bson:"tax_number"`                 // 纳税人识别号，也称税号或统一社会信用代码
	Address          string             `json:"address" bson:"address"`                       // 注册地址，购买方或销售方的地址
	PhoneNumber      string             `json:"phone_number" bson:"phone_number"`             // 注册电话，购买方或销售方的联系电话号码
	BankName         string             `json:"bank_name" bson:"bank_name"`                   // 开户银行，购买方或销售方的银行名称
	BankAccount      string             `json:"bank_account" bson:"bank_account"`             // 银行账户，购买方或销售方的银行账号
	Remark           string             `json:"remark" bson:"remark"`                         // 备注
	CreatedAt        int64              `json:"created_at" bson:"created_at"`                 // 创建时间
	UpdatedAt        int64              `json:"updated_at" bson:"updated_at"`                 // 更新时间
	DeletedAt        int64              `json:"deleted_at" bson:"deleted_at"`                 // 删除时间
}
