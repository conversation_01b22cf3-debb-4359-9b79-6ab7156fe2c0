package routeService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

// 缓存--干线
var cache = "routeByServicePoint:"

func get(r *redis.Client, id primitive.ObjectID) model.Route {
	key := cache + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.Route{}
		}
		var i model.Route
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.Route{}
		}
		return i
	}
	return model.Route{}
}

func set(r *redis.Client, info model.Route) {
	key := cache + info.ToServicePointID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().Error("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, time.Hour*24*30)
}

func del(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cache+id.Hex())
}
