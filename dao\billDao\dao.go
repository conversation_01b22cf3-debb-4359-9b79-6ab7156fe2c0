package billDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Bill) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Bill, error)
	List(ctx context.Context, filter bson.M) ([]model.Bill, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Bill, int64, error)
}

type billDao struct {
	db *mongo.Collection
}

// List 查询
func (s billDao) List(ctx context.Context, filter bson.M) ([]model.Bill, error) {
	var list []model.Bill
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s billDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Bill, int64, error) {
	var list []model.Bill
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s billDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s billDao) Create(ctx context.Context, data model.Bill) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s billDao) Get(ctx context.Context, filter bson.M) (model.Bill, error) {
	var data model.Bill
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Bill{}, err
	}
	return data, nil
}

func NewBillDao(collect string) DaoInt {
	return billDao{
		db: global.MDB.Collection(collect),
	}
}
