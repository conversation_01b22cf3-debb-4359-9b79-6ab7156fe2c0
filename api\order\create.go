package order

import (
	"base/core/xhttp"
	"base/global"
	"base/service/buyerService"
	"base/service/orderService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Create 创建订单
func Create(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()

	var req types.OrderCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	parentOrder, err := orderService.NewOrderService().Create(ctx, buyer, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, parentOrder)
}
