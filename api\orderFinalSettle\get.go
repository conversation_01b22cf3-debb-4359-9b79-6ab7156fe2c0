package orderFinalSettle

import (
	"base/core/xhttp"
	"base/service/orderFinalSettleService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// GetByOrderID 根据订单ID查询订单最终结算记录
func GetByOrderID(c *gin.Context) {
	var req struct {
		OrderID string `json:"order_id"`
	}

	err := xhttp.Parse(c, &req)
	if err != nil {
		return
	}

	// 转换ObjectID
	orderID, err := util.ConvertToObjectWithCtx(c, req.OrderID)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	// 查询订单最终结算记录
	result, err := orderFinalSettleService.NewService().GetByOrderID(c, orderID)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	xhttp.RespSuccess(c, result)
}
