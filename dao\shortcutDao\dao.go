package shortcutDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, swipe model.Shortcut) error
	Count(ctx context.Context) (int64, error)
	UpdateInfo(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Shortcut, int64, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.Shortcut, error)
	Get(ctx context.Context, filter bson.M) (model.Shortcut, error)
}

type shortcutDao struct {
	db *mongo.Collection
}

func NewShortcutDao(collect string) DaoInt {
	return shortcutDao{
		db: global.MDB.Collection(collect),
	}
}
func (s shortcutDao) Get(ctx context.Context, filter bson.M) (model.Shortcut, error) {
	var data model.Shortcut
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Shortcut{}, err
	}
	return data, nil
}

func (s shortcutDao) Create(ctx context.Context, swipe model.Shortcut) error {
	_, err := s.db.InsertOne(ctx, swipe)
	if err != nil {
		return err
	}

	return nil
}

func (s shortcutDao) Count(ctx context.Context) (int64, error) {
	count, err := s.db.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s shortcutDao) UpdateInfo(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s shortcutDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s shortcutDao) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := s.db.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (s shortcutDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Shortcut, int64, error) {
	var list []model.Shortcut
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts.SetSort(sort)
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s shortcutDao) ListByCus(ctx context.Context, filter bson.M) ([]model.Shortcut, error) {
	var list []model.Shortcut
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}
