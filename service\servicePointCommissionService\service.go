package servicePointCommissionService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/servicePointCommissionDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ServiceInterface 服务点佣金
type ServiceInterface interface {
	Upsert(ctx context.Context, data model.ServicePointCommission) error
	GetByServicePoint(servicePointID primitive.ObjectID) (model.ServicePointCommission, error)
	List(filter bson.M) ([]model.ServicePointCommission, error)
}

type servicePointCommissionService struct {
	rdb                       *redis.Client
	servicePointCommissionDao servicePointCommissionDao.DaoInt
}

func (s servicePointCommissionService) Upsert(ctx context.Context, data model.ServicePointCommission) error {
	if data.PointPercent < 0 || data.PointPercent > 100 {
		return xerr.NewErr(xerr.ErrParamError, nil, "佣金数值不合理,需在[0-100]区间")
	}
	if data.WarehousePercent < 0 || data.WarehousePercent > 100 {
		return xerr.NewErr(xerr.ErrParamError, nil, "佣金数值不合理,需在[0-100]区间")
	}
	if data.PlatformPercent < 0 || data.PlatformPercent > 100 {
		return xerr.NewErr(xerr.ErrParamError, nil, "佣金数值不合理,需在[0-100]区间")
	}
	if data.WarehousePercent+data.PlatformPercent+data.PointPercent != 100 {
		return xerr.NewErr(xerr.ErrParamError, nil, "佣金数值错误，平台，服务点，集中仓相加等于100")
	}
	err := s.servicePointCommissionDao.Upsert(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s servicePointCommissionService) GetByServicePoint(servicePointID primitive.ObjectID) (model.ServicePointCommission, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
	}
	partnerCommission, err := s.servicePointCommissionDao.Get(context.Background(), filter)
	if err != nil {
		return model.ServicePointCommission{}, err
	}
	return partnerCommission, nil
}

func (s servicePointCommissionService) List(filter bson.M) ([]model.ServicePointCommission, error) {
	list, err := s.servicePointCommissionDao.Find(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func NewPartnerCommissionService() ServiceInterface {
	return servicePointCommissionService{
		rdb:                       global.RDBDefault,
		servicePointCommissionDao: dao.ServicePointCommissionDao,
	}
}
