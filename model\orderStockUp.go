package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// OrderStockUp 备货单
type OrderStockUp struct {
	ID                      primitive.ObjectID   `json:"id" bson:"_id"`
	SupplierID              primitive.ObjectID   `json:"supplier_id" bson:"supplier_id"`           // 供应商ID
	SupplierName            string               `json:"supplier_name" bson:"supplier_name"`       // 供应商名称
	ProductID               primitive.ObjectID   `json:"product_id" bson:"product_id"`             // 商品ID
	CategoryIDs             []primitive.ObjectID `bson:"category_ids" json:"category_ids"`         // 商品分类信息
	PerRoughWeight          int                  `json:"per_rough_weight" bson:"per_rough_weight"` // 商品毛重
	IsCheckWeight           bool                 `json:"is_check_weight" bson:"is_check_weight"`   // 分拣检查重量  是/否
	HasParam                bool                 `json:"has_param" bson:"has_param"`               // 规格参数 有/无
	ProductParamType        ProductParamType     `json:"product_param_type" bson:"product_param_type"`
	StandardAttr            StandardAttr         `json:"standard_attr" bson:"standard_attr"`                           // 标品参数
	NonStandardAttr         NonStandardAttr      `json:"non_standard_attr" bson:"non_standard_attr"`                   // 非标品参数
	ProductTitle            string               `json:"product_title" bson:"product_title"`                           // 商品标题
	ProductCover            FileInfo             `json:"product_cover" bson:"product_cover"`                           // 商品封面
	OrderList               []PerOrder           `json:"order_list" bson:"order_list"`                                 // 订单列表
	StockUpDueNum           int                  `json:"stock_up_due_num" bson:"stock_up_due_num"`                     // 应备货数量
	StockUpHasNum           int                  `json:"stock_up_has_num" bson:"stock_up_has_num"`                     // 已经备货
	StockUpHas              bool                 `json:"stock_up_has" bson:"stock_up_has"`                             // 是否备货
	QualityServicePointList []PerServicePoint    `json:"quality_service_point_list" bson:"quality_service_point_list"` // 品控服务点列表
	QualityHas              bool                 `json:"quality_has" bson:"quality_has"`                               // 是否品控
	//SortHas                 bool                 `json:"sort_has" bson:"sort_has"`                                     // 是否分拣
	No        int64 `json:"no" bson:"no"` // 批次 到小时毫秒时间戳
	CreatedAt int64 `bson:"created_at" json:"created_at"`
	UpdatedAt int64 `bson:"updated_at" json:"updated_at"`
	DeletedAt int64 `bson:"deleted_at" json:"deleted_at"`
}

//QualityHasNum           int                `json:"quality_has_num" bson:"quality_has_num"`                       // 品控入库数

type PerOrder struct {
	OrderID          primitive.ObjectID `json:"order_id" bson:"order_id"`
	BuyerId          primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	BuyerName        string             `json:"buyer_name" bson:"buyer_name"`
	Address          OrderAddress       `json:"address" bson:"address"`                       // 地址
	ServicePointName string             `json:"service_point_name" bson:"service_point_name"` // 服务点名称
	WarehouseName    string             `json:"warehouse_name" bson:"warehouse_name"`         // 集中仓名称
	WarehouseID      primitive.ObjectID `json:"warehouse_id" bson:"warehouse_id"`             // 集中仓ID
	ServicePointID   primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`     // 服务点ID
	DueNum           int                `json:"due_num" bson:"due_num"`                       // 应有数量
	DueWeight        int                `json:"due_weight" bson:"due_weight"`                 // 应有重量
	QualityNum       int                `json:"quality_num" bson:"quality_num"`               // 品控入库数
	SortNum          int                `json:"sort_num" bson:"sort_num"`                     // 分拣数量
	SortWeight       int                `json:"sort_weight" bson:"sort_weight"`               // 分拣总重
	SortHas          bool               `json:"sort_has" bson:"sort_has"`                     // 是否分拣
	HasShip          bool               `json:"has_ship" bson:"has_ship"`                     // 是否已经发货-发货后，不能再更新
}

type PerServicePoint struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`     // 服务点ID
	ServicePointName string             `json:"service_point_name" bson:"service_point_name"` // 服务点名称
	QualityDueNum    int                `json:"quality_due_num" bson:"quality_due_num"`       // 应品控入库数
	QualityNum       int                `json:"quality_num" bson:"quality_num"`               // 品控入库数
}

type StockUpToDo struct {
	OrderID          primitive.ObjectID `json:"order_id"`               // 订单ID
	Address          OrderAddress       `json:"address" bson:"address"` // 地址
	ServicePointID   primitive.ObjectID `json:"service_point_id"`       // 服务点ID
	ServicePointName string             `json:"service_point_name"`     // 服务点名称
	OrderCreateTime  int64              `json:"order_create_time"`      // 下单时间
	ProductList      []PerProduct       `json:"product_list"`           // 商品列表
}

type PerProduct struct {
	ProductID       primitive.ObjectID `json:"product_id"`
	ProductTitle    string             `json:"product_title"`                              // 商品标题
	ProductCover    FileInfo           `json:"product_cover"`                              // 商品封面
	StandardAttr    StandardAttr       `json:"standard_attr" bson:"standard_attr"`         // 标品参数
	NonStandardAttr NonStandardAttr    `json:"non_standard_attr" bson:"non_standard_attr"` // 非标品参数
	Num             int                `json:"num"`                                        // 数量
}

type StockUpInfo struct {
	ProductID     primitive.ObjectID `json:"product_id"`       // 商品ID
	OrderIDList   []PerOrder         `json:"order_id_list"`    // 订单ID列表
	ProductTitle  string             `json:"product_title"`    // 商品标题
	ProductCover  FileInfo           `json:"product_cover"`    // 商品封面
	StockUpDueNum int                `json:"stock_up_due_num"` // 应有备货
	StockUpHasNum int                `json:"stock_up_has_num"` // 已经备货
}
