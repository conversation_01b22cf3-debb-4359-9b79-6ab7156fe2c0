package productAuditService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/productAuditDao"
	"base/global"
	"base/model"
	"base/service/categoryService"
	"base/service/messageService"
	"base/service/productCommissionService"
	"base/service/supplierService"
	"base/service/userService"
	"base/service/warehouseService"
	"base/util"
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 商品审核服务接口
type ServiceInterface interface {
	Create(ctx context.Context, product model.Product, isNew bool) error
	Get(ctx context.Context, id primitive.ObjectID) (model.ProductAudit, error)
	ListBySupplier(ctx context.Context, id primitive.ObjectID) ([]model.ProductAudit, error)
	List(ctx context.Context, status model.AuditStatusType, page, limit int64) ([]model.ProductAudit, int64, error)
	Update(ctx context.Context, audit model.ProductAudit, status model.AuditStatusType, failReason string) error
	UpdateFromAudit(ctx context.Context, auditID primitive.ObjectID, product model.Product) error
	CheckHistoryRecord(ctx context.Context, id primitive.ObjectID) error
	CountAudit(ctx context.Context) (int64, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
}

// productAuditService 商品审核服务
type productAuditService struct {
	rdb                *redis.Client
	categoryS          categoryService.ServiceInterface
	productAuditDao    productAuditDao.DaoInt
	supplierS          supplierService.ServiceInterface
	warehouseS         warehouseService.ServiceInterface
	msg                messageService.ServiceInterface
	productCommissionS productCommissionService.ServiceInterface
	userS              userService.ServiceInterface
}

// NewProductAuditService 商品审核服务
func NewProductAuditService() ServiceInterface {
	return productAuditService{
		rdb:                global.RDBDefault,
		categoryS:          categoryService.NewCategoryService(),
		productAuditDao:    dao.ProductAuditDao,
		supplierS:          supplierService.NewSupplierService(),
		warehouseS:         warehouseService.NewWarehouseServiceService(),
		msg:                messageService.NewMessageService(),
		productCommissionS: productCommissionService.NewProductCommissionService(),
		userS:              userService.NewUserService(),
	}
}

func (s productAuditService) Create(ctx context.Context, product model.Product, isNew bool) error {
	auditing, err := s.GetAuditingByProductID(ctx, product.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	now := time.Now().UnixMilli()

	if auditing.ID != primitive.NilObjectID {
		update := bson.M{
			"product":         product,
			"edit_updated_at": now,
		}
		err = s.productAuditDao.Update(ctx, bson.M{"_id": auditing.ID}, bson.M{"$set": update})
		if err != nil {
			return err
		}
		return nil
	}

	data := model.ProductAudit{
		ID:              primitive.NewObjectID(),
		Product:         product,
		IsNew:           isNew,
		EditAuditStatus: model.AuditStatusTypeDoing,
		EditFailReason:  "",
		EditCreatedAt:   now,
		EditUpdatedAt:   now,
	}

	err = s.productAuditDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s productAuditService) Get(ctx context.Context, id primitive.ObjectID) (model.ProductAudit, error) {
	filter := bson.M{
		"_id": id,
	}
	apply, err := s.productAuditDao.Get(ctx, filter)
	if err != nil {
		return model.ProductAudit{}, err
	}
	return apply, nil
}

func (s productAuditService) GetAuditingByProductID(ctx context.Context, productID primitive.ObjectID) (model.ProductAudit, error) {
	filter := bson.M{
		"product._id":       productID,
		"edit_audit_status": model.AuditStatusTypeDoing,
	}
	apply, err := s.productAuditDao.Get(ctx, filter)
	if err != nil {
		return model.ProductAudit{}, err
	}
	return apply, nil
}

// 校验分类是否存在
func (s productAuditService) verifyCategory(ctx context.Context, ids []string) ([]primitive.ObjectID, error) {
	if len(ids) != 3 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "分类信息缺失")
	}
	id, err := util.ConvertToObject(ids[2])
	if err != nil {
		return nil, err
	}

	info, err := s.categoryS.CheckExistByThird(ctx, id)
	if err != nil {
		return nil, err
	}
	var res []primitive.ObjectID
	for _, i := range info.Path {
		res = append(res, i)
	}

	res = append(res, info.ID)
	return res, nil
}

// ListBySupplier 根据供应商ID查询商品审核列表
func (s productAuditService) ListBySupplier(ctx context.Context, id primitive.ObjectID) ([]model.ProductAudit, error) {
	filter := bson.M{
		"product.supplier_id": id,
		"edit_audit_status": bson.M{
			"$in": []model.AuditStatusType{model.AuditStatusTypeDoing, model.AuditStatusTypeNotPass},
		},
		"edit_deleted_at": 0,
	}

	list, err := s.productAuditDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// List 查询商品审核列表
func (s productAuditService) List(ctx context.Context, status model.AuditStatusType, page, limit int64) ([]model.ProductAudit, int64, error) {
	filter := bson.M{}

	if status == model.AuditStatusTypeDoing {
		filter["edit_audit_status"] = model.AuditStatusTypeDoing
	}

	list, count, err := s.productAuditDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

// 审核数量
func (s productAuditService) CountAudit(ctx context.Context) (int64, error) {
	filter := bson.M{
		"edit_audit_status": model.AuditStatusTypeDoing,
	}
	count, err := s.productAuditDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// Update 审核商品
func (s productAuditService) Update(ctx context.Context, audit model.ProductAudit, status model.AuditStatusType, failReason string) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"edit_audit_status": status,
		"edit_updated_at":   milli,
	}

	update["edit_fail_reason"] = failReason

	err := s.productAuditDao.Update(ctx, bson.M{"_id": audit.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s productAuditService) UpdateFromAudit(ctx context.Context, auditID primitive.ObjectID, product model.Product) error {
	filter := bson.M{
		"_id": auditID,
	}
	update := bson.M{
		"product":           product,
		"edit_updated_at":   time.Now().UnixMilli(),
		"edit_audit_status": model.AuditStatusTypeDoing,
		"edit_fail_reason":  "",
	}
	err := s.productAuditDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

// CheckHistoryRecord 检查同商品，未审核通过记录
func (s productAuditService) CheckHistoryRecord(ctx context.Context, id primitive.ObjectID) error {
	// 查询该商品审核记录
	productAudit, err := s.productAuditDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}
	// 查询同商品未审核通过的记录
	filter := bson.M{
		"product._id":       productAudit.Product.ID,
		"edit_audit_status": model.AuditStatusTypeNotPass,
		"deleted_at":        bson.M{"$ne": 0}, // 排除当前记录
	}

	// 软删除未审核通过的记录
	softDeleteUpdate := bson.M{
		"deleted_at": time.Now().UnixMilli(),
	}
	err = s.productAuditDao.UpdateMany(ctx, filter, bson.M{"$set": softDeleteUpdate})
	if err != nil {
		return err
	}
	return nil
}

// Delete 软删除商品审核记录
func (s productAuditService) Delete(ctx context.Context, id primitive.ObjectID) error {
	// 检查记录是否存在
	audit, err := s.productAuditDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	// 检查是否已经被删除
	if audit.EditDeletedAt != 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "商品审核记录已被删除")
	}
	// 仅审核不通过的记录可以删除
	if audit.EditAuditStatus != model.AuditStatusTypeNotPass {
		return xerr.NewErr(xerr.ErrParamError, nil, "仅审核不通过的记录可以删除")
	}

	// 执行软删除
	update := bson.M{
		"edit_deleted_at": time.Now().UnixMilli(),
	}

	err = s.productAuditDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}
