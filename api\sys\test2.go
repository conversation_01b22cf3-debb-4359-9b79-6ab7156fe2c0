package sys

import (
	"base/core/xhttp"
	"base/dao"
	"base/dao/buyerDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/adminService"
	"base/service/authenticationService"
	"base/service/buyerBalanceOrderService"
	"base/service/buyerService"
	"base/service/indexPartService"
	"base/service/integralAccountService"
	"base/service/integralOrderService"
	"base/service/messageService"
	"base/service/orderAgentPayService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/service/productService"
	"base/service/servicePointService"
	"base/service/shortcutService"
	"base/service/supplierService"
	"base/service/trackService"
	"base/service/userAddrService"
	"base/service/userService"
	"base/service/yeeMerchantService"
	"base/util"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	pays "github.com/cnbattle/allinpay/service"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func Test2(ctx *gin.Context) {
	var req = struct {
		P1 string  `json:"p1"`
		P2 string  `json:"p2"`
		P3 int     `json:"p3"`
		P4 int64   `json:"p4"`
		P5 float64 `json:"p5"`
		P6 float64 `json:"p6"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//updateOneAddr(ctx)
	//dealKunmingAddr(ctx)
	//dealAddr(ctx)

	//batchDeal(ctx)
	//dealOneFee(ctx)

	// 发布后
	//initBuyerInfo(ctx)

	//initBuyerInfo(ctx)

	//dealSubsidyAmount(ctx)
	//findSubsidyAmount(ctx)
	//dealSubsidyAmount(ctx)
	//
	//agentPayDebt(ctx)

	//listAll(ctx)

	//dealProductBuyLimit(ctx)

	//dealProductDiscountList(ctx)

	//dealSupplierServiceFee(ctx)

	//dealSupplierServicePointID(ctx)

	//dealAddressPointID(ctx)

	//dealCartPointID(ctx)

	//s := dealPriceChange(2500, 0)
	//zap.S().Infof("value:%d", s)

	//dealPointAdmin(ctx)

	//dealShortcut(ctx)
	//dealIndexPart(ctx)
	//dealPromote(ctx)
	//dealProductCollect(ctx)

	//dealServiceFee(ctx)

	//dealOrderServiceFee(ctx)

	//dealOriginBuyerInfo(ctx)

	//dealTrack(ctx)

	//dealTrackLeaveAt(ctx)

	//dealDeliverNote(ctx)

	//testAgentPay(ctx)

	//refundSplit(ctx)

	//statsAgentPay(ctx)

	//dealAgentPayDeliver(ctx)

	//testAgentPay(ctx)

	//dealServiceFee2(ctx)

	//dealKunmingAddr222(ctx)

	//initBuyerByUser(ctx)

	//initServicePointPart(ctx)

	//dealRetailRefund(ctx)
	//
	//realOrderType(ctx)

	//dealSupplierProduct(ctx)

	//dealAddrUserType(ctx)

	//dealTrack2(ctx)

	//dealBuyer2(ctx)

	//dealSupplierName(ctx)

	//dealAccountStatus(ctx)

	//dealDebt(ctx)

	//dealIntegral(ctx)

	//initOverWeight(ctx)

	//dealDeliverNote(ctx)

	//dealAddress2(ctx)

	//dealCartStationID(ctx)

	//dealSupplierPoint(ctx)

	//dealUploadShip(ctx)
	//balanceSupplier(ctx)
	//balanceBuyer(ctx)
	//balanceOrder(ctx)
	//balanceParentOrder(ctx)
	//balanceDebtOrder(ctx)

	//dealQuality(ctx)
	//dealOrderSupplierLevel(ctx)
	//testBankOpen(ctx)
	//initSupplierMerchant(ctx, req.P1, req.P2)

	//xhttp.RespSuccess(ctx, d)

	//messageService.NewCaptchaService().SendServiceFeeRebate("***********", *************, 231)

	//initSupplierMerchant(ctx)

	//testMoney(ctx)

	//DoRefundYee(ctx)
	//testYee(ctx)

	//agentPayDebt(ctx)
	//testInitPointScope(ctx)

	//PayWarning("订单支付", "测试短信")

	//initBuyerMobileByUser(ctx)
	//initProductServiceFee(ctx)
	//undoProductServiceFee(ctx)
	//initOrderPoint(ctx)

	//dealIntegralBuyerID(ctx)
	//dealAdminMobile(ctx)

	//initBuyerServiceFee(ctx)

	//dealOrderDivideStatus(ctx)
	//testP(ctx)
	//testP2(ctx)
	//testOrderSettle(ctx, req.P1)
	//testDebtOrder(ctx)

	//buyerID, _ := primitive.ObjectIDFromHex("673d71c535f0cac6d9ee2f51")
	//
	//filter := bson.M{
	//	//"has_agent_pay":    false,
	//	//"order_refund_all": false,
	//	//"order_status": bson.M{
	//	//	"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
	//	//},
	//	"created_at": bson.M{
	//		//"$gte": 1685548800000, // 2023-06-1 00:00:00
	//		//"$lte": 1696089600000, // 2023-10-1 00:00:00
	//
	//		//"$gte": 1696089600000, // 2023-10-1 00:00:00
	//		//"$lte": 1704038400000, // 2024-1-1 00:00:00
	//
	//		"$gte": 1704038400000, // 2024-1-1 00:00:00
	//		"$lte": 1709222400000, // 2024-3-1 00:00:00
	//
	//		//"$gte": 1711900800000, // 2024-4-1 00:00:00
	//		//"$lte": 1714492800000, // 2024-5-1 00:00:00
	//
	//		//"$gte": 1714492800000, // 2024-5-1 00:00:00
	//		//"$lte": 1717171200000, // 2024-6-1 00:00:00
	//
	//		//"$gte": 1717171200000, // 2024-6-1 00:00:00
	//		//"$lte": 1722441600000, // 2024-8-1 00:00:00
	//
	//		//"$gte": 1722441600000, // 2024-8-1 00:00:00
	//		//"$lte": 1725120000000, // 2024-9-1 00:00:00
	//
	//		//"$gte": 1725120000000, // 2024-9-1 00:00:00
	//		//"$lte": *************, // 2024-10-1 00:00:00
	//
	//		//"$gte": *************, // 2024-10-1 00:00:00
	//		//"$lte": 1730390400000, // 2024-11-1 00:00:00
	//
	//		//"$gte": 1730390400000, // 2024-11-1 00:00:00
	//		//"$lte": 1732982400000, // 2024-12-1 00:00:00
	//
	//		//"$gte": 1732982400000, // 2024-12-1 00:00:00
	//		//"$lte": 1738339200000, // 2025-02-01 00:00:00
	//	},
	//	//"buyer_id": buyerID,
	//}
	//
	//orders, err := orderService.NewOrderService().List(ctx, filter)
	//if err != nil {
	//	return
	//}
	//
	//for i, order := range orders {
	//	testOrderRefundAndDebt(ctx, order)
	//	zap.S().Infof("处理订单：%d", i)
	//}

	//backRefundSupplyAmount(358, 350, 8000)
	//backRefundSupplyAmount(358, 350, 8000)

	//testDebt(ctx)
	//testDebtCreatedAt(ctx)
	//tempDealRefund(ctx)
	//testOrderSettle(ctx, req.P1)

}

func testDebtCreatedAt(ctx context.Context) {
	d := dao.OrderDebtDao
	//buyerID, _ := primitive.ObjectIDFromHex("673d71c535f0cac6d9ee2f51")

	debt, err := d.List(ctx, bson.M{
		"total_product_amount": 0,
	})
	if err != nil {
		return
	}

	for index, per := range debt {
		refunds, err := orderRefundService.NewOrderRefundService().ListShipRefundRefund(ctx, per.OrderID)
		if err != nil {
			return
		}
		_ = refunds
		_ = per
		zap.S().Infof("更新：%d", index)

		t := refunds[0].CreatedAt

		update := bson.M{
			"created_at": t,
			"updated_at": t,
		}

		d.UpdateOne(ctx, bson.M{
			"_id": per.ID,
		}, bson.M{
			"$set": update,
		})

	}
}

//
//func testDebt(ctx context.Context) {
//	d := dao.OrderDebtDao
//	//buyerID, _ := primitive.ObjectIDFromHex("673d71c535f0cac6d9ee2f51")
//
//	debt, err := d.List(ctx, bson.M{
//		//"buyer_id": buyerID,
//	})
//	if err != nil {
//		return
//	}
//
//	var count int
//	for _, per := range debt {
//		if len(per.ProductList) == 0 && per.RefundTotalProductAmount == 0 {
//			count++
//			format := time.UnixMilli(per.CreatedAt).Format(time.RFC3339)
//			zap.S().Infof("异常：%d,时间：%s", count, format)
//		}
//		//d.DeleteOne(ctx, bson.M{
//		//	"_id": per.ID,
//		//	//"buyer_id": buyerID,
//		//})
//
//	}
//	zap.S().Infof("异常：%d", count)
//}

func testOrderRefundAndDebt(ctx context.Context, order model.Order) {
	_ = ctx

	orderID := order.ID

	refunds, err := orderRefundService.NewOrderRefundService().ListShipRefundRefund(ctx, orderID)
	if err != nil {
		return
	}
	_ = refunds
	d := dao.OrderDebtDao

	debt, err := d.Get(ctx, bson.M{
		"order_id": orderID,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return
	}

	if len(refunds) == 0 {
		zap.S().Infof("跳过")
		return
	}

	var toCreate = false
	if debt.ID == primitive.NilObjectID {
		debt = model.OrderDebt{
			ID:               primitive.NewObjectID(),
			OrderID:          order.ID,
			ParentOrderID:    order.ParentOrderID,
			BuyerID:          order.BuyerID,
			BuyerName:        order.BuyerName,
			PayStatus:        model.PayStatusTypePaid,
			SupplierID:       order.SupplierID,
			SupplierName:     order.SupplierName,
			ServicePointID:   order.ServicePointID,
			ServicePointName: order.ServicePointName,
			ExpireAt:         0,
		}

		toCreate = true
	}

	var refundTotalProductAmount int
	var refundUnitTransportFee int
	var refundTotalTransportFee int
	var refundTotalServiceFee int
	var refundProductList []model.ProductQuality

	for _, refund := range refunds {
		per := model.ProductQuality{
			ProductID:                     refund.ProductID,
			ProductImageID:                refund.ProductImageID,
			ProductTitle:                  refund.ProductTitle,
			ProductCover:                  refund.ProductCover,
			IsCheckWeight:                 refund.IsCheckWeight,
			Price:                         refund.Price,
			Num:                           refund.Num,
			RoughWeight:                   refund.RoughWeight,
			OutWeight:                     refund.OutWeight,
			NetWeight:                     refund.NetWeight,
			ProductRoughWeightUnitPriceKG: refund.ProductRoughWeightUnitPriceKG,
			SortWeight:                    refund.SortWeight,
			SortNum:                       refund.SortNum,
			ProductAmount:                 refund.Amount,
			RefundWeight:                  refund.RefundWeight,
			UnitTransportFee:              refund.UnitTransportFee,
			TotalTransportFee:             refund.TotalTransportFee,
			TotalServiceFee:               refund.TotalServiceFee,
			TotalWarehouseLoadFee:         refund.TotalWarehouseLoadFee,
		}
		refundProductList = append(refundProductList, per)

		refundTotalProductAmount += per.ProductAmount
		refundUnitTransportFee = per.UnitTransportFee

		refundTotalTransportFee += per.TotalTransportFee
		refundTotalServiceFee += per.TotalServiceFee

		if debt.CreatedAt == 0 {
			debt.CreatedAt = refund.CreatedAt
			debt.UpdatedAt = refund.UpdatedAt
		}
	}

	update := bson.M{
		//"offset_product_amount": 0,
		//"paid_product_amount":   debt.TotalProductAmount,
	}

	if refundTotalProductAmount > 0 {
		if toCreate {
			err = d.Create(ctx, debt)
			if err != nil {
				return
			}
		}

		final := refundTotalProductAmount + refundTotalTransportFee + refundTotalServiceFee

		update["refund_product_list"] = refundProductList
		update["refund_total_product_amount"] = refundTotalProductAmount
		update["refund_unit_transport_fee"] = refundUnitTransportFee
		update["refund_total_transport_fee"] = refundTotalTransportFee
		update["refund_total_service_fee"] = refundTotalServiceFee
		update["refund_final_amount"] = final

	}

	if refundTotalProductAmount > 0 || debt.TotalProductAmount > 0 {
		d.UpdateOne(ctx, bson.M{
			"order_id": orderID,
		}, bson.M{
			"$set": update,
		})
	}

}

func testDebtOrder(ctx context.Context) {
	_ = ctx
	filter := bson.M{
		"created_at": bson.M{
			//"$gte": 1685548800000, // 2023-06-1 00:00:00
			//"$lte": 1696089600000, // 2023-10-1 00:00:00

			//"$gte": 1696089600000, // 2023-10-1 00:00:00
			//"$lte": 1704038400000, // 2024-1-1 00:00:00

			//"$gte": 1704038400000, // 2024-1-1 00:00:00
			//"$lte": 1717171200000, // 2024-6-1 00:00:00

			//"$gte": 1717171200000, //  2024-6-1 00:00:00
			//"$lte": 1730390400000, // 2024-11-1 00:00:00

			"$gte": 1730390400000, // 2024-11-1 00:00:00
			"$lte": 1738339200000, // 2025-02-01 00:00:00
		},
		//"buyer_id": buyerID,
	}

	d := dao.OrderDebtDao

	debtList, err := d.List(ctx, filter)
	if err != nil {
		return
	}

	for i, debt := range debtList {
		if debt.PaidProductAmount != 0 {
			continue
		}
		update := bson.M{
			"offset_product_amount": 0,
			"paid_product_amount":   debt.TotalProductAmount,
		}

		d.UpdateOne(ctx, bson.M{
			"_id": debt.ID,
		}, bson.M{
			"$set": update,
		})
		zap.S().Infof("更新：%d", i)
	}

}

func testP2(ctx context.Context) {
	//orders, err := orderService.NewOrderService().List(ctx, bson.M{
	//	"created_at": bson.M{
	//		"$gte": *************, // 2024-10-1 00:00:00
	//	},
	//})
	//if err != nil {
	//	return
	//}
	//
	//d := dao.OrderDao
	//_ = d
	//
	//var ids []primitive.ObjectID
	//
	////var buyerIDs []primitive.ObjectID
	//
	//for _, order := range orders {
	//	if order.UserType != "" {
	//		continue
	//	}
	//
	//	//if order.UserType == "" {
	//	//	zap.S().Infof("type:%s", order.ID.Hex())
	//	//	//buyerIDs = append(buyerIDs, order.BuyerID)
	//	//}
	//
	//	if order.UserType == "" {
	//		ids = append(ids, order.ID)
	//	}
	//}

	//cus, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
	//	"_id": bson.M{
	//		"$in": ids,
	//	},
	//})
	//if err != nil {
	//	return
	//}
	//for _, buyer := range cus {
	//	if buyer.UserType == model.UserTypeYHT {
	//		zap.S().Errorf("存在益禾堂客户：%s", buyer.ID.Hex())
	//	}
	//}

	//d.UpdateMany(ctx, bson.M{
	//	"_id": bson.M{
	//		"$in": ids,
	//	},
	//}, bson.M{
	//	"$set": bson.M{
	//		"user_type": model.UserTypeNormal,
	//	},
	//})

}

func testP(ctx context.Context) {
	cus, i, err := productService.NewProductService().ListByCus(ctx, bson.M{}, 1, 5000)
	if err != nil {
		return
	}
	_ = cus
	_ = i

	for _, product := range cus {
		v := product.AttrInfo

		marshal, err := json.Marshal(v)
		if err != nil {
			return
		}

		var l []any

		err = json.Unmarshal(marshal, &l)
		if err != nil {
			return
		}
		//zap.S().Infof("长度：%d", len(l))

		if len(l) < 3 {
			zap.S().Infof("内容：%s", string(marshal))
			zap.S().Errorf("类型错误：%s", product.ID.Hex())
		}
	}
}

func testDeliverDivide(ctx context.Context) {
	//
	//id, _ := primitive.ObjectIDFromHex("675ae1f4468d0c72d32a3e7e")
	//
	//queueProduceService.NewQueueProduceService().SendDeliverFeeDivide(ctx, id)
}
func dealOrderDivideStatus(ctx context.Context) {
	filter := bson.M{
		"has_agent_pay":    false,
		"order_refund_all": false,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"created_at": bson.M{
			"$gte": 1732696200000, // 2024-11-27 16:30:00
		},
		//"supplier_id": supplierID,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		return
	}

	for _, order := range orders {
		agentPay, err := orderAgentPayService.NewOrderAgentPayService().GetOrderByOrder(ctx, order.ID)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return
		}

		if agentPay.ID != primitive.NilObjectID {

			zap.S().Infof("存在分账：%s", order.ID.Hex())

			orderService.NewOrderService().UpdateOne(ctx, bson.M{
				"_id": order.ID,
			}, bson.M{
				"$set": bson.M{
					"has_agent_pay": true,
				},
			})
		}
	}

}

func backRefundSupplyAmount(price, supplyPrice, finalAmount int) int {
	if price == supplyPrice || supplyPrice == 0 {
		return finalAmount
	}
	diff := price - supplyPrice
	diffDe := decimal.NewFromInt(int64(diff))

	supplyPriceDe := decimal.NewFromInt(int64(supplyPrice))

	allDe := decimal.NewFromInt(1)
	serviceFeeDe := diffDe.Div(supplyPriceDe)

	supplyPercentDe := allDe.Sub(serviceFeeDe)

	finalAmountDe := decimal.NewFromInt(int64(finalAmount))

	refundSupplyDe := finalAmountDe.Mul(supplyPercentDe).Round(0)

	// 供应商
	refundSupply := int(refundSupplyDe.IntPart())

	return refundSupply
}

func initOrderPoint(ctx context.Context) {
	d := dao.OrderDao

	id, _ := primitive.ObjectIDFromHex("675b924d8d04c139ec7e82d3")
	err := d.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": bson.M{
			"second_point_id": id,
		},
	})
	if err != nil {

	}

}

func initBuyerServiceFee(ctx context.Context) {
	//d := buyerDao.NewBuyerDao()
	//err := d.UpdateMany(ctx, bson.M{
	//	"service_fee": bson.M{
	//		"$gte": 3,
	//	},
	//}, bson.M{
	//	"$set": bson.M{
	//		"service_fee_type": model.ServiceFeeTypeTwo,
	//	},
	//})
	//if err != nil {
	//}

	//buyers, err := d.ListByCus(ctx, bson.M{
	//	"user_type": model.UserTypeYHT,
	//})
	//if err != nil {
	//	return
	//}
	//
	//for _, buyer := range buyers {
	//	_ = buyer
	//	if buyer.UserType == model.UserTypeYHT && buyer.ServiceFeeType != model.ServiceFeeTypeNone {
	//		zap.S().Errorf("用户 ：%s", buyer.ID.Hex())
	//		d.Update(ctx, bson.M{
	//			"_id": buyer.ID,
	//		}, bson.M{
	//			"$set": bson.M{
	//				"service_fee_type": model.ServiceFeeTypeNone,
	//			},
	//		})
	//	}

	//if buyer.ServiceFeeType == "" {
	//	d.Update(ctx, bson.M{
	//		"_id": buyer.ID,
	//	}, bson.M{
	//		"$set": bson.M{
	//			"service_fee_type": model.ServiceFeeTypeOne,
	//		},
	//	})
	//	zap.S().Errorf("服务费类型:%d", i)
	//	i++
	//}

	//}

	//err = d.UpdateMany(ctx, bson.M{
	//	"service_fee": 0,
	//}, bson.M{
	//	"$set": bson.M{
	//		"service_fee_type": model.ServiceFeeTypeOne,
	//	},
	//})
	//if err != nil {
	//
	//}

}

func undoProductServiceFee(ctx context.Context) {
	d := dao.ProductDao

	//id, _ := primitive.ObjectIDFromHex("6481ef7527b29d1c3e17c056")
	list, _, err := d.ListByPage(ctx, bson.M{
		//"supplier_id": id,
	}, 1, 5000)
	if err != nil {
		return
	}

	for i, per := range list {
		_ = i
		supplyPrice := per.SupplyPrice

		err = d.Update(ctx, bson.M{
			"_id": per.ID,
		}, bson.M{
			"$set": bson.M{
				"price": supplyPrice,
			},
		})
		if err != nil {

		}

		if supplyPrice == 0 {
			zap.S().Errorf("商品：%s,更新价格错误", per.ID.Hex())
		}

		zap.S().Infof("更新：%d", i)
	}

}

func initProductServiceFee(ctx context.Context) {
	d := dao.ProductDao

	//id, _ := primitive.ObjectIDFromHex("6481ef7527b29d1c3e17c056")
	list, _, err := d.ListByPage(ctx, bson.M{
		//"supplier_id": id,
	}, 1, 5000)
	if err != nil {
		return
	}

	for i, per := range list {
		supplyPrice := per.Price
		salePrice := backSalePrice(supplyPrice)

		if per.SupplyPrice > 0 {
			continue
		}

		err = d.Update(ctx, bson.M{
			"_id": per.ID,
		}, bson.M{
			"$set": bson.M{
				"supply_price": supplyPrice,
				"price":        salePrice,
				"origin_price": 0,
			},
		})
		if err != nil {

		}

		zap.S().Infof("更新：%d", i)
	}

}

func backSalePrice(supplyPrice int) int {
	percent := decimal.NewFromFloat(1.03)

	amountNum := percent.Mul(decimal.NewFromInt(int64(supplyPrice))).Div(decimal.NewFromInt(100)).RoundCeil(0)

	intPart := int(amountNum.IntPart()) * 100

	if intPart-supplyPrice > 800 {
		intPart = supplyPrice + 800
	}

	return intPart
}

func initBuyerMobileByUser(ctx context.Context) {
	//for i := 1; i < 30; i++ {
	//buyers, _, err := buyerService.NewBuyerService().List(ctx, bson.M{
	//	"mobile": "",
	//}, 1, 15000)
	//if err != nil {
	//	return
	//}
	//
	//var ids []primitive.ObjectID
	//for _, b := range buyers {
	//	if b.Mobile != "" {
	//		continue
	//	}
	//	ids = append(ids, b.UserID)
	//
	//	zap.S().Infof("mobile:空：：%s", b.ID.Hex())
	//}
	//if len(ids) < 1 {
	//	return
	//}
	//
	//for _, buyer := range buyers {
	//	if buyer.Mobile != "" {
	//		continue
	//	}
	//	u, err := userService.NewUserService().Get(ctx, buyer.UserID)
	//	if err != nil {
	//		return
	//	}
	//
	//	buyerService.NewBuyerService().UpdateOne(ctx, buyer.ID, bson.M{
	//		"mobile":   u.Mobile,
	//		"open_id":  u.OpenID,
	//		"union_id": u.UnionID,
	//		"password": u.Password,
	//	})
	//	zap.S().Infof("更新")
	//}
}

func dealAdminMobile(ctx context.Context) {
	admins, err := adminService.NewAdminService().List(ctx, bson.M{})
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, a := range admins {
		ids = append(ids, a.UserID)
	}

	users, err := userService.NewUserService().ListByIDs(ctx, ids)

	d := dao.AdminDao

	for _, a := range admins {
		var mobile string

		for _, u := range users {
			if a.UserID == u.ID {
				mobile = u.Mobile
				break
			}
		}

		if len(a.Password) == 6 {
			continue
		}

		d.UpdateOne(ctx, bson.M{
			"_id": a.ID,
		}, bson.M{
			"$set": bson.M{
				"mobile":   mobile,
				"password": mobile[5:],
			},
		})
	}

}

func dealIntegralBuyerID(ctx context.Context) {
	accounts, _, err := integralAccountService.NewIntegralAccountService().ListByPage(ctx, bson.M{}, 1, 10000)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, account := range accounts {
		ids = append(ids, account.UserID)
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"user_id": bson.M{
			"$in": ids,
		},
	})

	d := dao.IntegralAccountDao

	for _, account := range accounts {
		var bID primitive.ObjectID

		for _, buyer := range buyers {
			if account.UserID == buyer.UserID {
				bID = buyer.ID
				break
			}
		}

		if bID == primitive.NilObjectID {
			continue
		}

		d.UpdateOne(ctx, bson.M{
			"_id": account.ID,
		}, bson.M{
			"$set": bson.M{
				"buyer_id": bID,
			},
		})
	}

}

func testInitPointScope(ctx context.Context) {
	points, err := servicePointService.NewServicePointService().ListCus(ctx, bson.M{})
	if err != nil {
		return
	}
	for _, point := range points {
		if point.Level == model.ServicePointLevelSecond && len(point.DeliveryScope) < 1 {

			pointLocations, err := servicePointService.NewServicePointService().InitScope(ctx, model.PointLocation{
				Longitude: point.Location.Longitude,
				Latitude:  point.Location.Latitude,
			})
			if err != nil {
				return
			}
			err = servicePointService.NewServicePointService().UpdateScope(ctx, point.ID, pointLocations)
			if err != nil {
				return
			}
		}
	}

}

func PayWarning(eventType, con string) {
	messageService.NewMessageService().SendWarning("***********", eventType, con)

}

func testBankOpen(ctx context.Context) {

	//userID, _ := util.ConvertToObjectWithCtx(ctx, "644de57c0b8f95deeaacffb3")
	//buyerID, _ := util.ConvertToObjectWithCtx(ctx, "6454a463d1cb098ae8c0e042")
	//
	//yeeMerchantService.NewYeeMerchantService().AccountOpen(ctx, userID, buyerID)
}

func testMoney(ctx context.Context) {
	str := util.DealMoneyToYuanStr(125689)
	zap.S().Infof("str: %s", str)
}

func initSupplierMerchant(ctx *gin.Context, p1, p2 string) {
	//id, _ := primitive.ObjectIDFromHex(p1)
	//
	//supplier, _ := supplierService.NewSupplierService().Get(ctx, id)
	//
	//err := yeeMerchantService.NewYeeMerchantService().CreateBySupplier(ctx, p2, supplier)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	//yeeMerchantDao := dao.YeeMerchantDao
	//
	//merchants, err := yeeMerchantDao.ListAll(ctx, bson.M{
	//	"request_no": "",
	//})
	//if err != nil {
	//	return
	//}
	//for _, merchant := range merchants {
	//	supplier, err := supplierService.NewSupplierService().Get(ctx, merchant.ObjectID)
	//	if err != nil {
	//		return
	//	}
	//
	//	supplierAuth, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, merchant.ObjectID)
	//	if err != nil {
	//		return
	//	}
	//
	//	update := bson.M{
	//		"merchant_subject_info.license_img":                 supplierAuth.Company.BusinessLicenseImg,
	//		"merchant_subject_info.licence_no":                  supplierAuth.Company.CreditCode,
	//		"business_address_info.address":                     supplierAuth.Company.BusinessLicense.Address,
	//		"merchant_subject_info.individual_bank_card_img":    supplierAuth.BankAccount.BankcardImg,
	//		"merchant_subject_info.sign_name":                   supplierAuth.Company.CompanyName,
	//		"merchant_subject_info.short_name":                  supplier.ShopSimpleName,
	//		"merchant_subject_info.sign_type":                   "INDIVIDUAL",
	//		"merchant_corporation_info.legal_name":              supplierAuth.Company.Legal.LegalName,
	//		"merchant_corporation_info.legal_licence_no":        supplierAuth.Company.Legal.LegalIds,
	//		"merchant_corporation_info.legal_licence_type":      "ID_CARD",
	//		"merchant_corporation_info.legal_licence_front_img": supplierAuth.Company.Legal.IdCardFrontImg,
	//		"merchant_corporation_info.legal_licence_back_img":  supplierAuth.Company.Legal.IdCardBackImg,
	//		"settlement_account_info.bank_account_type":         "DEBIT_CARD",
	//		"settlement_account_info.bank_card_no":              supplierAuth.BankAccount.CardNumber,
	//	}
	//
	//	if merchant.MerchantContactInfo.ContactEmail == "" {
	//		update["merchant_contact_info.contact_email"] = "<EMAIL>"
	//	}
	//
	//	err = yeeMerchantDao.Update(ctx, bson.M{
	//		"_id": merchant.ID,
	//	}, bson.M{
	//		"$set": update,
	//	})
	//	if err != nil {
	//		return
	//	}
	//}

}

func balanceBuyer(ctx context.Context) {
	list, err := buyerBalanceOrderService.NewBuyerBalanceOrderService().List(ctx, bson.M{})
	if err != nil {
		return
	}

	mBuyer := make(map[primitive.ObjectID]int)

	for _, order := range list {
		mBuyer[order.BuyerID] = 0
	}

	order := payModule.NewOrderS()

	var AllAmount int            //  总额
	var FreezenAmount int        //  冻结额
	var DepositFreezenAmount int //

	mStr := make(map[string]map[string]string)

	for b, _ := range mBuyer {
		auth, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, b)
		if err != nil {
			return
		}
		userBalance, err := order.QueryBalanceS(pays.QueryBalanceReq{
			BizUserId:    auth.PayBizUserId,
			AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
		})

		AllAmount += userBalance.AllAmount
		FreezenAmount += userBalance.FreezenAmount
		DepositFreezenAmount += userBalance.DepositFreezenAmount

		if userBalance.AllAmount > 0 {
			buyer, _ := buyerService.NewBuyerService().Get(b)
			user, _ := userService.NewUserService().Get(ctx, buyer.UserID)

			//	 查询是否开启记账簿
			queryRes, err := yeeMerchantService.NewYeeMerchantService().AccountQuery(ctx, buyer.ID)
			if err != nil {
				return
			}
			_ = queryRes

			if queryRes.YpAccountBookNo == "" {
				//err = yeeMerchantService.NewYeeMerchantService().AccountOpen(ctx, buyer.UserID, buyer.ID)
				//if err != nil {
				//	return
				//}
			}

			toYuan := util.DealMoneyToYuanStr(userBalance.AllAmount)

			mStr[buyer.BuyerName] = map[string]string{
				"mobile":     user.Mobile,
				"buyer_name": buyer.BuyerName,
				"amount":     toYuan,
				"account_no": queryRes.YpAccountBookNo,
			}
		}
	}

	zap.S().Infof("总额：%d，冻结额：%d，充值冻结额：%d", AllAmount, FreezenAmount, DepositFreezenAmount)

	marshal, _ := json.Marshal(mStr)
	zap.S().Infof("总额：%s ", string(marshal))
	zap.S().Infof(" 数量：%d", len(mStr))

	create, err := os.Create("f.csv")
	if err != nil {
		return
	}

	defer create.Close()

	writer := csv.NewWriter(create)
	defer writer.Flush()
	// 第一行
	record := []string{"会员名称", "手机号", "金额", "收款账号"}
	err = writer.Write(record)

	for _, m := range mStr {
		record := []string{m["buyer_name"], m["mobile"], m["amount"], m["account_no"]}
		err = writer.Write(record)
		if err != nil {
			return
		}
	}

}

func balanceSupplier(ctx context.Context) {
	list, _, err := supplierService.NewSupplierService().List(bson.M{}, 1, 100)
	if err != nil {
		return
	}

	order := payModule.NewOrderS()

	var AllAmount int            //  总额
	var FreezenAmount int        //  冻结额
	var DepositFreezenAmount int //

	for _, supplier := range list {
		auth, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, supplier.ID)
		if err != nil {
			return
		}
		userBalance, err := order.QueryBalanceS(pays.QueryBalanceReq{
			BizUserId:    auth.PayBizUserId,
			AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
		})

		AllAmount += userBalance.AllAmount
		FreezenAmount += userBalance.FreezenAmount
		DepositFreezenAmount += userBalance.DepositFreezenAmount
	}

	zap.S().Infof("总额：%d，冻结额：%d，充值冻结额：%d", AllAmount, FreezenAmount, DepositFreezenAmount)

}

func balanceOrder(ctx context.Context) {
	list, err := orderService.NewOrderService().List(ctx, bson.M{
		"has_agent_pay":    false,
		"pay_method":       model.PayMethodTypeBalance,
		"order_refund_all": false,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeFinish, model.OrderStatusTypeToStockUp, model.OrderStatusTypeToSort, model.IntegralOrderStatusToShip, model.OrderStatusTypeToReceive},
		},
		"created_at": bson.M{
			"$gte": *************, // 2024-10-08 17:00:36
			"$lte": *************, // 2024-11-08 18:00:36
		},
	})
	if err != nil {
		return
	}

	var AllAmount int

	for _, order := range list {
		AllAmount += order.PaidAmount
	}

	zap.S().Infof("总额：%d", AllAmount)

}

func balanceParentOrder(ctx context.Context) {
	list, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{
		"pay_status": model.PayStatusTypePaid,
		//"pay_result.biz_order_no": bson.M{
		"biz_order_no_result.biz_order_no": bson.M{
			"$ne": "",
		},
		"created_at": bson.M{
			//"$gte": 1730390400000, // 2024-11-01
			//"$lte": 1732982400000, // 2024-12-01 00:00:00
			//"$gte": *************, // 2024-10-01
			//"$lte": 1730390400000, // 2024-11-01 00:00:00
			//"$gte": 1725120000000, // 2024-9-01
			//"$lte": *************, // 2024-10-01 00:00:00
			//"$gte": 1722441600000, // 2024-8-01
			//"$lte": 1725120000000, // 2024-9-01 00:00:00

			"$gte": 1719763200000, // 2024-7-01
			"$lte": 1722441600000, // 2024-8-01 00:00:00
			//"$lte": *************, // 2024-10-01 00:00:00

			//"$gte": 1717171200000, // 2024-6-01
			//"$lte": 1719763200000, // 2024-7-01 00:00:00

			//"$gte": 1714492800000, // 2024-5-01
			//"$lte": 1717171200000, // 2024-6-01 00:00:00

			//"$gte": 1709222400000, // 2024-3-01
			//"$lte": 1714492800000, // 2024-5-01 00:00:00

			//"$gte": 1704038400000, // 2024-1-01
			//"$lte": 1709222400000, // 2024-3-01 00:00:00

			//"$gte": 1701360000000, // 23-12-01
			//"$lte": 1704038400000, // 2024-1-01 00:00:00

			//"$gte": 1698768000000, // 23-11-01
			//"$lte": 1701360000000, // 23-12
		},
	})
	if err != nil {
		return
	}

	total := len(list)

	var AllAmount int
	var idList []string
	for i, order := range list {
		r, err := payModule.NewOrderS().GetPaymentInformationDetailS(pays.GetPaymentInformationDetailReq{
			BizOrderNo: order.BizOrderNo,
		})
		if err != nil {
			zap.S().Errorf("查询订单错误：-----------%s,订单ID:%s", err.Error(), order.ID.Hex())
			time.Sleep(time.Second * 10)
		}
		if r.UnPayTotalAmount != 0 {
			AllAmount += r.UnPayTotalAmount
			idList = append(idList, order.ID.Hex())
		}
		zap.S().Infof("index:%d,total:%d", i, total)
	}
	zap.S().Infof("总额：%d,订单列表：%v", AllAmount, idList)

}

func tempAgentPay(oriBizOrderNo, receiverBizUserID string, orderSplitAmount, amount int) {
	var cPayList []pays.CollectPayItem
	cPayList = append(cPayList, pays.CollectPayItem{
		BizOrderNo: oriBizOrderNo,
		//OrderNo:    data.OriOrderNo,
		//BizOrderCreateDate:  // 一年以前订单必须上送
		Amount: amount, // 采购商实付  金额，单位：分；部分代付时，可以少于或等于托管代收订单金额
	})

	// 分账
	var splitList []pays.SplitRuleItem
	//if order.SupplierLevel == model.SupplierLevelStation {
	//	//	城市仓-仓配费
	//	stationFee += loadAmount
	//}

	//if pointFee > 0 {
	//	// 中心仓
	//	splitList = append(splitList, pays.SplitRuleItem{
	//		BizUserId: pointAuth.PayBizUserId,
	//		Amount:    pointFee,
	//		Fee:       platformPointServiceFee,
	//		Remark:    fmt.Sprintf("中心仓%s", order.ServicePointID.Hex()),
	//	})
	//}

	num := util.NewUUIDNum()

	req := pays.SignalAgentPayReq{
		BizOrderNo:     num,
		CollectPayList: cPayList,
		BizUserId:      receiverBizUserID,                                  // 供应商
		AccountSetNo:   global.AllInPayAccountSetInfo.EscrowUserNo,         // 托管账户集合
		BackUrl:        global.BackHost + global.BackUrlSignalAgentPayTemp, // 单笔代付
		Amount:         amount,
		Fee:            0, // 供应商-平台服务费
		SplitRuleList:  splitList,
		TradeCode:      global.TradeCodeCollectPay, // 电商及其它 代付购买金
		Summary:        fmt.Sprintf("补差处理ID"),
		ExtendInfo:     "",
	}

	res, err := payModule.NewOrderS().SignalAgentPayS(req)
	if err != nil {
	}

	_ = res
}

func dealUploadShip(ctx context.Context) {
	//
	//id, _ := primitive.ObjectIDFromHex("649bd748bb9e0e2be706087e")
	//
	//list, _ := orderService.NewOrderService().List(ctx, bson.M{
	//	"buyer_id":   id,
	//	"pay_method": model.PayMethodTypeWechat,
	//	"created_at": bson.M{
	//		"$gte": *************,
	//		"$lte": *************,
	//	},
	//})
	//
	//var totalAmount int
	//
	//for _, order := range list {
	//	totalAmount += order.PaidAmount
	//	for _, productOrder := range order.ProductList {
	//		zap.S().Infof("水果：%s---%d", productOrder.ProductTitle, productOrder.Price)
	//	}
	//}
	//
	//zap.S().Infof("%d", totalAmount)

	//content := "https://www.baidu.com"
	//content := "hello"

	// 生成二维码
	//err := qrcode.New(content, 2).WriteFile(500, "qrcode.png")
	//if err != nil {
	//	log.Fatal(err)
	//}

	//err := qrcode.WriteFile("https://www.baidu.com", qrcode.Medium, 256, "qrcode.png")
	//if err != nil {
	//	log.Fatal(err)
	//}

	//code, err := miniService.NewMiniService(model.ObjectTypeBuyer).GetUnlimitedQRCode("ok")
	//if err != nil {
	//	return
	//}
	//
	//toString := base64.StdEncoding.EncodeToString(code)
	//
	//zap.S().Infof("%s", toString)

	//

	//list, err := orderService.NewOrderService().List(ctx, bson.M{
	//	"has_agent_pay":    false,
	//	"order_refund_all": false,
	//	"order_status":     9,
	//	"created_at": bson.M{
	//		"$lte": 1727771125000,
	//		"$gte": 1715243125000,
	//	},
	//})
	//if err != nil {
	//	return
	//}
	//
	//_ = list
	//
	//for _, order := range list {
	//	zap.S().Infof("%s", order.ID.Hex())
	//	orderAgentPayService.NewOrderAgentPayService().SingleOrderAgentPay(ctx, order.ID)
	//}

	//debts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
	//	"wx_pay_result.trade_state": "SUCCESS",
	//})
	//
	//if err != nil {
	//	return
	//}
	//
	//_ = debts
	//
	//debtDao := dao.OrderDebtDao
	//orderDao := dao.OrderDao
	//
	//for _, debt := range debts {
	//	debtDao.UpdateOne(ctx, bson.M{"_id": debt.ID}, bson.M{"$set": bson.M{
	//		"pay_status": model.PayStatusTypePaid,
	//		//"has_debt_order_paid": true,
	//	}})
	//
	//	err = orderDao.UpdateOne(ctx, bson.M{"_id": debt.OrderID}, bson.M{"$set": bson.M{
	//		//"has_debt_order":      true,
	//		"has_debt_order_paid": true,
	//	}})
	//
	//}

	//list := []string{"67048bc45f8cf8a9f0752d07", "67048bc45f8cf8a9f0752cfc",
	//	"67048bc45f8cf8a9f0752cfd", "67048bc45f8cf8a9f0752cfa", "67048bc45f8cf8a9f0752cf8", "67048bc45f8cf8a9f0752cf5", "67048bc45f8cf8a9f0752cf2", "67048bc45f8cf8a9f0752cef"}
	//
	////list := []string{"67048bc45f8cf8a9f0752d00", "67048bc45f8cf8a9f0752cff"}
	//
	//var ids []primitive.ObjectID
	//for _, s := range list {
	//	id, _ := util.ConvertToObjectWithCtx(ctx, s)
	//	ids = append(ids, id)
	//}
	//
	//for _, id := range ids {
	//	refund, _ := orderRefundService.NewOrderRefundService().GetByID(ctx, id)
	//
	//	mnsSendService.NewMNSClient().SendSyncRefundData(refund.OrderID.Hex())
	//
	//	if refund.RefundType == model.RefundTypeAfterSale {
	//		//	 检查商品是否全退
	//		mnsSendService.NewMNSClient().SendCheckIsRefundAllProduct(refund.ID.Hex())
	//	}
	//
	//	//if refund.RefundType == model.RefundTypeAfterShip || refund.RefundType == model.RefundTypeAfterSale {
	//	//	if refund.RefundType == model.RefundTypeAfterShip {
	//	//		mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderQuality, refund.UserID, refund.ParentOrderID, (refund.AuditAmount)/100)
	//	//	}
	//	//	if refund.RefundType == model.RefundTypeAfterSale {
	//	//		mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderAfterSale, refund.UserID, refund.ParentOrderID, (refund.AuditAmount)/100)
	//	//	}
	//	//}
	//}

	//id, _ := primitive.ObjectIDFromHex("67049b595f8cf8a9f0753557")
	//
	//orderIDs := []primitive.ObjectID{id}
	//queueProduceService.NewQueueProduceService().UploadShipInfo(ctx, orderIDs)

	//service := miniService.NewMiniService(model.ObjectTypeBuyer)
	//
	//status, err := service.ShipStatus("4200002401202410080004113057")
	//if err != nil {
	//	return
	//}
	//_ = status
	//
	//service.ShipUploadInfo("oYzQW43qesujIm0rWZ6CrAeGuKm0", "4200002401202410080004113057")
}

func dealQuality(ctx context.Context) {
	d := dao.OrderQualityDao

	list, err := d.List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1727107200000,
		},
	})
	_ = err

	for _, s := range list {

		if s.SupplierLevel != "" {
			continue
		}

		d.UpdateOne(ctx, bson.M{
			"_id": s.ID,
		}, bson.M{
			"$set": bson.M{
				"supplier_level": "point",
			},
		})
	}

}

func dealOrderSupplierLevel(ctx context.Context) {
	d := dao.OrderDao

	list, err := d.List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1727107200000,
		},
	})
	_ = err

	for _, s := range list {

		if s.SupplierLevel != "" {
			continue
		}

		d.UpdateOne(ctx, bson.M{
			"_id": s.ID,
		}, bson.M{
			"$set": bson.M{
				"supplier_level": model.SupplierLevelPoint,
			},
		})
	}

}

func dealSupplierPoint(ctx context.Context) {
	supplierDao := dao.SupplierDao
	rdb := global.RDBDefault

	list, err := supplierDao.List(ctx, bson.M{})
	_ = err

	//id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	for _, s := range list {

		if s.Level != "" {
			rdb.Del(ctx, "supplier:"+s.ID.Hex())
			continue
		}

		level := "point"

		supplierDao.Update(ctx, bson.M{
			"_id": s.ID,
		}, bson.M{
			"$set": bson.M{
				"level": level,
			},
		})

		rdb.Del(ctx, "supplier:"+s.ID.Hex())

	}

}

func dealAddress2(ctx context.Context) {
	//addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{})
	//if err != nil {
	//	return
	//}
	//
	//addrDao := dao.UserAddrDao
	//rdb := global.RDBDefault
	//
	//id, _ := primitive.ObjectIDFromHex("66d7fee0e6aa983280068840")
	//_ = id
	////pointID, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	//
	//for _, address := range addresses {
	//
	//	if address.StationID != primitive.NilObjectID {
	//		continue
	//	}
	//
	//	addrDao.UpdateCus(ctx, bson.M{
	//		"_id": address.ID,
	//	}, bson.M{
	//		"$set": bson.M{
	//			"station_id":   id,
	//			"station_name": "昆明城市仓",
	//			//"service_point_name": "昆明中心仓",
	//		},
	//	})
	//
	//	rdb.Del(ctx, "address:"+address.ID.Hex())
	//
	//}

}

func dealDeliverNote(ctx context.Context) {
	list := []model.MNSBuyer{{
		BuyerID:     "66c17733536b003bad24263b",
		DeliverType: model.DeliverTypeDoor,
	}}

	//begin, end, _ := util.DayScopeTimestamp(zeroTimestamp)

	data := model.MNSGenDeliverNote{
		BuyerList: list,
		Timestamp: 1724224424000,
	}
	mnsSendService.NewMNSClient().SendDeliverNoteGenerate(data)
}

func dealIntegral(ctx context.Context) {
	id, _ := util.ConvertToObjectWithCtx(ctx, "66b49917797001673b898398")
	order, err := integralOrderService.NewIntegralOrderService().Get(ctx, id)
	if err != nil {
		return
	}

	mnsSendService.NewMNSClient().SendGenerateIntegral(model.RecordTypeExchange, order.BuyerID, order.ID, order.CostNum)
}

func initOverWeight(ctx context.Context) {
	orderIDs := []string{
		"66b591e589aadbc2618acf5c",
	}
	overWeightInfo := model.MNSOverWeight{
		OrderIDList: orderIDs,
	}

	mnsSendService.NewMNSClient().SendCheckSortOverWeight(overWeightInfo)

}

//func dealIntegral(ctx context.Context) {
//	d := dao.IntegralAccountDao
//
//	accounts, i, err := d.ListByPage(ctx, bson.M{
//		"num": bson.M{
//			"$gt": 0,
//		},
//	}, 1, 10)
//	if err != nil {
//		return
//	}
//	_ = i
//	_ = accounts
//
//	for _, account := range accounts {
//		orders, err := orderService.NewOrderService().List(ctx, bson.M{
//			"user_id": account.UserID,
//		})
//		if err != nil {
//
//		}
//		_ = orders
//
//	}
//
//}

func dealDebt(ctx context.Context) {
	d := dao.OrderDebtDao

	for i := 1; i < 10; i++ {
		debts, count, err := d.ListByPage(ctx, bson.M{
			"user_id": bson.M{
				"$exists": false,
			},
		}, int64(i), int64(i*200))
		_ = count
		if err != nil {

		}

		m := make(map[primitive.ObjectID][]primitive.ObjectID)
		for _, debt := range debts {
			m[debt.BuyerID] = append(m[debt.BuyerID], debt.ID)
		}

		for id, ids := range m {
			buyer, _ := buyerService.NewBuyerService().Get(id)
			d.UpdateOne(ctx, bson.M{
				"_id": bson.M{
					"$in": ids,
				},
			}, bson.M{
				"$set": bson.M{
					"user_id": buyer.UserID,
				},
			})

		}

	}

}

func dealSupplierName(ctx context.Context) {
	d := dao.ProductDao

	id, _ := util.ConvertToObjectWithCtx(ctx, "647e9d95b1fd9a6008654e69")

	list, err := d.List(ctx, bson.M{
		"supplier_id": id,
	})
	if err != nil {
		return
	}

	for _, product := range list {

		productService.NewProductService().UpdateCus(ctx, product.ID, bson.M{
			"supplier_simple_name": "优果甄选",
		})

	}

	_ = list

}

func dealAccountStatus(ctx context.Context) {
	d := buyerDao.NewBuyerDao()

	list, err := d.ListByCus(ctx, bson.M{})
	if err != nil {
		return
	}

	for _, b := range list {
		if b.AccountStatus != model.AccountStatusTypeNormal {
			zap.S().Infof("not normal,%d", b.AccountStatus)
		}
	}

	_ = list

}

func dealBuyer2(ctx context.Context) {
	d := buyerDao.NewBuyerDao()

	list, err := d.ListByCus(ctx, bson.M{
		"location.address": bson.M{
			"$regex": "昭通",
		},
	})
	if err != nil {
		return
	}

	_ = list

}

func dealUnit(ctx context.Context) {
	d := dao.ProductDao

	originID, _ := primitive.ObjectIDFromHex("64563f5ef5f92933e7be2bd4")
	targetID, _ := primitive.ObjectIDFromHex("64561e2338a7936ebbd210be")
	list, err := d.List(ctx, bson.M{
		"product_unit_id": originID,
	})
	if err != nil {
		return
	}

	for _, product := range list {
		productService.NewProductService().UpdateCus(ctx, product.ID,
			bson.M{
				"product_unit_id":        targetID,
				"product_unit_type_name": "件",
			})
	}

}

func dealTrack2(ctx context.Context) {
	d := dao.TrackDao

	d.DeleteMany(ctx, bson.M{
		//"event": model.EventTypeAppEnter,
		"event": model.EventTypeAppLeave,
		//"created_at": bson.M{
		//	"$lt": *************,
		//},
	})

}

func dealAddrUserType(ctx context.Context) {

	d := dao.UserAddrDao

	list, err := d.List(ctx, bson.M{})
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, u := range list {
		if u.UserType == "" {
			ids = append(ids, u.ID)
		}
	}

	if len(ids) > 0 {
		d.UpdateMany(ctx, bson.M{
			"_id": bson.M{
				"$in": ids,
			}}, bson.M{
			"$set": bson.M{
				"user_type": "normal",
			},
		},
		)
	}

}

func dealDeliverAssign(ctx context.Context) {

	d := dao.DeliverAssignDao

	list, err := d.List(ctx, bson.M{})
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, assign := range list {
		if assign.DeliverType == 0 {
			ids = append(ids, assign.ID)
		}
	}

	if len(ids) > 0 {
		d.UpdateMany(ctx, bson.M{
			"_id": bson.M{
				"$in": ids,
			}}, bson.M{
			"$set": bson.M{
				"deliver_type": model.DeliverTypeDoor,
			},
		},
		)
	}

}

func dealSupplierProduct(ctx context.Context) {
	id, _ := util.ConvertToObjectWithCtx(ctx, "66544f3bfd492ccde47cf59d")
	products, _, err := productService.NewProductService().ListByCus(ctx, bson.M{
		"supplier_id": id,
	}, 1, 100)
	if err != nil {

	}

	d := dao.ProductDao

	var ids []primitive.ObjectID
	for _, product := range products {
		ids = append(ids, product.ID)
	}

	d.UpdateMany(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		}}, bson.M{
		"$set": bson.M{
			"sale": true,
		},
	},
	)
	rdb := global.RDBDefault

	for _, product := range products {
		rdb.Del(ctx, "product:"+product.ID.Hex())
	}

}

func changeBuyerPoint(ctx context.Context) {

	rdb := global.RDBDefault

	filter := bson.M{
		"location.address": bson.M{
			"$regex": "蒙自",
		},
	}

	list, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
	if err != nil {
		return
	}

	targetID, _ := util.ConvertToObjectWithCtx(ctx, "665443407f5564d061a90bcd")

	buyerD := buyerDao.NewBuyerDao()

	var buyerIDs []primitive.ObjectID
	for _, buyer := range list {
		if buyer.ServicePointID == targetID {
			continue
		}

		buyerD.Update(ctx, bson.M{
			"_id": buyer.ID,
		}, bson.M{
			"$set": bson.M{
				"service_point_id": targetID,
			},
		})
		rdb.Del(ctx, "buyer:"+buyer.ID.Hex())
		rdb.Del(ctx, "buyerByUser:"+buyer.UserID.Hex())
	}

	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
	})
	if err != nil {
		return
	}

	addrD := dao.UserAddrDao

	for _, a := range addresses {
		if a.ServicePointID == targetID {
			continue
		}

		addrD.UpdateCus(ctx, bson.M{
			"_id": a.ID,
		}, bson.M{
			"$set": bson.M{
				"service_point_id": targetID,
			},
		})
		rdb.Del(ctx, "address:"+a.ID.Hex())
	}
}

func initBuyerByUser(ctx context.Context) {
	//for i := 1; i < 100; i++ {
	//	users, _, err := userService.NewUserService().List(ctx, 1, int64(100*i))
	//	if err != nil {
	//		return
	//	}
	//
	//	var ids []primitive.ObjectID
	//	for _, user := range users {
	//		ids = append(ids, user.ID)
	//	}
	//	if len(ids) < 1 {
	//		continue
	//	}
	//	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
	//		"user_id": bson.M{
	//			"$in": ids,
	//		},
	//	})
	//	if err != nil {
	//		return
	//	}
	//
	//	for _, user := range users {
	//		var f bool
	//		for _, buyer := range buyers {
	//			if buyer.UserID == user.ID {
	//				f = true
	//			}
	//		}
	//		if !f {
	//			buyerService.NewBuyerService().CheckBuyerInit(ctx, user.ID)
	//		}
	//
	//	}
	//	zap.S().Infof("%d", i)
	//}
}

func dealRetailRefund(ctx context.Context) {
	//for i := 1; i < 3; i++ {
	//	orders, _, err := orderService.NewOrderService().ListByPage(ctx, bson.M{
	//		"order_type": model.OrderTypeRetail,
	//	}, 1, int64(100*i))
	//	if err != nil {
	//		return
	//	}
	//
	//	var ids []primitive.ObjectID
	//	for _, o := range orders {
	//		ids = append(ids, o.ID)
	//	}
	//	if len(ids) < 1 {
	//		continue
	//	}
	//
	//	d := dao.OrderRefundDao
	//	//
	//	d.UpdateMany(ctx, bson.M{"order_id": bson.M{
	//		"$in": ids,
	//	}}, bson.M{"$set": bson.M{
	//		"order_type": model.OrderTypeRetail,
	//	}})
	//
	//	//pointID, err := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
	//	//if err != nil {
	//	//	return
	//	//}
	//	//
	//	//d.UpdateMany(ctx, bson.M{}, bson.M{"$set": bson.M{
	//	//	"service_point_id": pointID,
	//	//}})
	//
	//}
}

func realOrderType(ctx context.Context) {
	for i := 1; i < 200; i++ {
		orders, count, err := orderService.NewOrderService().ListByPage(ctx, bson.M{}, int64(i), 100)
		if err != nil {
			return
		}
		_ = count

		if len(orders) < 1 {
			return
		}
		oDao := dao.OrderDao

		var ids []primitive.ObjectID
		for _, order := range orders {
			if order.OrderType == "" {
				ids = append(ids, order.ID)
			}
		}

		if len(ids) < 1 {
			continue
		}
		oDao.UpdateMany(ctx, bson.M{
			"_id": bson.M{
				"$in": ids,
			},
		}, bson.M{
			"$set": bson.M{
				"order_type": model.OrderTypeWholeSale,
			},
		})

		zap.S().Infof("%d", i)
	}

}

// 初始化服务仓模块
func initServicePointPart(ctx context.Context) {
	// 来源
	fromID, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	targetID, _ := primitive.ObjectIDFromHex("66515bba7c90087729423c76")

	_ = fromID
	_ = targetID

	// 快捷栏
	shortcuts, err := shortcutService.NewShortcutService().ListAllWithPoint(ctx, true, fromID)
	if err != nil {
		return
	}

	for i, shortcut := range shortcuts {
		now := time.Now().UnixMilli()
		shortcut.ID = primitive.NewObjectID()
		shortcut.ServicePointID = targetID
		shortcut.ProductList = make([]primitive.ObjectID, 0)
		shortcut.CreatedAt = now + int64(i)
		shortcut.UpdatedAt = now + int64(i)
		shortcutService.NewShortcutService().Init(ctx, shortcut)
	}
	// 公告

	// 营销专区
	parts, err := indexPartService.NewIndexPartService().ListVisible(ctx, fromID)
	if err != nil {
		return
	}
	for i, part := range parts {
		part.ID = primitive.NewObjectID()
		part.ServicePointID = targetID
		now := time.Now().UnixMilli()
		part.CreatedAt = now + int64(i)
		part.UpdatedAt = now + int64(i)
		indexPartService.NewIndexPartService().Init(ctx, part)
	}

	//	推广
}

func dealKunmingAddr222(ctx context.Context) {
	filter := bson.M{
		"location.address": bson.M{
			"$regex": "蒙自",
		},
	}

	list, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
	if err != nil {
		return
	}
	_ = list

	var ids []primitive.ObjectID

	for index, per := range list {
		ids = append(ids, per.ID)

		latestOrderTime, err := orderService.NewOrderService().GetLatestOrderTime(ctx, per.ID)
		if err != nil {

		}

		var t string
		if latestOrderTime > 0 {
			milli := time.UnixMilli(latestOrderTime)
			t = milli.Format("2006-01-02")
		}

		fmt.Printf("%d,%s,%s,%s,%s,\n", index+1, per.BuyerName, t, per.Address, per.Location.Address)
	}

}

//func dealServiceFee2(ctx context.Context) {
//
//	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
//		"service_fee": 0,
//	})
//	if err != nil {
//
//	}
//	daoA := dao.UserAddrDao
//	rdb := global.RDBDefault
//	var cache = "address:"
//
//	for i, address := range addresses {
//		daoA.UpdateCus(ctx, bson.M{"_id": address.ID}, bson.M{
//			"$set": bson.M{
//				"service_fee":                3,
//				"service_fee_rebate_percent": 10,
//			},
//		})
//
//		rdb.Del(context.Background(), cache+address.ID.Hex())
//		zap.S().Infof("index:%d", i)
//	}
//}

func dealAgentPayDeliver(ctx context.Context) {
	d := dao.OrderAgentPayDao

	//id, _ := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")

	list, err := d.List(ctx, bson.M{
		"agent_pay_type":   5,
		"service_point_id": primitive.NilObjectID,
	})
	if err != nil {
		return
	}

	for _, pay := range list {
		_ = pay
		//d.UpdateOne(ctx, bson.M{"_id": pay.ID}, bson.M{
		//	"$set": bson.M{
		//		"service_point_id": id,
		//	},
		//})

	}

}

func statsAgentPay(ctx context.Context) {

	d := dao.OrderAgentPayDao

	list, err := d.List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1713579404000,
		},
		"agent_pay_type": bson.M{
			"$in": []int{1, 2},
		},
	})
	if err != nil {
		return
	}

	var pointServiceAmount int
	var pointServiceFee int
	for _, pay := range list {
		pointServiceAmount += pay.ServiceAmount
		pointServiceFee += pay.ServiceFeeAmount
	}

	zap.S().Infof("amount:%d,amountFee:%d", pointServiceAmount, pointServiceFee)
}

func refundSplit(ctx context.Context) {
	amount := 1477
	var rList []pays.RefundItem
	//rList = append(rList, pays.RefundItem{
	//	BizUserId:    "fd171958-9d56-4e1f-89d6-7c855d93aa0d",
	//	AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	//	Amount:       1477,
	//})
	rList = append(rList, pays.RefundItem{
		BizUserId: "1b75b9d5-731b-4fff-bac4-0bbf0995cd81",
		Amount:    60,
	})
	//rList = append(rList, pays.RefundItem{
	//	BizUserId: "70ec9f15-e938-4f68-a4dc-594f5a182630",
	//	Amount:    61,
	//})

	tlt := "TLT"
	payReq := pays.RefundReq{
		BizOrderNo:    util.NewUUID(),
		OriBizOrderNo: "fbcdae10-9591-4dac-a9ac-db5e2ef69df9",
		//OriOrderNo:    "1669331439306416128",
		BizUserId:     "1b75b9d5-731b-4fff-bac4-0bbf0995cd81", // 收款人-原订单的付款方  lgl
		RefundType:    "D0",
		RefundList:    rList,
		BackUrl:       global.BackHost + global.BackUrlRefundManual,
		Amount:        amount,
		CouponAmount:  0,
		FeeAmount:     0,
		RefundAccount: tlt,
		ExtendInfo:    "退款：手动",
	}
	res, err := payModule.NewOrderS().RefundS(payReq)
	if err != nil {
		log.Println(err)
		return
	}
	if res.PayStatus == "fail" {
		log.Println(err)
		return
	}
	_ = res
	log.Println(res)
}

func dealTrackLeaveAt(ctx context.Context) {
	filter := bson.M{
		"event":      "AppLeave",
		"created_at": 0,
	}

	trackDao := dao.TrackDao

	tracks, err := trackDao.List(ctx, filter)
	if err != nil {
		return
	}

	for _, track := range tracks {
		trackDao.UpdateOne(ctx, bson.M{"_id": track.ID}, bson.M{
			"$set": bson.M{
				"created_at": track.LeavedAt,
			},
		})

	}

}

// 客户服务费
func dealTrack(ctx context.Context) {
	filter := bson.M{
		"created_at": bson.M{
			"$lte": time.Now().UnixMilli(),
		},
	}
	trackService.NewTrackService().DeleteMany(ctx, filter)
}

// 客户服务费
func dealServiceFee(ctx context.Context) {
	//buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
	//	"price_level": bson.M{
	//		"$gt": 0,
	//	},
	//})
	//if err != nil {
	//	return
	//}
	//
	//newBuyerDao := buyerDao.NewBuyerDao()
	//
	//for _, buyer := range buyers {
	//	newBuyerDao.Update(ctx, bson.M{"_id": buyer.ID}, bson.M{
	//		"$set": bson.M{
	//			"service_fee": buyer.PriceLevel,
	//		},
	//	})
	//}

	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
		"price_level": bson.M{
			"$gt": 0,
		},
	})
	if err != nil {
		return
	}

	addrDao := dao.UserAddrDao

	for _, a := range addresses {
		addrDao.UpdateCus(ctx, bson.M{"_id": a.ID}, bson.M{
			"$set": bson.M{
				"service_fee": a.PriceLevel,
			},
		})
	}

}

// 客户服务费
func dealOriginBuyerInfo(ctx context.Context) {
	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"contact_mobile": bson.M{
			"$exists": false,
		},
	})
	if err != nil {
		return
	}

	_ = buyers

	newBuyerDao := buyerDao.NewBuyerDao()

	for i, buyer := range buyers {

		user, err := userService.NewUserService().Get(ctx, buyer.UserID)
		if err != nil {
			zap.S().Errorf("查询异常：%s", err.Error())
			continue
		}

		newBuyerDao.Update(ctx, bson.M{"_id": buyer.ID}, bson.M{
			"$set": bson.M{
				"contact_mobile": user.Mobile,
			},
		})
		zap.S().Infof("%d", i)
	}
}

// 将原来订单的服务费置零
func dealOrderServiceFee(ctx context.Context) {
	orderDao := dao.OrderDao

	orderDao.UpdateMany(ctx, bson.M{
		"created_at": bson.M{
			"$lte": 1713882229000, // 2024-04-23 22:23:49
		},
	}, bson.M{
		"$set": bson.M{
			"total_service_fee":                  0,
			"product_list.$[].total_service_fee": 0,
		},
	})
}

func dealProductCollect(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	point, err := servicePointService.NewServicePointService().Get(ctx, id)
	if err != nil {
		return
	}

	//list, _, err := shortcutService.NewShortcutService().List(ctx, 1, 99)
	//if err != nil {
	//	return
	//}

	d := dao.ProductCollectDao

	d.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": bson.M{
			"service_point_id": point.ID,
		},
	})

}

func dealPromote(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	point, err := servicePointService.NewServicePointService().Get(ctx, id)
	if err != nil {
		return
	}

	//list, _, err := shortcutService.NewShortcutService().List(ctx, 1, 99)
	//if err != nil {
	//	return
	//}

	d := dao.PromoteDao

	d.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": bson.M{
			"service_point_id": point.ID,
		},
	})

}

// 服务仓管理员-默认关联用户成为管理员
func dealPointAdmin(ctx context.Context) {
	//id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	//point, err := servicePointService.NewServicePointService().Get(id)
	//if err != nil {
	//	return
	//}
	//
	//admin, err := adminService.NewAdminService().GetByUser(ctx, point.UserID)
	//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//	return
	//}
	//_ = admin
	//if errors.Is(err, mongo.ErrNoDocuments) {
	//	//	 新建
	//	authList := []string{}
	//	adminService.NewAdminService().Upsert(ctx, point.UserID, point.Name, []string{string(model.RoleTypePointAdmin)}, authList)
	//}

}

func dealIndexPart(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	point, err := servicePointService.NewServicePointService().Get(ctx, id)
	if err != nil {
		return
	}

	//list, _, err := shortcutService.NewShortcutService().List(ctx, 1, 99)
	//if err != nil {
	//	return
	//}

	d := dao.IndexPartDao

	d.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": bson.M{
			"service_point_id": point.ID,
		},
	})

}
func dealShortcut(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	point, err := servicePointService.NewServicePointService().Get(ctx, id)
	if err != nil {
		return
	}

	//list, _, err := shortcutService.NewShortcutService().List(ctx, 1, 99)
	//if err != nil {
	//	return
	//}

	shortcutDao := dao.ShortcutDao

	shortcutDao.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": bson.M{
			"service_point_id": point.ID,
		},
	})

}

func dealPriceChange(price, priceChange int) int {
	priceDe := decimal.NewFromInt(int64(price))
	priceChangeDe := decimal.NewFromInt(int64(100 + priceChange)).Div(decimal.NewFromInt(100))

	// 四舍五入至元
	part := priceDe.Mul(priceChangeDe).Div(decimal.NewFromInt(100)).Round(0).Mul(decimal.NewFromInt(100)).IntPart()

	return int(part)
}

func dealCartPointID(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	update := bson.M{
		"service_point_id": id,
	}
	d := dao.CartDao

	d.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": update,
	})

}

func dealCartStationID(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("66d7fee0e6aa983280068840")

	update := bson.M{
		"station_id": id,
	}

	d := dao.CartDao

	d.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": update,
	})

}

// 供应商绑定服务仓ID
func dealSupplierServicePointID(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")
	update := bson.M{
		"service_point_id": id,
	}
	productDao := dao.ProductDao
	productDao.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": update,
	})

}

func dealAddressPointID(ctx context.Context) {
	//list, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{})
	//if err != nil {
	//	return
	//}
	//_ = list

	addrDao := dao.UserAddrDao

	pointID, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")

	addrDao.UpdateMany(ctx, bson.M{}, bson.M{
		"$set": bson.M{
			"service_point_id": pointID,
		},
	})

}

func dealSupplierServiceFee(ctx context.Context) {
	//list, _, err := supplierService.NewSupplierService().List(bson.M{}, 1, 100)
	//if err != nil {
	//	return
	//}
	//
	//update := bson.M{
	//	"supplier_service_fee": 1.5,
	//}
	//supplierService.NewSupplierService().UpdateMany(ctx, bson.M{}, bson.M{
	//	"$set": update,
	//})
}

func dealProductBuyLimit(ctx context.Context) {
	filter := bson.M{
		"buy_min_limit": bson.M{
			"$gte": 2,
		},
	}
	products, err := productService.NewProductService().List(ctx, filter)
	if err != nil {
		return
	}

	for _, product := range products {
		update := bson.M{
			"buy_limit_limit": 0,
		}

		productService.NewProductService().UpdateCus(ctx, product.ID, update)
	}

}

func listAll(ctx context.Context) {
	// 地址

	list, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{})
	if err != nil {
		return
	}
	for _, address := range list {
		if address.SubsidyAmount != 50000 {
			format := time.UnixMilli(address.CreatedAt).Format(time.RFC3339)
			zap.S().Errorf("补贴问题,%s", format)
		}
	}

}

func dealSubsidyAmount(ctx context.Context) {
	// 地址
	//update := bson.M{
	//	"subsidy_amount":  50000,
	//	"subsidy_percent": 50,
	//}
	//userAddrService.NewUserAddrService().UpdateMany(ctx, bson.M{}, bson.M{
	//	"$set": update,
	//})

	//dao := buyerDao.NewBuyerDao()
	//dao.UpdateMany(ctx, bson.M{}, bson.M{
	//	"$set": update,
	//})

}

func findSubsidyAmount(ctx context.Context) {
	// 地址
	//update := bson.M{
	//	"subsidy_amount":  50000,
	//	"subsidy_percent": 50,
	//}
	//userAddrService.NewUserAddrService().UpdateMany(ctx, bson.M{}, bson.M{
	//	"$set": update,
	//})
	//
	//dao := buyerDao.NewBuyerDao()
	//dao.UpdateMany(ctx, bson.M{}, bson.M{
	//	"$set": update,
	//})
}

func reject(ctx context.Context) {

	filter := bson.M{
		//"_id": bson.M{
		//	"$in": latestIDs,
		//},
		"audit_status": model.AuditStatusTypeNotPass,
		"location.address": bson.M{
			"$regex": "昆明",
		},
		//"updated_at": bson.M{
		//	"$lte": 1710420059000, // 2024-03-14 20:40:59
		//},
		//"$gte": 1710429659000, // 2024-03-14 23:20:59
	}

	list, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
	if err != nil {
		return
	}

	pointLongitude := 102.746418
	pointLatitude := 25.025472

	var c int
	for _, buyer := range list {
		addrL := buyer.Location
		km, deliverFee, person, track := util.CalcDeliverFee(pointLongitude, pointLatitude, addrL)

		_ = deliverFee
		_ = person
		_ = track
		//_=deliverFee

		if km <= 12 {
			c++
		}

	}

	zap.S().Infof("总计:%d", c)

	// 昆明地区被拒绝的历史会员，地址距昆明仓
	// 1. 10km内，有59个
	// 2. 12km内，有72个
	// 3. 15km内，有80个
}

// 对新增的会员信息进行参数初始化

func initBuyerInfo(ctx context.Context) {
	dao := buyerDao.NewBuyerDao()

	buyers, err := dao.ListByCus(ctx, bson.M{
		"instant_deliver": nil,
	})

	if err != nil {
		return
	}

	pointLongitude := 102.746418
	pointLatitude := 25.025472

	for _, buyer := range buyers {
		format := time.UnixMilli(buyer.CreatedAt).Format(time.RFC3339)
		zap.S().Infof("创建时间：%s,需要初始化：%s", format, buyer.BuyerName)

		addrL := buyer.Location

		km, deliverFee, person, track := util.CalcDeliverFee(pointLongitude, pointLatitude, addrL)

		entity := 2
		if buyer.BusinessLicenseImg.Name != "" {
			entity = 1
		}

		pointID, _ := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
		_ = pointID

		update := bson.M{
			//"service_point_id":   pointID,
			//"service_point_name": "昆明城市服务仓",
			//"buyer_id":           buyer.ID,
			//"instant_deliver":    instantDeliver,
			//"audit_status":       model.AuditStatusTypePass,
			"entity":             entity,
			"service_point_id":   pointID,
			"service_point_name": "昆明城市服务仓",
		}

		// 配送到店
		instantDeliver := make([]model.InstantDeliver, 0, 2)
		instantDeliver = append(instantDeliver, model.InstantDeliver{
			ID:     1,
			Name:   "跑腿",
			Amount: person,
		})
		instantDeliver = append(instantDeliver, model.InstantDeliver{
			ID:     2,
			Name:   "货拉拉",
			Amount: track,
		})

		update["deliver_fee"] = deliverFee
		update["instant_deliver"] = instantDeliver

		if km <= 16 {
			update["deliver_type"] = []model.DeliverType{model.DeliverTypeDoor, model.DeliverTypeSelfPickUp, model.DeliverTypeInstantDeliver}
			update["deliver_free_begin"] = 1710345600000
			update["deliver_free_end"] = 1710950399999
		}

		if km > 16 {
			update["deliver_type"] = []model.DeliverType{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics}
		}

		buyerDao.NewBuyerDao().Update(ctx, bson.M{
			"_id": buyer.ID,
		}, bson.M{"$set": update})
		if err != nil {
			zap.S().Errorf("更新地址失败：%s", buyer.ID.Hex())
			return
		}
	}
}

func updateOneAddr(ctx context.Context) {
	//instantDeliver := make([]model.InstantDeliver, 0, 2)
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     1,
	//	Name:   "跑腿",
	//	Amount: 1800,
	//})
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     2,
	//	Name:   "货拉拉",
	//	Amount: 6000,
	//})
	//
	//update := bson.M{
	//	"deliver_type":    []model.DeliverType{model.DeliverTypeInstantDeliver},
	//	"instant_deliver": instantDeliver,
	//	"logistics_note":  "首次物流下单，请联系客服",
	//}
	////id, _ := util.ConvertToObjectWithCtx(ctx, "65e545534d63d6d23acc4f13")
	//err := userAddrService.NewUserAddrService().UpdateMany(ctx, bson.M{
	//	"audit_status": model.AuditStatusTypeDoing,
	//}, bson.M{"$set": update})
	//if err != nil {
	//	zap.S().Errorf("更新地址失败：%s", err.Error())
	//	return
	//}

}

func dealKunmingAddr(ctx context.Context) {
	filter := bson.M{
		"location.address": bson.M{
			"$regex": "昆明",
		},
	}

	list, err := userAddrService.NewUserAddrService().ListByCus(ctx, filter)
	if err != nil {
		return
	}
	_ = list

	var KMIDs []primitive.ObjectID

	for _, address := range list {
		KMIDs = append(KMIDs, address.ID)
	}

	//	 昆明
	//update := bson.M{
	//	"service_point_id":   pointID,
	//	"service_point_name": "昆明城市服务仓",
	//	"buyer_id":           buyer.ID,
	//	"instant_deliver":    instantDeliver,
	//	"audit_status":       model.AuditStatusTypeDoing,
	//}
	//err = userAddrService.NewUserAddrService().UpdateCus(ctx, bson.M{
	//	"_id": address.ID,
	//}, bson.M{"$set": update})
	//if err != nil {
	//	zap.S().Errorf("更新地址失败：%s", address.UserID.Hex())
	//	return
	//}
	//	 所有
	//update := bson.M{
	//	"deliver_type":   []model.DeliverType{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
	//	"logistics_note": "首次物流下单，请联系客服",
	//}
	//id, _ := util.ConvertToObjectWithCtx(ctx, "65e545534d63d6d23acc4f13")
	//err = userAddrService.NewUserAddrService().UpdateMany(ctx, bson.M{
	//	"_id": id,
	//}, bson.M{"$set": update})
	//if err != nil {
	//	zap.S().Errorf("更新地址失败：%s", err.Error())
	//	return
	//}

}

func dealAddr(ctx context.Context) {
	// 服务仓id
	// 服务仓名称
	// 会员id

	list, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
		//"audit_status": bson.M{
		//	"$ne": model.AuditStatusTypePass,
		//},
		//"buyer_id": primitive.NilObjectID,
	})
	if err != nil {
		return
	}
	_ = list

	//pointID, _ := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
	//_ = pointID

	instantDeliver := make([]model.InstantDeliver, 0, 2)
	instantDeliver = append(instantDeliver, model.InstantDeliver{
		ID:     1,
		Name:   "跑腿",
		Amount: 1800,
	})
	instantDeliver = append(instantDeliver, model.InstantDeliver{
		ID:     2,
		Name:   "货拉拉",
		Amount: 6000,
	})

	//for i, address := range list {
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, address.UserID)
	//if err != nil {
	//	zap.S().Errorf("地址index:%d,查询会员失败：%s", i+1, address.UserID.Hex())
	//
	//	//	 直接删除
	//	//err := userAddrService.NewUserAddrService().Del(ctx, address.ID, address.UserID)
	//	//if err != nil {
	//	//	zap.S().Errorf("地址删除失败:%s", address.ID.Hex())
	//	//	return
	//	//}
	//}
	//_ = buyer
	//		update := bson.M{
	//			//"service_point_id":   pointID,
	//			//"service_point_name": "昆明城市服务仓",
	//			//"buyer_id":           buyer.ID,
	//			"instant_deliver": instantDeliver,
	//			//"audit_status":       model.AuditStatusTypeDoing,
	//		}
	//		err = userAddrService.NewUserAddrService().UpdateCus(ctx, bson.M{
	//			"_id": address.ID,
	//		}, bson.M{"$set": update})
	//		if err != nil {
	//			zap.S().Errorf("更新地址失败：%s", address.UserID.Hex())
	//			return
	//		}
	//		zap.S().Errorf("更新地址index：%d", i+1)
	//	}
}
