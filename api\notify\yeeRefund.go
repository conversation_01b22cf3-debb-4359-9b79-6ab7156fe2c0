package notify

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderAdjustSettleService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"encoding/json"
	"sync"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var refundLock sync.Mutex

// YeeTradeRefundCancel 子单退款
func YeeTradeRefundCancel(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("子单退款：%s", call)

	var r model.YeeTradeRefundNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = orderRefundService.NewOrderRefundService().YeeNotifyCancel(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}

// YeeTradeRefundAfterSale 售后
func YeeTradeRefundAfterSale(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("售后退款：%s", call)

	var r model.YeeTradeRefundNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = orderRefundService.NewOrderRefundService().YeeNotifyRefundAfterSale(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}

func YeeTradeRefundDeliver(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("配送费退款：%s", call)

	var r model.YeeTradeRefundNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = orderRefundService.NewOrderRefundService().YeeNotifyRefundDeliver(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}

// YeeTradeRefundQuality 品控
func YeeTradeRefundQuality(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("品控退款：%s", call)

	var r model.YeeTradeRefundNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = orderDebtService.NewOrderDebtService().YeeNotifyRefundQuality(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}

// YeeTradeRefundAdjustSettle 调整结算退款
func YeeTradeRefundAdjustSettle(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("调整结算退款：%s", call)

	var r model.YeeTradeRefundNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = orderAdjustSettleService.NewService().YeeNotifyRefundAdjustSettle(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}

// NotifyUrlYeePayTradeRefundManual 手动退款回调
func NotifyUrlYeePayTradeRefundManual(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("手动退款：%s", call)

	xhttp.NotifyYeeRefundSuccess(ctx)
}

// YeeAccountBookRefund 记账簿充值-退款
func YeeAccountBookRefund(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("记账簿充值-退款：%s", call)

	xhttp.NotifyYeeRefundSuccess(ctx)
}

//
//// YeeAccountBookRefundPayCancel 记账簿支付-退款-取消订单
//func YeeAccountBookRefundPayCancel(ctx *gin.Context) {
//	call, err := ParseYeeCall(ctx)
//	if err != nil {
//		zap.S().Errorf("%s", err.Error())
//		return
//	}
//	_ = call
//
//	zap.S().Infof("记账簿支付-退款-取消订单：%s", call)
//
//	var r model.YeeTradeRefundNotify
//
//	err = json.Unmarshal([]byte(call), &r)
//	if err != nil {
//		zap.S().Errorf("解析回调失败：%s", err.Error())
//		return
//	}
//
//	err = orderRefundService.NewOrderRefundService().YeeNotifyCancel(ctx, r)
//	if err != nil {
//		zap.S().Errorf("更新回调异常：%s", err.Error())
//		return
//	}
//
//	xhttp.NotifyYeeRefundSuccess(ctx)
//}

// YeeTradeRefundDebtPaid 补差已支付
func YeeTradeRefundDebtPaid(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("补差已支付退款：%s", call)

	var r model.YeeTradeRefundNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = orderDebtService.NewOrderDebtService().YeeNotifyRefundDebtPaid(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}
