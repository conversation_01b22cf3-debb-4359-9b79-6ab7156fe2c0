package handler

import (
	"base/api/yeeMerchant"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 认证
func yeeMerchantRouter(r *gin.RouterGroup) {
	r = r.Group("/yee/merchant")

	r.Use(middleware.CheckToken)

	r.POST("/get/by/supplier", yeeMerchant.GetBySupplier)
	r.POST("/get/by/buyer", yeeMerchant.GetByBuyer)
	r.POST("/get/by/service/point", yeeMerchant.GetByPoint)
	r.POST("/get/register/status/by/supplier", yeeMerchant.QueryMerchantRegister)
	r.POST("/get/register/status/by/service/point", yeeMerchant.QueryMerchantRegisterByPoint)
	r.POST("/update", yeeMerchant.Update)
	r.POST("/update/by/second/point", yeeMerchant.UpdateSecondPoint)
	r.POST("/upload", yeeMerchant.MerchantUpload)
	r.POST("/upload/by/service/point", yeeMerchant.MerchantUploadByPoint)

	r.POST("/register", yeeMerchant.Register)
	r.POST("/register/by/service/point", yeeMerchant.RegisterByPoint)
	r.POST("/wechat/auth/apply", yeeMerchant.WechatAuthApply)
	r.POST("/wechat/auth/get", yeeMerchant.WechatAuthGet)
	r.POST("/wechat/auth/query", yeeMerchant.WechatAuthQuery)
	r.POST("/wechat/auth/cancel", yeeMerchant.MerchantWechatAuthCancel)

	r.POST("/wechat/auth/apply/by/service/point", yeeMerchant.WechatAuthApplyByPoint)
	r.POST("/wechat/auth/get/by/service/point", yeeMerchant.WechatAuthGetByPoint)

	r.POST("/wechat/app/id/config", yeeMerchant.WechatAppIDConfig)

	// 记账簿
	r.POST("/account/book/open", yeeMerchant.AccountOpen)
	r.POST("/account/book/query", yeeMerchant.AccountQuery)
	r.POST("/account/book/recharge/query", yeeMerchant.AccountRechargeQuery)
	r.POST("/account/book/recharge/refund", yeeMerchant.AccountRechargeRefund)

	r.POST("/account/withdraw/card/query/by/supplier", yeeMerchant.AccountWithDrawCardQueryBySupplier)
	r.POST("/account/withdraw/card/query/by/point", yeeMerchant.AccountWithDrawCardQueryByPoint)
	r.POST("/account/withdraw/card/bind/by/supplier", yeeMerchant.AccountWithDrawCardBindBySupplier)
	r.POST("/account/withdraw/by/supplier", yeeMerchant.AccountWithDrawBySupplier)
	r.POST("/account/withdraw/by/point", yeeMerchant.AccountWithDrawByPoint)
	r.POST("/account/withdraw/query", yeeMerchant.AccountWithDrawQuery)

	r.POST("/balance/query/by/supplier", yeeMerchant.BalanceQueryBySupplier)
	r.POST("/balance/query/by/service/point", yeeMerchant.BalanceQueryByPoint)

	r.POST("/product/query/by/supplier", yeeMerchant.QueryProductBySupplier)
	r.POST("/product/update/by/supplier", yeeMerchant.UpdateProductBySupplier)

	//
	//// 绑定支付手机号
	//r.POST("/bind/pay/phone", authentication.BindPayMobile)
	//r.POST("/bind/pay/phone/by/auth", authentication.BindPayMobileByAuth)
	//r.POST("/bind/pay/phone/by/station", authentication.BindPayMobileByStation)
	//r.POST("/send/pay/phone/by/station", authentication.SendPayBindMobileByStation)
	//r.POST("/unbind/pay/mobile/by/auth", authentication.UnbindPayMobile)
	//r.POST("/company/info/set", authentication.SetCompany)
	//r.POST("/company/bind/personal/bank", authentication.BindCompanyPersonalBank)
	//// 提现协议
	//r.POST("/sign/acct/protocol", withdraw.SignAcctProtocol)
	//r.POST("/sign/acct/protocol/buyer", withdraw.SignAcctProtocolBuyer)
	//r.POST("/sign/acct/protocol/query", withdraw.SignContractQuery)
	//r.POST("/sign/acct/protocol/front/back", withdraw.SignContractFrontBack)
	//
	////	银行卡
	//r.POST("/bank/unbind", authentication.UnbindBank)
	//r.POST("/bank/unbind/individual", authentication.UnbindIndividualBank)
	//r.POST("/bank/apply/bind", authentication.ApplyBindBank)
}
