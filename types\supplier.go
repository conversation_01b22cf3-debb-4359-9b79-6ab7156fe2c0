package types

import (
	"base/model"
)

type SupplierApplyReq struct {
	ID              string              `json:"id"`                                      // 供应商ID
	ShopSimpleName  string              `json:"shop_simple_name"  validate:"required"`   // 店铺简称
	ServicePointID  string              `json:"service_point_id"`                        // 服务仓
	StationID       string              `json:"station_id"`                              // 服务仓
	Level           model.SupplierLevel `json:"level"`                                   // 供应商等级  point：一级，station：二级
	ContactUserName string              `json:"contact_user_name"   validate:"required"` // 联系人
	Mobile          string              `json:"mobile"`
	Location        model.Location      `json:"location" validate:"dive"`    // 定位地址
	Address         string              `json:"address" validate:"required"` // 详细地址
}

// ShopHeadImg       model.FileInfo    `bson:"shop_head_img" json:"shop_head_img" validate:"required"`        // 门头照
// ShopImgList       []model.FileInfo  `bson:"shop_img_list" json:"shop_img_list" validate:"required"`        // 经营场所-图片列表
// MainBusiness      []model.MainBusinessType `json:"main_business" validate:"min=1"`                                // 主营行业
// InvoiceSupport    bool                     `json:"invoice_support"`                                               // 发票支持
// OriginSiteID      string                   `json:"origin_site_id"`                                                // 旧数据siteID
// ShopName          string                   `bson:"shop_name" json:"shop_name"  validate:"required"`               // 店铺名称

// AuthenticationReq 认证请求
type AuthenticationReq struct {
	UserID              string            `json:"user_id"`                                 // 用户ID
	ContactUserName     string            `json:"contact_user_name"   validate:"required"` // 联系人
	CompanyType         model.CompanyType `json:"company_type" validate:"oneof=1 2"`       // 企业类型
	Location            model.Location    `json:"location" validate:"dive"`                // 定位地址
	Address             string            `json:"address" validate:"required"`             // 详细地址
	BusinessLicenseImg  model.FileInfo    `json:"business_license_img"`                    // 营业执照图
	LegalPhone          string            `json:"legal_phone" validate:"required"`         // 法人手机号
	LegalIdCardFrontImg model.FileInfo    `json:"legal_id_card_front_img"`                 // 法人身份证-正面
	LegalIdCardBackImg  model.FileInfo    `json:"legal_id_card_back_img"`                  // 法人身份证-背面
	BankcardImg         model.FileInfo    `json:"bankcard_img"`                            // 银行卡图片
	CardNumber          string            `json:"card_number" validate:"required"`         // 账号
	BankName            string            `json:"bank_name"`                               // 对公--开户行支行名称
	UnionBank           string            `json:"union_bank"`                              // 对公--支付行号，12位数字
	BankReservedMobile  string            `json:"bank_reserved_mobile"`                    // 对私--银行预留手机号
}

type SupplierRes struct {
	model.Supplier
	IsMobileVerify bool   `json:"is_mobile_verify"`
	PayMobile      string `json:"pay_mobile"`
}
