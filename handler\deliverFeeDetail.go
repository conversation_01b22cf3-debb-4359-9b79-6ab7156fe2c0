package handler

import (
	"base/api/deliverFeeDetail"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 配送费明细
func deliverFeeDetailRouter(r *gin.RouterGroup) {
	r = r.Group("/deliver/fee/detail")
	r.Use(middleware.CheckToken)

	// 获取单个配送费明细记录
	r.POST("/get", deliverFeeDetail.Get)
	// 统计配送费明细
	r.POST("/stats", deliverFeeDetail.Stats)
	// 按月查询配送费明细
	r.POST("/list/monthly", deliverFeeDetail.ListMonthly)
	// 按日查询配送费明细
	r.POST("/list/daily", deliverFeeDetail.ListDaily)
}
