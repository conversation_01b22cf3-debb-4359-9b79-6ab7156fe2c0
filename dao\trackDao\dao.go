package trackDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, swipe model.Track) error
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Track, int64, error)
	GetLatest(ctx context.Context, filter bson.M) (model.Track, error)
	List(ctx context.Context, filter bson.M) ([]model.Track, error)
	Get(ctx context.Context, filter bson.M) (model.Track, error)
}

type trackDao struct {
	db *mongo.Collection
}

func NewTrackDao(collect string) DaoInt {
	return trackDao{
		db: global.MDB.Collection(collect),
	}
}

func (s trackDao) Get(ctx context.Context, filter bson.M) (model.Track, error) {
	var data model.Track
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Track{}, err
	}
	return data, nil
}

func (s trackDao) Create(ctx context.Context, swipe model.Track) error {
	_, err := s.db.InsertOne(ctx, swipe)
	if err != nil {
		return err
	}

	return nil
}

func (s trackDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s trackDao) DeleteMany(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	_ = res

	return nil
}

func (s trackDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Track, int64, error) {
	var list []model.Track
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s trackDao) GetLatest(ctx context.Context, filter bson.M) (model.Track, error) {
	var list []model.Track
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return model.Track{}, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return model.Track{}, err
	}
	if len(list) > 0 {
		return list[0], err
	}

	return model.Track{}, nil
}

func (s trackDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s trackDao) List(ctx context.Context, filter bson.M) ([]model.Track, error) {
	var list []model.Track
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}
