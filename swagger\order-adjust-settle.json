{"swagger": "2.0", "info": {"title": "订单调整结算 API", "description": "订单调整结算管理系统，支持订单调价的创建、查询、确认和退款等操作", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "host": "api.example.com", "basePath": "/v1", "schemes": ["https", "http"], "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {"bearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "description": "Bearer token for authentication. Format: 'Bearer {token}'"}}, "security": [{"bearerAuth": []}], "tags": [{"name": "orderAdjustSettle", "description": "订单调整结算相关接口"}], "paths": {"/order-adjust-settle/create": {"post": {"tags": ["orderAdjustSettle"], "summary": "创建调整结算记录", "description": "为指定订单创建调整结算记录，支持多商品调价", "operationId": "createOrderAdjustSettle", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "创建调整结算记录请求参数", "schema": {"$ref": "#/definitions/OrderAdjustSettleCreateRequest"}}], "responses": {"200": {"description": "创建成功", "schema": {"$ref": "#/definitions/ApiResponse"}, "examples": {"application/json": {"code": 200, "message": "success", "data": null}}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/order-adjust-settle/list": {"post": {"tags": ["orderAdjustSettle"], "summary": "查询调整结算记录列表", "description": "分页查询调整结算记录列表，支持按采购商和供应商筛选", "operationId": "listOrderAdjustSettle", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "查询调整结算记录列表请求参数", "schema": {"$ref": "#/definitions/OrderAdjustSettleListRequest"}}], "responses": {"200": {"description": "查询成功", "schema": {"allOf": [{"$ref": "#/definitions/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/OrderAdjustSettleListResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/order-adjust-settle/get": {"post": {"tags": ["orderAdjustSettle"], "summary": "获取调整结算记录详情", "description": "根据调整结算记录ID获取详细信息", "operationId": "getOrderAdjustSettle", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "获取调整结算记录详情请求参数", "schema": {"type": "object", "properties": {"id": {"type": "string", "description": "调整结算记录ID", "example": "507f1f77bcf86cd799439016"}}, "required": ["id"]}}], "responses": {"200": {"description": "查询成功", "schema": {"allOf": [{"$ref": "#/definitions/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/OrderAdjustSettle"}}}]}}, "404": {"description": "记录不存在", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/order-adjust-settle/get-by-order-id": {"post": {"tags": ["orderAdjustSettle"], "summary": "根据订单ID获取调整结算记录", "description": "根据订单ID获取对应的调整结算记录", "operationId": "getOrderAdjustSettleByOrderId", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "根据订单ID获取调整结算记录请求参数", "schema": {"type": "object", "properties": {"order_id": {"type": "string", "description": "订单ID", "example": "507f1f77bcf86cd799439011"}}, "required": ["order_id"]}}], "responses": {"200": {"description": "查询成功", "schema": {"allOf": [{"$ref": "#/definitions/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/OrderAdjustSettle"}}}]}}, "404": {"description": "记录不存在", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/order-adjust-settle/update": {"post": {"tags": ["orderAdjustSettle"], "summary": "编辑调整结算记录", "description": "编辑调整结算记录（仅草稿状态可编辑）", "operationId": "updateOrderAdjustSettle", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "编辑调整结算记录请求参数", "schema": {"$ref": "#/definitions/OrderAdjustSettleUpdateRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/ApiResponse"}}, "400": {"description": "请求参数错误或状态不允许编辑", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/order-adjust-settle/confirm": {"post": {"tags": ["orderAdjustSettle"], "summary": "确认调整结算记录", "description": "确认调整结算记录，进入退款流程", "operationId": "confirmOrderAdjustSettle", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "确认调整结算记录请求参数", "schema": {"type": "object", "properties": {"id": {"type": "string", "description": "调整结算记录ID", "example": "507f1f77bcf86cd799439016"}}, "required": ["id"]}}], "responses": {"200": {"description": "确认成功", "schema": {"$ref": "#/definitions/ApiResponse"}}, "400": {"description": "状态不允许确认", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/order-adjust-settle/close": {"post": {"tags": ["orderAdjustSettle"], "summary": "关闭调整结算记录", "description": "关闭调整结算记录（仅草稿状态可关闭）", "operationId": "closeOrderAdjustSettle", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "关闭调整结算记录请求参数", "schema": {"type": "object", "properties": {"id": {"type": "string", "description": "调整结算记录ID", "example": "507f1f77bcf86cd799439016"}}, "required": ["id"]}}], "responses": {"200": {"description": "关闭成功", "schema": {"$ref": "#/definitions/ApiResponse"}}, "400": {"description": "状态不允许关闭", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}}, "definitions": {"ApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"type": "object", "description": "响应数据"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误状态码", "example": 400}, "message": {"type": "string", "description": "错误消息", "example": "参数错误"}, "data": {"type": "object", "example": null}}}, "OrderAdjustSettleCreateRequest": {"type": "object", "required": ["order_id", "remark", "product_list"], "properties": {"order_id": {"type": "string", "description": "订单ID", "example": "507f1f77bcf86cd799439011"}, "remark": {"type": "string", "description": "调整备注", "example": "商品价格调整"}, "product_list": {"type": "array", "description": "商品调整列表", "items": {"$ref": "#/definitions/ProductAdjustSettleRequest"}}}}, "OrderAdjustSettleListRequest": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "minimum": 1, "example": 1}, "limit": {"type": "integer", "description": "每页数量", "minimum": 1, "maximum": 100, "example": 20}, "buyer_id": {"type": "string", "description": "采购商ID（可选）", "example": "507f1f77bcf86cd799439014"}, "supplier_id": {"type": "string", "description": "供应商ID（可选）", "example": "507f1f77bcf86cd799439015"}}}, "OrderAdjustSettleUpdateRequest": {"type": "object", "required": ["id", "remark", "product_list"], "properties": {"id": {"type": "string", "description": "调整结算记录ID", "example": "507f1f77bcf86cd799439016"}, "remark": {"type": "string", "description": "调整备注", "example": "更新后的调整备注"}, "product_list": {"type": "array", "description": "商品调整列表", "items": {"$ref": "#/definitions/ProductAdjustSettleRequest"}}}}, "ProductAdjustSettleRequest": {"type": "object", "required": ["product_id", "sku_id_code", "adjustment_amount"], "properties": {"product_id": {"type": "string", "description": "商品ID", "example": "507f1f77bcf86cd799439012"}, "sku_id_code": {"type": "string", "description": "SKU编码", "example": "SKU001"}, "adjustment_amount": {"type": "integer", "description": "调整金额（分），正数表示涨价，负数表示降价", "example": -500}, "adjustment_remark": {"type": "string", "description": "调整备注", "example": "价格下调5元"}}}, "OrderAdjustSettleListResponse": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数", "example": 100}, "list": {"type": "array", "description": "调整结算记录列表", "items": {"$ref": "#/definitions/OrderAdjustSettle"}}}}, "OrderAdjustSettle": {"type": "object", "properties": {"id": {"type": "string", "description": "调整结算记录ID", "example": "507f1f77bcf86cd799439016"}, "order_id": {"type": "string", "description": "关联订单ID", "example": "507f1f77bcf86cd799439011"}, "buyer_id": {"type": "string", "description": "采购商ID", "example": "507f1f77bcf86cd799439014"}, "buyer_name": {"type": "string", "description": "采购商名称", "example": "张三采购商"}, "supplier_id": {"type": "string", "description": "供应商ID", "example": "507f1f77bcf86cd799439015"}, "supplier_name": {"type": "string", "description": "供应商名称", "example": "李四供应商"}, "total_amount": {"type": "integer", "description": "总调整金额（分）", "example": -200}, "product_list": {"type": "array", "description": "商品调整明细列表", "items": {"$ref": "#/definitions/ProductAdjustSettle"}}, "remark": {"type": "string", "description": "调整备注", "example": "商品价格调整"}, "created_by_user_id": {"type": "string", "description": "创建人ID", "example": "507f1f77bcf86cd799439017"}, "created_by_name": {"type": "string", "description": "创建人姓名", "example": "管理员"}, "confirmed_by_user_id": {"type": "string", "description": "确认人ID", "example": "507f1f77bcf86cd799439018"}, "confirmed_by_name": {"type": "string", "description": "确认人姓名", "example": "审核员"}, "refund_result": {"$ref": "#/definitions/YeeRefundResult"}, "status": {"type": "string", "description": "调整结算状态", "enum": ["draft", "confirmed", "refunding", "completed", "failed", "closed"], "example": "confirmed"}, "confirmed_at": {"type": "integer", "description": "确认时间（毫秒时间戳）", "example": 1626789600000}, "created_at": {"type": "integer", "description": "创建时间（毫秒时间戳）", "example": 1626789600000}, "updated_at": {"type": "integer", "description": "更新时间（毫秒时间戳）", "example": 1626789600000}}}, "ProductAdjustSettle": {"type": "object", "properties": {"product_id": {"type": "string", "description": "商品ID", "example": "507f1f77bcf86cd799439012"}, "product_title": {"type": "string", "description": "商品标题", "example": "苹果"}, "product_cover_img": {"type": "string", "description": "商品封面图", "example": "apple.jpg"}, "sku_id_code": {"type": "string", "description": "SKU编码", "example": "SKU001"}, "sku_name": {"type": "string", "description": "SKU名称", "example": "红富士苹果 5kg装"}, "order_amount": {"type": "integer", "description": "订单金额（分）", "example": 5000}, "adjustment_amount": {"type": "integer", "description": "调整金额（分）", "example": -500}, "adjustment_remark": {"type": "string", "description": "调整备注", "example": "价格下调5元"}}}, "YeeRefundResult": {"type": "object", "properties": {"order_id": {"type": "string", "description": "易宝订单ID", "example": "yee_order_123456"}, "refund_request_id": {"type": "string", "description": "退款请求ID", "example": "refund_req_123456"}, "parent_merchant_no": {"type": "string", "description": "父商户号", "example": "10090765586"}, "merchant_no": {"type": "string", "description": "商户号", "example": "10090789568"}, "refund_amount": {"type": "integer", "description": "退款金额（分）", "example": 500}, "status": {"type": "string", "description": "退款状态", "enum": ["PROCESSING", "SUCCESS", "FAIL"], "example": "PROCESSING"}, "notify_payment_method": {"type": "string", "description": "支付方式", "example": "WECHAT"}, "notify_refund_success_date": {"type": "string", "description": "退款成功日期", "example": "2023-07-14 18:30:00"}, "notify_error_message": {"type": "string", "description": "错误消息", "example": ""}, "notify_unique_refund_no": {"type": "string", "description": "唯一退款单号", "example": "refund_unique_123456"}}}}}