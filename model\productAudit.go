package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// ProductAudit 商品审核
type ProductAudit struct {
	ID              primitive.ObjectID `bson:"_id" json:"id"`
	Product         Product            `json:"product" bson:"product"`
	IsNew           bool               `bson:"is_new" json:"is_new"`
	EditAuditStatus AuditStatusType    `bson:"edit_audit_status" json:"edit_audit_status"` // 编辑审核状态
	EditFailReason  string             `bson:"edit_fail_reason" json:"edit_fail_reason"`   // 编辑审核失败原因
	EditCreatedAt   int64              `bson:"edit_created_at" json:"edit_created_at"`
	EditUpdatedAt   int64              `bson:"edit_updated_at" json:"edit_updated_at"`
	EditDeletedAt   int64              `bson:"edit_deleted_at" json:"edit_deleted_at"`
}
