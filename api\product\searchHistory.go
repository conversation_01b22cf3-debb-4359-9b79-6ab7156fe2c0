package product

import (
	"base/core/xhttp"
	"base/service/productSearchTopService"
	"base/service/productService"
	"github.com/gin-gonic/gin"
)

// SearchTopList 搜索榜单列表
func SearchTopList(ctx *gin.Context) {
	list, err := productSearchTopService.NewProductSearchTopService().List(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

// SearchTopUpsert 搜索榜单更新
func SearchTopUpsert(ctx *gin.Context) {
	var req = struct {
		List []string `json:"list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = productSearchTopService.NewProductSearchTopService().Upsert(ctx, req.List)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// SearchHistory 搜索历史
func SearchHistory(ctx *gin.Context) {
	list, err := productService.NewProductService().ListSearchHistory(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}
