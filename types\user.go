package types

import (
	"base/model"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LoginUserRes 登录用户响应
type LoginUserRes struct {
	AccessToken string             `json:"access_token"`
	RefreshToken string             `json:"refresh_token"`
	Expires     int64              `json:"expires"`
	UserID      primitive.ObjectID `json:"user_id"`
	User        model.User         `json:"user"`
	RoleInfo    []model.RoleInfo   `bson:"role_info" json:"role_info"`
}

// LoginRes 登录响应
type LoginRes struct {
	AccessToken string           `json:"access_token"`
	Expires     int64            `json:"expires"`
	Buyer       model.Buyer      `json:"buyer"`
	AuthList    []model.AuthType `json:"auth_list"`
}

// LoginAdminRes 登录管理员响应
type LoginAdminRes struct {
	AccessToken string           `json:"access_token"`
	Expires     int64            `json:"expires"`
	Admin       model.Admin      `json:"admin"`
	AuthList    []model.AuthType `json:"auth_list"`
}

//type LoginRefreshToken struct {
//	AccessToken  string `json:"access_token"`
//	RefreshToken string `json:"refresh_token"`
//	Expires      int64  `json:"expires"`
//}

// AdminRes 管理员响应
type AdminRes struct {
	model.Admin
	Mobile string `json:"mobile"`
}
