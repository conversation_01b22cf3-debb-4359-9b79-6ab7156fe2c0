package types

import "go.mongodb.org/mongo-driver/bson/primitive"

// ProfitRes 利润响应
type ProfitRes struct {
	ProductTotalAmount     int `json:"product_total_amount"`      // 商品销售金额
	ProductAfterSaleAmount int `json:"product_after_sale_amount"` // 商品售后金额
	ProductDebtAmount      int `json:"product_debt_amount"`       // 商品补差金额
	ProductProfitAmount    int `json:"product_profit_amount"`     // 商品利润
}

// ProfitSettlementRes 结算利润响应
type ProfitSettlementRes struct {
	ID           primitive.ObjectID `json:"id"`
	SupplierID   primitive.ObjectID `json:"supplier_id"`
	SupplierName string             `json:"supplier_name"`
	Amount       int                `json:"amount"`
	Remark       string             `json:"remark"`
	CreatedAt    int64              `json:"created_at"`
}
