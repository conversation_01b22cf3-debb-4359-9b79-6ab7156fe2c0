package buyerActiveDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 采购商活跃
type DaoInt interface {
	Create(ctx context.Context, data model.BuyerActive) error
	Update(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.BuyerActive, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerActive, int64, error)
}

type buyerActiveDao struct {
	db *mongo.Collection
}

func (s buyerActiveDao) Get(ctx context.Context, filter bson.M) (model.BuyerActive, error) {
	var i model.BuyerActive
	err := s.db.FindOne(ctx, filter).Decode(&i)
	if err != nil {
		return model.BuyerActive{}, err
	}
	return i, nil
}

// List 查询
func (s buyerActiveDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerActive, int64, error) {
	var list []model.BuyerActive
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s buyerActiveDao) Update(ctx context.Context, filter, update bson.M) error {
	one, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = one
	return nil
}

func (s buyerActiveDao) Create(ctx context.Context, data model.BuyerActive) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func NewBuyerActiveDao(collect string) DaoInt {
	return buyerActiveDao{
		db: global.MDB.Collection(collect),
	}
}
