package orderServicePoint

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sort"
)

// ListLogistics  物流单列表
func ListLogistics(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListToArrive service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListLogistics(ctx, id, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, o := range list {
		m[o.BuyerID] = append(m[o.BuyerID], o)
	}
	var resList []logisticsRes
	for i, orders := range m {
		mBuyerAddr := make(map[primitive.ObjectID][]model.Order)
		for _, order := range orders {
			mBuyerAddr[order.Address.AddressID] = append(mBuyerAddr[order.Address.AddressID], order)
		}

		for _, orderList := range mBuyerAddr {

			var sWeight int
			var sNum int

			var existNotLogistics bool
			for _, order := range orderList {
				for _, p := range order.ProductList {
					if !p.IsShipRefundAll {
						sWeight += p.SortWeight
						sNum += p.SortNum
					}
				}
				if !existNotLogistics && order.LogisticsTime == 0 && order.OrderStatus != model.OrderStatusTypeFinish && !order.OrderRefundAll {
					existNotLogistics = true
				}

			}

			var bName string
			if bName == "" {
				bName = orderList[0].BuyerName
				if len(mBuyerAddr) > 1 {
					bName += "【多地址】"
				}
			}

			var logisticsName string
			if logisticsName == "" {
				logisticsName = orderList[0].LogisticsName
			}

			item := logisticsRes{
				BuyerId:   i,
				BuyerName: bName,
				Address:   orderList[0].Address,
				//Distance:   backDistance(point, l.Longitude, l.Latitude),
				SortWeight:    sWeight,
				SortNum:       sNum,
				LogisticsAll:  !existNotLogistics,
				LogisticsName: logisticsName,
				DeliverType:   orderList[0].DeliverType,
			}
			resList = append(resList, item)
		}
	}

	sort.Sort(LogisticsList(resList))

	xhttp.RespSuccess(ctx, resList)
}

// 物流
type logisticsRes struct {
	BuyerId       primitive.ObjectID `json:"buyer_id"`
	BuyerName     string             `json:"buyer_name"`
	Address       model.OrderAddress `json:"address"`
	Distance      int                `json:"distance"`
	SortWeight    int                `json:"sort_weight"`
	SortNum       int                `json:"sort_num"`
	LogisticsAll  bool               `json:"logistics_all"`  // 全部装车
	LogisticsName string             `json:"logistics_name"` // 物流公司名称
	DeliverType   model.DeliverType  `json:"deliver_type"`
}

// LogisticsCreate 物流信息上传
func LogisticsCreate(ctx *gin.Context) {
	var req = struct {
		OrderIDList              []string         `json:"order_id_list"`
		LogisticsCompanyID       string           `json:"logistics_company_id"`
		LogisticsAutoReceiveHour int              `json:"logistics_auto_receive_hour"`
		LogisticsNoList          []string         `json:"logistics_no_list"`
		LogisticsImageList       []model.FileInfo `json:"logistics_image_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.OrderIDList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单ID缺失"))
		return
	}

	if len(req.LogisticsImageList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "确认订单图片缺失"))
		return
	}

	for _, info := range req.LogisticsImageList {
		if info.Name == "" {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "确认订单图片缺失"))
			return
		}
	}

	if req.LogisticsAutoReceiveHour != 12 && req.LogisticsAutoReceiveHour != 24 && req.LogisticsAutoReceiveHour != 36 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "自动收货时间错误"))
		return
	}

	var orderIDs []primitive.ObjectID
	for _, s := range req.OrderIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		orderIDs = append(orderIDs, id)
	}

	err = orderPointService.NewOrderPointService().LogisticsCreate(ctx, orderIDs, req.LogisticsNoList, req.LogisticsImageList, req.LogisticsAutoReceiveHour)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ListByBuyerIDLogistics 物流订单详情
func ListByBuyerIDLogistics(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		BuyerID        string `json:"buyer_id" validate:"len=24"`
		AddressID      string `json:"address_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListByBuyerID service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "ListByBuyerID buyer_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	addressID, err := util.ConvertToObjectWithCtx(ctx, req.AddressID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListByBuyerAndPointLogistics(ctx, buyerID, pointID, addressID, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(list) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	xhttp.RespSuccess(ctx, list)
}
