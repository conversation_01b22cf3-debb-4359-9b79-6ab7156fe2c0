package servicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/servicePointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"sort"
)

// ListByLocation 定位拉取
func ListByLocation(ctx *gin.Context) {
	var req = struct {
		Longitude float64 `json:"longitude" validate:"min=0"` // 经度
		Latitude  float64 `json:"latitude" validate:"min=0"`  // 纬度
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, err := servicePointService.NewServicePointService().ListByLocation(ctx, req.Longitude, req.Latitude)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var resList []res
	for _, i := range list {
		resList = append(resList,
			res{
				ServicePoint: i,
				Distance:     backDistance(i, req.Longitude, req.Latitude),
			})
	}

	sort.Sort(CartList(resList))

	xhttp.RespSuccess(ctx, resList)
}

type res struct {
	model.ServicePoint
	Distance int `json:"distance"` // 米
}

func ListSecondByWeb(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
		"level":      model.ServicePointLevelSecond,
	}

	//if req.OpenStatus == 1 {
	//	filter["is_open"] = false
	//}
	//
	//if req.OpenStatus == 2 {
	//	filter["is_open"] = true
	//}

	list, count, err := servicePointService.NewServicePointService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

func ListSecondByIndex(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
		"level":      model.ServicePointLevelSecond,
		"is_open":    true,
	}

	list, count, err := servicePointService.NewServicePointService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

func backDistance(point model.ServicePoint, lon, lat float64) int {
	if lon == 0 || lat == 0 {
		return 0
	}
	return int(util.LatitudeLongitudeDistance(point.Location.Longitude, point.Location.Latitude, lon, lat))
}

// ListByWarehouse 根据集中仓
func ListByWarehouse(ctx *gin.Context) {
	var req = struct {
		WarehouseID string `json:"warehouse_id" validate:"len=24"`
		IsOpen      bool   `json:"is_open" validate:"-"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.WarehouseID, "warehouse_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := servicePointService.NewServicePointService().ListByWarehouse(ctx, id, req.IsOpen)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, int64(len(list)))

}
