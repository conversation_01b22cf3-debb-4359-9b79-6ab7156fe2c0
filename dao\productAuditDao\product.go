package productAuditDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 商品审核DAO接口
type DaoInt interface {
	Create(ctx context.Context, product model.ProductAudit) error
	Find(ctx context.Context, filter bson.M) ([]model.ProductAudit, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProductAudit, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.ProductAudit, error)
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.ProductAudit, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	DeleteMany(ctx context.Context, filter bson.M) error
}

// ProductAuditDao 商品审核DAO
type ProductAuditDao struct {
	db *mongo.Collection
}

// NewProductAuditDao 创建商品审核DAO
func NewProductAuditDao(collect string) DaoInt {
	return ProductAuditDao{
		db: global.MDB.Collection(collect),
	}
}

// ListByPage 分页查询商品审核列表
func (s ProductAuditDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProductAudit, int64, error) {
	var list []model.ProductAudit
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

// List 查询商品审核列表
func (s ProductAuditDao) List(ctx context.Context, filter bson.M) ([]model.ProductAudit, error) {
	var list []model.ProductAudit
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// Get 获取商品审核
func (s ProductAuditDao) Get(ctx context.Context, filter bson.M) (model.ProductAudit, error) {
	var data model.ProductAudit
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductAudit{}, err
	}
	return data, nil
}

// Create 创建商品审核
func (s ProductAuditDao) Create(ctx context.Context, product model.ProductAudit) error {
	_, err := s.db.InsertOne(ctx, product)
	if err != nil {
		return err
	}
	return nil
}

// Find 查询商品审核
func (s ProductAuditDao) Find(ctx context.Context, filter bson.M) ([]model.ProductAudit, error) {
	// 排序
	var list []model.ProductAudit
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts := options.Find()
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// Update 更新商品审核
func (s ProductAuditDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

// UpdateMany 批量更新商品审核
func (s ProductAuditDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	_ = res
	return err
}

// Count 统计商品审核数量
func (s ProductAuditDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// DeleteMany 删除商品审核
func (s ProductAuditDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(ctx, filter)
	return err
}
