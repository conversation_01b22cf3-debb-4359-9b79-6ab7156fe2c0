package productOffline

import (
	"base/core/xhttp"
	"base/service/productOfflineService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func ListOffline(ctx *gin.Context) {
	var req = struct {
		Timestamp int64 `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	zeroTimestamp, err := util.DayStartZeroTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	offlineInfos, err := productOfflineService.NewProductOfflineService().ListByDay(ctx, zeroTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
	}

	xhttp.RespSuccessList(ctx, offlineInfos, int64(len(offlineInfos)))

}
