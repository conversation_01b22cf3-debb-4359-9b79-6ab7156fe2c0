package trackService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/trackDao"
	"base/global"
	"base/model"
	"base/util"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strconv"
	"time"
)

var cacheOnlineNum = "onlineNum"

var latestActive = "latestActive"

// 用户停留时长
var standingTime = "standingTime:"

type ServiceInterface interface {
	Create(ctx context.Context, data model.Track) error
	ListByBuyerID(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Track, int64, error)
	DeleteMany(ctx context.Context, filter bson.M) error

	RecordEnterApp(userID primitive.ObjectID, ip string, num int)
	RecordLeaveApp(num int)
	RecordStandingTime(userID primitive.ObjectID)
	GetStandingTime(userID primitive.ObjectID) []redis.Z
	GetOnlineNum() int
	ListLatestActive(ctx context.Context, page, limit int64) ([]redis.Z, int64, error)
}

type trackService struct {
	rdb      *redis.Client
	trackDao trackDao.DaoInt
}

func NewTrackService() ServiceInterface {
	return trackService{
		trackDao: dao.TrackDao,
		rdb:      global.RDBDefault,
	}
}

func (s trackService) Create(ctx context.Context, data model.Track) error {
	if data.Event == model.EventTypeProductEnter {
		//	进入商品
		err := s.trackDao.Create(ctx, data)
		if err != nil {
			return err
		}
	}

	if data.Event == model.EventTypeProductLeave {
		//	离开商品
		buyerID := data.BuyerID
		latest, err := s.trackDao.GetLatest(ctx, bson.M{
			"buyer_id":   buyerID,
			"product_id": data.ProductID,
		})
		if err != nil {
			return err
		}
		err = s.trackDao.UpdateOne(ctx, bson.M{
			"_id": latest.ID,
		}, bson.M{
			"$set": bson.M{
				"leaved_at": data.CreatedAt,
			},
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func (s trackService) DeleteMany(ctx context.Context, filter bson.M) error {
	err := s.trackDao.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

//func (s trackService) Get(ctx context.Context, id primitive.ObjectID) (model.Swipe, error) {
//	get, err := s.db.Get(ctx, bson.M{"_id": id})
//	return get, err
//}

func (s trackService) ListByBuyerID(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Track, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}
	list, count, err := s.trackDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}

	_ = count
	_ = list

	return list, count, nil
}

func (s trackService) RecordEnterApp(buyerID primitive.ObjectID, ip string, num int) {
	s.UpdateOnlineNum(num)

	milli := time.Now().UnixMilli()

	m := buyerID.Hex()

	//day, _ := util.DayStartZeroTimestamp(milli)
	//dayStr := strconv.Itoa(int(day))
	s.rdb.ZAdd(context.Background(), latestActive, &redis.Z{
		Member: m,
		Score:  float64(milli),
	})
}

func (s trackService) RecordLeaveApp(num int) {
	s.UpdateOnlineNum(num)
}

func (s trackService) UpdateOnlineNum(num int) {
	s.rdb.Set(context.Background(), cacheOnlineNum, num, time.Hour)
}

func (s trackService) GetOnlineNum() int {
	i, err := s.rdb.Get(context.Background(), cacheOnlineNum).Int()
	if err != nil {
		return 0
	}

	return i
}

func (s trackService) ListLatestActive(ctx context.Context, page, limit int64) ([]redis.Z, int64, error) {
	if page < 1 {
		return nil, 0, xerr.NewErr(xerr.ErrParamError, nil, "页数最小为1")
	}
	key := latestActive

	count := s.rdb.ZCard(ctx, key).Val()

	start := (page - 1) * limit
	stop := page*limit - 1

	zs := s.rdb.ZRevRangeWithScores(ctx, key, start, stop).Val()

	return zs, count, nil
}

func (s trackService) RecordStandingTime(buyerID primitive.ObjectID) {
	if buyerID == primitive.NilObjectID {
		return
	}
	key := standingTime + buyerID.Hex()

	milli := time.Now().UnixMilli()

	zeroTimestamp, err := util.DayStartZeroTimestamp(milli)
	_ = err

	dayStr := strconv.Itoa(int(zeroTimestamp))

	s.rdb.ZIncrBy(context.Background(), key, 5, dayStr)
}

func (s trackService) GetStandingTime(userID primitive.ObjectID) []redis.Z {
	if userID == primitive.NilObjectID {
		return nil
	}
	key := standingTime + userID.Hex()

	zs := s.rdb.ZRevRangeWithScores(context.Background(), key, 0, 4).Val()

	return zs
}
