package adminBuyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func Count(ctx *gin.Context) {
	i, err := buyerService.NewBuyerService().Count(ctx, bson.M{
		"audit_status": model.AuditStatusTypeDoing,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

func CountAudit(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = pointID

	filter := bson.M{
		"audit_status": model.AuditStatusTypeDoing,
	}

	buyerCount, err := buyerService.NewBuyerService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filterAddress := bson.M{
		"audit_status": model.AuditStatusTypeDoing,
	}

	addressCount, err := userAddrService.NewUserAddrService().Count(ctx, filterAddress)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, buyerCount+addressCount)
}
