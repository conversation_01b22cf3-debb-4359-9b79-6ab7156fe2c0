package stationDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Station) error
	Update(ctx context.Context, filter, update bson.M) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Station, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.Station, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Station, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Station, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type stationDao struct {
	db *mongo.Collection
}

func (s stationDao) Get(ctx context.Context, id primitive.ObjectID) (model.Station, error) {
	var data model.Station
	err := s.db.FindOne(ctx, bson.M{"_id": id}).Decode(&data)
	if err != nil {
		return model.Station{}, err
	}
	return data, nil
}

func (s stationDao) Create(ctx context.Context, data model.Station) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s stationDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s stationDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s stationDao) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Station, error) {
	var data model.Station
	err := s.db.FindOne(ctx, bson.M{"user_id": userID}).Decode(&data)
	if err != nil {
		return model.Station{}, err
	}
	return data, nil
}

func (s stationDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Station, int64, error) {
	var list []model.Station
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// List 查询
func (s stationDao) List(ctx context.Context, filter bson.M) ([]model.Station, error) {
	var list []model.Station
	//skip := (page - 1) * limit
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}
func NewStationDao(collect string) DaoInt {
	return stationDao{
		db: global.MDB.Collection(collect),
	}
}
