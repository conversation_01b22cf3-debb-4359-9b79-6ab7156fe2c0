package util

import (
	"errors"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"time"
)

func MysqlInit(connStr string, l logger.LogLevel) (*gorm.DB, error) {
	//connStr += "?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=Local"

	slowLogger := logger.New(
		//将标准输出作为Writer
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			//设定慢查询时间阈值为1ms
			SlowThreshold: 500 * 1000 * time.Microsecond,
			//设置日志级别，只有Warn和Info级别会输出慢查询日志
			LogLevel: l,
			//IgnoreRecordNotFoundError: true,
			Colorful: true,
		},
	)

	db, err := gorm.Open(mysql.Open(connStr), &gorm.Config{
		Logger: slowLogger,
	})

	if err != nil {
		return nil, err
	}
	if db == nil {
		return nil, errors.New("[error] 连接失败")
	}
	sqlDB, _ := db.DB()
	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(100)

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(100)

	// SetConnMaxLifetime 设置了连接可复用的最大时间。

	sqlDB.SetConnMaxLifetime(100 * time.Second)

	//connections are not closed due to a connection's idle time.

	sqlDB.SetConnMaxIdleTime(100 * time.Second)

	err = sqlDB.Ping()
	if err != nil {
		return nil, errors.New("mysql 启动失败:" + err.Error())
	}
	return db, nil
}
