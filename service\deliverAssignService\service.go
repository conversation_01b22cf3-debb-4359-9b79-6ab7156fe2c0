package deliverAssigneService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/deliverAssignDao"
	"base/global"
	"base/model"
	"base/service/miniService"
	"base/service/orderService"
	"base/service/userService"
	"context"
	"encoding/base64"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// ServiceInterface 配送指派
type ServiceInterface interface {
	CreateAssign(ctx context.Context, pointID primitive.ObjectID, deliveryMan model.DeliveryMan, buyerIDs []primitive.ObjectID, begin, end, timestamp int64, deliverType model.DeliverType) error
	Create(ctx context.Context, userID primitive.ObjectID, key string, timestamp int64) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	ListByUser(ctx context.Context, userID primitive.ObjectID, timestamp int64) ([]model.DeliverAssign, error)
	ListByBuyerIDs(ctx context.Context, ids []primitive.ObjectID, timestamp int64) ([]model.DeliverAssign, error)
	GetDeliverQr(ctx context.Context, pointID primitive.ObjectID, buyerIDs []primitive.ObjectID, begin, end int64) (string, error)
	GetDeliverByKey(ctx context.Context, key string) ([]model.DeliverAssignInfo, error)
}

type deliverAssignService struct {
	mdb              *mongo.Database
	rdb              *redis.Client
	DeliverAssignDao deliverAssignDao.DaoInt
	MiniS            miniService.ServiceInterface
	UserS            userService.ServiceInterface
	orderS           orderService.ServiceInterface
}

func NewDeliverAssignService() ServiceInterface {
	return deliverAssignService{
		mdb:              global.MDB,
		rdb:              global.RDBDefault,
		DeliverAssignDao: dao.DeliverAssignDao,
		MiniS:            miniService.NewMiniService(),
		UserS:            userService.NewUserService(),
		orderS:           orderService.NewOrderService(),
	}
}

func (s deliverAssignService) CreateAssign(ctx context.Context, pointID primitive.ObjectID, deliveryMan model.DeliveryMan, buyerIDs []primitive.ObjectID, begin, end, timestamp int64, deliverType model.DeliverType) error {
	filter := bson.M{
		"service_point_id": pointID,
		//"delivery_allot_has": true,
		"deliver_type": deliverType,
		//"order_status": model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return err
	}

	var orderAllIDs []primitive.ObjectID
	for _, order := range orders {
		orderAllIDs = append(orderAllIDs, order.ID)
	}

	// 判断
	count, err := s.DeliverAssignDao.Count(ctx, bson.M{
		"order_id_list": bson.M{
			"$in": orderAllIDs,
		},
	})
	if err != nil {
		return err
	}
	if count > 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "存在订单已分配")
	}

	mBuyerName := make(map[primitive.ObjectID]string)
	mBuyer := make(map[primitive.ObjectID][]model.Order)
	for _, order := range orders {
		mBuyer[order.BuyerID] = append(mBuyer[order.BuyerID], order)
		mBuyerName[order.BuyerID] = order.BuyerName
	}

	now := time.Now().UnixMilli()

	dataList := make([]model.DeliverAssign, 0)

	for buyerID, orderList := range mBuyer {
		orderIDs := make([]primitive.ObjectID, 0)
		var sWeight int
		var sNum int

		for _, order := range orderList {
			orderIDs = append(orderIDs, order.ID)
			for _, p := range order.ProductList {
				if !p.IsShipRefundAll {
					sWeight += p.SortWeight
					sNum += p.SortNum
				}
			}

		}
		item := model.DeliverAssign{
			ID:              primitive.NewObjectID(),
			UserID:          deliveryMan.UserID,
			DeliveryManID:   deliveryMan.ID,
			DeliveryManName: deliveryMan.UserName,
			BuyerID:         buyerID,
			BuyerName:       mBuyerName[buyerID],
			OrderIDList:     orderIDs,
			DeliverType:     deliverType,
			ServicePointID:  orderList[0].ServicePointID,
			Address:         orderList[0].Address,
			SortWeight:      sWeight,
			SortNum:         sNum,
			Timestamp:       timestamp,
			CreatedAt:       now,
			UpdatedAt:       now,
		}

		err = s.orderS.UpdateMany(ctx, bson.M{
			"_id": bson.M{
				"$in": orderIDs,
			},
		}, bson.M{
			"$set": bson.M{
				"delivery_user_id":   deliveryMan.UserID,
				"delivery_user_name": deliveryMan.UserName,
			},
		})

		dataList = append(dataList, item)
	}

	if len(dataList) > 0 {
		err := s.DeliverAssignDao.CreateMany(ctx, dataList)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s deliverAssignService) Create(ctx context.Context, userID primitive.ObjectID, key string, timestamp int64) error {
	list := make([]model.DeliverAssignInfo, 0)

	key = deliverInfoKey + key
	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := s.rdb.Get(ctx, key).Bytes()
		if err != nil {
			return err
		}

		err = json.Unmarshal(bytes, &list)
		if err != nil {
			return err
		}
	} else {
		return nil
	}

	var orderAllIDs []primitive.ObjectID
	for _, info := range list {
		for _, order := range info.OrderList {
			orderAllIDs = append(orderAllIDs, order.ID)
		}
	}

	// 判断
	count, err := s.DeliverAssignDao.Count(ctx, bson.M{
		"order_id_list": bson.M{
			"$in": orderAllIDs,
		},
	})
	if err != nil {
		return err
	}
	if count > 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "存在订单已分配")
	}

	now := time.Now().UnixMilli()

	dataList := make([]model.DeliverAssign, 0)
	for _, info := range list {

		var orderIDs []primitive.ObjectID
		var sWeight int
		var sNum int

		for _, order := range info.OrderList {
			orderIDs = append(orderIDs, order.ID)

			for _, p := range order.ProductList {
				if !p.IsShipRefundAll {
					sWeight += p.SortWeight
					sNum += p.SortNum
				}
			}
		}

		item := model.DeliverAssign{
			ID:             primitive.NewObjectID(),
			UserID:         userID,
			BuyerID:        info.BuyerID,
			BuyerName:      info.BuyerName,
			OrderIDList:    orderIDs,
			ServicePointID: info.OrderList[0].ServicePointID,
			Address:        info.OrderList[0].Address,
			SortWeight:     sWeight,
			SortNum:        sNum,
			Timestamp:      timestamp,
			CreatedAt:      now,
			UpdatedAt:      now,
		}

		dataList = append(dataList, item)
	}

	if len(dataList) > 0 {
		err := s.DeliverAssignDao.CreateMany(ctx, dataList)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s deliverAssignService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}

	assign, err := s.DeliverAssignDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	err = s.DeliverAssignDao.Delete(ctx, filter)
	if err != nil {
		return err
	}

	s.orderS.UpdateMany(ctx, bson.M{
		"_id": bson.M{
			"$in": assign.OrderIDList,
		},
	}, bson.M{
		"$set": bson.M{
			"delivery_user_id":   primitive.NilObjectID,
			"delivery_user_name": "",
		},
	})

	return nil
}

func (s deliverAssignService) ListByUser(ctx context.Context, userID primitive.ObjectID, timestamp int64) ([]model.DeliverAssign, error) {
	filter := bson.M{
		"user_id":   userID,
		"timestamp": timestamp,
	}
	list, err := s.DeliverAssignDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s deliverAssignService) ListByBuyerIDs(ctx context.Context, ids []primitive.ObjectID, timestamp int64) ([]model.DeliverAssign, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"buyer_id": bson.M{
			"$in": ids,
		},
		"timestamp": timestamp,
	}
	list, err := s.DeliverAssignDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

var deliverInfoKey = "deliverInfoKey:"

func (s deliverAssignService) GetDeliverQr(ctx context.Context, pointID primitive.ObjectID, buyerIDs []primitive.ObjectID, begin, end int64) (string, error) {
	filter := bson.M{
		"service_point_id": pointID,
		//"delivery_allot_has": true,
		"deliver_type": model.DeliverTypeDoor,
		//"order_status": model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return "", err
	}
	_ = orders

	mBuyerName := make(map[primitive.ObjectID]string)
	mBuyer := make(map[primitive.ObjectID][]model.Order)
	for _, order := range orders {
		mBuyer[order.BuyerID] = append(mBuyer[order.BuyerID], order)
		mBuyerName[order.BuyerID] = order.BuyerName
	}

	list := make([]model.DeliverAssignInfo, 0)
	for id, i := range mBuyer {
		item := model.DeliverAssignInfo{
			BuyerID:   id,
			BuyerName: mBuyerName[id],
			OrderList: i,
		}
		list = append(list, item)
	}

	marshal, err := json.Marshal(list)
	if err != nil {
		return "", err
	}

	key := primitive.NewObjectID().Hex()

	s.rdb.Set(ctx, deliverInfoKey+key, string(marshal), time.Hour*8)

	deliverQRCode, err := s.MiniS.GetUnlimitedDeliverQRCode(key)
	if err != nil {
		return "", err
	}

	toString := base64.StdEncoding.EncodeToString(deliverQRCode)

	return toString, nil
}

func (s deliverAssignService) GetDeliverByKey(ctx context.Context, key string) ([]model.DeliverAssignInfo, error) {
	list := make([]model.DeliverAssignInfo, 0)

	key = deliverInfoKey + key
	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := s.rdb.Get(ctx, key).Bytes()
		if err != nil {
			return nil, err
		}

		err = json.Unmarshal(bytes, &list)
		if err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}

	return list, nil
}
