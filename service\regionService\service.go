package regionService

import (
	"base/dao"
	"base/dao/regionDao"
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// ServiceInterface 区域
type ServiceInterface interface {
	ListAll(ctx context.Context) ([]model.Region, error)
	Create(ctx context.Context, name string) error
}

type regionService struct {
	rdb       *redis.Client
	regionDao regionDao.DaoInt
}

func (s regionService) Create(ctx context.Context, name string) error {
	data := model.Region{
		ID:        primitive.NewObjectID(),
		Name:      name,
		CreatedAt: time.Now().UnixMilli(),
	}
	err := s.regionDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s regionService) ListAll(ctx context.Context) ([]model.Region, error) {
	list, err := s.regionDao.List(ctx, bson.M{})
	return list, err
}

func NewRegionService() ServiceInterface {
	return regionService{
		rdb:       global.RDBDefault,
		regionDao: dao.RegionDao,
	}
}
