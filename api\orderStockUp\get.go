package orderStockUp

import (
	"base/core/xhttp"
	"base/service/orderStockUpService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// GetStockUp  备货
func GetStockUp(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "GetStockUp id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := orderStockUpService.NewOrderStockUpService().GetStockUp(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}
