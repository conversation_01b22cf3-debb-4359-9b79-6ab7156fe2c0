package types

import "base/model"

// OrderCreateReq 创建订单
type OrderCreateReq struct {
	SupplierList       []Supplier        `json:"supplier_list" validate:"dive"` // 供应商列表
	AddressID          string            `json:"address_id"  validate:"-"`      // 地址信息ID
	BuyerID            string            `json:"buyer_id"`
	DeliverType        model.DeliverType `json:"deliver_type"`                         // 配送方式
	InstantDeliverType int               `json:"instant_deliver_type"`                 // 配送方式
	OrderNote          string            `json:"order_note"`                           // 订单备注
	LogisticsName      string            `json:"logistics_name" bson:"logistics_name"` // 物流名称
	CouponUserID       string            `json:"coupon_user_id"`                       // 优惠券ID
}

// Supplier 供应商
type Supplier struct {
	SupplierID  string       `json:"supplier_id"  validate:"len=24"` // 供应商ID
	ProductList []PerProduct `json:"product_list"  validate:"min=1"` // 产品列表
}

// PerProduct 产品
type PerProduct struct {
	ProductID string `json:"product_id"`  // 商品ID
	SkuIDCode string `json:"sku_id_code"` // sku编号
	Num       int    `json:"num"`         // 数量
	Remark    string `json:"remark"`      // 备注
}

// PerProductForCalc 产品
type PerProductForCalc struct {
	ProductID   string `json:"product_id"`   // 商品ID
	SkuIDCode   string `json:"sku_id_code"`  // sku编号
	Price       int    `json:"price"`        // 单价
	Num         int    `json:"num"`          // 数量
	RoughWeight int    `json:"rough_weight"` // 单一毛重
}

// OrderSortReq 分拣
type OrderSortReq struct {
	ID        string    `json:"id" validate:"len=24"`
	OrderList []PerSort `json:"order_list" validate:"min=1"`
}

// PerSort 分拣
type PerSort struct {
	OrderID string `json:"order_id"`
	Num     int    `json:"num" validate:"min=0"`    // 总数
	Weight  int    `json:"weight" validate:"min=0"` // 总重
}
