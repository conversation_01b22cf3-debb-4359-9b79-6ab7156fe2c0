package types

import "base/model"

type RetailOrderCreateReq struct {
	OrderNote   string          `json:"order_note"`    // 留言
	ProductList []RetailProduct `json:"product_list" ` // 产品列表
}

type RetailProduct struct {
	ProductID string `json:"product_id"` // 商品ID
	Price     int    `json:"price"`      // 单价
	Num       int    `json:"num"`        // 数量
}

type RetailAddrCreate struct {
	Address  string         `json:"address"`  // 具体地址
	Contact  model.Contact  `json:"contact"`  // 收货人信息
	Location model.Location `json:"location"` // 地址经纬度
}

type RetailAddrUpdate struct {
	ID       string         `json:"id" validate:"-"`       // id
	Address  string         `json:"address" validate:"-"`  // 具体地址
	Contact  model.Contact  `json:"contact" validate:"-"`  // 收货人信息
	Location model.Location `json:"location" validate:"-"` // 地址经纬度
}
