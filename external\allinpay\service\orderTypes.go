package service

import "encoding/json"

type ValidateType int

const (
	ValidateTypeNone        ValidateType = 0 // 0 无验证 	仅渠道验证，通商云不做交易验证
	ValidateTypeCaptcha     ValidateType = 1 // 0 短信验证码 通商云发送并验证短信验证码，有效期3分钟。
	ValidateTypePayPassword ValidateType = 2 // 0 支付密码 验证通商云支付密码
)

// DepositApplyReq 充值申请-01
type DepositApplyReq struct {
	BizOrderNo          string                 `json:"bizOrderNo"`          // 必填  商户订单号（支付订单） 	全局唯一，不可重复，商户侧务必保障此订单号全局唯一，不可重复，如订单号重复，则影响订单退款。
	BizUserId           string                 `json:"bizUserId"`           // 必填  商户系统用户标识，商户系统中唯一编号。 支持个人会员、企业会员、平台。 若平台，上送固定值：#yunBizUserId_B2C#
	AccountSetNo        string                 `json:"accountSetNo"`        // 必填  账户集编号  100001标准余额账户集 个人会员、企业会员填写托管专用账户集编号--test:400193 若平台，填写平台对应账户集编号
	Amount              int                    `json:"amount"`              // 必填  单位：分。包含手续费
	Fee                 int                    `json:"fee"`                 // 必填  手续费 内扣，如果不存在，则填0。 单位：分。
	ValidateType        ValidateType           `json:"validateType"`        //      交易验证方式 如不填，默认为短信验证码确认 0无验证 1短信验证码 2支付密码
	FrontUrl            string                 `json:"frontUrl"`            //      前台通知地址 前台交易时必填，支付后，跳转的前台页面； 5.收银宝微信JS选填，若指定页面跳转上送该字段  注：必须为https协议地址，且不允许带参数
	BackUrl             string                 `json:"backUrl"`             // 必填  后台通知地址
	OrderExpireDatetime string                 `json:"orderExpireDatetime"` //      订单过期时间 控制订单可支付时间,订单最长时效为24小时
	PayMethod           map[string]interface{} `json:"payMethod"`           // 必填  支付方式 JSONObject
	GoodsName           string                 `json:"goodsName"`           //      商品名称 注：符号不支持 微信原生APP支付必传  收银宝渠道：最大100个字节(50个中文字符)
	IndustryCode        string                 `json:"industryCode"`        // 必填  行业代码-----
	IndustryName        string                 `json:"industryName"`        // 必填  行业名称
	Source              SourceType             `json:"source"`              // 必填  访问终端类型
	Summary             string                 `json:"summary"`             // 摘要
	ExtendInfo          string                 `json:"extendInfo"`          // 扩展信息
}

type DepositApplyRes struct {
	PayStatus              string                 `json:"payStatus"`              // 支付状态 	仅交易验证方式为“0”时返回  成功：success  进行中：pending  失败：fail  订单成功时会发订单结果通知商户。
	PayFailMessage         string                 `json:"payFailMessage"`         // 支付失败信息  仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId              string                 `json:"bizUserId"`              // 商户系统用户标识，商户系统中唯一编号。  仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
	OrderNo                string                 `json:"orderNo"`                // 云商通订单号
	BizOrderNo             string                 `json:"bizOrderNo"`             // 商户订单号（支付订单）
	ReqPayInterfaceNo      string                 `json:"reqPayInterfaceNo"`      // 请求渠道流水号
	PayInterfaceOutTradeNo string                 `json:"payInterfaceOutTradeNo"` // 渠道交易流水号
	PayInterfacetrxcode    string                 `json:"payInterfacetrxcode"`    // 通道交易类型
	Acct                   string                 `json:"acct"`                   // 支付人帐号 	仅收银宝-付款码支付方式返回， 微信支付的openid 支付宝平台的user_id
	ChannelFee             string                 `json:"channelFee"`             // 渠道手续费  仅收银宝支付方式返回
	Chnldata               string                 `json:"chnldata"`               // 收银宝渠道信息
	ChannelPaytime         string                 `json:"channelPaytime"`         // 渠道交易完成时间 取值为收银宝接口交易完成时间 格式：yyyyMMddHHmmss
	Cusid                  string                 `json:"cusid"`                  // 渠道商户号	收银宝商户号
	TradeNo                string                 `json:"tradeNo"`                // 交易编号
	WeChatAPPInfo          map[string]interface{} `json:"weChatAPPInfo"`          // 微信APP支付信息	微信app支付必传（原生）
	PayInfo                string                 `json:"payInfo"`                // 扫码支付信息/ JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息
	ValidationType         int                    `json:"validationType"`         // 交易验证方式 当支付方式为收银宝快捷且需验证短信验证码时才返回，返回值为“1”表示需继续调用【确认支付（后台+短信验证码确认）】
	MiniprogramPayInfoVSP  map[string]interface{} `json:"miniprogramPayInfo_VSP"` // 小程序收银台支付参数
	MobilePayInfoVSP       map[string]interface{} `json:"mobilePayInfo_VSP"`      // 手机安全控件支付参数
	ExtendInfo             string                 `json:"extendInfo"`             //
}

// WithdrawApplyReq 提现申请-02
type WithdrawApplyReq struct {
	BizOrderNo          string                 `json:"bizOrderNo"`          // 必填 商户订单号（支付订单） 全局唯一，不可重复
	BizUserId           string                 `json:"bizUserId"`           // 必填 商户系统用户标识，商户系统中唯一编号。
	AccountSetNo        string                 `json:"accountSetNo"`        // 必填 账户集编号 个人会员、企业会员填写托管专用账户集编号 若平台，填写平台对应账户集编号，详细 注：不支持100002-标准保护金账户集，100003-准备金额度账户集、100004/5中间账户集
	Amount              int                    `json:"amount"`              // 必填 订单金额	单位：分，包含手续费
	Fee                 int                    `json:"fee"`                 // 必填 手续费	内扣，如果不存在，则填0。
	ValidateType        int                    `json:"validateType"`        //     交易验证方式	详细 如不填，默认为短信验证码确认 平台提现，只支持无验证和短信验证
	BackUrl             string                 `json:"backUrl"`             // 必填 后台通知地址
	OrderExpireDatetime string                 `json:"orderExpireDatetime"` //      订单过期时间 格式：yyyy-MM-dd HH:mm:ss  控制订单可支付时间，订单最长时效为24小时，即过期时间不能大于订单创建时间24小时； 提现支持无验证提现； 短信/密码验证，需确认支付情况-确认支付时间不能大于订单过期时间；
	PayMethod           map[string]interface{} `json:"payMethod"`           //      支付方式 如不传，则默认为通联通代付
	BankCardNo          string                 `json:"bankCardNo"`          // 必填 银行卡号/账号  绑定的银行卡号/账号 AES加密
	BankCardPro         int                    `json:"bankCardPro"`         //     银行卡/账户属性	0：个人银行卡  1：企业对公账户 如果不传默认为0 平台提现，必填1
	WithdrawType        string                 `json:"withdrawType"`        //     提现方式 默认为D0
	IndustryCode        string                 `json:"industryCode"`        // 必填 行业代码
	IndustryName        string                 `json:"industryName"`        // 必填 行业名称
	Source              SourceType             `json:"source"`              // 必填 访问终端类型
	Summary             string                 `json:"summary"`             //     摘要
	ExtendInfo          string                 `json:"extendInfo"`          // 	   扩展信息
}

type WithdrawApplyRes struct {
	PayStatus      string `json:"payStatus"`      // 支付状态  仅交易验证方式为“0”时返回 成功：success 进行中：pending 失败：fail 提现在成功和失败时都会通知商户。 其他订单只在成功时会通知商户。
	PayFailMessage string `json:"payFailMessage"` // 支付失败信息	仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId      string `json:"bizUserId"`      // 商户系统用户标识，商户系统中唯一编号。
	OrderNo        string `json:"orderNo"`        // 云商通订单号
	BizOrderNo     string `json:"bizOrderNo"`     // 商户订单号（支付订单）
	ExtendInfo     string `json:"extendInfo"`     // 扩展信息
}

type RecieverItem struct {
	BizUserId string `json:"bizUserId"`
	Amount    int    `json:"amount"`
}

// AgentCollectApplyReq 托管代收申请（标准版）-03
type AgentCollectApplyReq struct {
	BizOrderNo          string                 `json:"bizOrderNo"`          // 必填 商户订单号（支付订单） 全局唯一，不可重复
	PayerId             string                 `json:"payerId"`             // 必填 商户系统用户标识，商户系统中唯一编号。 付款人  付款用户的bizUserId，支持个人会员、企业会员的
	RecieverList        []RecieverItem         `json:"recieverList"`        // 必填 收款列表
	GoodsType           int                    `json:"goodsType"`           //     商品类型
	BizGoodsNo          string                 `json:"bizGoodsNo"`          //     商户系统商品编号 商家录入商品后，发起交易时可上送商品编号
	TradeCode           string                 `json:"tradeCode"`           // 必填 业务码
	Amount              int                    `json:"amount"`              // 必填 订单金额 单位：分。订单金额=收款列表总金额+手续费
	Fee                 int                    `json:"fee"`                 //     手续费
	ValidateType        int                    `json:"validateType"`        //     交易验证方式 如不填，默认为短信验证码确认
	FrontUrl            string                 `json:"frontUrl"`            //     frontUrl 前台通知地址
	BackUrl             string                 `json:"backUrl"`             // 必填 后台通知地址
	OrderExpireDatetime string                 `json:"orderExpireDatetime"` //     订单过期时间 yyyy-MM-dd HH:mm:ss
	PayMethod           map[string]interface{} `json:"payMethod"`           // 必填 支付方式
	GoodsName           string                 `json:"goodsName"`           //     商品名称
	GoodsDesc           string                 `json:"goodsDesc"`           //     商品描述
	IndustryCode        string                 `json:"industryCode"`        // 必填 行业代码
	IndustryName        string                 `json:"industryName"`        // 必填 行业名称
	Source              SourceType             `json:"source"`              // 必填 访问终端类型
	Summary             string                 `json:"summary"`             // 	   摘要
	ExtendInfo          string                 `json:"extendInfo"`          // 	   扩展参数
}

type AgentCollectApplyRes struct {
	PayStatus              string                 `json:"payStatus"`              // 支付状态 仅交易验证方式为“0”时返回 成功：success 进行中：pending 失败：fail 订单成功时会发订单结果通知商户。
	PayFailMessage         string                 `json:"payFailMessage"`         // 支付失败信息  	仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId              string                 `json:"bizUserId"`              // 商户系统用户标识，商户系统中唯一编号。
	OrderNo                string                 `json:"orderNo"`                // 云商通订单号
	BizOrderNo             string                 `json:"bizOrderNo"`             // 商户订单号（支付订单）
	ReqPayInterfaceNo      string                 `json:"reqPayInterfaceNo"`      // 请求渠道流水号
	PayInterfaceOutTradeNo string                 `json:"payInterfaceOutTradeNo"` // 渠道交易流水号
	PayInterfacetrxcode    string                 `json:"payInterfacetrxcode"`    // 通道交易类型
	Acct                   string                 `json:"acct"`                   // 支付人帐号
	ChannelFee             string                 `json:"channelFee"`             // 渠道手续费
	Chnldata               string                 `json:"chnldata"`               // 收银宝渠道信息
	ChannelPaytime         string                 `json:"channelPaytime"`         // 渠道交易完成时间
	Cusid                  string                 `json:"cusid"`                  // 渠道商户	收银宝商户号
	TradeNo                string                 `json:"tradeNo"`                // 交易编号
	ExtendInfo             string                 `json:"extendInfo"`             // 扩展参数	接口将原样返回
	WeChatAPPInfo          map[string]interface{} `json:"weChatAPPInfo"`          // 微信APP支付信息
	PayInfo                string                 `json:"payInfo"`                // 扫码支付信息/ JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息
	MiniprogrampayinfoVsp  map[string]interface{} `json:"miniprogramPayInfo_VSP"` // 小程序收银台支付参数
	MobilepayinfoVsp       map[string]interface{} `json:"payCode"`                // 渠道交易流水号
	ValidationType         int                    `json:"交易验证方式"`                 // 交易验证方式 当支付方式为收银宝快捷且需验证短信验证码时才返回，返回值为“1”表示需继续调用【确认支付（后台+短信验证码确认）】
}

// BatchAgentPayReq 批量托管代付（标准版-04
type BatchAgentPayReq struct {
	BizBatchNo   string         `json:"bizBatchNo"`   // 必填  商户批次号
	BatchPayList []BatchPayItem `json:"batchPayList"` // 必填  批量代付列表	详细，最多支持200笔
	GoodsType    int            `json:"goodsType"`    // 	  商品类型	详细
	BizGoodsNo   string         `json:"bizGoodsNo"`   // 	  商户系统商品编号	商家录入商品后，发起交易时可上送商品编号
	TradeCode    string         `json:"tradeCode"`    // 必填  业务码	详细
}

type BatchPayItem struct {
	BizOrderNo     string           `json:"bizOrderNo"`     // 必填   商户订单号（支付订单） 全局唯一，不可重复
	CollectPayList []CollectPayItem `json:"collectPayList"` // 必填   源托管代收订单付款信息；最多支持100个；详情
	BizUserId      string           `json:"bizUserId"`      // 必填   商户系统用户标识，商户系统中唯一编号；托管代收订单中指定的收款方。
	AccountSetNo   string           `json:"accountSetNo"`   // 必填   收款方的账户集编号
	BackUrl        string           `json:"backUrl"`        // 必填   后台通知地址
	Amount         int              `json:"amount"`         // 必填   金额，单位：分
	Fee            int              `json:"fee"`            // 必填   手续费，单位：分；内扣
	SplitRuleList  []SplitRuleItem  `json:"splitRuleList"`  //       内扣。 支持分账到会员或者平台账户。 支持分账到会员或者平台账户。
	Summary        string           `json:"summary"`        //       摘要；最多50个字符
	ExtendInfo     string           `json:"extendInfo"`     //       最多100个字符，商户拓展参数，用于透传给商户
}

// CollectPayItem 源托管代收订单付款信息
type CollectPayItem struct {
	BizOrderNo         string `json:"bizOrderNo"`         // 相关代收交易的“商户订单号”
	OrderNo            string `json:"orderNo"`            // 相关代收订单“云商通订单号
	BizOrderCreateDate string `json:"bizOrderCreateDate"` // 非必填，一年前的代收订单必须上送，yyyy-MM-dd 精确到天
	Amount             int    `json:"amount"`             // 金额，单位：分；部分代付时，可以少于或等于托管代收订单金额
}

type SplitRuleItem struct {
	BizUserId     string          `json:"bizUserId"`     // 必填  商户系统用户标识，商户系统中唯一编号。 如果是平台，则填#yunBizUserId_B2C#
	AccountSetNo  string          `json:"accountSetNo"`  //      如果向会员分账，不上送，默认为唯一托管账户集。 如果向平台分账，请填写平台的标准账户集编号（不支持100003-准备金额度账户集）
	Amount        int             `json:"amount"`        // 必填  金额，单位：分
	Fee           int             `json:"fee"`           // 必填  手续费，内扣，单位：分
	Remark        string          `json:"remark"`        // 备注，最长50个字符
	SplitRuleList []SplitRuleItem `json:"splitRuleList"` // 分账列表
}

type BatchAgentPayRes struct {
	BizBatchNo string `json:"bizBatchNo"` // 必填 商户批次号
}

// RefundReq 退款申请 -05
type RefundReq struct {
	BizOrderNo    string       `json:"bizOrderNo"`     // 必填  商户订单号（支付订单）
	OriBizOrderNo string       `json:"oriBizOrderNo"`  // 必填  商户原订单号	需要退款的原交易订单号
	OriOrderNo    string       `json:"oriOrderNo"`     // 		原云商通订单号	上送，则按照此字段进行退款  未上送，则按照商户订单号进行退款
	BizUserId     string       `json:"bizUserId"`      // 必填  商户系统用户标识，商户系统中唯一编号。 退款收款人。
	RefundType    string       `json:"refundType"`     //    退款方式  默认D1  D1：D+1日14:30——18:00分批向渠道发起退款，退款到账时间以实际到账为准  D0：D+0实时向渠道发起退款   说明：此参数仅对支持退款金额原路返回的支付订单有效
	RefundList    []RefundItem `json:"refundList"`     //    托管代收订单中的收款人的退款金额
	BackUrl       string       `json:"backUrl"`        //    后台通知地址	如果不填，则不通知。  退款成功时，才会通知。
	Amount        int          `json:"amount"`         // 必填  本次退款总金额	单位：分。 不得超过原订单金额。
	CouponAmount  int          `json:"couponAmount"`   //    代金券退款金额  如不填，则默认为0。 如为0，则不退代金券。
	FeeAmount     int          `json:"feeAmount"`      //    手续费退款金额   如不填，则默认为0。 如为0，则不退手续费。
	RefundAccount string       `json:"refund_account"` //    仅支持通联通存管模式上送：TLT 上送此字段，收银宝待结算资金不足的情况，则从通联通资金调拨回收银宝，实现退款。  未上送此字段，则维持原功能。
	ExtendInfo    string       `json:"extendInfo"`     //    扩展信息
}
type RefundItem struct {
	AccountSetNo string `json:"accountSetNo"` //      账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
	BizUserId    string `json:"bizUserId"`    // 必填  商户系统用户标识，商户系统中唯一编号。
	Amount       int    `json:"amount"`       // 必填  金额，单位：分
}

type RefundRes struct {
	PayStatus      string `json:"payStatus"`      //     	 支付状态	成功：success 进行中：pending 失败：fail 退款到银行卡/微信/支付宝成功、失败时会发订单结果通知商户。
	PayFailMessage string `json:"payFailMessage"` //         支付失败信息	只有payStatus为fail时有效
	OrderNo        string `json:"orderNo"`        // 必填     云商通订单编号
	BizOrderNo     string `json:"bizOrderNo"`     // 必填     商户订单号（支付订单）
	Amount         int    `json:"amount"`         // 必填     本次退款总金额	单位：分
	CouponAmount   int    `json:"couponAmount"`   //   	     代金券退款金额	单位：分
	FeeAmount      int    `json:"feeAmount"`      //         手续费退款金额	单位：分
	ExtendInfo     string `json:"extendInfo"`     //         扩展信息
}

// ApplicationTransferReq 平台转账 -06
type ApplicationTransferReq struct {
	BizTransferNo      string `json:"bizTransferNo"`      // 必填    商户系统转账订单号，商户系统唯一	全局唯一，不可重复，商户侧务必保障此订单号全局唯一，不可重复，如订单号重复，则影响订单退款。
	SourceAccountSetNo string `json:"sourceAccountSetNo"` // 必填    源账户集编号	云商通统一的标准账户集合的编号。详细
	TargetBizUserId    string `json:"targetBizUserId"`    // 必填    目标商户系统用户标识，商户系统中唯一编号。	收款会员的BizUserId
	TargetAccountSetNo string `json:"targetAccountSetNo"` // 必填    目标账户集编号	云商通分配的托管专用账户集的编号
	Amount             int    `json:"amount"`             // 必填    金额	单位：分。
	ExtendInfo         string `json:"extendInfo"`         //  扩展信息
}

type ApplicationTransferRes struct {
	TransferNo    string `json:"transferNo"`    // 必填  云商通转账订单号
	BizTransferNo string `json:"bizTransferNo"` // 必填  商户系统转账订单号
	Amount        int    `json:"amount"`        // 必填  金额	单位：分。
	ExtendInfo    string `json:"extendInfo"`    // 	    扩展信息
}

// GetOrderStatusReq 订单状态查询 -07
type GetOrderStatusReq struct {
	OrderNo    string `json:"orderNo"`    //  	   云商通订单号
	BizOrderNo string `json:"bizOrderNo"` // 必填   商户订单号（支付订单）
}

type GetOrderStatusRes struct {
	OrderNo          string `json:"orderNo"`          // 必填   云商通订单号
	BizOrderNo       string `json:"bizOrderNo"`       // 必填   商户订单号（支付订单）
	OrderStatus      int    `json:"orderStatus"`      // 必填   订单状态
	ErrorMessage     string `json:"errorMessage"`     // 	   失败原因	订单失败时返回
	Amount           int    `json:"amount"`           // 必填   订单金额
	PayDatetime      string `json:"payDatetime"`      //       订单支付完成时间	yyyy-MM-dd HH:mm:ss
	IsAccountSuccess int    `json:"isAccountSuccess"` //       是否记账成功	1：是 2：否 仅订单状态“交易成功”返回
}

// GetOrderDetailReq 订单详细信息查询 -08
type GetOrderDetailReq struct {
	BizOrderNo string `json:"bizOrderNo"` // 必填    商户订单号（支付订单）
}

type GetOrderDetailRes struct {
	OrderNo                string `json:"orderNo"`                // 必填  云商通订单号
	BizOrderNo             string `json:"bizOrderNo"`             // 必填  商户订单号（支付订单）
	OriOrderNo             string `json:"oriOrderNo"`             //      云商通原订单号	退款订单该字段才返回
	OriBizOrderNo          string `json:"oriBizOrderNo"`          //      原商户订单号	退款订单该字段才返回
	OrderStatus            int    `json:"orderStatus"`            // 必填  订单状态
	ErrorMessage           string `json:"errorMessage"`           // 	  失败原因	订单失败时返回
	Amount                 int    `json:"amount"`                 // 必填  订单金额
	PayDatetime            string `json:"payDatetime"`            //      订单支付完成时间	yyyy-MM-dd HH:mm:ss
	IsAccountSuccess       int    `json:"isAccountSuccess"`       //      是否记账成功	1：是 2：否 仅订单状态“交易成功”返回
	BuyerBizUserId         string `json:"buyerBizUserId"`         // 必填    商户系统用户标识，商户系统中唯一编号。 付款人
	RefundWhereabouts      int    `json:"refundWhereabouts"`      //       退款去向	退款，必填 1：到账户余额 2：到原支付账户银行卡/微信/支付宝等
	PayWhereabouts         int    `json:"payWhereabouts"`         //       代付去向	代付去向 1：到账户余额
	Acct                   string `json:"acct"`                   //       支付人帐号
	Accttype               string `json:"accttype"`               //       借贷标志
	Termno                 string `json:"termno"`                 //       终端号
	Cusid                  string `json:"cusid"`                  //       渠道商户号
	PayInterfaceOutTradeNo string `json:"payInterfaceOutTradeNo"` //       通道交易流水号
	Chnltrxid              string `json:"chnltrxid"`              //       支付渠道交易单号
	Termrefnum             string `json:"termrefnum"`             //       交易参考号
	ChannelFee             string `json:"channelFee"`             //       渠道手续费	取值为收银宝接口手续费字段-fee
	ChannelPaytime         string `json:"channelPaytime"`         //       渠道交易完成时间
	PayInterfacetrxcode    string `json:"payInterfacetrxcode"`    //       	通道交易类型
	Traceno                string `json:"traceno"`                //       收银宝终端流水
	Chnldata               string `json:"chnldata"`               //       	收银宝渠道信息
	ExtendInfo             string `json:"extendInfo"`             //       扩展参数
}

// QueryBalanceReq 查询余额 -09
type QueryBalanceReq struct {
	BizUserId    string `json:"bizUserId"`    // 必填    商户系统用户标识，商户系统中唯一编号。
	AccountSetNo string `json:"accountSetNo"` // 必填 	账户集编号	云商通分配的托管专用账户集的编号
}

type QueryBalanceRes struct {
	AllAmount            int `json:"allAmount"`            // 必填  总额
	FreezenAmount        int `json:"freezenAmount"`        // 必填  冻结额
	DepositFreezenAmount int `json:"depositFreezenAmount"` // 	    当日充值冻结金额
}

// CloseOrderReq 订单关闭-10
type CloseOrderReq struct {
	BizOrderNo string `json:"bizOrderNo"` // 必填  商户订单号（支付订单）
}

type CloseOrderRes struct {
	CloseResult   int    `json:"closeResult"`   // 必填    关闭结果	1-关闭成功 2-关闭失败
	OrderNo       string `json:"orderNo"`       // 必填    云商通订单号
	BizOrderNo    string `json:"bizOrderNo"`    // 必填    商户订单号（支付订单）
	CloseDatetime string `json:"closeDatetime"` // 必填    订单关闭完成时间	yyyy-MM-dd HH:mm:ss
	ErrorMessage  string `json:"errorMessage"`  //        关闭失败原因
	OrderStatus   int    `json:"orderStatus"`   //        订单状态	1-未支付  3-交易失败  4-交易完成  5-交易完成（发生退款）  6-关闭  99-进行中
}

// GetPaymentInformationDetailReq	付款方资金代付明细查询
type GetPaymentInformationDetailReq struct {
	BizOrderNo string `json:"bizOrderNo"` //      商户订单号（支付订单）	全局唯一，不可重复
}

type GetPaymentInformationDetailRes struct {
	CollOrderNo          string         `json:"collOrderNo"`          // 必填  代收云商通订单号
	CollAmount           int            `json:"collAmount"`           // 必填  代收金额
	PayerName            string         `json:"payerName"`            // 必填  代收姓名
	PayerId              string         `json:"payerId"`              // 必填  付款人编号  商户系统用户标识，商户系统中唯一编号。
	CollTime             string         `json:"collTime"`             // 必填  代收时间	yyyyMMdd HH:MM:SS
	PayTotalAmount       int            `json:"payTotalAmount"`       // 必填  已代付金额
	PayRefundTotalAmount int            `json:"payRefundTotalAmount"` // 必填  已代付退款总金额
	RefundTotalAmount    int            `json:"refundTotalAmount"`    // 必填  未代付退款总金额
	UnPayTotalAmount     int            `json:"unPayTotalAmount"`     // 必填  未代付金额
	ReceiverInfoList     []ReceiverInfo `json:"receiverInfoList"`     //      	收款人收款信息列表
}
type ReceiverInfo struct {
	ReceiverName      string `json:"receiverName"`      // 必填  收款人姓名
	ReceiverId        string `json:"receiverId"`        // 必填  商户系统用户标识，商户系统中唯一编号。收款人
	ManagedCollAmount int    `json:"managedCollAmount"` // 必填  托管代收金额（分
	PayAmount         int    `json:"payAmount"`         // 必填  收款人已收金额
	PayRefundAmount   int    `json:"payRefundAmount"`   // 必填  收款人已代付退款金额
	RefundAmount      int    `json:"refundAmount"`      // 必填  收款人未代付退款金额
	UnPayAmount       int    `json:"unPayAmount"`       // 必填  收款人未收金额
	Status            int    `json:"status"`            // 必填  状态（0:未收款；1：已部分收款；2:已收款）
}

// QueryInExpDetailReq	查询账户收支明细
type QueryInExpDetailReq struct {
	BizUserId     string    `json:"bizUserId"`     // 必填  商户系统用户标识，商户系统中唯一编号。
	AccountSetNo  string    `json:"accountSetNo"`  //      账户集编号   个人会员、企业会员填写托管专用账户集编号。  如果不传，则查询该用户下所有现金账户的收支明细。  平台，填写平台对应账户集编号， 详见详情。  如果不传，则查询平台所有标准账户集的收支明细。
	BizOrderNo    string    `json:"bizOrderNo"`    //      商户订单号（支付订单）	全局唯一，不可重复
	DateStart     string    `json:"dateStart"`     // 必填  开始日期	yyyy-MM-dd
	DateEnd       string    `json:"dateEnd"`       // 必填  结束日期	yyyy-MM-dd HH:mm:ss，支持时间区间段查询，但起始时间，结束时间的区间差不能超过31天（含起始、结束） 建议跨度不超过7天
	StartPosition int       `json:"startPosition"` // 必填  起始位置	取值>0，eg：查询第11条到20条的记录（start =11）
	QueryNum      int       `json:"queryNum"`      // 必填  查询条数	eg：查询第11条到20条的记录（queryNum =10）
	TradeType     TradeType `json:"tradeType"`     //      交易类型
}

type InExpDetail struct {
	TradeNo          string        `json:"tradeNo"`          // 必填  收支明细流水号
	AccountSetName   string        `json:"accountSetName"`   // 必填  	账户集名称
	ChangeTime       string        `json:"changeTime"`       // 必填  变更时间	yyyy-MM-dd HH:mm:ss
	CurAmount        string        `json:"curAmount"`        // 必填  现有金额
	OriAmount        string        `json:"oriAmount"`        // 必填  	原始金额
	ChgAmount        string        `json:"chgAmount"`        // 必填  变更金额
	CurFreezenAmount string        `json:"curFreezenAmount"` // 必填  现有冻结金额
	BizOrderNo       string        `json:"bizOrderNo"`       //      商户订单号（支付订单）
	TradeType        TradeType     `json:"tradeType"`        //      交易类型
	Type             TradeTypeNext `json:"type"`             //      交易子类型
	Remark           string        `json:"remark"`           //      分账备注	分账时上送的remark信息
	ExtendInfo       string        `json:"extendInfo"`       //      扩展参数	bizOrderNo订单申请上送的扩展参数信息，不可包含“|”特殊字符
}
type QueryInExpDetailRes struct {
	BizUserId   string        `json:"bizUserId"`   // 必填  商户系统用户标识，商户系统中唯一编号。
	TotalNum    int           `json:"totalNum"`    // 必填  该账户收支明细总条数
	InExpDetail []InExpDetail `json:"inExpDetail"` // 必填  收支明细
	ExtendInfo  string        `json:"extendInfo"`  //      扩展参数
}

// GetOrderSplitRuleListDetailReq 订单分账明细
type GetOrderSplitRuleListDetailReq struct {
	BizOrderNo string `json:"bizOrderNo"` // 必填  商户订单号（支付订单）	全局唯一，不可重复
}

// SplitRuleListDetail 分账规则
type SplitRuleListDetail struct {
	BizUserId     string          `json:"bizUserId"`    // 必填  商户系统用户标识，商户系统中唯一编号。  如果是平台，则填#yunBizUserId_B2C#
	AccountSetNo  string          `json:"accountSetNo"` //      如果向会员分账，不上送，默认为唯一托管账户集。 如果向平台分账，请填写平台的标准账户集编号（不支持100003-准备金额度账户集）。
	Amount        int             `json:"amount"`       // 必填  金额，单位：分
	Fee           int             `json:"fee"`          // 必填  手续费，内扣，单位：分
	Remark        string          `json:"remark"`       //      备注，最长50个字符
	SplitRuleList json.RawMessage `json:"orderNo"`      //      分账列表
}

type GetOrderSplitRuleListDetailRes struct {
	OrderNo             string                `json:"orderNo"`             // 必填  云商通订单号
	BizOrderNo          string                `json:"bizOrderNo"`          // 必填  商户订单号（支付订单）	全局唯一，不可重复
	RecieverId          string                `json:"recieverId"`          // 必填  商户系统用户标识，商户系统中唯一编号。 收款方
	SplitRuleListDetail []SplitRuleListDetail `json:"splitRuleListDetail"` // 必填  分账明细
	State               int                   `json:"state"`               // 必填  分账状态	0-失败 1-成功
	ExtendInfo          string                `json:"extendInfo"`          //      扩展信息
}

// PayByBackSMSReq 确认支付（后台+短信验证码确认）
type PayByBackSMSReq struct {
	BizUserId        string `json:"bizUserId"`        // 必填
	BizOrderNo       string `json:"bizOrderNo"`       // 必填  商户订单号（支付订单）	全局唯一，不可重复
	VerificationCode string `json:"verificationCode"` // 必填  短信验证码
	ConsumerIp       string `json:"consumerIp"`       // 必填  ip地址
}

type PayByBackSMSRes struct {
	PayStatus      string `json:"payStatus"`      // 必填  成功：success 进行中：pending  未支付：unpay  失败：fail
	BizOrderNo     string `json:"bizOrderNo"`     // 必填  商户订单号（支付订单）	全局唯一，不可重复
	BizUserId      string `json:"bizUserId"`      // 必填
	PayFailMessage string `json:"payFailMessage"` //
}

// ResendPaySMSReq 重发支付短信验证码
type ResendPaySMSReq struct {
	BizOrderNo string `json:"bizOrderNo"` // 必填  商户订单号（支付订单）	全局唯一，不可重复
}

type ResendPaySMSRes struct {
	BizOrderNo string `json:"bizOrderNo"` // 必填  商户订单号（支付订单）	全局唯一，不可重复
	OrderNo    string `json:"orderNo"`    // 必填
	Phone      string `json:"phone"`      // 必填
}

// TradeType 交易类型
type TradeType int

const (
	TradeTypeDeposit               TradeType = 1  // 充值
	TradeTypeTransfer              TradeType = 2  // 转账
	TradeTypeWithdraw              TradeType = 3  // 提现
	TradeTypeRefund                TradeType = 4  //退款
	TradeTypeDeserveReceiveConfirm TradeType = 5  // 应收账款确认
	TradeTypeShouYinBaoRefund      TradeType = 9  // 收银宝退款资金调拨
	TradeTypeDeserveReceiveFee     TradeType = 10 // 应收账款手续费确认
	TradeTypeReturnedCheque        TradeType = 12 // 退票
)

// TradeTypeNext 交易子类型
type TradeTypeNext int

const (
// TradeTypeNextDeposit               TradeType = 1  // 消费
// TradeTypeNextTransfer              TradeType = 2  // 代收
// TradeTypeNextWithdraw              TradeType = 3  // 代付
// TradeTypeNextRefund                TradeType = 4  //批量代付
// TradeTypeNextDeserveReceiveConfirm TradeType = 6  // 调账
// TradeTypeNextShouYinBaoRefund      TradeType = 9  // 分账
// TradeTypeNextShouYinBaoRefund      TradeType = 11  // 批量退款
// TradeTypeNextDeserveReceiveFee     TradeType = 20 // 协议消费
// TradeTypeNextReturnedCheque        TradeType = 21 // 协议代收
// TradeTypeNextReturnedCheque        TradeType = 30 // 手续费
// TradeTypeNextReturnedCheque        TradeType = 31 // 应用手续费
// TradeTypeNextReturnedCheque        TradeType = 32 // 手续费退款
// TradeTypeNextReturnedCheque        TradeType = 34 // 无验证充值
// TradeTypeNextReturnedCheque        TradeType = 35 // 无验证提现
// TradeTypeNextReturnedCheque        TradeType = 36 // 无验证消费
// TradeTypeNextReturnedCheque        TradeType = 37 // 平台转账
// TradeTypeNextReturnedCheque        TradeType = 38 // 无验证代收
// TradeTypeNextReturnedCheque        TradeType = 50 // 无验证简单校验代收
// TradeTypeNextReturnedCheque        TradeType = 51 // 简单校验代付
// TradeTypeNextReturnedCheque        TradeType = 49 // 简单校验批量代付
// TradeTypeNextReturnedCheque        TradeType = 46 // 平台标准余额账户充值
// TradeTypeNextReturnedCheque        TradeType = 66 // 汇款充值
// TradeTypeNextReturnedCheque        TradeType = 68 // 收银宝集团子商户内部转账
// TradeTypeNextReturnedCheque        TradeType = 69 // 收银宝退款准备金账户转账
// TradeTypeNextReturnedCheque        TradeType = 70 // 平台账户间转账
// TradeTypeNextReturnedCheque        TradeType = 71 // 分账退款
// TradeTypeNextReturnedCheque        TradeType = 72 // 电子账户资金调拨收银宝
// TradeTypeNextReturnedCheque        TradeType = 73 // 订单分账退款
// TradeTypeNextReturnedCheque        TradeType = 74 // 批量平台转账
// TradeTypeNextReturnedCheque        TradeType = 75 // 通联通资金调拨收银宝
// TradeTypeNextReturnedCheque        TradeType = 76 // 通联通退票
// TradeTypeNextReturnedCheque        TradeType = 77 // 银行退票
)

// SignalAgentPayReq 单笔托管代付（标准版）
type SignalAgentPayReq struct {
	BizOrderNo     string           `json:"bizOrderNo"`     // 必填 商户订单号（支付订单）
	CollectPayList []CollectPayItem `json:"collectPayList"` // 必填 源托管代收订单付款信息
	BizUserId      string           `json:"bizUserId"`      // 必填 	商户系统用户标识，商户系统中唯一编号。	收款账户bizUserId，支持个人会员、企业会员
	AccountSetNo   string           `json:"accountSetNo"`   // 必填 收款人的账户集编号。	云商通分配的托管专用账户集的编号
	BackUrl        string           `json:"backUrl"`        // 必填 后台通知地址
	Amount         int              `json:"amount"`         // 必填 	总金额	单位：分
	Fee            int              `json:"fee"`            // 必填 手续费	内扣，如果不存在，则填0 单位：分。 如amount为100，fee为2，实际到账金额为98。
	SplitRuleList  []SplitRuleItem  `json:"splitRuleList"`  //     分账规则	内扣。详细 支持分账到会员或者平台账户。 分账规则：分账层级数<=3，分账总会员数<=10
	GoodsType      int              `json:"goodsType"`      //     商品类型
	BizGoodsNo     string           `json:"bizGoodsNo"`     //     商户系统商品编号	商家录入商品后，发起交易时可上送商品编号
	TradeCode      string           `json:"tradeCode"`      //     业务码
	Summary        string           `json:"summary"`        //     摘要  最多50个字符
	ExtendInfo     string           `json:"extendInfo"`     //     扩展参数  最多100个字符
}

type SignalAgentPayRes struct {
	PayStatus      string `json:"payStatus"`      // 必填  托管代付状态	1、成功：success 2、进行中：pending 3、失败：fail
	PayFailMessage string `json:"payFailMessage"` //       支付失败信息	只有payStatus为fail时有效
	OrderNo        string `json:"orderNo"`        //  必填 云商通订单号
	BizOrderNo     string `json:"bizOrderNo"`     //  必填 商户订单号（支付订单）
	PayWhereabouts int    `json:"payWhereabouts"` //  必填 代付去向	1：到账户余额
	ExtendInfo     string `json:"extendInfo"`     //  必填
}

// FreezeMoneyReq 冻结金额
type FreezeMoneyReq struct {
	BizUserId    string `json:"bizUserId"`    // 必填 商户系统用户标识，商户系统中唯一编号。
	BizFreezenNo string `json:"bizFreezenNo"` // 必填 商户冻结金额订单号
	AccountSetNo string `json:"accountSetNo"` // 必填 收款人的账户集编号。	云商通分配的托管专用账户集的编号
	Amount       int    `json:"amount"`       // 必填 	总金额	单位：分
}

type FreezeMoneyRes struct {
	BizFreezenNo string `json:"bizFreezenNo"` // 必填 商户冻结金额订单号
	Amount       int    `json:"amount"`       // 必填 	总金额	单位：分
}

// UnfreezeMoneyReq 解冻金额
type UnfreezeMoneyReq struct {
	BizUserId    string `json:"bizUserId"`    // 必填 商户系统用户标识，商户系统中唯 一编号。
	BizFreezenNo string `json:"bizFreezenNo"` // 必填 商户冻结金额订单号  对应冻结金额时的订单号
	AccountSetNo string `json:"accountSetNo"` // 必填 收款人的账户集编号。	云商通分配的托管专用账户集的编号
	Amount       int    `json:"amount"`       // 必填 	总金额	单位：分
}

type UnfreezeMoneyRes struct {
	BizFreezenNo string `json:"bizFreezenNo"` // 必填 商户冻结金额订单号   对应冻结金额时的订单号
	Amount       int    `json:"amount"`       // 必填 	总金额	单位：分
}
