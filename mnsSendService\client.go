package mnsSendService

import (
	"base/global"
	"base/model"
	"base/util"
	"context"
	"encoding/base64"
	"encoding/json"
	_ "net/http/pprof"
	"time"

	ali_mns "github.com/aliyun/aliyun-mns-go-sdk"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type MnsClient struct {
	queue               ali_mns.AliMNSQueue
	afterSaleExpireHour int64
	orderExpireMinute   int64
}

func NewMNSClient() *MnsClient {
	c := global.MNSConf
	cli := ali_mns.NewAliMNSClient(c.Url, c.<PERSON>d, c.AccessKeySecret)
	queue := ali_mns.NewMNSQueue(global.QueueOrder, cli)

	return &MnsClient{
		queue:               queue,
		afterSaleExpireHour: global.OrderAfterSaleHour,
		orderExpireMinute:   global.OrderExpireMinute,
	}
}

func (s *MnsClient) SendDeleteProduct(productID string) {
	msg := "deleteProduct@" + productID
	var t int64
	t = 0
	s.send(msg, t)
}

func (s *MnsClient) SendCloseParentOrder(parentID string) {
	msg := "close@" + parentID
	var t int64
	duration := time.Minute*time.Duration(s.orderExpireMinute) + time.Second*5 // 延迟n秒

	t = int64(duration.Seconds())
	s.send(msg, t)
}

func (s *MnsClient) SendCheckUserPay(parentOrderID string) {
	msg := "checkUserPay@" + parentOrderID
	var t int64
	t = 10 // 30 秒
	s.send(msg, t)
}

func (s *MnsClient) SendCheckDebtPay(debtID string) {
	msg := "checkDebtPay@" + debtID
	var t int64
	t = 10 // 3 秒
	s.send(msg, t)
}

func (s *MnsClient) SendRemoveDebtPayInfo(debtOrderID string) {
	msg := "removeDebtPayInfo@" + debtOrderID
	var t int64
	duration := time.Minute*time.Duration(s.orderExpireMinute) + time.Second*10 // 晚1

	t = int64(duration.Seconds())
	s.send(msg, t)
}

func (s *MnsClient) SendAgentPay(orderID string) {
	// 检查代付
	msg := "agentPay@" + orderID
	var t int64
	duration := time.Hour * time.Duration(s.afterSaleExpireHour)

	// 每天的13-18点分账
	now := time.Now()

	payTime := now.Add(duration)

	begin := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 14, 0, 0, 0, payTime.Location())
	end := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 16, 0, 0, 0, payTime.Location())

	var seconds float64
	if payTime.Before(begin) {
		//	延迟当日
		seconds = begin.Sub(now).Seconds()
	}
	if payTime.After(end) {
		// 延迟次日
		seconds = begin.Add(time.Hour * 24).Sub(now).Seconds()
	}

	if seconds == 0 {
		seconds = begin.Sub(now).Seconds()
	}

	number := util.RangeRandom(1, 120)

	addSeconds := (time.Minute * time.Duration(number)).Seconds()

	t = int64(seconds + addSeconds)

	s.send(msg, t)
}

func (s *MnsClient) SendIntegralAgentPay(data model.MNSIntegralAgentPayOrder) {
	content := encodeContentStruct(data)

	msg := model.MNSIntegralAgentPay + "@" + content

	var t int64
	duration := time.Hour * time.Duration(48)

	// 每天的13-18点分账
	now := time.Now()

	payTime := now.Add(duration)

	begin := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 14, 0, 0, 0, payTime.Location())
	end := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 16, 0, 0, 0, payTime.Location())

	var seconds float64
	if payTime.Before(begin) {
		//	延迟当日
		seconds = begin.Sub(now).Seconds()
	}
	if payTime.After(end) {
		// 延迟次日
		seconds = begin.Add(time.Hour * 24).Sub(now).Seconds()
	}

	if seconds == 0 {
		seconds = begin.Sub(now).Seconds()
	}

	number := util.RangeRandom(1, 120)

	addSeconds := (time.Minute * time.Duration(number)).Seconds()

	t = int64(seconds + addSeconds)

	s.send(msg, t)
}

//
//func (s *MnsClient) SendNextAgentPay(orderID string) {
//	// 检查代付
//	msg := "agentPay@" + orderID
//	var t int64
//	// 每天的14-16点分账
//	now := time.Now()
//
//	payTime := now
//
//	begin := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 14, 0, 0, 0, payTime.Location())
//	end := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 16, 0, 0, 0, payTime.Location())
//
//	var seconds float64
//	if payTime.Before(begin) {
//		//	延迟当日
//		seconds = begin.Sub(now).Seconds()
//	}
//	if payTime.After(end) {
//		// 延迟次日
//		seconds = begin.Add(time.Hour * 24).Sub(now).Seconds()
//	}
//
//	if seconds == 0 {
//		seconds = begin.Sub(now).Seconds()
//	}
//
//	number := util.RangeRandom(1, 120)
//
//	addSeconds := (time.Minute * time.Duration(number)).Seconds()
//
//	t = int64(seconds + addSeconds)
//
//	s.send(msg, t)
//}

func (s *MnsClient) SendDebtAgentPay(debtID string) {
	// 检查代付
	msg := "debtAgentPay@" + debtID
	var t int64
	// 每天的13-18点分账
	now := time.Now()

	payTime := now

	begin := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 14, 0, 0, 0, payTime.Location())
	end := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 16, 0, 0, 0, payTime.Location())

	var seconds float64
	if payTime.Before(begin) {
		//	延迟当日
		seconds = begin.Sub(now).Seconds()
	}
	if payTime.After(end) {
		// 延迟次日
		seconds = begin.Add(time.Hour * 24).Sub(now).Seconds()
	}

	if seconds == 0 {
		seconds = begin.Sub(now).Seconds()
	}

	number := util.RangeRandom(1, 120)

	addSeconds := (time.Minute * time.Duration(number)).Seconds()

	t = int64(seconds + addSeconds)

	s.send(msg, t)
}

func (s *MnsClient) SendCheckWithdraw(withdrawID string) {
	msg := "checkWithdraw@" + withdrawID
	var t int64
	t = 10
	s.send(msg, t)
}

// SendSyncSortData 发货后同步分拣数据[逗号拼接]
func (s *MnsClient) SendSyncSortData(orderIDs string) {
	msg := model.MNSSyncSortData + "@" + orderIDs
	var t int64
	t = 0
	s.send(msg, t)
}

// SendSyncRefundData 同步退款数据
func (s *MnsClient) SendSyncRefundData(orderID string) {
	msg := model.MNSSyncRefundData + "@" + orderID
	var t int64
	t = 0
	s.send(msg, t)
}

// SendSyncQualityRefundData 同步品控退款数据
func (s *MnsClient) SendSyncQualityRefundData(orderID string) {
	msg := model.MNSSyncQualityRefundData + "@" + orderID
	var t int64
	t = 0
	s.send(msg, t)
}

// SendCheckAfterShip 检查发货后补差和退款
func (s *MnsClient) SendCheckAfterShip(orderIDs string) {
	msg := model.MNSCheckAfterShip + "@" + orderIDs
	var t int64
	t = 0
	s.send(msg, t)
}

func (s *MnsClient) SendRecoverProductStockByOrder(orderID string) {
	msg := "recoverProductStock@" + orderID
	var t int64
	t = 5 // 秒
	s.send(msg, t)
}
func (s *MnsClient) SendRecoverProductStockByParentOrder(parentOrderID string) {
	msg := "recoverProductStockByParent@" + parentOrderID
	var t int64
	t = 5 // 秒
	s.send(msg, t)
}

// SendDoAfterShipRefund 执行发货退款
//func (s *MnsClient) SendDoAfterShipRefund(refundIDs []primitive.ObjectID, basicSecond int64) {
//	list := make([]string, 0, len(refundIDs))
//	for _, id := range refundIDs {
//		list = append(list, id.Hex())
//	}
//	m := map[string]interface{}{
//		"refund_id_list": list,
//	}
//	content := encodeContent(m)
//
//	msg := model.MNSDoAfterShipRefund + "@" + content
//	var t int64
//	t = 10 + basicSecond // 秒
//	s.send(msg, t)
//}
//
//func (s *MnsClient) SendDoAfterShipRefundNext(refundIDs []primitive.ObjectID) {
//	list := make([]string, 0, len(refundIDs))
//	for _, id := range refundIDs {
//		list = append(list, id.Hex())
//	}
//	m := map[string]interface{}{
//		"refund_id_list": list,
//	}
//	content := encodeContent(m)
//	msg := model.MNSDoAfterShipRefund + "@" + content
//	var t int64
//	t = 200 // 秒
//	s.send(msg, t)
//}

//func (s *MnsClient) SendCheckOrderHasRefundAll(orderID string) {
//	msg := "checkOrderHasRefundAll@" + orderID
//	var t int64
//	t = 1 // 秒
//	s.send(msg, t)
//}

// SendCloseBizOrderNo 关闭云商通订单
func (s *MnsClient) SendCloseBizOrderNo(bizOrderNo string) {
	msg := "closeAllInPayBizOrderNo@" + bizOrderNo
	var t int64
	duration := time.Minute*5 + 10
	t = int64(duration.Seconds())
	s.send(msg, t)
}

//func (s *MnsClient) SendRefundDeliverAlone(parentOrderID string) {
//	msg := model.MNSRefundDeliverAlone + "@" + parentOrderID
//	var t int64
//	t = 30
//	s.send(msg, t)
//}

// SendActiveRewardCoupon 激活奖励券
//func (s *MnsClient) SendActiveRewardCoupon(couponAccountID primitive.ObjectID) {
//	m := map[string]interface{}{
//		"coupon_account_id": couponAccountID.Hex(),
//	}
//	content := encodeContent(m)
//
//	msg := model.MNSCouponActiveReward + "@" + content
//	var t int64
//	t = 0
//	s.send(msg, t)
//}

//
//// SendCouponCheckAccountValid 检查代金券有效性
//func (s *MnsClient) SendCouponCheckAccountValid(couponAccount model.CouponAccount) {
//	m := map[string]interface{}{
//		"coupon_account_id": couponAccount.ID.Hex(),
//	}
//	content := encodeContent(m)
//
//	msg := model.MNSCouponCheckAccountValid + "@" + content
//	var t int64
//
//	seconds := time.UnixMilli(couponAccount.ValidUseEnd).Sub(time.Now()).Seconds()
//
//	maxSeconds := (time.Hour * 24 * 5).Seconds()
//	if seconds > maxSeconds {
//		//大于5天,最多可以7
//		seconds = maxSeconds
//	}
//
//	t = int64(seconds)
//
//	s.send(msg, t)
//}

func encodeContent(data map[string]interface{}) string {
	marshal, err := json.Marshal(data)
	if err != nil {
		zap.S().Errorf("encodeContent,内容:%s，err:%v", data, err)
		return ""
	}

	toString := base64.StdEncoding.EncodeToString(marshal)
	return toString
}

func encodeContentStruct(data interface{}) string {
	marshal, err := json.Marshal(data)
	if err != nil {
		zap.S().Errorf("encodeContentStruct,内容:%s，err:%v", data, err)
		return ""
	}

	toString := base64.StdEncoding.EncodeToString(marshal)
	return toString
}

// SendCreateFruitClass 创建水果等级
func (s *MnsClient) SendCreateFruitClass(categoryID string) {
	msg := "createFruitClass@" + categoryID
	var t int64
	t = 0
	s.send(msg, t)
}

//
//// SendCancelOrderCoupon 订单代金券退款
//func (s *MnsClient) SendCancelOrderCoupon(orderID string) {
//	msg := model.MNSCancelOrderCoupon + "@" + orderID
//	var t int64
//	t = 0
//	s.send(msg, t)
//}

//// SendUpdateInviteStatus 更新邀请状态
//func (s *MnsClient) SendUpdateInviteStatus(invitedUserID primitive.ObjectID, status model.InviteStatus) {
//	m := map[string]interface{}{
//		"invited_user_id": invitedUserID.Hex(),
//		"invite_status":   status,
//	}
//	content := encodeContent(m)
//
//	msg := model.MNSInviteUpdateStatus + "@" + content
//	var t int64
//
//	t = 0
//
//	s.send(msg, t)
//}

// SendOrderAutoReceive 自动收货
func (s *MnsClient) SendOrderAutoReceive(orderID primitive.ObjectID, hour int) {
	m := map[string]interface{}{
		"order_id": orderID.Hex(),
	}
	content := encodeContent(m)

	msg := model.MNSOrderAutoReceive + "@" + content
	var t int64

	seconds := (time.Hour * time.Duration(hour)).Seconds()

	t = int64(seconds)

	s.send(msg, t)
}

// SendOrderCheckRemoveCommission 订单移除服务费
//func (s *MnsClient) SendOrderCheckRemoveCommission(orderID primitive.ObjectID) {
//	m := map[string]interface{}{
//		"order_id": orderID.Hex(),
//	}
//	content := encodeContent(m)
//
//	msg := model.MNSOrderCheckRemoveCommission + "@" + content
//	var t int64
//
//	seconds := (time.Minute * 1).Seconds()
//
//	t = int64(seconds)
//
//	s.send(msg, t)
//}

// SendAgentPayDeliver 代付配送费
//func (s *MnsClient) SendAgentPayDeliver(parentOrderID primitive.ObjectID) {
//	m := map[string]interface{}{
//		"parent_order_id": parentOrderID.Hex(),
//	}
//	content := encodeContent(m)
//
//	msg := model.MNSAgentPayDeliver + "@" + content
//	var t int64
//
//	seconds := (time.Hour * 1).Seconds()
//
//	number := util.RangeRandom(1, 30)
//
//	addSeconds := (time.Minute * time.Duration(number)).Seconds()
//
//	t = int64(seconds + addSeconds)
//
//	s.send(msg, t)
//}

// SendGenerateIntegral 产生积分
func (s *MnsClient) SendGenerateIntegral(recordType model.RecordType, buyerID, objectID primitive.ObjectID, basicNum int) {
	m := map[string]interface{}{
		"buyer_id":    buyerID.Hex(),
		"record_type": recordType,
		"object_id":   objectID.Hex(),
		"basic_num":   basicNum,
	}
	content := encodeContent(m)

	msg := model.MNSIntegralGenerate + "@" + content
	var t int64

	//number := util.RangeRandom(1, 10)
	//
	//addSeconds := (time.Minute * time.Duration(number)).Seconds()
	//
	//t = int64(0 + addSeconds)

	s.send(msg, t)
}

// SendConsumeIntegral 消费积分
func (s *MnsClient) SendConsumeIntegral(recordType model.RecordType, buyerID, objectID primitive.ObjectID, basicNum int) {
	m := map[string]interface{}{
		"buyer_id":    buyerID.Hex(),
		"record_type": recordType,
		"object_id":   objectID.Hex(),
		"basic_num":   basicNum,
	}
	content := encodeContent(m)

	msg := model.MNSIntegralConsume + "@" + content
	var t int64

	s.send(msg, t)
}

// SendDeliverNoteGenerate 消费积分
func (s *MnsClient) SendDeliverNoteGenerate(data model.MNSGenDeliverNote) {
	//list := make([]string, 0, len(buyerIDList))
	//for _, id := range buyerIDList {
	//	list = append(list, id.Hex())
	//}

	content := encodeContentStruct(data)

	msg := model.MNSDeliverNoteGenerate + "@" + content
	var t int64

	number := util.RangeRandom(1, 20)

	addSeconds := (time.Second * time.Duration(number)).Seconds()

	t = int64(0 + addSeconds)

	s.send(msg, t)
}

// SendCloseDepositOrder 关闭充值订单
//func (s *MnsClient) SendCloseDepositOrder(data model.MNSBizOrderNo) {
//	content := encodeContentStruct(data)
//
//	msg := model.MNSCloseDepositOrder + "@" + content
//	var t int64
//
//	addSeconds := (time.Minute * 6).Seconds()
//
//	t = int64(0 + addSeconds)
//
//	s.send(msg, t)
//}

// SendCloseIntegralOrder 关闭积分订单
func (s *MnsClient) SendCloseIntegralOrder(data model.MNSBizOrderNo) {
	content := encodeContentStruct(data)

	msg := model.MNSCloseIntegralOrder + "@" + content
	var t int64

	addSeconds := (time.Minute * 6).Seconds()

	t = int64(0 + addSeconds)

	s.send(msg, t)
}

// SendCreateRecord 余额消费记录
func (s *MnsClient) SendCreateRecord(buyerBalanceRecordType model.BuyerBalanceRecordType, buyerID, objectID primitive.ObjectID, amount int) {
	content := encodeContentStruct(model.MNSBalanceRecord{
		BuyerID:                buyerID.Hex(),
		ObjectID:               objectID.Hex(),
		BuyerBalanceRecordType: buyerBalanceRecordType,
		Amount:                 amount,
	})

	msg := model.MNSBalanceCreateRecord + "@" + content
	var t int64

	t = 0

	s.send(msg, t)
}

// SendCreateBalanceRecord 余额记录
func (s *MnsClient) SendCreateBalanceRecord(buyerBalanceRecordType model.BuyerBalanceRecordType, buyerID, objectID primitive.ObjectID, amount int) {
	content := encodeContentStruct(model.MNSBalanceRecord{
		BuyerID:                buyerID.Hex(),
		ObjectID:               objectID.Hex(),
		BuyerBalanceRecordType: buyerBalanceRecordType,
		Amount:                 amount,
	})

	msg := model.MNSBalanceCreateRecord + "@" + content
	var t int64

	t = 0

	s.send(msg, t)
}

func (s *MnsClient) SendRemoveBuyerStats(buyerID primitive.ObjectID) {
	content := encodeContentStruct(model.MNSID{
		ID: buyerID.Hex(),
	})

	msg := model.MNSRemoveBuyerStats + "@" + content
	var t int64

	s.send(msg, t)
}

func (s *MnsClient) SendRemoveProductStats(productID primitive.ObjectID) {
	content := encodeContentStruct(model.MNSID{
		ID: productID.Hex(),
	})

	msg := model.MNSRemoveProductStats + "@" + content
	var t int64

	s.send(msg, t)
}

func (s *MnsClient) SendCheckSortOverWeight(data model.MNSOverWeight) {
	content := encodeContentStruct(data)

	msg := model.MNSCheckOverWeight + "@" + content
	var t int64

	t = 5

	s.send(msg, t)
}

// SendRefundChangeAuditor 修改审核员
func (s *MnsClient) SendRefundChangeAuditor(refundID primitive.ObjectID) {
	content := encodeContentStruct(model.MNSRefund{
		RefundID: refundID,
	})

	msg := model.MNSRefundChangeAuditor + "@" + content
	var t int64

	addSeconds := (time.Hour * 24).Seconds()

	t = int64(10 + addSeconds)

	s.send(msg, t)
}

// SendRefundComplete 售后完结
func (s *MnsClient) SendRefundComplete(refundID primitive.ObjectID, delay int64) {
	content := encodeContentStruct(model.MNSRefund{
		RefundID: refundID,
	})

	msg := model.MNSRefundComplete + "@" + content
	var t int64

	t = delay

	s.send(msg, t)
}

// SendOrderAdjustSettleRefund 订单调整结算退款
func (s *MnsClient) SendOrderAdjustSettleRefund(adjustSettleID primitive.ObjectID, delay int64) {
	content := encodeContentStruct(model.MNSOrderAdjustSettle{
		AdjustSettleID: adjustSettleID,
	})

	msg := model.MNSOrderAdjustSettleRefund + "@" + content
	var t int64

	t = delay

	s.send(msg, t)
}

func (s *MnsClient) send(msg string, delaySecond int64) {
	toString := base64.StdEncoding.EncodeToString([]byte(msg))
	req := ali_mns.MessageSendRequest{
		MessageBody:  toString,
		DelaySeconds: delaySecond,
		Priority:     8,
	}
	ret, err := s.queue.SendMessage(req)
	if err != nil {
		zap.S().Errorf("mns 发送失败%v", err)
		return
	}
	_ = ret
	zap.S().Infof("MNS发送,消息ID：%s，内容：%v", ret.MessageId, msg)
	return
}

// SendSupplierFreezeAmount 发送供应商冻结金额消息
func (s *MnsClient) SendSupplierFreezeAmount(ctx context.Context, supplierID primitive.ObjectID, amount int, delaySecond int64) {
	_ = ctx

	content := encodeContentStruct(model.MNSSupplierFreezeAmountInfo{
		SupplierID: supplierID.Hex(),
		Amount:     amount,
	})

	msg := model.MNSSupplierFreezeAmount + "@" + content

	// 立即发送
	s.send(msg, delaySecond)
}
