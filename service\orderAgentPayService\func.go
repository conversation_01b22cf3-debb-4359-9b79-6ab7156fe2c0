package orderAgentPayService

import (
	"base/core/xerr"
	"base/model"

	"github.com/shopspring/decimal"
)

// 配送费
//func (s orderAgentPayService) backDeliverAmount(finalDeliverAmount int) (int, int) {
//	amount := finalDeliverAmount
//
//	fromFloat := decimal.NewFromFloat(s.PlatformPayCommission) // 支付手续费
//	mul := fromFloat.Mul(decimal.NewFromInt(int64(amount)))
//
//	fee := int(mul.Round(0).IntPart())
//	//deliverAmount := amount - fee
//	return amount, fee
//}

// 订单-配送费服务费
func (s orderAgentPayService) backOrderDeliverAmountFee(percent float64, amount int) (int, error) {
	var totalFee int

	percentDe := decimal.NewFromFloat(percent).Div(decimal.NewFromInt(100))

	amountDe := decimal.NewFromInt(int64(amount))
	fee := percentDe.Mul(amountDe).Round(0).IntPart()
	totalFee = int(fee)

	return totalFee, nil
}

// 各角色所得抽成
func backRolesCommission(percent model.ServicePointCommission, amount int) (int, int, int, error) {
	err := checkServicePointCommission(percent)
	if err != nil {
		return 0, 0, 0, err
	}
	// 平台参与分成： 平台=总-集中仓（四舍五入）-服务点（四舍五入）
	// 平台不参与分成： 集中仓=总-服务点（四舍五入）
	var platform int
	var warehouse int

	all := decimal.NewFromInt(int64(amount))
	pointDecimal := all.Mul(decimal.NewFromInt(int64(percent.PointPercent)).Div(decimal.NewFromInt(100))).Round(0) // 四舍五入

	point := int(pointDecimal.IntPart())

	if percent.PlatformPercent == 0 {
		// 平台不参与分成
		warehouse = amount - point
	} else {
		warehouseDecimal := all.Mul(decimal.NewFromInt(int64(percent.WarehousePercent)).Div(decimal.NewFromInt(100))).Round(0)
		warehouse = int(warehouseDecimal.IntPart())
	}

	platform = amount - point - warehouse

	return platform, warehouse, point, nil
}

// 各角色所得抽成
func backRolesCommissionForDebt(percent model.ServicePointCommission, amount int) (int, int, int, error) {
	err := checkServicePointCommission(percent)
	if err != nil {
		return 0, 0, 0, err
	}
	// 平台参与分成： 平台=总-集中仓（四舍五入）-服务点（四舍五入）
	// 平台不参与分成： 集中仓=总-服务点（四舍五入）
	var platform int
	var warehouse int

	all := decimal.NewFromInt(int64(amount))
	pointDecimal := all.Mul(decimal.NewFromInt(int64(percent.PointPercent)).Div(decimal.NewFromInt(100))).Round(0) // 四舍五入

	point := int(pointDecimal.IntPart())

	if percent.PlatformPercent == 0 {
		// 平台不参与分成
		warehouse = amount - point
	} else {
		warehouseDecimal := all.Mul(decimal.NewFromInt(int64(percent.WarehousePercent)).Div(decimal.NewFromInt(100))).Round(0)
		warehouse = int(warehouseDecimal.IntPart())
	}

	platform = amount - point - warehouse

	return platform, warehouse, point, nil
}

// 再次做检查
func checkServicePointCommission(percent model.ServicePointCommission) error {
	if percent.PointPercent+percent.WarehousePercent+percent.PlatformPercent != 100 {
		return xerr.NewErr(xerr.ErrParamError, nil, "分账检查分成不等于100")
	}
	return nil
}

// 服务仓服务费-返利
func backRebate(amount, serviceFeeRebatePercent int) (int, error) {
	if amount == 0 || serviceFeeRebatePercent == 0 {
		return 0, nil
	}

	var totalFee int

	percentDe := decimal.NewFromInt(int64(serviceFeeRebatePercent)).Div(decimal.NewFromInt(100))

	pAmountDe := decimal.NewFromInt(int64(amount))     // 服务费
	fee := percentDe.Mul(pAmountDe).Round(0).IntPart() // 手续费
	totalFee += int(fee)

	return totalFee, nil
}

func backStationCommission(serviceAmount, stationRate int) (pointServiceAmount, stationServiceAmount int, err error) {
	if serviceAmount == 0 {
		return 0, 0, nil
	}

	if stationRate == 0 {
		return serviceAmount, 0, nil
	}

	percentDe := decimal.NewFromInt(int64(stationRate)).Div(decimal.NewFromInt(100))

	amountDe := decimal.NewFromInt(int64(serviceAmount)) // 服务费
	fee := percentDe.Mul(amountDe).Round(0).IntPart()    // 手续费

	pointServiceAmount = int(fee)
	stationServiceAmount = serviceAmount - pointServiceAmount
	return
}

// 商品交易服务费
func backPlatformServiceFee(amount int) (int, error) {
	percent := 0.006
	percentDe := decimal.NewFromFloat(percent)

	pAmountDe := decimal.NewFromInt(int64(amount))     // 商品总额
	fee := percentDe.Mul(pAmountDe).Round(0).IntPart() // 手续费

	return int(fee), nil
}
