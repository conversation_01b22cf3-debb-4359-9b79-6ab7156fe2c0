package promote

import (
	"base/core/xhttp"
	"base/model"
	"base/service/promoteService"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		Status model.PromoteStatus `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := promoteService.NewPromoteService().List(ctx, req.Status, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}
