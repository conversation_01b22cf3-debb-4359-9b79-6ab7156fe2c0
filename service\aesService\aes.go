package aesService

import (
	"base/core/config"
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	En(originPwd string) (string, error)
	De(encryptedPwd string) (string, error)
}
type Aes struct {
	key []byte
}

func NewAesService() *Aes {
	return &Aes{key: []byte(config.Conf.App.AesKey)}
}

// En 加密
func (s Aes) En(origin string) (string, error) {
	encrypted, err := s.aesEncryptCBC([]byte(origin))
	if err != nil {
		zap.S().Errorf("加密失败，明文：%s", origin)
		return "", err
	}
	encodeToString := base64.StdEncoding.EncodeToString(encrypted)
	return encodeToString, nil
}

// De 解密
func (s Aes) De(encrypted string) (string, error) {
	decodeString, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return "", err
	}

	decrypted, err := s.aesDecryptCBC(decodeString)
	if err != nil {
		zap.S().Errorf("解密失败，密文：%s", decodeString)
		return "", err
	}
	return string(decrypted), nil
}

func (s Aes) aesEncryptCBC(origData []byte) (encrypted []byte, err error) {
	// 分组秘钥
	// NewCipher该函数限制了输入k的长度必须为16, 24或者32
	block, err := aes.NewCipher(s.key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()                                // 获取秘钥块的长度
	origData = pKCS7Padding(origData, blockSize)                  // 补全码
	blockMode := cipher.NewCBCEncrypter(block, s.key[:blockSize]) // 加密模式
	encrypted = make([]byte, len(origData))                       // 创建数组
	blockMode.CryptBlocks(encrypted, origData)                    // 加密
	return encrypted, nil
}
func (s Aes) aesDecryptCBC(encrypted []byte) (decrypted []byte, err error) {
	block, err := aes.NewCipher(s.key) // 分组秘钥
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()                                // 获取秘钥块的长度
	blockMode := cipher.NewCBCDecrypter(block, s.key[:blockSize]) // 加密模式
	decrypted = make([]byte, len(encrypted))                      // 创建数组
	blockMode.CryptBlocks(decrypted, encrypted)                   // 解密
	decrypted = pKCS7UnPadding(decrypted)                         // 去除补全码
	return decrypted, nil
}

func pKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func pKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}
