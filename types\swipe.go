package types

import (
	"base/model"
)

type SwipeCreate struct {
	Visible bool            `json:"visible"`                    // 是否展示
	Type    model.SwipeType `json:"type" validate:"oneof=1 2 "` // 跳转类型，1. 纯展示 2.h5
	Url     string          `json:"url"`                        // 页面
	Img     model.FileInfo  `json:"img" validate:"required"`    // 轮播图展示图片地址
}

type SwipeUpdate struct {
	ID      string          `json:"id" validate:"len=24"`
	Sort    int             `json:"sort"`                       // 排序
	Visible bool            `json:"visible"`                    // 是否展示
	Type    model.SwipeType `json:"type" validate:"oneof=1 2 "` // 跳转类型，1. 纯展示 2.h5
	Url     string          `json:"url"`                        // 页面
	Img     model.FileInfo  `json:"img" validate:"required"`    // 轮播图展示图片地址
}

type SwipeUpdateSort struct {
	List []Sort `json:"list"`
}

type Sort struct {
	ID   string `json:"id" validate:"len=24"`
	Sort int    `json:"sort"` // 排序
}
