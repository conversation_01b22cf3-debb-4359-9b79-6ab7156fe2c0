package orderAdjustSettle

import (
	"base/core/xhttp"
	"base/global"
	"base/service/orderAdjustSettleService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Update 编辑调整结算记录(仅草稿状态可编辑)
func Update(ctx *gin.Context) {
	global.OrderAdjustLock.Lock()
	defer global.OrderAdjustLock.Unlock()

	var req types.OrderAdjustSettleUpdateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 获取用户ID
	userID, err := xhttp.GetUserID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	adjustmentID, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 更新调整结算记录
	err = orderAdjustSettleService.NewService().Update(ctx, adjustmentID, req, userID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
