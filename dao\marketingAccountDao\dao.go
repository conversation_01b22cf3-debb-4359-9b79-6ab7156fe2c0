package marketingAccountDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, list model.MarketingAccount) error
	Count(ctx context.Context, filter bson.M) (int64, error)
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.MarketingAccount, error)
}

type marketingAccountDao struct {
	db *mongo.Collection
}

func NewMarketingAccountDao(collect string) DaoInt {
	return marketingAccountDao{
		db: global.MDB.Collection(collect),
	}
}

func (s marketingAccountDao) List(ctx context.Context, filter bson.M) ([]model.MarketingAccount, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.MarketingAccount
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s marketingAccountDao) Create(ctx context.Context, data model.MarketingAccount) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s marketingAccountDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s marketingAccountDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	return count, err
}
