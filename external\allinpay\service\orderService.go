package service

import (
	"encoding/hex"
	"github.com/cnbattle/allinpay/core"
	"github.com/cnbattle/allinpay/utils"
)

// OrderService 订单
type OrderService interface {
	// DepositApply 充值申请
	DepositApply(req DepositApplyReq) (DepositApplyRes, error)
	// WithdrawApply 提现申请
	WithdrawApply(req WithdrawApplyReq) (WithdrawApplyRes, error)
	// AgentCollectApply 托管代收申请
	AgentCollectApply(req AgentCollectApplyReq) (AgentCollectApplyRes, error)
	// SignalAgentPay 单笔托管代付（标准版）
	SignalAgentPay(req SignalAgentPayReq) (SignalAgentPayRes, error)
	// BatchAgentPay 批量托管代付（标准版
	BatchAgentPay(req BatchAgentPayReq) (BatchAgentPayRes, error)
	Refund(req RefundReq) (RefundRes, error) // Refund	退款申请
	// ApplicationTransfer	平台转账
	ApplicationTransfer(req ApplicationTransferReq) (ApplicationTransferRes, error)
	// GetOrderStatus	订单状态查询
	GetOrderStatus(req GetOrderStatusReq) (GetOrderStatusRes, error)
	// GetOrderDetail	订单详细信息查询
	GetOrderDetail(req GetOrderDetailReq) (GetOrderDetailRes, error)
	// QueryBalance	查询余额
	QueryBalance(req QueryBalanceReq) (QueryBalanceRes, error)
	// CloseOrder	关闭订单
	CloseOrder(req CloseOrderReq) (CloseOrderRes, error)

	// GetPaymentInformationDetail 付款方资金代付明细查询
	GetPaymentInformationDetail(req GetPaymentInformationDetailReq) (GetPaymentInformationDetailRes, error)

	// QueryInExpDetail	付款方资金代付明细查询
	QueryInExpDetail(req QueryInExpDetailReq) (QueryInExpDetailRes, error)
	// GetOrderSplitRuleListDetail	订单分账明细
	GetOrderSplitRuleListDetail(req GetOrderSplitRuleListDetailReq) (GetOrderSplitRuleListDetailRes, error)
	// PayByBackSMS 确认支付（后台+短信验证码确认）
	PayByBackSMS(req PayByBackSMSReq) (PayByBackSMSRes, error)

	// ResendPaySMS 重发支付短信验证码
	ResendPaySMS(req ResendPaySMSReq) (ResendPaySMSRes, error)
}

type order struct {
	cli *core.Client
}

func NewOrder(cli *core.Client) OrderService {
	return &order{
		cli: cli,
	}

}

// DepositApply 充值申请
func (s order) DepositApply(req DepositApplyReq) (DepositApplyRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo
	params["bizUserId"] = req.BizUserId
	// 账户集编号
	params["accountSetNo"] = req.AccountSetNo
	params["amount"] = req.Amount
	params["fee"] = req.Fee
	// 如不填，默认为短信验证码确认
	params["validateType"] = req.ValidateType

	//params["frontUrl"] = req.FrontUrl

	params["backUrl"] = req.BackUrl
	params["orderExpireDatetime"] = req.OrderExpireDatetime
	params["payMethod"] = req.PayMethod
	//params["goodsName"] = req.GoodsName
	params["industryCode"] = req.IndustryCode
	params["industryName"] = req.IndustryName
	params["source"] = req.Source

	params["summary"] = req.Summary
	params["extendInfo"] = req.ExtendInfo

	method := "allinpay.yunst.orderService.depositApply"

	var res DepositApplyRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return DepositApplyRes{}, err
	}

	return res, nil
}

// WithdrawApply 提现申请
func (s order) WithdrawApply(req WithdrawApplyReq) (WithdrawApplyRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo
	params["bizUserId"] = req.BizUserId

	// 账户集编号
	params["accountSetNo"] = req.AccountSetNo
	params["amount"] = req.Amount
	params["fee"] = req.Fee

	//如不填，默认为短信验证码确认
	//平台提现，只支持无验证和短信验证
	params["validateType"] = req.ValidateType
	params["backUrl"] = req.BackUrl
	params["orderExpireDatetime"] = req.OrderExpireDatetime
	// 如不传，则默认为通联通代付
	params["payMethod"] = req.PayMethod
	params["bankCardPro"] = req.BankCardPro

	// aes
	sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
	if err != nil {
		return WithdrawApplyRes{}, err
	}
	params["bankCardNo"] = hex.EncodeToString(utils.AesEcbEncrypt([]byte(req.BankCardNo), sha1PRNG))

	params["withdrawType"] = req.WithdrawType

	params["industryCode"] = req.IndustryCode
	params["industryName"] = req.IndustryName
	params["source"] = req.Source

	params["summary"] = req.Summary
	params["extendInfo"] = req.ExtendInfo
	method := "allinpay.yunst.orderService.withdrawApply"

	var res WithdrawApplyRes

	err = s.cli.Request(method, params, &res)
	if err != nil {
		return WithdrawApplyRes{}, err
	}

	return res, nil
}

// AgentCollectApply 托管代收申请
func (s order) AgentCollectApply(req AgentCollectApplyReq) (AgentCollectApplyRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo
	params["payerId"] = req.PayerId

	params["recieverList"] = req.RecieverList
	//params["goodsType"] = req.GoodsType
	//params["bizGoodsNo"] = req.BizGoodsNo

	params["tradeCode"] = req.TradeCode
	params["amount"] = req.Amount
	params["fee"] = req.Fee
	params["validateType"] = req.ValidateType
	//params["frontUrl"] = req.FrontUrl
	params["backUrl"] = req.BackUrl
	params["orderExpireDatetime"] = req.OrderExpireDatetime

	params["payMethod"] = req.PayMethod

	params["backUrl"] = req.BackUrl
	//params["goodsName"] = req.GoodsName
	//params["goodsDesc"] = req.GoodsDesc

	params["industryCode"] = req.IndustryCode
	params["industryName"] = req.IndustryName
	params["source"] = req.Source

	params["summary"] = req.Summary
	params["extendInfo"] = req.ExtendInfo
	method := "allinpay.yunst.orderService.agentCollectApply"

	var res AgentCollectApplyRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return AgentCollectApplyRes{}, err
	}

	return res, nil
}

// BatchAgentPay 批量托管代付（标准版
func (s order) BatchAgentPay(req BatchAgentPayReq) (BatchAgentPayRes, error) {
	params := map[string]interface{}{}
	params["bizBatchNo"] = req.BizBatchNo
	params["batchPayList"] = req.BatchPayList
	//params["bizGoodsNo"] = req.BizGoodsNo
	//params["goodsType"] = req.GoodsType
	params["tradeCode"] = req.TradeCode
	method := "allinpay.yunst.orderService.batchAgentPay"

	var res BatchAgentPayRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return BatchAgentPayRes{}, err
	}

	return res, nil
}

// Refund	退款申请
func (s order) Refund(req RefundReq) (RefundRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo
	params["oriBizOrderNo"] = req.OriBizOrderNo
	params["oriOrderNo"] = req.OriOrderNo

	params["bizUserId"] = req.BizUserId

	params["refundType"] = "D0"

	params["refundList"] = req.RefundList
	params["backUrl"] = req.BackUrl
	params["amount"] = req.Amount
	params["couponAmount"] = req.CouponAmount
	params["feeAmount"] = req.FeeAmount
	if req.RefundAccount == "TLT" {
		params["refundAccount"] = req.RefundAccount
	}
	//params["benefitdetail"] =
	params["extendInfo"] = req.ExtendInfo

	method := "allinpay.yunst.orderService.refund"

	var res RefundRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return RefundRes{}, err
	}

	return res, nil
}

// ApplicationTransfer	平台转账
func (s order) ApplicationTransfer(req ApplicationTransferReq) (ApplicationTransferRes, error) {
	params := map[string]interface{}{}
	params["bizTransferNo"] = req.BizTransferNo
	params["sourceAccountSetNo"] = req.SourceAccountSetNo
	params["targetBizUserId"] = req.TargetBizUserId
	params["targetAccountSetNo"] = req.TargetAccountSetNo
	params["amount"] = req.Amount
	params["extendInfo"] = req.ExtendInfo

	method := "allinpay.yunst.orderService.applicationTransfer"

	var res ApplicationTransferRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return ApplicationTransferRes{}, err
	}

	return res, nil
}

// GetOrderStatus	订单状态查询
func (s order) GetOrderStatus(req GetOrderStatusReq) (GetOrderStatusRes, error) {
	params := map[string]interface{}{}
	params["orderNo"] = req.OrderNo
	params["bizOrderNo"] = req.BizOrderNo

	method := "allinpay.yunst.orderService.getOrderStatus"

	var res GetOrderStatusRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return GetOrderStatusRes{}, err
	}

	return res, nil
}

// GetOrderDetail	订单详细信息查询
func (s order) GetOrderDetail(req GetOrderDetailReq) (GetOrderDetailRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo

	method := "allinpay.yunst.orderService.getOrderDetail"

	var res GetOrderDetailRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return GetOrderDetailRes{}, err
	}

	return res, nil
}

// QueryBalance	查询余额
func (s order) QueryBalance(req QueryBalanceReq) (QueryBalanceRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["accountSetNo"] = req.AccountSetNo

	method := "allinpay.yunst.orderService.queryBalance"

	var res QueryBalanceRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return QueryBalanceRes{}, err
	}

	return res, nil
}

// CloseOrder	关闭订单
func (s order) CloseOrder(req CloseOrderReq) (CloseOrderRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo

	method := "allinpay.yunst.orderService.closeOrder"

	var res CloseOrderRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return CloseOrderRes{}, err
	}

	return res, nil
}

// QueryInExpDetail	账户收支明细
func (s order) QueryInExpDetail(req QueryInExpDetailReq) (QueryInExpDetailRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["dateStart"] = req.DateStart
	params["dateEnd"] = req.DateEnd
	params["startPosition"] = req.StartPosition
	params["queryNum"] = req.QueryNum

	// 非必填
	params["tradeType"] = req.TradeType
	params["bizOrderNo"] = req.BizOrderNo
	params["accountSetNo"] = req.AccountSetNo

	method := "allinpay.yunst.orderService.queryInExpDetail"

	var res QueryInExpDetailRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return QueryInExpDetailRes{}, err
	}

	return res, nil
}

// GetPaymentInformationDetail	付款方资金代付明细查询
func (s order) GetPaymentInformationDetail(req GetPaymentInformationDetailReq) (GetPaymentInformationDetailRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo

	method := "allinpay.yunst.orderService.getPaymentInformationDetail"

	var res GetPaymentInformationDetailRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return GetPaymentInformationDetailRes{}, err
	}

	return res, nil
}

// GetOrderSplitRuleListDetail	订单分账明细
func (s order) GetOrderSplitRuleListDetail(req GetOrderSplitRuleListDetailReq) (GetOrderSplitRuleListDetailRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo

	method := "allinpay.yunst.orderService.getOrderSplitRuleListDetail"

	var res GetOrderSplitRuleListDetailRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return GetOrderSplitRuleListDetailRes{}, err
	}

	return res, nil
}

// PayByBackSMS	确认支付（后台+短信验证码确认）
func (s order) PayByBackSMS(req PayByBackSMSReq) (PayByBackSMSRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["bizOrderNo"] = req.BizOrderNo
	params["verificationCode"] = req.VerificationCode
	params["consumerIp"] = req.ConsumerIp

	method := "allinpay.yunst.orderService.payByBackSMS"

	var res PayByBackSMSRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return PayByBackSMSRes{}, err
	}

	return res, nil
}

// ResendPaySMS	重发支付短信验证码
func (s order) ResendPaySMS(req ResendPaySMSReq) (ResendPaySMSRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo

	method := "allinpay.yunst.orderService.resendPaySMS"

	var res ResendPaySMSRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return ResendPaySMSRes{}, err
	}

	return res, nil
}

// SignalAgentPay 单笔托管代付（标准版）
func (s order) SignalAgentPay(req SignalAgentPayReq) (SignalAgentPayRes, error) {
	params := map[string]interface{}{}
	params["bizOrderNo"] = req.BizOrderNo
	params["collectPayList"] = req.CollectPayList
	params["bizUserId"] = req.BizUserId
	params["accountSetNo"] = req.AccountSetNo
	params["backUrl"] = req.BackUrl
	params["amount"] = req.Amount
	params["fee"] = req.Fee
	params["splitRuleList"] = req.SplitRuleList
	params["tradeCode"] = req.TradeCode
	params["summary"] = req.Summary
	params["extendInfo"] = req.ExtendInfo

	//params["bizGoodsNo"] = req.BizGoodsNo
	//params["goodsType"] = req.GoodsType
	method := "allinpay.yunst.orderService.signalAgentPay"

	var res SignalAgentPayRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return SignalAgentPayRes{}, err
	}

	return res, nil
}
