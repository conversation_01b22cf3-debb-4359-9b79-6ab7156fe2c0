package cartService

import (
	"base/dao"
	"base/dao/cartDao"
	"base/global"
	"base/model"
	"base/service/productService"
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 购物车服务接口
type ServiceInterface interface {
	List(ctx context.Context, buyerID primitive.ObjectID) ([]model.Cart, error)
	ListByProduct(ctx context.Context, buyerID, productID primitive.ObjectID) ([]model.Cart, error)
	GetNumByBuyer(ctx context.Context, buyerID, pointID primitive.ObjectID) (int, error)
	GetTotalNumByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int, error)
	DeleteByProduct(ctx context.Context, buyerID primitive.ObjectID, productIDs []primitive.ObjectID) error
	DeleteByProductOne(ctx context.Context, buyerID primitive.ObjectID, productID primitive.ObjectID, skuIDCode string) error
	DeleteByIDList(ctx context.Context, iDs []primitive.ObjectID) error
	Upsert(ctx context.Context, buyerID, productID primitive.ObjectID, skuIDCode string, count int) error
	UpdateCount(ctx context.Context, id primitive.ObjectID, num int) error
	RemoveCartProduct(ctx context.Context, buyerID primitive.ObjectID, mCart map[string]primitive.ObjectID) error
}

type cartService struct {
	mdb      *mongo.Database
	db       cartDao.DaoInt
	productS productService.ServiceInterface
}

// ListByProduct 根据买家ID和商品ID获取购物车商品列表
func (s cartService) ListByProduct(ctx context.Context, buyerID, productID primitive.ObjectID) ([]model.Cart, error) {
	filter := bson.M{
		"buyer_id":   buyerID,
		"product_id": productID,
	}
	cartList, err := s.db.List(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	}
	return cartList, nil
}

// GetNumByBuyer 根据买家ID和店铺ID获取购物车商品数量
func (s cartService) GetNumByBuyer(ctx context.Context, buyerID, pointID primitive.ObjectID) (int, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		//"service_point_id": pointID,
	}
	//i, err := s.db.Count(ctx, filter)
	//if err != nil {
	//	return 0, err
	//}

	carts, err := s.db.List(ctx, filter)
	if err != nil {
		return 0, err
	}
	var i int
	for _, cart := range carts {
		i += cart.Count
	}

	return i, nil
}

// GetTotalNumByBuyer 根据买家ID获取购物车商品总数
func (s cartService) GetTotalNumByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}

	carts, err := s.db.List(ctx, filter)
	if err != nil {
		return 0, err
	}
	var i int
	for _, cart := range carts {
		i += cart.Count
	}

	return i, nil
}

// UpdateCount 更新购物车商品数量
func (s cartService) UpdateCount(ctx context.Context, id primitive.ObjectID, num int) error {
	filter := bson.M{
		"_id": id,
	}

	update := bson.M{
		"$set": bson.M{
			"count": num,
		},
	}

	err := s.db.Update(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

// Upsert 更新或创建购物车商品
func (s cartService) Upsert(ctx context.Context, buyerID, productID primitive.ObjectID, skuIDCode string, count int) error {
	if count < 1 {
		return nil
	}

	product, err := s.productS.Get(ctx, productID)
	if err != nil {
		return err
	}
	// 查询商品的所有购物车sku列表
	filter := bson.M{"buyer_id": buyerID, "product_id": productID, "sku_id_code": skuIDCode}
	cart, err := s.db.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	millis := time.Now().UnixMilli()

	if cart.ID.IsZero() {
		// 不存在购物车，创建购物车
		data := model.Cart{
			ID:         primitive.NewObjectID(),
			BuyerID:    buyerID,
			ProductID:  productID,
			SupplierID: product.SupplierID,
			SkuIDCode:  skuIDCode,
			Count:      count,
			CreatedAt:  millis,
		}
		err = s.db.Create(ctx, data)
		if err != nil {
			return err
		}
	} else {
		// 存在购物车，更新购物车
		update := bson.M{
			"$set": bson.M{
				"count": count,
			},
		}
		err = s.db.Update(ctx, filter, update)
		if err != nil {
			return err
		}
	}

	return nil
}

// List 根据买家ID获取购物车商品列表
func (s cartService) List(ctx context.Context, buyerID primitive.ObjectID) ([]model.Cart, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}

	list, err := s.db.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// DeleteByProduct 根据商品ID列表删除购物车商品
func (s cartService) DeleteByProduct(ctx context.Context, buyerID primitive.ObjectID, productIDs []primitive.ObjectID) error {
	if len(productIDs) < 1 {
		return nil
	}
	filter := bson.M{
		"buyer_id": buyerID,
		"product_id": bson.M{
			"$in": productIDs,
		},
	}
	err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// DeleteByProductOne 根据商品ID和SKU ID删除购物车商品
func (s cartService) DeleteByProductOne(ctx context.Context, buyerID primitive.ObjectID, productID primitive.ObjectID, skuIDCode string) error {
	filter := bson.M{
		"buyer_id":    buyerID,
		"product_id":  productID,
		"sku_id_code": skuIDCode,
	}
	err := s.db.Delete(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// DeleteByIDList 根据ID列表删除购物车商品
func (s cartService) DeleteByIDList(ctx context.Context, iDs []primitive.ObjectID) error {
	if len(iDs) < 1 {
		return nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": iDs,
		},
	}
	err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// RemoveCartProduct 删除购物车商品
func (s cartService) RemoveCartProduct(ctx context.Context, buyerID primitive.ObjectID, mCart map[string]primitive.ObjectID) error {
	// 遍历删除
	for k, v := range mCart {
		filter := bson.M{
			"buyer_id":    buyerID,
			"product_id":  v,
			"sku_id_code": k,
		}
		err := s.db.Delete(ctx, filter)
		if err != nil {
			return err
		}
	}
	return nil
}

// NewCartService 创建购物车服务
func NewCartService() ServiceInterface {
	return cartService{
		mdb:      global.MDB,
		db:       dao.CartDao,
		productS: productService.NewProductService(),
	}
}
