package orderAdjustSettle

import (
	"base/core/xhttp"
	"base/global"
	"base/service/orderAdjustSettleService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Confirm 确认调整结算记录
func Confirm(ctx *gin.Context) {
	global.OrderAdjustLock.Lock()
	defer global.OrderAdjustLock.Unlock()

	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	adjustmentID, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 确认调整结算记录
	err = orderAdjustSettleService.NewService().Confirm(ctx, adjustmentID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
