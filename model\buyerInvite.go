package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type InviteStatus int

const (
	InviteStatusToOrder  InviteStatus = 1 // 待下单
	InviteStatusOrdered  InviteStatus = 2 // 已下单
	InviteStatusReceived InviteStatus = 3 // 已收货
	InviteStatusFailed   InviteStatus = 4 // 失败
)

// Invite 邀请信息
type Invite struct {
	ID            primitive.ObjectID `bson:"_id" json:"id"`
	InviterUserID primitive.ObjectID `json:"inviter_user_id" bson:"inviter_user_id"` // 邀请人ID
	InvitedUserID primitive.ObjectID `json:"invited_user_id" bson:"invited_user_id"` // 被邀请者ID
	InviteStatus  InviteStatus       `json:"invite_status" bson:"invite_status"`     // 状态
	CreatedAt     int64              `bson:"created_at" json:"created_at"`
	UpdatedAt     int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt     int64              `bson:"deleted_at" json:"deleted_at"`
}

// InviteConfig 邀请配置
type InviteConfig struct {
	IsOpen    bool  `json:"is_open"`
	TimeBegin int64 `json:"time_begin"`
	TimeEnd   int64 `json:"time_end"`
	UpdatedAt int64 `json:"updated_at"`
}
