package tag

import (
	"base/core/xhttp"
	"base/service/tagService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func UpdateTitle(ctx *gin.Context) {
	var req = struct {
		ID    string `uri:"id" validate:"len=24"`
		Title string `json:"title"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = tagService.NewTagService().UpdateTitle(ctx, id, req.Title)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateValid(ctx *gin.Context) {
	var req = struct {
		ID    string `uri:"id" validate:"len=24"`
		Valid bool   `json:"valid"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = tagService.NewTagService().UpdateStatus(ctx, id, req.Valid)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
