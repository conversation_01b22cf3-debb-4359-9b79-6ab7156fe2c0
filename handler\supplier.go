package handler

import (
	"base/api/supplier"
	"base/api/supplier/supplierTag"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 供应商
func supplierRouter(r *gin.RouterGroup) {
	r = r.Group("/supplier")

	r2 := r.Group("/")
	r2.POST("/stats/for/user", supplier.GetStatsForUser)
	r2.GET("/:id", supplier.Get)
	r2.POST("/get/by/web", supplier.GetByWeb)
	r2.POST("/get", supplier.GetByPost)

	r3 := r.Group("/").Use(middleware.CheckToken)
	r3.POST("/create", supplier.Apply)
	//r3.POST("/apply/update", supplier.UpdateApply)
	r3.POST("/update/avatar", supplier.UpdateAvatar)
	r3.POST("/update/status", supplier.UpdateStatus)
	r3.POST("/update/business/manage", supplier.UpdateIsBusinessManage)
	r3.GET("/user/:user_id", supplier.GetByUser)
	r3.POST("/get/user", supplier.GetByUserPost)
	r3.POST("/search", supplier.Search)
	r3.POST("/tab/bar/tip", supplier.GetTabBarTip)

	r5 := r.Group("/tag")
	r5.POST("/create", supplierTag.Create)
	r5.POST("/list", supplierTag.List)
	r5.POST("/update", supplierTag.UpdateTitle)
	r5.POST("/delete", supplierTag.Delete)
	// 绑定标签，权限
	r5.POST("/bind", supplier.BindTag)
}
