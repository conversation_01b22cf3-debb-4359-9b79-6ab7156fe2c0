package promoteService

import (
	"base/dao"
	"base/dao/promoteDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.Promote) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.PromoteStatus) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Promote, error)
	List(ctx context.Context, status model.PromoteStatus, pointID primitive.ObjectID) ([]model.Promote, error)
	DownProduct(ctx context.Context, productID primitive.ObjectID) error
}

type promoteService struct {
	promoteDao promoteDao.DaoInt
}

func NewPromoteService() ServiceInterface {
	return promoteService{
		promoteDao: dao.PromoteDao,
	}
}

func (s promoteService) Create(ctx context.Context, data model.Promote) error {
	err := s.promoteDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s promoteService) Update(ctx context.Context, filter, update bson.M) error {
	err := s.promoteDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s promoteService) UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.PromoteStatus) error {
	err := s.promoteDao.UpdateOne(ctx, bson.M{
		"_id": id,
	}, bson.M{
		"$set": bson.M{
			"status": status,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (s promoteService) List(ctx context.Context, status model.PromoteStatus, pointID primitive.ObjectID) ([]model.Promote, error) {
	list, err := s.promoteDao.List(ctx, bson.M{
		"status":           status,
		"service_point_id": pointID,
	})
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s promoteService) Get(ctx context.Context, id primitive.ObjectID) (model.Promote, error) {
	data, err := s.promoteDao.Get(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return model.Promote{}, err
	}

	return data, nil
}

func (s promoteService) Delete(ctx context.Context, id primitive.ObjectID) error {
	err := s.promoteDao.DeleteOne(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s promoteService) DownProduct(ctx context.Context, productID primitive.ObjectID) error {
	filter := bson.M{
		"product_list": bson.M{
			"$in": bson.A{productID},
		},
	}

	list, err := s.promoteDao.List(ctx, filter)
	if err != nil {
		return err
	}
	if len(list) > 0 {

		update := bson.M{
			"$pull": bson.M{
				"product_list": bson.M{
					"$in": bson.A{productID},
				},
			},
		}

		err = s.promoteDao.UpdateMany(ctx, filter, update)
		if err != nil {
			return err
		}
	}

	return nil
}
