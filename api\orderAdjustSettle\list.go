package orderAdjustSettle

import (
	"base/core/xhttp"
	"base/service/orderAdjustSettleService"
	"base/types"

	"github.com/gin-gonic/gin"
)

// List 查询调整结算记录列表
func List(ctx *gin.Context) {
	var req types.OrderAdjustSettleListReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, total, err := orderAdjustSettleService.NewService().List(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, total)
}

// ListByBuyer 查询调整结算记录列表
func ListByBuyer(ctx *gin.Context) {
	var req types.OrderAdjustSettleListReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, total, err := orderAdjustSettleService.NewService().ListByBuyer(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, total)
}

// ListBySupplier 查询调整结算记录列表
func ListBySupplier(ctx *gin.Context) {
	var req types.OrderAdjustSettleListReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, total, err := orderAdjustSettleService.NewService().ListBySupplier(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, total)
}
