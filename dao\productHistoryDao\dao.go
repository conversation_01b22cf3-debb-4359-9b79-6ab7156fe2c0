package productHistoryDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, product model.ProductHistory) error
	Find(ctx context.Context, filter bson.M) ([]model.ProductHistory, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProductHistory, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.ProductHistory, error)
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	DeleteOne(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.ProductHistory, error)
}

type productHistoryDao struct {
	db *mongo.Collection
}

func (s productHistoryDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProductHistory, int64, error) {
	var list []model.ProductHistory
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s productHistoryDao) List(ctx context.Context, filter bson.M) ([]model.ProductHistory, error) {
	var list []model.ProductHistory
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s productHistoryDao) Get(ctx context.Context, filter bson.M) (model.ProductHistory, error) {
	var data model.ProductHistory
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductHistory{}, err
	}
	return data, nil
}

func NewProductHistoryDao(collect string) DaoInt {
	return productHistoryDao{
		db: global.MDB.Collection(collect),
	}
}

func (s productHistoryDao) Create(ctx context.Context, product model.ProductHistory) error {
	_, err := s.db.InsertOne(ctx, product)
	if err != nil {
		return err
	}
	return nil
}

func (s productHistoryDao) Replace(ctx context.Context, data model.ProductHistory) error {
	_, err := s.db.ReplaceOne(ctx, bson.M{"_id": data.ID}, data)
	if err != nil {
		return err
	}
	return nil
}

func (s productHistoryDao) Find(ctx context.Context, filter bson.M) ([]model.ProductHistory, error) {
	var list []model.ProductHistory
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts := options.Find()
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s productHistoryDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s productHistoryDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	_ = res
	return err
}

func (s productHistoryDao) DeleteMany(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteMany(ctx, filter)
	_ = res
	return err
}

func (s productHistoryDao) DeleteOne(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteOne(ctx, filter)
	_ = res
	return err
}
