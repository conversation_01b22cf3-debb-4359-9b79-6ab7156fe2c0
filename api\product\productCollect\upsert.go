package productCollect

import (
	"base/core/xhttp"
	"base/service/productCollectService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Upsert(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		ProductID string `json:"product_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithNote(req.ProductID, "product_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productCollectService.NewProductCollectService().Upsert(ctx, id, productID, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
