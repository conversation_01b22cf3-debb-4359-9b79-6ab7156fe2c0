package orderWarehouse

import (
	"base/core/xhttp"
	"base/service/orderWarehouseService"
	"github.com/gin-gonic/gin"
)

func ListOverWeight(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		//StationID      string `json:"station_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	weight, err := orderWarehouseService.NewOrderWarehouseService().ListOverWeight(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, weight)
}
