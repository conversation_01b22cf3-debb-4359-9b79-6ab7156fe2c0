package payModule

import (
	"base/global"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
)

// OrderService 订单
type OrderService interface {
	// DepositApplyS 充值申请
	DepositApplyS(req pays.DepositApplyReq) (pays.DepositApplyRes, error)
	// WithdrawApplyS 提现申请
	WithdrawApplyS(req pays.WithdrawApplyReq) (pays.WithdrawApplyRes, error)

	// AgentCollectApplyS 托管代收申请
	AgentCollectApplyS(req pays.AgentCollectApplyReq) (pays.AgentCollectApplyRes, error)
	// SignalAgentPayS 单笔托管代付（标准版）
	SignalAgentPayS(req pays.SignalAgentPayReq) (pays.SignalAgentPayRes, error)

	// BatchAgentPayS 批量托管代付（标准版
	BatchAgentPayS(req pays.BatchAgentPayReq) (pays.BatchAgentPayRes, error)

	// RefundS	退款申请
	RefundS(req pays.RefundReq) (pays.RefundRes, error)
	// ApplicationTransferS	平台转账
	ApplicationTransferS(req pays.ApplicationTransferReq) (pays.ApplicationTransferRes, error)
	// GetOrderStatusS	订单状态查询
	GetOrderStatusS(req pays.GetOrderStatusReq) (pays.GetOrderStatusRes, error)
	// GetOrderDetailS	订单详细信息查询
	GetOrderDetailS(req pays.GetOrderDetailReq) (pays.GetOrderDetailRes, error)
	// QueryBalanceS	查询余额
	QueryBalanceS(req pays.QueryBalanceReq) (pays.QueryBalanceRes, error)
	// CloseOrderS	关闭订单
	CloseOrderS(req pays.CloseOrderReq) (pays.CloseOrderRes, error)

	// QueryInExpDetailS	账户收支明细
	QueryInExpDetailS(req pays.QueryInExpDetailReq) (pays.QueryInExpDetailRes, error)

	// GetPaymentInformationDetailS 付款方资金代付明细查询
	GetPaymentInformationDetailS(req pays.GetPaymentInformationDetailReq) (pays.GetPaymentInformationDetailRes, error)

	// GetOrderSplitRuleListDetailS	订单分账明细
	GetOrderSplitRuleListDetailS(req pays.GetOrderSplitRuleListDetailReq) (pays.GetOrderSplitRuleListDetailRes, error)

	//	PayByBackSMS 确认支付（后台+短信验证码确认）
	PayByBackSMS(req pays.PayByBackSMSReq) (pays.PayByBackSMSRes, error)

	// ResendPaySMS 重发支付短信验证码
	ResendPaySMS(req pays.ResendPaySMSReq) (pays.ResendPaySMSRes, error)
}

type order struct {
	cli *allinpay.AllInPay
}

func NewOrderS() OrderService {
	return &order{
		cli: global.AllinPayClient,
	}

}

// DepositApplyS 充值申请
func (s order) DepositApplyS(req pays.DepositApplyReq) (pays.DepositApplyRes, error) {
	res, err := s.cli.OrderService.DepositApply(req)
	return res, err
}

// WithdrawApplyS 提现申请
func (s order) WithdrawApplyS(req pays.WithdrawApplyReq) (pays.WithdrawApplyRes, error) {
	res, err := s.cli.OrderService.WithdrawApply(req)
	return res, err
}

// AgentCollectApplyS 托管代收申请
func (s order) AgentCollectApplyS(req pays.AgentCollectApplyReq) (pays.AgentCollectApplyRes, error) {
	res, err := s.cli.OrderService.AgentCollectApply(req)
	return res, err
}

// SignalAgentPayS 单笔托管代付（标准版）
func (s order) SignalAgentPayS(req pays.SignalAgentPayReq) (pays.SignalAgentPayRes, error) {
	res, err := s.cli.OrderService.SignalAgentPay(req)
	return res, err
}

// BatchAgentPayS 批量托管代付（标准版
func (s order) BatchAgentPayS(req pays.BatchAgentPayReq) (pays.BatchAgentPayRes, error) {
	res, err := s.cli.OrderService.BatchAgentPay(req)
	return res, err
}

// RefundS	退款申请
func (s order) RefundS(req pays.RefundReq) (pays.RefundRes, error) {
	res, err := s.cli.OrderService.Refund(req)
	return res, err
}

// ApplicationTransferS	平台转账
func (s order) ApplicationTransferS(req pays.ApplicationTransferReq) (pays.ApplicationTransferRes, error) {
	res, err := s.cli.OrderService.ApplicationTransfer(req)
	return res, err
}

// GetOrderStatusS	订单状态查询
func (s order) GetOrderStatusS(req pays.GetOrderStatusReq) (pays.GetOrderStatusRes, error) {
	res, err := s.cli.OrderService.GetOrderStatus(req)
	return res, err
}

// GetOrderDetailS	订单详细信息查询
func (s order) GetOrderDetailS(req pays.GetOrderDetailReq) (pays.GetOrderDetailRes, error) {
	res, err := s.cli.OrderService.GetOrderDetail(req)
	return res, err
}

// QueryBalanceS	查询余额
func (s order) QueryBalanceS(req pays.QueryBalanceReq) (pays.QueryBalanceRes, error) {
	res, err := s.cli.OrderService.QueryBalance(req)
	return res, err
}

// CloseOrderS	关闭订单
func (s order) CloseOrderS(req pays.CloseOrderReq) (pays.CloseOrderRes, error) {
	res, err := s.cli.OrderService.CloseOrder(req)
	return res, err
}

// QueryInExpDetailS	账户收支明细
func (s order) QueryInExpDetailS(req pays.QueryInExpDetailReq) (pays.QueryInExpDetailRes, error) {
	res, err := s.cli.OrderService.QueryInExpDetail(req)
	return res, err
}

// GetPaymentInformationDetailS 付款方资金代付明细查询
func (s order) GetPaymentInformationDetailS(req pays.GetPaymentInformationDetailReq) (pays.GetPaymentInformationDetailRes, error) {
	res, err := s.cli.OrderService.GetPaymentInformationDetail(req)
	return res, err
}

// GetOrderSplitRuleListDetailS	订单分账明细
func (s order) GetOrderSplitRuleListDetailS(req pays.GetOrderSplitRuleListDetailReq) (pays.GetOrderSplitRuleListDetailRes, error) {
	res, err := s.cli.OrderService.GetOrderSplitRuleListDetail(req)
	return res, err
}

// PayByBackSMS	确认支付（后台+短信验证码确认）
func (s order) PayByBackSMS(req pays.PayByBackSMSReq) (pays.PayByBackSMSRes, error) {
	res, err := s.cli.OrderService.PayByBackSMS(req)
	return res, err
}

// ResendPaySMS	重发支付短信验证码
func (s order) ResendPaySMS(req pays.ResendPaySMSReq) (pays.ResendPaySMSRes, error) {
	res, err := s.cli.OrderService.ResendPaySMS(req)
	return res, err
}
