package signProtocolDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Upsert(data model.Supplier) error
	Create(ctx context.Context, data model.Supplier) (primitive.ObjectID, error)
	Update(ctx context.Context, filter, update bson.M) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Supplier, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.Supplier, error)
}

type signPupplierDao struct {
	db *mongo.Collection
}

func (s signPupplierDao) Upsert(data model.Supplier) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(context.Background(), bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}

	return nil
}

func (s signPupplierDao) Get(ctx context.Context, id primitive.ObjectID) (model.Supplier, error) {
	var data model.Supplier
	err := s.db.FindOne(ctx, bson.M{"_id": id}).Decode(&data)
	if err != nil {
		return model.Supplier{}, err
	}
	return data, nil
}

func (s signPupplierDao) Create(ctx context.Context, data model.Supplier) (primitive.ObjectID, error) {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return data.ID, nil
}

func (s signPupplierDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s signPupplierDao) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Supplier, error) {
	var data model.Supplier
	err := s.db.FindOne(ctx, bson.M{"user_id": userID}).Decode(&data)
	if err != nil {
		return model.Supplier{}, err
	}
	return data, nil
}

func NewSignProtocolDao(collect string) DaoInt {
	return signPupplierDao{
		db: global.MDB.Collection(collect),
	}
}
