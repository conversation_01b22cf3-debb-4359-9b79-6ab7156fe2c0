package orderDebt

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		//ServicePointID string `json:"service_point_id"`
		SearchContent string `json:"search_content"`
		PayStatus     int    `json:"pay_status"`
		TimeBegin     int64  `json:"time_begin"`
		TimeEnd       int64  `json:"time_end"`
		Page          int64  `json:"page" validate:"min=1"`
		Limit         int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}
	//filter["pay_status"] = bson.M{
	//	"$or": bson.A{model.PayStatusTypePaid, model.PayStatusTypePaidButRefund},
	//}

	//pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//filter["service_point_id"] = pointID

	if req.PayStatus != 0 {
		filter["pay_status"] = req.PayStatus
	}

	if req.SearchContent != "" {
		filter["$or"] = bson.A{
			bson.M{
				"address.contact.name": bson.M{
					"$regex": req.SearchContent,
				},
			},
			bson.M{
				"address.contact.mobile": bson.M{
					"$regex": req.SearchContent,
				},
			},
			bson.M{
				"buyer_name": bson.M{
					"$regex": req.SearchContent,
				},
			},
		}
	}

	orders, count, err := orderDebtService.NewOrderDebtService().ListByPage(ctx, filter, req.Page, req.Limit)

	//var pAmount int
	//var loadAmount int
	//for _, order := range orders {
	//	pAmount += order.TotalProductAmount
	//	loadAmount += order.TotalWarehouseLoadFee
	//}
	//
	//zap.S().Info(pAmount)
	//zap.S().Info(loadAmount)

	xhttp.RespSuccessList(ctx, orders, count)
}

func ListByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		QueryType string `json:"query_type"`
		//PayStatus model.PayStatusType `json:"pay_status"`
		Page  int64 `json:"page" validate:"min=1"`
		Limit int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}
	//filter["pay_status"] = bson.M{
	//	"$or": bson.A{model.PayStatusTypePaid, model.PayStatusTypePaidButRefund},
	//}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["buyer_id"] = id

	if req.QueryType == "toPay" {
		filter["pay_status"] = bson.M{
			"$ne": model.PayStatusTypePaid,
		}
	}

	orders, count, err := orderDebtService.NewOrderDebtService().ListByPage(ctx, filter, req.Page, req.Limit)

	//var pAmount int
	//var loadAmount int
	//for _, order := range orders {
	//	pAmount += order.TotalProductAmount
	//	loadAmount += order.TotalWarehouseLoadFee
	//}
	//
	//zap.S().Info(pAmount)
	//zap.S().Info(loadAmount)

	xhttp.RespSuccessList(ctx, orders, count)
}
