package webhookService

import (
	"base/util"
	"fmt"
	"go.uber.org/zap"
)

var webhookPath = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9cdd30ac-cb44-448d-8e69-c2c7d75722b3"

type ServiceInterface interface {
	NewCreateBuyer(buyerName, contactUser, address, location, note string)
}

type webhookService struct {
}

func NewWebhookService() ServiceInterface {
	return webhookService{}
}

func (s webhookService) NewCreateBuyer(buyerName, contactUser, address, location, note string) {
	msg := fmt.Sprintf(`【采购商】-新增审核，请相关同事注意。\
	        >名称:<font color=\"comment\">%s</font>
	        >联系人:<font color=\"comment\">%s</font>
	        >地址:<font color=\"comment\">%s</font>
	        >定位:<font color=\"comment\">%s</font>
	        >备注:<font color=\"comment\">%s</font>
	`, buyerName, contactUser, address, location, note)
	s.newMarkdown(msg)
}

func (s webhookService) newMarkdown(content string) {
	body := map[string]interface{}{
		"msgtype": "markdown",
		"markdown": map[string]interface{}{
			"content": content,
		},
	}

	bytes, err := util.DoHttp(webhookPath, "POST", nil, body)
	if err != nil {
		zap.S().Error(err)
		return
	}
	zap.S().Infof("%s", string(bytes))
}
