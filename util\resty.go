package util

import "github.com/go-resty/resty/v2"

// RestyInterface http请求
type RestyInterface interface {
	Post(url string, body interface{}, result interface{}) (*resty.Response, error)
	Get(url string, params map[string]string, result interface{}) (*resty.Response, error)
}

type restyU struct {
}

func NewResty() RestyInterface {
	return restyU{}
}

func (s restyU) Post(url string, body interface{}, result interface{}) (*resty.Response, error) {
	client := resty.New()

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		SetResult(result).
		Post(url)

	return resp, err
}

func (s restyU) Get(url string, params map[string]string, result interface{}) (*resty.Response, error) {
	client := resty.New()

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(params).
		SetResult(result).
		Get(url)

	return resp, err
}
