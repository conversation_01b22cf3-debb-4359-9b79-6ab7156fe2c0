package servicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/multiUserService"
	"base/service/servicePointService"
	"base/types"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetByUser(ctx *gin.Context) {
	var req = struct {
		UserID string                  `json:"user_id"`
		Level  model.ServicePointLevel `json:"level"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	uid, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := servicePointService.NewServicePointService().GetByUser(ctx, uid, req.Level)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}

	if errors.Is(err, mongo.ErrNoDocuments) {

		ot := model.ObjectTypeServicePoint
		if req.Level == model.ServicePointLevelSecond {
			ot = model.ObjectTypeSecondPoint
		}

		byUserAndObject, err := multiUserService.NewMultiUserService().GetByUserAndObject(ctx, uid, ot)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			xhttp.RespErr(ctx, err)
			return
		}
		if byUserAndObject.ID != primitive.NilObjectID {
			//i.ID = byUserAndObject.ObjectID
			data, err = servicePointService.NewServicePointService().Get(ctx, byUserAndObject.ObjectID)
			if err != nil {
				xhttp.RespErr(ctx, err)
				return
			}
		}
	}

	if data.ID == primitive.NilObjectID {
		xhttp.RespNoExist(ctx)
		return
	}

	xhttp.RespSuccess(ctx, data)

}

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	objectID, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := servicePointService.NewServicePointService().Get(ctx, objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := types.PointRes{
		ServicePoint: data,
	}
	xhttp.RespSuccess(ctx, r)
}

func GetInfo(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	objectID, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := servicePointService.NewServicePointService().Get(ctx, objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}
