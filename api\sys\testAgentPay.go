package sys

import (
	"base/service/orderDebtService"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"log"
)

func checkAgentPay(ctx context.Context) {
	debts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
		"has_agent_pay": false,
		"pay_status":    4,
		"biz_order_no": bson.M{
			"$ne": "",
		},
	})
	if err != nil {
		log.Println(err)
		return
	}
	//for _, v := range debts {
	//	mnsSendService.NewMNSClient().SendDebtAgentPay(v.ID.Hex())
	//	time.Sleep(time.Second * 10)
	//}

	_ = debts

}

//65f46f5e8db2325cc3aefafd

func agentPayDebt(ctx context.Context) {

	//list := []string{"65f791b3af06deafe0abbd11", "65f791b3af06deafe0abbd0d", "65f68ad79f90108ebc3eec99", "65f681cf9f90108ebc3eec72"}
	//
	//for _, s := range list {
	//	time.Sleep(time.Second * 1)
	//	err := orderAgentPayService.NewOrderAgentPayService().ToAgentPayForDebt(ctx, s)
	//	if err != nil {
	//		log.Println(err)
	//		return
	//	}
	//}

}

func agentPayOrder(ctx context.Context) {
	//orders, err := orderService.NewOrderService().List(ctx, bson.M{
	//	"order_status":  model.OrderStatusTypeFinish,
	//	"has_agent_pay": false,
	//	"coupon_split_amount": bson.M{
	//		"$gt": 0,
	//	},
	//	"created_at": bson.M{
	//		"$lt": time.Now().AddDate(0, 0, -3).UnixMilli(),
	//	},
	//})
	//if err != nil {
	//	return
	//}
	//
	//for _, order := range orders {
	//	//id, _ := primitive.ObjectIDFromHex("6481ef7527b29d1c3e17c056")
	//	//if order.SupplierID == id {
	//	mnsSendService.NewMNSClient().SendNormalOrderDivide(ctx, order.ID, 4)
	//	zap.S().Infof("order_id:%s,优惠券金额：%d", order.ID.Hex(), order.CouponSplitAmount)
	//	time.Sleep(time.Second * 12)
	//	//}
	//}

	////err := orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(ctx, id)
	////if err != nil {
	////	zap.S().Errorf("err:%s", err.Error())
	////	return
	////}
	//

}
