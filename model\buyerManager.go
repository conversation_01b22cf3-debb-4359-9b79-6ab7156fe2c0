package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BuyerManager 客户经理
type BuyerManager struct {
	ID        primitive.ObjectID `bson:"_id" json:"id"`
	UserID    primitive.ObjectID `bson:"user_id" json:"user_id"`
	BuyerID   primitive.ObjectID `bson:"buyer_id" json:"buyer_id"`
	UserName  string             `json:"user_name" bson:"user_name"`
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
}
