package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func SearchForSupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID   string `json:"supplier_id" validate:"-"`
		CategoryID   string `json:"category_id" validate:"-"`
		ProductTitle string `json:"product_title" validate:"-"`
		SaleType     int    `json:"sale_type" validate:"-"` // 1 true 2 false
		AuditStatus  int    `json:"audit_status"`           //
		Page         int64  `json:"page" validate:"min=1"`
		Limit        int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
	}

	if req.AuditStatus == 1 {
		filter["audit_status"] = model.AuditStatusTypeDoing
	} else {
		if req.SaleType != 0 {
			filter["sale"] = false
			if req.SaleType == 1 {
				filter["sale"] = true
			}
		}
	}

	if len(req.ProductTitle) != 0 {
		filter["title"] = bson.M{
			"$regex": req.ProductTitle,
		}
	}

	if len(req.SupplierID) == 24 {
		id, err := util.ConvertToObject(req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = id
	}

	if len(req.CategoryID) == 24 {
		cid, err := util.ConvertToObject(req.CategoryID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["category_ids.1"] = cid
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range list {
			list[j].Price = 0
			list[j].OriginPrice = 0
		}
	}

	xhttp.RespSuccessList(ctx, list, count)

}

// Search 搜索-主程序
func Search(ctx *gin.Context) {
	var req = struct {
		SupplierID   string `json:"supplier_id" validate:"-"`
		ProductTitle string `json:"product_title" validate:"-"`
		Page         int64  `json:"page" validate:"min=1"`
		Limit        int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"sale":       true,
		"deleted_at": 0,
	}

	if len(req.ProductTitle) != 0 {
		//filter["title"] = bson.M{
		//	"$regex": req.ProductTitle,
		//}
		//filter["search_tag_list"] = bson.M{
		//	"$elemMatch": bson.M{
		//		"$eq": req.ProductTitle,
		//	},
		//}
		filter["$or"] = []bson.M{
			bson.M{
				"title": bson.M{
					"$regex": req.ProductTitle,
				},
			},
		}
	}

	if len(req.SupplierID) == 24 {
		id, err := util.ConvertToObject(req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = id
	}

	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter["service_point_id"] = pointID

	expressID, _ := primitive.ObjectIDFromHex("66727385d948593db3eee799")
	yhtID, _ := primitive.ObjectIDFromHex("66792215e7ea14140bec6cba")
	stationID, _ := primitive.ObjectIDFromHex("66ea8e010bf5034b411ea72f")

	excludeIDList := []primitive.ObjectID{expressID, yhtID, stationID}

	filter["category_ids.1"] = bson.M{
		"$nin": excludeIDList,
	}

	err = productService.NewProductService().RecordSearchHistory(ctx, req.ProductTitle)
	if err != nil {
		zap.S().Errorf("RecordSearchHistory err: %v", err.Error())
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range list {
			list[j].Price = 0
			list[j].OriginPrice = 0
			list[j].StartPrice = 0
			for k, _ := range list[j].Product.SkuList {
				list[j].Product.SkuList[k].Price = 0
			}
		}
	}

	xhttp.RespSuccessList(ctx, list, count)

}
