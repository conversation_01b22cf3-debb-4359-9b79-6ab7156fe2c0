package user

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/messageService"
	"base/service/userService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// BindMobile 绑定手机号
func BindMobile(ctx *gin.Context) {
	var req = struct {
		UserID  string `json:"user_id"`
		Mobile  string `json:"mobile"`
		Captcha string `json:"captcha"` // 验证码
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	userID, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	byMobile, err := userService.NewUserService().GetByMobile(ctx, req.Mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		err = nil
	}

	if byMobile.ID != primitive.NilObjectID && byMobile.ID != userID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该手机号已被使用"))
		return
	}
	if byMobile.ID != primitive.NilObjectID && byMobile.Mobile == req.Mobile {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "与当前登录手机号相同"))
		return
	}

	// 校验验证码
	err = messageService.NewMessageService().Check(req.Mobile, req.Captcha)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//return
	err = userService.NewUserService().UpdateMobile(ctx, userID, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
