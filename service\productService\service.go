package productService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/productDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/categoryService"
	"base/service/messageService"
	"base/service/productAuditService"
	"base/service/productCommissionService"
	"base/service/supplierService"
	"base/service/userService"
	"base/service/warehouseService"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Create(ctx context.Context, req types.ProductCreateReq, supplier model.Supplier, unit model.ProductUnit) error
	Duplicate(ctx context.Context, data model.Product) error
	Update(ctx context.Context, req types.ProductUpdateReq, unit model.ProductUnit) error
	Audit(ctx context.Context, auditID primitive.ObjectID, status model.AuditStatusType, failReason string, skuPriceList []types.SkuPrice) error
	UpdateExistApply(ctx context.Context, id primitive.ObjectID, exist bool) error
	DeleteCategory(ctx context.Context, id primitive.ObjectID) ([]model.Product, error)
	AddSoldCount(ctx context.Context, m map[primitive.ObjectID]int) error

	//RefreshCategory(ctx context.Context) error

	Count(ctx context.Context, filter bson.M) (int64, error)
	StatsNumAllSupplier(ctx context.Context, filter bson.M) ([]model.SupplierStats, error)
	StatsNumBySupplier(ctx context.Context, id primitive.ObjectID) (model.SupplierStats, error)
	CategoryUsedBySupplier(ctx context.Context, saleStatus int, id primitive.ObjectID) ([]model.CategoryOnly, error)
	List(ctx context.Context, filter bson.M) ([]model.Product, error)
	ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, page, limit int64) ([]model.Product, int64, error)
	ListByCus(ctx context.Context, filter bson.M, page, limit int64) ([]model.Product, int64, error)
	RecordSearchHistory(ctx context.Context, content string) error
	ListSearchHistory(ctx context.Context) ([]redis.Z, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Product, error)
	UpdateStock(ctx context.Context, productID primitive.ObjectID, stock int) error
	UpdateCustomTag(ctx context.Context, productID primitive.ObjectID, list []model.CustomTag) error
	UpdateBuyLimit(ctx context.Context, productId primitive.ObjectID, min, max int) error
	UpdatePrice(ctx context.Context, productId primitive.ObjectID, priceList []model.PerPrice) error
	UpdatePriceSingle(ctx context.Context, productID primitive.ObjectID, price, marketWholesalePrice, estimatePurchasePrice int) error
	UpdateUserType(ctx context.Context, productID primitive.ObjectID, t model.UserType) error
	CreateLink(ctx context.Context, productID primitive.ObjectID, priceChange int, supplier model.Supplier) error
	ListLink(ctx context.Context, supplierID primitive.ObjectID) ([]primitive.ObjectID, error)
	ListLinkByProduct(ctx context.Context, productID primitive.ObjectID) ([]primitive.ObjectID, error)
	CheckLink(ctx context.Context, productID, supplierID primitive.ObjectID) (bool, error)
	GetLinkInfo(ctx context.Context, productID, supplierID primitive.ObjectID) (model.Product, error)
	RemoveLink(ctx context.Context, productID primitive.ObjectID) error
	UpdateLinkPriceChange(ctx context.Context, productID primitive.ObjectID, change int) error
	UpdateLinkedSaleStatus(ctx context.Context, productID primitive.ObjectID, sale bool) error
	UpdateRecommend(ctx context.Context, id primitive.ObjectID, ids []primitive.ObjectID) error
	//UpdateDiscountPrice(ctx context.Context, productID primitive.ObjectID, list []model.DiscountPrice) error
	//UpdateCommission(ctx context.Context, productId primitive.ObjectID, commission int) error
	//UpdateCommissionBatch(ctx context.Context, productIDList []primitive.ObjectID, commission int) error
	UpdateExternalSale(ctx context.Context, productIDList []primitive.ObjectID, isExternalSale bool) error
	UpdatePricePart(ctx context.Context, productId primitive.ObjectID, price, originPrice, costPrice int) error
	UpdateDesc(ctx context.Context, productId primitive.ObjectID, desc string) error
	UpdateTitle(ctx context.Context, productId primitive.ObjectID, title string) error
	UpdatePurchaseNote(ctx context.Context, productId primitive.ObjectID, note string) error
	UpdateAttr(ctx context.Context, productId primitive.ObjectID, attr []model.FieldInfo) error
	UpdateSale(ctx context.Context, productId primitive.ObjectID, sale bool) error
	UpdateCus(ctx context.Context, id primitive.ObjectID, update bson.M) error
	Del(ctx context.Context, id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Product, error)
	GetUpdate(ctx context.Context, id primitive.ObjectID) (model.Product, error)

	//BackPriceByNum(ctx context.Context, list []model.PerPrice, num int) (int, error)

	BackValidDiscountPrice(ctx context.Context, num int, list []model.DiscountPrice) (int, error)

	// MinusStock 减库存
	MinusStock(ctx context.Context, m map[primitive.ObjectID]int) error
	// RecoverStock 恢复库存
	RecoverStock(ctx context.Context, m map[primitive.ObjectID]int) error

	//	UpdateFruitClass 水果等级
	CheckFruitClass(ctx context.Context, id primitive.ObjectID) error
	UpdateFruitClass(ctx context.Context, id primitive.ObjectID, name string) error

	//	UpdateProductTag 商品标签
	BindProductTag(ctx context.Context, tag model.ProductTag, productIDs []primitive.ObjectID, updateType string) error
	BindCoverProductTag(ctx context.Context, tag model.ProductTag, productIDs []primitive.ObjectID, updateType string) error
	UpdateTag(ctx context.Context, id primitive.ObjectID, title, color string, img model.FileInfo) error
	UpdateCoverTag(ctx context.Context, id primitive.ObjectID, title, color string, img model.FileInfo) error
	CheckProductTag(ctx context.Context, id primitive.ObjectID) error
	CheckProductCoverTag(ctx context.Context, id primitive.ObjectID) error

	//	单位
	UpdateUnit(ctx context.Context, id primitive.ObjectID, name string) error

	//	下架审核
	ListOffLineAudit(ctx context.Context) ([]types.ProductAudit, error)
	CreateOffLineAudit(ctx context.Context, productID primitive.ObjectID, reason string) error
	OffLineAudit(ctx context.Context, productID primitive.ObjectID, auditStatus model.AuditStatusType) error
	CountOffLineAudit(ctx context.Context) (int, error)
}

type productService struct {
	mdb                *mongo.Database
	rdb                *redis.Client
	categoryS          categoryService.ServiceInterface
	productDb          productDao.DaoInt
	supplierS          supplierService.ServiceInterface
	warehouseS         warehouseService.ServiceInterface
	msg                messageService.ServiceInterface
	productCommissionS productCommissionService.ServiceInterface
	userS              userService.ServiceInterface
	productAuditS      productAuditService.ServiceInterface
}

// NewProductService 商品服务
func NewProductService() ServiceInterface {
	return productService{
		mdb:                global.MDB,
		rdb:                global.RDBDefault,
		categoryS:          categoryService.NewCategoryService(),
		productDb:          dao.ProductDao,
		supplierS:          supplierService.NewSupplierService(),
		warehouseS:         warehouseService.NewWarehouseServiceService(),
		msg:                messageService.NewMessageService(),
		productCommissionS: productCommissionService.NewProductCommissionService(),
		userS:              userService.NewUserService(),
		productAuditS:      productAuditService.NewProductAuditService(),
	}
}

// MinusStock 减库存
func (s productService) MinusStock(ctx context.Context, m map[primitive.ObjectID]int) error {
	for id, num := range m {
		err := s.productDb.Update(ctx, bson.M{"_id": id}, bson.M{"$inc": bson.M{
			"stock":      -num,
			"sold_count": num,
		}})
		if err != nil {
			return err
		}
		del(s.rdb, id)
	}
	return nil
}

func (s productService) AddSoldCount(ctx context.Context, m map[primitive.ObjectID]int) error {
	for id, num := range m {
		err := s.productDb.Update(ctx, bson.M{"_id": id}, bson.M{"$inc": bson.M{
			"sold_count": num,
		}})
		if err != nil {
			return err
		}
		del(s.rdb, id)
	}
	return nil
}

func (s productService) Create(ctx context.Context, req types.ProductCreateReq, supplier model.Supplier, unit model.ProductUnit) error {
	err := checkCreate(req)
	if err != nil {
		return err
	}

	categoryIDs, err := s.verifyCategory(ctx, req.CategoryIDs)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	// standard := model.StandardAttr{
	// 	IncludedNum: req.StandardAttr.IncludedNum,
	// 	UnitName:    req.StandardAttr.UnitName,
	// }

	// nonstandard := model.NonStandardAttr{
	// 	FruitClassID:   fruitClass.ID,
	// 	FruitClassName: fruitClass.Name,
	// 	Width:          req.NonStandardAttr.Width,
	// 	Size:           req.NonStandardAttr.Size,
	// }

	// if !req.HasParam {
	// 	standard = model.StandardAttr{}
	// 	nonstandard = model.NonStandardAttr{}
	// }

	// if req.HasParam && req.ProductParamType == model.ProductParamTypeFruit {
	// 	standard = model.StandardAttr{}
	// }

	// if req.HasParam && req.ProductParamType == model.ProductParamTypeOther {
	// 	nonstandard = model.NonStandardAttr{}
	// }

	startPrice, startWeightUnitPrice, skuList, err := dealSkuList(req.SkuList, req.IsCheckWeight)
	if err != nil {
		return err
	}

	// 封面取轮播图第一张
	coverImg := req.DisplayFile[0]

	product := model.Product{
		ID:                  primitive.NewObjectID(),
		SupplierID:          supplier.ID,
		SupplierSimpleName:  supplier.ShopSimpleName,
		ServicePointID:      supplier.ServicePointID,
		ProductParamType:    req.ProductParamType,
		ProductUnitID:       unit.ID,
		ProductUnitTypeName: unit.Name,
		HasParam:            req.HasParam,
		IsCheckWeight:       req.IsCheckWeight,
		CategoryIDs:         categoryIDs,
		Title:               util.DealWrap(req.Title),
		Desc:                util.DealWrap(req.Desc),
		PurchaseNote:        util.DealWrap(req.PurchaseNote),
		VideoFile:           req.VideoFile,
		CoverImg:            coverImg,
		DisplayFile:         req.DisplayFile,
		DescImg:             req.DescImg,
		Sale:                false,
		Stock:               100,
		BuyMinLimit:         0,
		BuyMaxLimit:         req.BuyMaxLimit,
		// Price:                 req.Price,
		// MarketWholesalePrice:  req.MarketWholesalePrice,
		// EstimatePurchasePrice: req.EstimatePurchasePrice,
		//SupplyPrice:         req.SupplyPrice,
		SoldCount: req.SoldCount,
		// Weight:    req.Weight,
		AttrInfo: req.AttrInfo,
		// StandardAttr:      standard,
		// NonStandardAttr:   nonstandard,
		Version:              now,
		CommissionPercent:    0, // 默认5
		TagList:              []model.ProductTag{},
		CreatedAt:            now,
		UpdatedAt:            now,
		AuditStatus:          model.AuditStatusTypeDoing,
		FailReason:           "",
		UserType:             model.UserTypeNormal,
		ProductOriginType:    req.ProductOriginType,
		StartPrice:           startPrice,
		StartWeightUnitPrice: startWeightUnitPrice,
		SkuList:              skuList,
	}

	// err = s.productDb.Create(ctx, product)
	// if err != nil {
	// 	return err
	// }
	// del(s.rdb, product.ID)

	err = s.productAuditS.Create(ctx, product, true)
	if err != nil {
		return err
	}

	return nil
}

func dealSkuList(skuList []model.Sku, isCheckWeight bool) (int, int, []model.Sku, error) {
	if len(skuList) == 0 {
		return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku列表不能为空")
	}

	// 起售价为sku列表中价格最低的
	minPrice := 0
	minRoughWeight := 0

	for i, sku := range skuList {
		if minPrice == 0 || sku.Price < minPrice {
			minPrice = sku.Price
			minRoughWeight = sku.RoughWeight
		}

		if sku.IDCode == "" {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku编号不能为空")
		}

		if sku.Cover == "" {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku封面不能为空")
		}

		if sku.Price == 0 {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku价格不能为0")
		}

		if sku.MarketWholesalePrice == 0 {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku市场批发价不能为0")
		}

		if sku.Name == "" {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku名称不能为空")
		}

		if sku.EstimatePurchasePrice == 0 {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku预估采购价不能为0")
		}

		if sku.RoughWeight == 0 {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku毛重不能为0")
		}

		if sku.OutWeight == 0 {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku皮重不能为0")
		}

		if sku.NetWeight == 0 {
			return 0, 0, nil, xerr.NewErr(xerr.ErrParamError, nil, "sku净重不能为0")
		}

		skuList[i].Name = util.DealWrap(sku.Name)
		skuList[i].Description = util.DealWrap(sku.Description)
	}

	startWeightUnitPrice := 0

	if isCheckWeight {
		// 使用decimal计算
		minRoughWeightDecimal := decimal.NewFromInt(int64(minRoughWeight)).Div(decimal.NewFromInt(1000)) // kg
		minPriceDecimal := decimal.NewFromInt(int64(minPrice))

		startWeightUnitPriceDecimal := minPriceDecimal.Div(minRoughWeightDecimal)
		startWeightUnitPrice = int(startWeightUnitPriceDecimal.Div(decimal.NewFromInt(10)).Round(0).IntPart() * 10)
	}

	return minPrice, startWeightUnitPrice, skuList, nil
}

func (s productService) Duplicate(ctx context.Context, data model.Product) error {
	err := s.productDb.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s productService) Update(ctx context.Context, req types.ProductUpdateReq, unit model.ProductUnit) error {
	err := checkUpdate(req)
	if err != nil {
		return err
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		return err
	}

	var data model.Product
	if req.From == "list" {

		data, err = s.Get(ctx, id)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return xerr.NewErr(xerr.ErrNoDocument, err, "商品不存在")
			}
			return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
		}
	}

	if req.From == "audit" {
		// 审核记录
		auditInfo, err := s.productAuditS.Get(ctx, id)
		if err != nil {
			return err
		}
		data = auditInfo.Product
	}

	categoryIDs, err := s.verifyCategory(ctx, req.CategoryIDs)
	if err != nil {
		return err
	}

	// 校验商品信息

	startPrice, startWeightUnitPrice, skuList, err := dealSkuList(req.SkuList, req.IsCheckWeight)
	if err != nil {
		return err
	}

	data.Title = util.DealWrap(req.Title)
	data.CategoryIDs = categoryIDs
	data.ProductParamType = req.ProductParamType
	data.ProductUnitID = unit.ID
	data.ProductUnitTypeName = unit.Name
	data.HasParam = req.HasParam
	data.PurchaseNote = util.DealWrap(req.PurchaseNote)
	data.IsCheckWeight = req.IsCheckWeight
	data.Desc = util.DealWrap(req.Desc)
	data.VideoFile = req.VideoFile

	coverImg := req.DisplayFile[0]
	data.CoverImg = coverImg
	data.DisplayFile = req.DisplayFile
	data.DescImg = req.DescImg
	data.Stock = 100
	data.Price = req.Price
	data.MarketWholesalePrice = req.MarketWholesalePrice
	data.EstimatePurchasePrice = req.EstimatePurchasePrice
	data.AttrInfo = req.AttrInfo
	data.Weight = req.Weight
	data.SkuList = skuList
	data.StartPrice = startPrice
	data.ProductOriginType = req.ProductOriginType
	data.StartWeightUnitPrice = startWeightUnitPrice

	if req.From == "list" {
		err = s.productAuditS.Create(ctx, data, false)
		if err != nil {
			return err
		}
	}

	if req.From == "audit" {
		err = s.productAuditS.UpdateFromAudit(ctx, id, data)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s productService) GetUpdate(ctx context.Context, id primitive.ObjectID) (model.Product, error) {
	m := getUpdate(s.rdb, id)
	return m, nil
}

func dealSkuPriceList(skuList []model.Sku, skuPriceList []types.SkuPrice) []model.Sku {
	for i, v := range skuList {
		for _, v2 := range skuPriceList {
			if v.IDCode == v2.SkuIDCode {
				skuList[i].Price = v2.Price
				break
			}
		}
	}
	return skuList
}

func (s productService) Audit(ctx context.Context, auditID primitive.ObjectID, status model.AuditStatusType, failReason string, skuPriceList []types.SkuPrice) error {
	productAudit, err := s.productAuditS.Get(ctx, auditID)
	if err != nil {
		return err
	}

	if productAudit.EditAuditStatus != model.AuditStatusTypeDoing {
		return xerr.NewErr(xerr.ErrParamError, nil, "已审核，请勿重复审核")
	}

	if status == model.AuditStatusTypeDoing {
		return xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误")
	}

	if status == model.AuditStatusTypePass {
		failReason = ""
	}

	if status == model.AuditStatusTypePass {
		skuList := dealSkuPriceList(productAudit.Product.SkuList, skuPriceList)
		productAudit.Product.SkuList = skuList
	}

	// RangeRandom
	if productAudit.Product.SoldCount < 50 {
		num := util.RangeRandom(100, 200)
		productAudit.Product.SoldCount = num
	}

	// 先更新商品审核信息
	err = s.productAuditS.Update(ctx, productAudit, status, failReason)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	p := productAudit.Product

	startPrice, startWeightUnitPrice, skuList, err := dealSkuList(p.SkuList, p.IsCheckWeight)
	if err != nil {
		return err
	}

	p.SkuList = skuList
	p.StartPrice = startPrice
	p.StartWeightUnitPrice = startWeightUnitPrice

	if status == model.AuditStatusTypePass {
		if productAudit.IsNew {
			//	 新建并上架商品
			data := p

			data.CreatedAt = now
			data.UpdatedAt = now
			data.Version = now
			data.Sale = true

			err = s.productDb.Create(ctx, data)
			if err != nil {
				return err
			}
			return nil
		} else {
			// 更新

			update := bson.M{
				"title": p.Title,
				//"category_ids":            categoryIDs,
				//"product_param_type":      req.ProductParamType,
				//"product_unit_id":         unit.ID,
				//"product_unit_type_name":  unit.Name,
				//"has_param":               req.HasParam,
				"is_check_weight":         p.IsCheckWeight,
				"desc":                    p.Desc,
				"video_file":              p.VideoFile,
				"cover_img":               p.CoverImg,
				"display_file":            p.DisplayFile,
				"desc_img":                p.DescImg,
				"stock":                   100,
				"price":                   p.Price,
				"market_wholesale_price":  p.MarketWholesalePrice,  // 市场批发价
				"estimate_purchase_price": p.EstimatePurchasePrice, // 预估采购价
				"attr_info":               p.AttrInfo,
				"weight":                  p.Weight,
				"standard_attr":           p.StandardAttr,
				"non_standard_attr":       p.NonStandardAttr,
				"product_origin_type":     p.ProductOriginType,
				"audit_status":            model.AuditStatusTypePass,
				"sale":                    true,
				"purchase_note":           p.PurchaseNote,
				"sku_list":                skuList,
				"sold_count":              p.SoldCount,
				"start_price":             startPrice,
				"start_weight_unit_price": startWeightUnitPrice,
				"version":                 now,
				"updated_at":              now,
			}

			err = s.productDb.Update(ctx, bson.M{"_id": p.ID}, bson.M{"$set": update})
			if err != nil {
				return err
			}

			del(s.rdb, p.ID)

			err = s.productAuditS.CheckHistoryRecord(ctx, auditID)
			if err != nil {
				return err
			}

			mnsSendService.NewMNSClient().SendProductUp(ctx, p.ID)
		}
	}

	supplier, err := s.supplierS.Get(ctx, productAudit.Product.SupplierID)
	if err != nil {
		return err
	}

	user, err := s.userS.Get(ctx, supplier.UserID)
	if err != nil {
		return err
	}

	statusStr := "已通过"
	if status == model.AuditStatusTypeNotPass {
		statusStr = "未通过"
	}

	//  title 截取前15个字符
	title := productAudit.Product.Title
	if len(title) > 15 {
		title = title[:15] + "..."
	}
	messageService.NewMessageService().SendNotifyAuditProductEdit(user.Mobile, title, statusStr)

	return nil
}

// RecoverStock 恢复库存
func (s productService) RecoverStock(ctx context.Context, m map[primitive.ObjectID]int) error {
	for id, num := range m {
		product, err := s.Get(ctx, id)
		if err != nil {
			return err
		}
		err = s.productDb.Update(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{"stock": product.Stock + num}})
		if err != nil {
			return err
		}
		del(s.rdb, id)
	}
	return nil
}

func (s productService) ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, page, limit int64) ([]model.Product, int64, error) {
	filter := bson.M{
		"warehouse_id": warehouseID,
		"sale":         true,
		"deleted_at":   0,
	}
	products, i, err := s.productDb.ListByPage(ctx, filter, page, limit)
	return products, i, err
}
func (s productService) ListByCus(ctx context.Context, filter bson.M, page, limit int64) ([]model.Product, int64, error) {
	products, i, err := s.productDb.ListByPage(ctx, filter, page, limit)
	return products, i, err
}

func (s productService) RecordSearchHistory(ctx context.Context, content string) error {
	content = strings.TrimSpace(content)

	if len(content) < 1 {
		return nil
	}
	s.rdb.ZIncrBy(ctx, "search_history", 1, content)
	// 设置过期时间
	s.rdb.Expire(ctx, "search_history", time.Hour*24*30)
	return nil
}

func (s productService) ListSearchHistory(ctx context.Context) ([]redis.Z, error) {
	val := s.rdb.ZRevRangeWithScores(ctx, "search_history", 0, 100).Val()

	return val, nil
}

func (s productService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Product, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	products, err := s.productDb.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	return products, nil
}

func (s productService) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.productDb.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return i, nil
}

func (s productService) StatsNumAllSupplier(ctx context.Context, filter bson.M) ([]model.SupplierStats, error) {
	stats, err := s.productDb.NumAllSupplier(ctx)
	if err != nil {
		return nil, err
	}
	return stats, nil
}
func (s productService) StatsNumBySupplier(ctx context.Context, id primitive.ObjectID) (model.SupplierStats, error) {
	stats, err := s.productDb.NumBySupplier(ctx, id)
	if err != nil {
		return model.SupplierStats{}, err
	}

	t := time.Now().Add(time.Hour * time.Duration(int64(-24*3)))

	filter := bson.M{
		"version": bson.M{
			"$lte": t.UnixMilli(),
		},
		"sale":        true,
		"supplier_id": id,
		"deleted_at":  0,
	}
	count, err := s.productDb.Count(ctx, filter)
	if err != nil {
		return model.SupplierStats{}, err
	}
	stats.TotalToUpdateCount = int(count)

	return stats, nil
}
func (s productService) CategoryUsedBySupplier(ctx context.Context, saleStatus int, id primitive.ObjectID) ([]model.CategoryOnly, error) {
	list, err := s.productDb.FilterCategoryBySupplier(ctx, saleStatus, id)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s productService) Get(ctx context.Context, id primitive.ObjectID) (model.Product, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		i, err := s.productDb.Get(ctx, bson.M{"_id": id})
		if err != nil {
			return model.Product{}, err
		}
		if i.DeletedAt == 0 {
			set(s.rdb, i)
		}
		return i, nil
	}
	return m, nil
}

// UpdateStock 修改库存
func (s productService) UpdateStock(ctx context.Context, productId primitive.ObjectID, stock int) error {
	// 校验商品信息
	product, err := s.Get(ctx, productId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": bson.M{
			"stock": stock,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, product.ID)

	return nil
}

func (s productService) CreateLink(ctx context.Context, productID primitive.ObjectID, priceChange int, supplier model.Supplier) error {
	// 校验商品信息
	product, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	f, err := s.CheckLink(ctx, product.ID, supplier.ID)
	if err != nil {
		return err
	}
	if f {
		return xerr.NewErr(xerr.ErrSysBusy, err, "已关联商品，请下拉刷新或返回")
	}

	data := product

	now := time.Now().UnixMilli()
	data.ID = primitive.NewObjectID()

	data.SupplierID = supplier.ID
	data.SupplierSimpleName = supplier.ShopSimpleName
	data.ServicePointID = supplier.ServicePointID

	data.LinkProductID = product.ID
	data.LinkProductSupplierID = product.SupplierID
	data.LinkProductStatus = 2
	data.LinkProductPriceChange = priceChange

	data.Title = product.Title
	data.Price = dealPriceChange(data.Price, priceChange)
	data.OriginPrice = dealPriceChange(data.OriginPrice, priceChange)

	data.LinkProductPrice = product.Price
	data.LinkProductOriginPrice = product.OriginPrice

	data.Sale = true
	data.BuyMinLimit = 0
	data.BuyMaxLimit = 0
	data.OriginID = ""

	data.LinkBrandID = primitive.NilObjectID
	data.LinkBrandStatus = 1
	data.LinkBrandName = ""
	data.PurchaseNote = ""

	data.AuditStatus = model.AuditStatusTypePass
	data.OriginID = ""
	data.CreatedAt = now
	data.UpdatedAt = now
	data.Version = now

	err = s.productDb.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s productService) RemoveLink(ctx context.Context, productID primitive.ObjectID) error {
	// 校验商品信息
	data, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	now := time.Now().UnixMilli()

	// 更新
	err = s.productDb.Update(ctx, bson.M{"_id": data.ID}, bson.M{
		"$set": bson.M{
			"sale":    false,
			"version": now,

			"link_product_id":           primitive.NilObjectID,
			"link_product_supplier_id":  primitive.NilObjectID,
			"link_product_status":       1,
			"link_product_price_change": 0,
			"link_product_price":        0,
			"link_product_origin_price": 0,

			"link_brand_status": 1,
			"link_brand_id":     primitive.NilObjectID,
			"link_brand_name":   "",
			"purchase_note":     "",
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, data.ID)

	return nil
}

// CheckAndRemoveLink 移除商品库检查关联
func (s productService) CheckAndRemoveLink(ctx context.Context, ids []primitive.ObjectID) error {
	products, err := s.List(ctx, bson.M{
		"link_product_id": bson.M{
			"$in": ids,
		},
		"link_product_status": 2,
	})
	if err != nil {
		return err
	}
	if len(products) < 1 {
		return nil
	}

	err = s.productDb.UpdateMany(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}, bson.M{
		"$set": bson.M{
			"link_product_id":           primitive.NilObjectID,
			"link_product_supplier_id":  primitive.NilObjectID,
			"link_product_price_change": 0,
			"link_product_price":        0,
			"link_product_origin_price": 0,
		},
	})
	if err != nil {
		return err
	}

	for _, product := range products {
		del(s.rdb, product.ID)
	}

	return nil
}

// 处理价格调整
func dealPriceChange(price, priceChange int) int {
	priceDe := decimal.NewFromInt(int64(price))
	priceChangeDe := decimal.NewFromInt(int64(100 + priceChange)).Div(decimal.NewFromInt(100))

	// 四舍五入至元
	part := priceDe.Mul(priceChangeDe).Div(decimal.NewFromInt(100)).Round(0).Mul(decimal.NewFromInt(100)).IntPart()

	return int(part)
}

func (s productService) ListLink(ctx context.Context, supplierID primitive.ObjectID) ([]primitive.ObjectID, error) {
	// 校验商品信息
	filter := bson.M{
		"supplier_id":         supplierID,
		"link_product_status": 2,
	}
	products, err := s.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	ids := make([]primitive.ObjectID, 0, len(products))
	for _, p := range products {
		ids = append(ids, p.LinkProductID)
	}

	return ids, nil
}

func (s productService) ListLinkByProduct(ctx context.Context, productID primitive.ObjectID) ([]primitive.ObjectID, error) {
	// 校验商品信息
	filter := bson.M{
		"link_product_id":     productID,
		"link_product_status": 2,
	}
	products, err := s.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	ids := make([]primitive.ObjectID, 0, len(products))
	for _, p := range products {
		ids = append(ids, p.LinkProductID)
	}

	return ids, nil
}

func (s productService) CheckLink(ctx context.Context, productID, supplierID primitive.ObjectID) (bool, error) {
	// 校验商品信息
	filter := bson.M{
		"supplier_id":         supplierID,
		"link_product_id":     productID,
		"link_product_status": 2,
		"deleted_at":          0,
	}
	i, err := s.Count(ctx, filter)
	if err != nil {
		return false, err
	}

	return i > 0, nil
}

func (s productService) GetLinkInfo(ctx context.Context, productID, supplierID primitive.ObjectID) (model.Product, error) {
	// 校验商品信息
	filter := bson.M{
		"supplier_id":         supplierID,
		"link_product_id":     productID,
		"link_product_status": 2,
		"deleted_at":          0,
	}
	i, err := s.productDb.Get(ctx, filter)
	if err != nil {
		return model.Product{}, err
	}

	return i, nil
}

func (s productService) UpdateLinkPriceChange(ctx context.Context, productID primitive.ObjectID, change int) error {
	// 校验商品信息
	product, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	if product.LinkProductStatus != 2 {
		return xerr.NewErr(xerr.ErrSysBusy, err, "未关联商品，请下拉刷新或返回")
	}

	pPrice := dealPriceChange(product.LinkProductPrice, change)
	pOriginPrice := dealPriceChange(product.LinkProductOriginPrice, change)

	update := bson.M{
		"price":                     pPrice,
		"origin_price":              pOriginPrice,
		"link_product_price_change": change,
	}
	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, product.ID)

	return nil
}

// UpdateCustomTag 更新
func (s productService) UpdateCustomTag(ctx context.Context, productID primitive.ObjectID, list []model.CustomTag) error {
	// 校验商品信息
	product, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": bson.M{
			"custom_tag_list": list,
		},
	})
	if err != nil {
		return err
	}
	del(s.rdb, product.ID)

	return nil

}

// UpdateBuyLimit 修改限购
func (s productService) UpdateBuyLimit(ctx context.Context, productId primitive.ObjectID, min, max int) error {
	// 校验商品信息
	product, err := s.Get(ctx, productId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": bson.M{
			"buy_min_limit": min,
			"buy_max_limit": max,
			//"updated_at": time.Now().UnixMilli(),
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, product.ID)

	return nil
}

// DeleteCategory 删除分类
func (s productService) DeleteCategory(ctx context.Context, id primitive.ObjectID) ([]model.Product, error) {
	filter := bson.M{
		"category_ids": bson.M{
			"$in": bson.A{id},
		},
	}
	products, err := s.productDb.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(products) > 0 {
		return products, xerr.XerrCategoryExistProduct
	}
	err = s.categoryS.Del(ctx, id)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

//
//func (s productService) RefreshCategory(ctx context.Context) error {
//	list, err := s.productDb.FilterCategory(ctx, 1)
//	if err != nil {
//		return err
//	}
//
//	var ids []primitive.ObjectID
//	for _, only := range list {
//		for _, id := range only.CategoryIds {
//			var f bool
//			for _, perID := range ids {
//				if id == perID {
//					f = true
//				}
//			}
//			if !f {
//				ids = append(ids, id)
//			}
//		}
//	}
//
//	err = s.categoryS.RefreshVisible(ctx, ids)
//
//	return nil
//}

// UpdateSale 更新上下架
func (s productService) UpdateSale(ctx context.Context, productID primitive.ObjectID, sale bool) error {
	// 校验商品信息
	product, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}
	//if product.AuditStatus != model.AuditStatusTypePass {
	//	return xerr.NewErr(xerr.ErrSysBusy, err, "商品未审核通过，不可上下架操作")
	//}

	unixMilli := time.Now().UnixMilli()
	if sale {
		mnsSendService.NewMNSClient().SendProductUp(ctx, productID)
	} else {
		mnsSendService.NewMNSClient().SendProductDown(ctx, productID)

		mnsSendService.NewMNSClient().SendProductOffLineCheck(productID, unixMilli)
	}

	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": bson.M{
			"sale":       sale,
			"updated_at": unixMilli,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, product.ID)

	return nil
}

var productOfflineAuditKey = "productOfflineAudit:"

// CreateOffLineAudit 下架审核-新增
func (s productService) CreateOffLineAudit(ctx context.Context, productID primitive.ObjectID, reason string) error {
	now := time.Now()

	key := productOfflineAuditKey + productID.Hex()
	// 判断是否存在
	exists, err := s.rdb.Exists(ctx, key).Result()
	if err != nil {
		return err
	}
	if exists == 1 {
		return xerr.NewErr(xerr.ErrParamError, err, "商品已存在下架审核")
	}

	if len(reason) == 0 {
		return xerr.NewErr(xerr.ErrParamError, err, "请填写下架理由")
	}

	product, err := s.Get(ctx, productID)
	if err != nil {
		return err
	}

	data := types.ProductAudit{
		Product: product,
		Reason:  reason,
		ApplyAt: now.UnixMilli(),
	}

	marshal, err := json.Marshal(data)
	if err != nil {
		return err
	}

	s.rdb.Set(ctx, key, string(marshal), time.Hour*24*5)

	return nil
}

// ListOffLineAudit 下架审核-查询
func (s productService) ListOffLineAudit(ctx context.Context) ([]types.ProductAudit, error) {
	// 查询所有缓存key
	keys, err := s.rdb.Keys(ctx, productOfflineAuditKey+"*").Result()
	if err != nil {
		return nil, err
	}

	// 查询所有缓存key对应的商品
	products := make([]types.ProductAudit, 0)
	for _, key := range keys {
		var data types.ProductAudit
		bytes, err := s.rdb.Get(ctx, key).Bytes()
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(bytes, &data)
		if err != nil {
			return nil, err
		}

		products = append(products, data)
	}
	return products, nil
}

func (s productService) CountOffLineAudit(ctx context.Context) (int, error) {
	// 查询所有缓存key
	keys, err := s.rdb.Keys(ctx, productOfflineAuditKey+"*").Result()
	if err != nil {
		return 0, err
	}

	return len(keys), nil
}

// OffLineAudit 下架审核
func (s productService) OffLineAudit(ctx context.Context, productID primitive.ObjectID, auditStatus model.AuditStatusType) error {
	if auditStatus == model.AuditStatusTypePass {
		err := s.UpdateSale(ctx, productID, false)
		if err != nil {
			return err
		}
	}

	s.rdb.Del(ctx, productOfflineAuditKey+productID.Hex())

	product, err := s.Get(ctx, productID)
	if err != nil {
		return err
	}
	supplier, err := s.supplierS.Get(ctx, product.SupplierID)
	if err != nil {
		return err
	}

	user, err := s.userS.Get(ctx, supplier.UserID)
	if err != nil {
		return err
	}

	status := "已通过"
	if auditStatus == model.AuditStatusTypeNotPass {
		status = "未通过"
	}

	//  title 截取前15个字符
	title := product.Title
	if len(title) > 15 {
		title = title[:15] + "..."
	}

	messageService.NewMessageService().SendNotifyAuditProductOffline(user.Mobile, title, status)

	return nil
}

// UpdateCus 更新
func (s productService) UpdateCus(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	err := s.productDb.Update(ctx, bson.M{"_id": id}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}
	del(s.rdb, id)

	return nil
}

// UpdatePrice 修改价格
func (s productService) UpdatePrice(ctx context.Context, productId primitive.ObjectID, priceList []model.PerPrice) error {
	// 校验商品信息
	product, err := s.Get(ctx, productId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}
	now := time.Now().UnixMilli()
	del(s.rdb, product.ID)
	return s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": bson.M{
			"price_list": priceList,
			"version":    now,
			"updated_at": now,
		},
	})
}

// UpdatePriceSingle 修改价格
func (s productService) UpdatePriceSingle(ctx context.Context, productID primitive.ObjectID, price, marketWholesalePrice, estimatePurchasePrice int) error {
	// 校验商品信息
	product, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	//setUpdate(s.rdb, product)

	//if product.LinkProductStatus == 2 {
	//	return xerr.NewErr(xerr.ErrSysBusy, err, "共享商品不可修改价格")
	//}

	if price <= 0 {
		return xerr.NewErr(xerr.ErrSysBusy, err, "销售价需大于0")
	}

	//now := time.Now().UnixMilli()

	//salePrice := backSalePrice(supplyPrice)

	//update := bson.M{
	//	"price":                   price,
	//	"market_wholesale_price":  marketWholesalePrice,  // 市场批发价
	//	"estimate_purchase_price": estimatePurchasePrice, // 预估采购价
	//	"version":                 now,
	//	"updated_at":              now,
	//}
	////
	////if price > product.Price {
	////	// 新价格大于原价格
	//update["audit_status"] = model.AuditStatusTypeDoing
	//update["sale"] = false
	////}
	//
	//err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
	//	"$set": update,
	//})
	//if err != nil {
	//	return err
	//}

	//del(s.rdb, product.ID)

	// 更新关联商品价格
	//err = s.UpdateLinkedPrice(ctx, productID, price, originPrice)
	//if err != nil {
	//	zap.S().Errorf("更新商品价格，同步关联商品价格失败")
	//}

	product.Price = price
	product.MarketWholesalePrice = marketWholesalePrice
	product.EstimatePurchasePrice = estimatePurchasePrice

	err = s.productAuditS.Create(ctx, product, false)
	if err != nil {
		return err
	}

	return nil
}

func (s productService) UpdateUserType(ctx context.Context, productID primitive.ObjectID, t model.UserType) error {
	// 校验商品信息
	product, err := s.Get(ctx, productID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	setUpdate(s.rdb, product)

	now := time.Now().UnixMilli()

	update := bson.M{
		"user_type":  t,
		"version":    now,
		"updated_at": now,
	}
	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	del(s.rdb, product.ID)

	return nil
}

// UpdateLinkedPrice 更新关联的共享商品价格
func (s productService) UpdateLinkedPrice(ctx context.Context, productID primitive.ObjectID, price, originPrice int) error {
	// 校验商品信息
	filter := bson.M{
		"link_product_id":     productID,
		"link_product_status": 2,
	}
	linkedProducts, err := s.List(ctx, filter)
	if err != nil {
		return err
	}

	for _, p := range linkedProducts {
		pPrice := dealPriceChange(price, p.LinkProductPriceChange)
		pOriginPrice := dealPriceChange(originPrice, p.LinkProductPriceChange)

		update := bson.M{
			"price":                     pPrice,
			"origin_price":              pOriginPrice,
			"link_product_price":        price,
			"link_product_origin_price": originPrice,
			"sale":                      false,
			"audit_status":              model.AuditStatusTypeDoing,
		}
		err = s.productDb.Update(ctx, bson.M{"_id": p.ID}, bson.M{"$set": update})
		if err != nil {
			return err
		}
		del(s.rdb, p.ID)
	}

	return nil
}

func (s productService) UpdateLinkedSaleStatus(ctx context.Context, productID primitive.ObjectID, sale bool) error {
	// 校验商品信息
	filter := bson.M{
		"link_product_id":     productID,
		"link_product_status": 2,
	}
	linkedProducts, err := s.List(ctx, filter)
	if err != nil {
		return err
	}

	for _, p := range linkedProducts {
		update := bson.M{
			"sale": sale,
		}
		err = s.productDb.Update(ctx, bson.M{"_id": p.ID}, bson.M{"$set": update})
		if err != nil {
			return err
		}
		del(s.rdb, p.ID)
	}

	return nil
}

// UpdateDiscountPrice 修改折扣价格
//func (s productService) UpdateDiscountPrice(ctx context.Context, productID primitive.ObjectID, list []model.DiscountPrice) error {
//	// 校验商品信息
//	product, err := s.Get(ctx, productID)
//	if err != nil {
//		if errors.Is(err, mongo.ErrNoDocuments) {
//			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
//		}
//		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
//	}
//	now := time.Now().UnixMilli()
//	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
//		"$set": bson.M{
//			"discount_price_list": list,
//			"version":             now,
//			"updated_at":          now,
//		},
//	})
//	if err != nil {
//		return err
//	}
//
//	del(s.rdb, product.ID)
//
//	return nil
//}

//
//// UpdateCommission 修改服务费
//func (s productService) UpdateCommission(ctx context.Context, productId primitive.ObjectID, commission int) error {
//	// 校验商品信息
//	product, err := s.Get(ctx, productId)
//	if err != nil {
//		if errors.Is(err, mongo.ErrNoDocuments) {
//			return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
//		}
//		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
//	}
//	now := time.Now().UnixMilli()
//	err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
//		"$set": bson.M{
//			"commission_percent": commission,
//			"version":            now,
//			"updated_at":         now,
//		},
//	})
//	if err != nil {
//		return err
//	}
//
//	del(s.rdb, product.ID)
//
//	return nil
//}
//
//// UpdateCommissionBatch 修改服务费-批量
//func (s productService) UpdateCommissionBatch(ctx context.Context, productIDList []primitive.ObjectID, commission int) error {
//	// 校验商品信息
//	if len(productIDList) == 0 {
//		return nil
//	}
//
//	now := time.Now().UnixMilli()
//	filter := bson.M{
//		"_id": bson.M{
//			"$in": productIDList,
//		},
//	}
//	err := s.productDb.UpdateMany(ctx, filter, bson.M{
//		"$set": bson.M{
//			"commission_percent": commission,
//			"version":            now,
//			"updated_at":         now,
//		},
//	})
//	if err != nil {
//		return err
//	}
//
//	for _, id := range productIDList {
//		del(s.rdb, id)
//	}
//
//	return nil
//}
//

func (s productService) UpdateExternalSale(ctx context.Context, productIDList []primitive.ObjectID, isExternalSale bool) error {
	// 校验商品信息
	if len(productIDList) == 0 {
		return nil
	}

	//now := time.Now().UnixMilli()
	filter := bson.M{
		"_id": bson.M{
			"$in": productIDList,
		},
	}
	err := s.productDb.UpdateMany(ctx, filter, bson.M{
		"$set": bson.M{
			"is_external_sale": isExternalSale,
			//"version":          now,
			//"updated_at":       now,
		},
	})
	if err != nil {
		return err
	}

	var ids []primitive.ObjectID
	for _, id := range productIDList {
		ids = append(ids, id)
		del(s.rdb, id)
	}

	// 移除商品库，已经关联的商品解除关联，并下架
	err = s.CheckAndRemoveLink(ctx, ids)
	if err != nil {
		return err
	}

	return nil
}

// UpdatePricePart 修改价格
func (s productService) UpdatePricePart(ctx context.Context, productId primitive.ObjectID, price, originPrice, costPrice int) error {
	//// 校验商品信息
	//product, err := s.Get(ctx, productId)
	//if err != nil {
	//	if errors.Is(err, mongo.ErrNoDocuments) {
	//		return xerr.NewErr(xerr.ErrParamError, err, "商品不存在")
	//	}
	//	return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	//}
	//now := time.Now().UnixMilli()
	//err = s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
	//	"$set": bson.M{
	//		"price":        price,
	//		"origin_price": originPrice,
	//		"cost_price":   costPrice,
	//		"version":      now,
	//	},
	//})
	//if err != nil {
	//	return err
	//}
	//
	//del(s.rdb, product.ID)

	return nil
}

// UpdateDesc 修改描述
func (s productService) UpdateDesc(ctx context.Context, productId primitive.ObjectID, desc string) error {
	now := time.Now().UnixMilli()
	err := s.productDb.Update(ctx, bson.M{"_id": productId}, bson.M{
		"$set": bson.M{
			"desc":    desc,
			"version": now,
			//"updated_at": now,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, productId)

	return nil
}

// UpdateTitle 修改标题
func (s productService) UpdateTitle(ctx context.Context, productId primitive.ObjectID, title string) error {
	now := time.Now().UnixMilli()
	err := s.productDb.Update(ctx, bson.M{"_id": productId}, bson.M{
		"$set": bson.M{
			"title":   title,
			"version": now,
			//"updated_at": now,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, productId)

	return nil
}

func (s productService) UpdatePurchaseNote(ctx context.Context, productId primitive.ObjectID, note string) error {
	err := s.productDb.Update(ctx, bson.M{"_id": productId}, bson.M{
		"$set": bson.M{
			"purchase_note": note,
			//"updated_at": now,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, productId)

	return nil
}

func (s productService) UpdateAttr(ctx context.Context, productId primitive.ObjectID, attr []model.FieldInfo) error {
	now := time.Now().UnixMilli()
	err := s.productDb.Update(ctx, bson.M{"_id": productId}, bson.M{
		"$set": bson.M{
			"attr_info": attr,
			"version":   now,
			//"updated_at": now,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, productId)

	return nil
}

func (s productService) UpdateExistApply(ctx context.Context, id primitive.ObjectID, exist bool) error {
	update := bson.M{
		"exist_audit": exist,
	}
	err := s.productDb.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, id)
	return nil
}

// 校验分类是否存在
func (s productService) verifyCategory(ctx context.Context, ids []string) ([]primitive.ObjectID, error) {
	if len(ids) != 3 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "分类信息缺失")
	}
	id, err := util.ConvertToObject(ids[2])
	if err != nil {
		return nil, err
	}

	info, err := s.categoryS.CheckExistByThird(ctx, id)
	if err != nil {
		return nil, err
	}
	var res []primitive.ObjectID
	for _, i := range info.Path {
		res = append(res, i)
	}

	res = append(res, info.ID)
	return res, nil
}

// 校验集中仓信息
func (s productService) verifyWarehouse(ctx context.Context, id string) (primitive.ObjectID, error) {
	warehouseId, err := util.ConvertToObject(id)
	if err != nil {
		return primitive.NilObjectID, err
	}
	err = s.warehouseS.CheckOneExist(warehouseId)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return warehouseId, nil
}

// 校验标签是否存在
func (s productService) verifyTagList(ctx context.Context, tagList []string) ([]primitive.ObjectID, error) {
	list := make([]primitive.ObjectID, len(tagList))
	var err error
	for i, val := range tagList {
		if list[i], err = util.ConvertToObject(val); err != nil {
			return list, err
		}
	}

	if len(list) > 0 {

	}
	return list, err
}

func (s productService) List(ctx context.Context, filter bson.M) ([]model.Product, error) {
	products, err := s.productDb.Find(ctx, filter)
	return products, err
}

func (s productService) Del(ctx context.Context, id primitive.ObjectID) error {
	product, err := s.Get(ctx, id)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品查询错误")
	}

	now := time.Now().UnixMilli()
	del(s.rdb, product.ID)

	if product.Sale {
		return xerr.NewErr(xerr.ErrSysBusy, err, "商品上架中，不能删除")
	}

	mnsSendService.NewMNSClient().SendDeleteProduct(id.Hex())

	return s.productDb.Update(ctx, bson.M{"_id": product.ID}, bson.M{
		"$set": bson.M{
			"deleted_at": now,
			"updated_at": now,
		},
	})
}

func (s productService) CheckFruitClass(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"non_standard_attr.fruit_class_id": id,
	}
	get, err := s.productDb.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "删除错误，该水果等级已被相关商品使用：", get.ID.Hex())
	}
	return nil
}

func (s productService) BindProductTag(ctx context.Context, tag model.ProductTag, productIDs []primitive.ObjectID, updateType string) error {
	if len(productIDs) < 1 {
		return nil
	}

	filter := bson.M{
		"_id": bson.M{
			"$in": productIDs,
		},
	}

	update := bson.M{
		"$addToSet": bson.M{
			"tag_list": tag,
		},
	}

	if updateType == "remove" {
		update = bson.M{
			"$pull": bson.M{
				"tag_list": tag,
			},
		}
	}

	for _, d := range productIDs {
		del(s.rdb, d)
	}
	err := s.productDb.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

func (s productService) BindCoverProductTag(ctx context.Context, tag model.ProductTag, productIDs []primitive.ObjectID, updateType string) error {
	if len(productIDs) < 1 {
		return nil
	}

	filter := bson.M{
		"_id": bson.M{
			"$in": productIDs,
		},
	}

	update := bson.M{
		"cover_tag": tag,
	}
	if updateType == "remove" {
		update["cover_tag"] = model.ProductTag{}
	}

	for _, d := range productIDs {
		del(s.rdb, d)
	}
	err := s.productDb.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s productService) CheckProductTag(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"tag_list._id": id,
	}
	get, err := s.productDb.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "删除错误,已被相关商品使用：", get.ID.Hex())
	}
	return nil
}

func (s productService) CheckProductCoverTag(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"cover_tag._id": id,
	}
	data, err := s.productDb.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if data.ID != primitive.NilObjectID {
		zap.S().Warnf("删除封面标签错误,已被相关商品使用%s", data.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, "删除错误,已被相关商品使用")
	}
	return nil
}

func (s productService) UpdateTag(ctx context.Context, id primitive.ObjectID, title, color string, img model.FileInfo) error {
	filter := bson.M{
		"tag_list._id": id,
	}
	list, err := s.productDb.List(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"tag_list.$.title": title,
		"tag_list.$.color": color,
		"tag_list.$.img":   img,
	}
	err = s.productDb.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	for _, i := range list {
		del(s.rdb, i.ID)
	}

	return nil
}

func (s productService) UpdateCoverTag(ctx context.Context, id primitive.ObjectID, title, color string, img model.FileInfo) error {
	filter := bson.M{
		"cover_tag._id": id,
	}
	list, err := s.productDb.List(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"cover_tag.title": title,
		"cover_tag.color": color,
		"cover_tag.img":   img,
	}
	err = s.productDb.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	for _, i := range list {
		del(s.rdb, i.ID)
	}

	return nil
}

func (s productService) UpdateFruitClass(ctx context.Context, id primitive.ObjectID, name string) error {
	filter := bson.M{
		"non_standard_attr.fruit_class_id": id,
	}
	list, err := s.productDb.List(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"non_standard_attr.fruit_class_id":   id,
		"non_standard_attr.fruit_class_name": name,
	}
	err = s.productDb.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	for _, i := range list {
		del(s.rdb, i.ID)
	}

	return nil
}

func (s productService) UpdateUnit(ctx context.Context, id primitive.ObjectID, name string) error {
	filter := bson.M{
		"product_unit_id": id,
	}
	list, err := s.productDb.List(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"product_unit_type_name": name,
	}
	err = s.productDb.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	for _, i := range list {
		del(s.rdb, i.ID)
	}

	return nil
}

func (s productService) UpdateRecommend(ctx context.Context, id primitive.ObjectID, ids []primitive.ObjectID) error {
	// ids 不包含本商品
	zap.S().Infof("主关联：%s", id.Hex())
	marshal, _ := json.Marshal(ids)
	zap.S().Infof("对象关联：%s", string(marshal))

	list, _, err := s.ListByCus(ctx, bson.M{
		"recommend_product_list": bson.M{
			"$in": []primitive.ObjectID{id},
		},
	}, 1, 20)
	if err != nil {
		return err
	}
	_ = list

	var removeIDs []primitive.ObjectID
	for _, product := range list {
		if id == product.ID {
			continue
		}
		var f bool
		for _, objectID := range ids {
			if objectID == product.ID {
				f = true
				break
			}
		}
		if !f {
			//	 不存在，清空关联
			removeIDs = append(removeIDs, product.ID)
			zap.S().Infof("清空：%s", product.ID.Hex())
		}
	}

	if len(ids) > 0 {
		ids = append(ids, id)
	}

	filterIDs := ids
	filterIDs = append(filterIDs, id)

	err = s.productDb.UpdateMany(ctx, bson.M{"_id": bson.M{
		"$in": filterIDs,
	}}, bson.M{
		"$set": bson.M{
			"recommend_product_list": ids,
		},
	})
	if err != nil {
		return err
	}

	if len(removeIDs) > 0 {
		err = s.productDb.UpdateMany(ctx, bson.M{"_id": bson.M{
			"$in": removeIDs,
		}}, bson.M{
			"$set": bson.M{
				"recommend_product_list": []primitive.ObjectID{},
			},
		})
		if err != nil {
			return err
		}
	}

	for _, objectID := range ids {
		del(s.rdb, objectID)
	}

	for _, objectID := range removeIDs {
		del(s.rdb, objectID)
	}

	return nil
}
