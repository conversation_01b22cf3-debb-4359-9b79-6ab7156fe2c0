package global

import (
	"base/core/config"

	"go.uber.org/zap"
)

func Init() {
	c := config.Conf
	MDB = InitMongo(c.Mongo)
	RDBDefault = InitRedis(c.Redis)

	//Division = initDivision(RDBSys)

	//	casbin
	// InitCasbin()

	// 日志
	// 支付
	PayLogger = InitZapLogger("logs/pay.log", 10)
	// 系统
	SysLogger = InitZapLogger("logs/sys.log", 25)
	// 订单
	OrderLogger = InitZapLogger("logs/order.log", 10)
	zap.ReplaceGlobals(SysLogger)

	//	支付
	InitAllinPay(c, PayLogger)

	//	ali oss
	InitOss(c.<PERSON>)

	//	微信小程序appID
	InitWechat(c)

	initPay()

	initYeePay(c.YeePay)

	InitMNS(c)

	initAliOcr(c.AliOcr)

	InitZSetQueue(c)
}
