package types

import "base/model"

type CommentCreate struct {
	OrderID      string           `json:"order_id"`
	ProductID    string           `json:"product_id"`
	Content      string           `json:"content"`
	Video        model.FileInfo   `json:"video"`
	ImgList      []model.FileInfo `json:"img_list"`
	DeliverStar  int              `json:"deliver_star"`  // 配送
	LogisticStar int              `json:"logistic_star"` // 物流
	ProductStar  int              `json:"product_star"`  // 商品
	SupplierStar int              `json:"supplier_star"` // 商家
}

type CommentCreateCus struct {
	BuyerName       string           `json:"buyer_name"`
	AvatarImg       model.FileInfo   `json:"avatar_img"`
	ProductID       string           `json:"product_id"`
	ProductTitle    string           `json:"product_title"`
	Content         string           `json:"content"`
	ProductCoverImg model.FileInfo   `json:"product_cover_img"`
	Video           model.FileInfo   `json:"video"`
	ImgList         []model.FileInfo `json:"img_list"`
	SupplierID      string           `json:"supplier_id"`
	SupplierName    string           `json:"supplier_name"`
	ProductStar     int              `json:"product_star"`  // 商品
	DeliverStar     int              `json:"deliver_star"`  // 配送
	LogisticStar    int              `json:"logistic_star"` // 物流
	SupplierStar    int              `json:"supplier_star"` // 商家
	CreatedAt       int64            `json:"created_at"`
}
