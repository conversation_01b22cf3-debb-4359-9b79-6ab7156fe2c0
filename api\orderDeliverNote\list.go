package orderDeliverNote

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/deliverNoteService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 查询发票信息
	filter := bson.M{
		"deleted_at": 0,
	}

	if len(req.BuyerID) != 24 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil))
		return
	}

	id, err := util.ConvertToObjectWithNote(req.BuyerID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["buyer_id"] = id

	list, count, err := deliverNoteService.NewDeliverNoteService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

func ListByWeb(ctx *gin.Context) {
	var req = struct {
		TimeStamp int64 `json:"time_stamp"`
		Page      int64 `json:"page" validate:"min=1"`
		Limit     int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.TimeStamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 查询发票信息
	filter := bson.M{
		"deleted_at": 0,
		"day_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	list, count, err := deliverNoteService.NewDeliverNoteService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var resList []res
	for _, v := range list {
		buyer, err := buyerService.NewBuyerService().Get(v.BuyerID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		resList = append(resList, res{
			DeliveryNote: v,
			BuyerName:    buyer.BuyerName,
		})
	}

	xhttp.RespSuccessList(ctx, resList, count)
}

type res struct {
	model.DeliveryNote
	BuyerName string `json:"buyer_name"`
}
