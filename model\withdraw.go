package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// WithdrawApplyOrder 提现申请订单
type WithdrawApplyOrder struct {
	ID                primitive.ObjectID `json:"id" bson:"_id"`
	AuthenticationID  primitive.ObjectID `json:"authentication_id" bson:"authentication_id"` // 认证信息ID
	Amount            int                `json:"amount" bson:"amount"`
	ObjectID          primitive.ObjectID `json:"object_id" bson:"object_id"`
	ObjectType        ObjectType         `json:"object_type" bson:"object_type"`
	BizUserId         string             `json:"biz_user_id" bson:"biz_user_id"`                 // 仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
	BizOrderNo        string             `json:"biz_order_no" bson:"biz_order_no"`               // 商户订单号（支付订单）
	AccountSetNo      string             `json:"account_set_no" bson:"account_set_no"`           // 账户集编号
	Fee               int                `json:"fee" bson:"fee"`                                 // 内扣
	ValidateType      int                `json:"validate_type" bson:"validate_type"`             // 若不填，默认为短信验证码确认  平台提现，只支持无验证和短信验证
	BankCardNo        string             `json:"bank_card_no" bson:"bank_card_no"`               // 银行卡号/账号	绑定的银行卡号/账号
	BankCardPro       int                `json:"bank_card_pro" bson:"bank_card_pro"`             // 0：个人银行卡  1：企业对公账户  如果不传默认为0  平台提现，必填1
	PayStatus         PayStatusType      `json:"pay_status" bson:"pay_status"`                   // 支付状态
	WithdrawPayResult WithdrawPayResult  `json:"withdraw_pay_result" bson:"withdraw_pay_result"` // 申请结果
	YeeWithdrawResult YeeWithdrawResult  `json:"yee_withdraw_result" bson:"yee_withdraw_result"` // 申请结果
	CreatedAt         int64              `bson:"created_at" json:"created_at"`
	UpdatedAt         int64              `bson:"updated_at" json:"updated_at"`
}

type YeeWithdrawResult struct {
	FinishTime              string `json:"finish_time" bson:"finish_time"`
	ReverseTime             string `json:"reverse_time" bson:"reverse_time"`
	OrderNo                 string `json:"order_no" bson:"order_no"`
	AccountName             string `json:"account_name" bson:"account_name"`
	Fee                     int    `json:"fee" bson:"fee"`
	BankName                string `json:"bank_name" bson:"bank_name"`
	DebitAmount             int    `json:"debit_amount" bson:"debit_amount"`
	RequestNo               string `json:"request_no" bson:"request_no"`
	ReceiveAmount           int    `json:"receive_amount" bson:"receive_amount"`
	OrderTime               string `json:"order_time" bson:"order_time"`
	OrderAmount             int    `json:"order_amount" bson:"order_amount"`
	FeeUndertakerMerchantNo string `json:"fee_undertaker_merchant_no" bson:"fee_undertaker_merchant_no"`
	AccountNo               string `json:"account_no" bson:"account_no"`
	FailReason              string `json:"fail_reason" bson:"fail_reason"`
	ReceiveType             string `json:"receive_type" bson:"receive_type"`
	MerchantNo              string `json:"merchant_no" bson:"merchant_no"`
	Status                  string `json:"status" bson:"status"`
	IsReversed              string `json:"is_reversed" bson:"is_reversed"`
}
