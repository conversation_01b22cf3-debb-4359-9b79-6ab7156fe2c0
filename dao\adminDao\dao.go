package adminDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Admin) error
	Delete(ctx context.Context, filter bson.M) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Admin, error)
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Admin, error)
	Get(ctx context.Context, filter bson.M) (model.Admin, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Admin, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Admin, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
}

type adminDao struct {
	db *mongo.Collection
}

func (s adminDao) Create(ctx context.Context, data model.Admin) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s adminDao) Delete(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s adminDao) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Admin, error) {
	var data model.Admin
	err := s.db.FindOne(ctx, bson.M{"user_id": userID}).Decode(&data)
	if err != nil {
		return model.Admin{}, err
	}
	return data, nil
}

func (s adminDao) GetByID(ctx context.Context, id primitive.ObjectID) (model.Admin, error) {
	var data model.Admin
	err := s.db.FindOne(ctx, bson.M{"_id": id}).Decode(&data)
	if err != nil {
		return model.Admin{}, err
	}
	return data, nil
}

func (s adminDao) Get(ctx context.Context, filter bson.M) (model.Admin, error) {
	var u model.Admin
	err := s.db.FindOne(ctx, filter).Decode(&u)
	if err != nil {
		return model.Admin{}, err
	}
	return u, nil
}

func (s adminDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

// List 查询
func (s adminDao) List(ctx context.Context, filter bson.M) ([]model.Admin, error) {
	var list []model.Admin
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// List 查询
func (s adminDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Admin, int64, error) {
	var list []model.Admin
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func NewAdminDao(collect string) DaoInt {
	return adminDao{
		db: global.MDB.Collection(collect),
	}
}
