package sys

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/buyerStatsService"
	"base/service/productService"
	"base/service/trackService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

//
//func Track(ctx *gin.Context) {
//	var req = struct {
//		Event   model.EventType `json:"event"`
//		Content string          `json:"content"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	t := time.Now().UnixMilli()
//
//	auth := strings.TrimSpace(ctx.GetHeader("Authorization"))
//
//	ip := ctx.Request.Header.Get("X-Real-IP")
//	if ip == "" {
//		ip = ctx.Request.Header.Get("X-Forwarded-For")
//	}
//	if ip == "" {
//		ip = ctx.Request.RemoteAddr
//		if strings.Contains(ip, ":") {
//			split := strings.Split(ip, ":")
//			ip = split[0]
//		}
//	}
//
//	var visitType int
//	var userID, buyerID, productID primitive.ObjectID
//
//	if req.Event == model.EventTypeProductEnter || req.Event == model.EventTypeProductLeave {
//		productID, err = util.ConvertToObjectWithCtx(ctx, req.Content)
//		if err != nil {
//			return
//		}
//	}
//
//	if len(auth) < 1 {
//		// 游客
//		onlineService.NewOnlineService().AddTourVisit(ctx, t, ip)
//	} else {
//		//	已登录
//		myClaims, err := jwtService.NewJwtService().ParseToken(auth)
//		if err != nil {
//			return
//		}
//		userID, err = util.ConvertToObjectWithNote(myClaims.UserID, "myClaims.UserID")
//		if err != nil {
//			return
//		}
//		visitType = 1
//
//		// 是否是采购商
//		buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
//		if errors.Is(err, mongo.ErrNoDocuments) {
//			//	不是
//			onlineService.NewOnlineService().AddUserVisit(ctx, t, userID)
//			return
//		}
//		visitType = 2
//		buyerID = buyer.ID
//
//		onlineService.NewOnlineService().AddBuyerVisit(ctx, t, buyer.ID)
//	}
//
//	now := time.Now().UnixMilli()
//	data := model.Track{
//		ID:        primitive.NewObjectID(),
//		VisitType: visitType,
//		UserID:    userID,
//		BuyerID:   buyerID,
//		ProductID: productID,
//		Event:     req.Event,
//		IP:        ip,
//		CreatedAt: now,
//		LeavedAt:  0,
//	}
//
//	trackService.NewTrackService().Create(ctx, data)
//
//	//switch req.Event {
//	//case model.EventTypeAppEnter:
//	//	//	进入小程序
//	//
//	//	_ = visitType
//	//
//	//	break
//	//case model.EventTypeAppLeave:
//	//	break
//	//
//	//case model.EventTypeProductEnter:
//	//	break
//	//
//	//case model.EventTypeProductLeave:
//	//	break
//	//default:
//	//	zap.S().Errorf("unknown event: %s", req.Event)
//	//}
//
//	xhttp.RespSuccess(ctx, nil)
//}

func ListTrackByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page"`
		Limit   int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	tracks, count, err := trackService.NewTrackService().ListByBuyerID(ctx, buyerID, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]resTrack, 0, len(tracks))

	productIDs := make([]primitive.ObjectID, 0)
	for _, track := range tracks {
		if track.ProductID != primitive.NilObjectID {
			productIDs = append(productIDs, track.ProductID)
		}
	}

	ps, err := productService.NewProductService().ListByIDs(ctx, productIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, track := range tracks {
		var pTitle string
		var SupplierSimpleName string
		var CoverImg model.FileInfo

		for _, product := range ps {
			if product.ID == track.ProductID {
				pTitle = product.Title
				SupplierSimpleName = product.SupplierSimpleName
				CoverImg = product.CoverImg
			}
		}

		list = append(list, resTrack{
			Track:        track,
			ProductTitle: pTitle,
			SupplierName: SupplierSimpleName,
			ProductCover: CoverImg,
		})
	}

	xhttp.RespSuccessList(ctx, list, count)
}

type resTrack struct {
	model.Track
	ProductTitle string         `json:"product_title"`
	SupplierName string         `json:"supplier_name"`
	ProductCover model.FileInfo `json:"product_cover"`
}

func ListActive(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	zs, count, err := trackService.NewTrackService().ListLatestActive(ctx, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]activeRes, 0, len(zs))

	for _, z := range zs {
		t := int64(z.Score)
		name := z.Member.(string)
		buyerID, _ := util.ConvertToObjectWithCtx(ctx, name)

		item := activeRes{
			ActiveAt: t,
			BuyerID:  buyerID,
		}
		list = append(list, item)
	}

	var ids []primitive.ObjectID

	for _, a := range list {
		ids = append(ids, a.BuyerID)
	}

	if len(ids) > 0 {
		buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
			"_id": bson.M{
				"$in": ids,
			},
		})
		if err != nil {
			return
		}

		//addressList, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
		//	"user_id": bson.M{
		//		"$in": userIDs,
		//	},
		//})

		for i, a := range list {
			for _, buyer := range buyers {
				if a.BuyerID == buyer.ID {
					list[i].BuyerID = buyer.ID
					list[i].BuyerName = buyer.BuyerName
					list[i].AuthAt = buyer.CreatedAt

					stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyer.ID)
					if err != nil {
						return
					}
					list[i].BuyerStats = stats
					standingTimeList := trackService.NewTrackService().GetStandingTime(buyer.ID)

					var standingTime int64
					for _, z := range standingTimeList {
						standingTime += int64(z.Score)
					}
					list[i].StandingTime = standingTime
					list[i].ManagerBuyerID = buyer.ManagerBuyerID
					list[i].ManagerUserName = buyer.ManagerUserName
				}
			}
		}
	}

	xhttp.RespSuccessList(ctx, list, count)
}

type activeRes struct {
	ActiveAt int64 `json:"active_at"`
	//UserType     string             `json:"user_type"`
	BuyerID         primitive.ObjectID `json:"buyer_id"`
	BuyerName       string             `json:"buyer_name"`
	AuthAt          int64              `json:"auth_at"`
	StandingTime    int64              `json:"standing_time"`
	BuyerStats      model.BuyerStats   `json:"buyer_stats"`
	ManagerBuyerID  primitive.ObjectID `json:"manager_buyer_id"`
	ManagerUserName string             `json:"manager_user_name"`
}

type simpleAddr struct {
	ServiceFee              int `json:"service_fee"`                // 服务费
	ServiceFeeRebatePercent int `json:"service_fee_rebate_percent"` // 服务费-返利
}
