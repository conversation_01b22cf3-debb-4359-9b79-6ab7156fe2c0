package bankAccountService

import (
	"base/dao"
	"base/dao/bankAccountDao"
	"base/global"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/go-redis/redis/v8"
)

// ServiceInterface 银行卡
type ServiceInterface interface {
	//Upsert(ctx context.Context, req types.BankAccount, originData model.BankAccount, objectID primitive.ObjectID, objectType model.ObjectType) (primitive.ObjectID, error)
	//GetBySupplier(supplierID primitive.ObjectID) (model.BankAccount, error)
	//GetByServicePoint(servicePointID primitive.ObjectID) (model.BankAccount, error)
	//GetByWarehouse(warehouseID primitive.ObjectID) (model.BankAccount, error)
	//
	//CheckBySupplier(supplierID primitive.ObjectID) (bool, error)
	//CheckByServicePoint(servicePointID primitive.ObjectID) (bool, error)
	//CheckByWarehouse(warehouseID primitive.ObjectID) (bool, error)
}

type bankAccountService struct {
	rdb            *redis.Client
	bankAccountDao bankAccountDao.DaoInt
}

//
//func (s bankAccountService) Upsert(ctx context.Context, req types.BankAccount, originData model.BankAccount, objectID primitive.ObjectID, objectType model.ObjectType) (primitive.ObjectID, error) {
//	now := time.Now().UnixMilli()
//	data := model.BankAccount{
//		ID:         primitive.NewObjectID(),
//		ObjectType: objectType,
//		ObjectID:   objectID,
//
//		AccountType: req.AccountType,
//		CardNumber:  req.CardNumber,
//		//对公
//		ParentBankName: req.ParentBankName,
//		BankName:       req.BankName,
//		UnionBank:      req.UnionBank,
//
//		//对私
//		BankReservedMobile: req.BankReservedMobile,
//
//		Bankcard:    req.Bankcard,
//		BankcardImg: req.BankcardImg,
//		CreatedAt:   now,
//	}
//
//	if originData.ID != primitive.NilObjectID {
//		data.ID = originData.ID
//		data.UpdatedAt = now
//		data.CreatedAt = originData.CreatedAt
//	}
//
//	err := s.bankAccountDao.Upsert(ctx, data)
//	if err != nil {
//		return primitive.NilObjectID, err
//	}
//	return data.ID, nil
//}
//
//func (s bankAccountService) CheckBySupplier(supplierID primitive.ObjectID) (bool, error) {
//	i, err := s.GetBySupplier(supplierID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	if i.ID == primitive.NilObjectID {
//		return false, nil
//	}
//	return true, nil
//}
//
//func (s bankAccountService) CheckByWarehouse(warehouseID primitive.ObjectID) (bool, error) {
//	i, err := s.GetByWarehouse(warehouseID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	if i.ID == primitive.NilObjectID {
//		return false, nil
//	}
//	return true, nil
//}
//
//func (s bankAccountService) CheckByServicePoint(servicePointID primitive.ObjectID) (bool, error) {
//	i, err := s.GetByServicePoint(servicePointID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	if i.ID == primitive.NilObjectID {
//		return false, nil
//	}
//	return true, nil
//}
//
//func (s bankAccountService) GetBySupplier(supplierID primitive.ObjectID) (model.BankAccount, error) {
//	object, err := s.bankAccountDao.GetByObject(int(model.ObjectTypeSupplier), supplierID)
//	if err != nil {
//		return model.BankAccount{}, err
//	}
//	return object, nil
//}
//
//func (s bankAccountService) GetByServicePoint(servicePointID primitive.ObjectID) (model.BankAccount, error) {
//	object, err := s.bankAccountDao.GetByObject(int(model.ObjectTypeServicePoint), servicePointID)
//	if err != nil {
//		return model.BankAccount{}, err
//	}
//	return object, nil
//}
//func (s bankAccountService) GetByWarehouse(warehouseID primitive.ObjectID) (model.BankAccount, error) {
//	object, err := s.bankAccountDao.GetByObject(int(model.ObjectTypeWarehouse), warehouseID)
//	if err != nil {
//		return model.BankAccount{}, err
//	}
//	return object, nil
//}

func NewBankCardService() ServiceInterface {
	return bankAccountService{
		rdb:            global.RDBDefault,
		bankAccountDao: dao.BankAccountDao,
	}
}
