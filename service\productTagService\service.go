package productTagService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/productTagDao"
	"base/global"
	"base/model"
	"base/service/productService"
	"context"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Create(ctx context.Context, title, color string, img model.FileInfo, tagType model.ProductTagType) error
	Update(ctx context.Context, id primitive.ObjectID, title, color string, img model.FileInfo) error

	Get(ctx context.Context, id primitive.ObjectID) (model.ProductTag, error)
	ListByType(ctx context.Context, t int) ([]model.ProductTag, error)
	List(ctx context.Context) ([]model.ProductTag, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) []model.ProductTag
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type productTagService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	pTagDao  productTagDao.DaoInt
	productS productService.ServiceInterface
}

func (s productTagService) Get(ctx context.Context, id primitive.ObjectID) (model.ProductTag, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		i, err := s.pTagDao.Get(context.Background(), bson.M{"_id": id})
		if err != nil {
			return model.ProductTag{}, err
		}
		// 检查是否被软删除
		if i.DeletedAt > 0 {
			return model.ProductTag{}, xerr.NewErr(xerr.ErrNoDocument, nil, "标签不存在或已被删除")
		}
		set(s.rdb, i)
		return i, nil
	}
	// 检查缓存中的标签是否被软删除
	if m.DeletedAt > 0 {
		return model.ProductTag{}, xerr.NewErr(xerr.ErrNoDocument, nil, "标签不存在或已被删除")
	}
	return m, nil
}

func (s productTagService) Create(ctx context.Context, title, color string, img model.FileInfo, tagType model.ProductTagType) error {
	now := time.Now().UnixMilli()
	data := model.ProductTag{
		ID:        primitive.NewObjectID(),
		Title:     title,
		Color:     color,
		TagType:   tagType,
		Img:       img,
		CreatedAt: now,
		UpdatedAt: now,
		DeletedAt: 0,
	}

	err := s.pTagDao.Create(ctx, data)
	return err
}

func (s productTagService) List(ctx context.Context) ([]model.ProductTag, error) {
	filter := bson.M{
		"deleted_at": 0, // 只查询未删除的标签
	}
	list, err := s.pTagDao.List(ctx, filter)
	return list, err
}

func (s productTagService) ListByType(ctx context.Context, t int) ([]model.ProductTag, error) {
	filter := bson.M{
		"tag_type":   t,
		"deleted_at": 0, // 只查询未删除的标签
	}
	list, err := s.pTagDao.List(ctx, filter)
	return list, err
}

func (s productTagService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) []model.ProductTag {
	var list []model.ProductTag
	if len(ids) < 1 {
		return nil
	}
	for _, id := range ids {
		tag, err := s.Get(ctx, id)
		if err != nil {
			zap.S().Error("查询商品标签错误", err)
			continue
		}
		if tag.Title != "" {
			list = append(list, tag)

		}
	}
	return list
}

func (s productTagService) Delete(ctx context.Context, id primitive.ObjectID) error {
	tag, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	// 检查是否已经被删除
	if tag.DeletedAt > 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "标签已被删除")
	}

	if tag.TagType == 1 {
		err = s.productS.CheckProductCoverTag(ctx, id)
		if err != nil {
			return err
		}
	}
	if tag.TagType == 2 {
		err = s.productS.CheckProductTag(ctx, id)
		if err != nil {
			return err
		}
	}

	// 软删除：设置删除时间
	now := time.Now().UnixMilli()
	filter := bson.M{"_id": id}
	update := bson.M{
		"$set": bson.M{
			"deleted_at": now,
		},
	}

	err = s.pTagDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

func (s productTagService) Update(ctx context.Context, id primitive.ObjectID, title, color string, img model.FileInfo) error {
	tag, err := s.Get(ctx, id)
	if err != nil {
		return err
	}
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{
			"_id": id,
		}

		now := time.Now().UnixMilli()
		update := bson.M{
			"$set": bson.M{
				"title":      title,
				"color":      color,
				"img":        img,
				"updated_at": now,
			},
		}
		err := s.pTagDao.Update(ctx, filter, update)
		if err != nil {
			return nil, err
		}
		if tag.TagType == 1 {
			err = s.productS.UpdateCoverTag(sessCtx, id, title, color, img)
			if err != nil {
				return nil, err
			}
		}
		if tag.TagType == 2 {
			err = s.productS.UpdateTag(sessCtx, id, title, color, img)
			if err != nil {
				return nil, err
			}
		}
		return nil, nil
	})
	if err != nil {
		return err
	}

	del(s.rdb, id)
	return nil
}

func NewProductTagService() ServiceInterface {
	return productTagService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		pTagDao:  dao.ProductTagDao,
		productS: productService.NewProductService(),
	}
}
