package util

import "math"

// LatitudeLongitudeDistance 经纬度计算
func LatitudeLongitudeDistance(lng1, lat1, lng2, lat2 float64) (distance int) {
	radius := 6371000.00
	rad := math.Pi / 180.0
	lat1 = lat1 * rad
	lng1 = lng1 * rad
	lat2 = lat2 * rad
	lng2 = lng2 * rad

	theta := lng2 - lng1
	dist := math.Acos(math.Sin(lat1)*math.Sin(lat2) + math.Cos(lat1)*math.Cos(lat2)*math.Cos(theta))

	return int(dist * radius)
}
