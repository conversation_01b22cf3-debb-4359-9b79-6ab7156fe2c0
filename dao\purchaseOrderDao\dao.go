package purchaseOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 订单
type DaoInt interface {
	Create(ctx context.Context, data model.PurchaseOrder) error
	CreateMany(ctx context.Context, data []model.PurchaseOrder) error
	Get(ctx context.Context, filter bson.M) (model.PurchaseOrder, error)
	GetMaxStockUpNo(ctx context.Context, filter bson.M) (int, error)
	List(ctx context.Context, filter bson.M) ([]model.PurchaseOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PurchaseOrder, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	CountProductNum(ctx context.Context, buyerID primitive.ObjectID) (int64, int64, error)
	CountBuyerAndProductNum(ctx context.Context, productID primitive.ObjectID) (int64, int64, int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type purchaseOrderDao struct {
	db *mongo.Collection
}

func (s purchaseOrderDao) CreateMany(ctx context.Context, list []model.PurchaseOrder) error {
	data := make([]interface{}, len(list))
	for i, v := range list {
		data[i] = v
	}

	_, err := s.db.InsertMany(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s purchaseOrderDao) Create(ctx context.Context, data model.PurchaseOrder) error {
	_, err := s.db.InsertOne(ctx, data)

	return err
}

func (s purchaseOrderDao) Get(ctx context.Context, filter bson.M) (model.PurchaseOrder, error) {
	var data model.PurchaseOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s purchaseOrderDao) GetMaxStockUpNo(ctx context.Context, filter bson.M) (int, error) {
	var data model.PurchaseOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	//return data, err
	return 0, err
}

func (s purchaseOrderDao) List(ctx context.Context, filter bson.M) ([]model.PurchaseOrder, error) {
	var list []model.PurchaseOrder
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s purchaseOrderDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PurchaseOrder, int64, error) {
	var list []model.PurchaseOrder
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s purchaseOrderDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s purchaseOrderDao) CountProductNum(ctx context.Context, buyerID primitive.ObjectID) (int64, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	}
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return 0, 0, err
	}
	var list []model.Order
	err = cursor.All(ctx, &list)
	if err != nil {
		return 0, 0, err
	}
	var productNum int64
	var amount int64
	for _, order := range list {
		for _, productOrder := range order.ProductList {
			productNum += int64(productOrder.Num)
		}
		amount += int64(order.PaidAmount)
	}

	return productNum, amount, nil
}

type name struct {
	ProductSum int64 `bson:"product_sum"`
	AmountSum  int64 `bson:"amount_sum"`
}

func (s purchaseOrderDao) CountBuyerAndProductNum(ctx context.Context, productID primitive.ObjectID) (int64, int64, int64, error) {
	filter := bson.M{
		"product_list.product_id": productID,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	}
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return 0, 0, 0, err
	}
	var list []model.Order
	err = cursor.All(ctx, &list)
	if err != nil {
		return 0, 0, 0, err
	}
	var productNum int64
	mBuyer := make(map[primitive.ObjectID]int64)
	for _, order := range list {
		for _, productOrder := range order.ProductList {
			if productOrder.ProductID == productID {
				productNum += int64(productOrder.Num)
			}
		}
		mBuyer[order.BuyerID] = 0
	}

	return productNum, int64(len(mBuyer)), int64(len(list)), nil
}

func (s purchaseOrderDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s purchaseOrderDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return err
}

func NewPurchaseOrderDao(collect string) DaoInt {
	return purchaseOrderDao{
		db: global.MDB.Collection(collect),
	}
}
