package user

import (
	"base/core/xhttp"
	"base/model"
	"base/service/couponUserService"
	"base/service/userService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// Get 获取用户
func Get(ctx *gin.Context) {
	var req = struct {
		UserID string `uri:"user_id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	objectID, err := util.ConvertToObject(req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	user, err := userService.NewUserService().Get(ctx, objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	user.Mobile = util.DealMobile(user.Mobile)

	xhttp.RespSuccess(ctx, user)
}

// GetByPost 获取用户
func GetByPost(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	user, err := userService.NewUserService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data := resUser{
		User:        user,
		MobileValue: user.Mobile,
	}

	data.Mobile = util.DealMobile(user.Mobile)

	xhttp.RespSuccess(ctx, data)
}

type resUser struct {
	model.User
	MobileValue string `json:"mobile_value"`
}

// GetQrCode 获取用户二维码
func GetQrCode(ctx *gin.Context) {
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//get, err := qrCodeService.NewQrCodeService().Get(ctx, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, get)
}

// GetAssetStat 个人资产统计
func GetAssetStat(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	userID, err := util.ConvertToObjectWithNote(req.UserID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"user_id":       userID,
		"coupon_status": model.CouponStatusTypeValid,
		"deleted_at":    0,
	}
	couponCount, err := couponUserService.NewCouponUserService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data := AssetStatRes{
		CouponNum: couponCount,
		Integral:  0,
	}

	xhttp.RespSuccess(ctx, data)
}

// AssetStatRes 个人资产统计
type AssetStatRes struct {
	CouponNum int64 `json:"coupon_num"` // 代金券数
	Integral  int   `json:"integral"`   // 积分
}
