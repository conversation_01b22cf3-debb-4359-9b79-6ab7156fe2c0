package middleware

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/adminService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CheckVisitor(ctx *gin.Context) {
	userIDStr := ctx.GetString("user_id")
	objectID, err := util.ConvertToObject(userIDStr)
	if err != nil {
		zap.S().Warn("未登录")
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.Abort()
		return
	}
	checkAdmin, err := adminService.NewAdminService().CheckVisitor(ctx, objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		ctx.Abort()
		return
	}
	if !checkAdmin {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.Abort()
		return
	}

	ctx.Next()
}

func CheckSuperAdmin(ctx *gin.Context) {
	userIDStr := ctx.GetString("user_id")
	objectID, err := util.ConvertToObject(userIDStr)
	if err != nil {
		zap.S().Warn("未登录")
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.Abort()
		return
	}
	f, err := adminService.NewAdminService().CheckSuperAdmin(ctx, objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		ctx.Abort()
		return
	}
	if !f {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.Abort()
		return
	}

	ctx.Next()
}
