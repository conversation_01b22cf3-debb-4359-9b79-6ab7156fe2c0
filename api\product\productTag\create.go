package productTag

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productTagService"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		Title   string         `json:"title" validate:"required"`
		Color   string         `json:"color" validate:"-"`
		TagType int            `json:"tag_type" validate:"oneof=1 2"`
		Img     model.FileInfo `json:"img"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = productTagService.NewProductTagService().Create(ctx, req.Title, req.Color, req.Img, model.ProductTagType(req.TagType))
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
