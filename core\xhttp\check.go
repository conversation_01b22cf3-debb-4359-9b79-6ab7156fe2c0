package xhttp

import (
	"base/model"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strings"
)

func CheckPrice(ctx *gin.Context) (bool, primitive.ObjectID, error) {
	auth := ctx.GetHeader("Authorization")
	auth = strings.TrimSpace(auth)
	valid := false
	var bID primitive.ObjectID
	if len(auth) > 0 {
		// 已登录，获取uid
		//myClaims, err := jwtService.NewJwtService().ParseToken(auth)
		//if err != nil {
		//	zap.S().Warnf("解析token错误:%v,token:%v", err.Error(), auth)
		//	RespErr(ctx, err)
		//	ctx.Abort()
		//	return false, [12]byte{}, err
		//}
		//if myClaims.UserID == "" {
		//	err = xerr.NewErr(xerr.ErrLoginExpire, nil)
		//	RespErr(ctx, err)
		//	ctx.Abort()
		//	return false, [12]byte{}, err
		//}
		//id, err := util.ConvertToObjectWithCtx(ctx, myClaims.UserID)
		//if err != nil {
		//	RespErr(ctx, err)
		//	ctx.Abort()
		//	return false, [12]byte{}, err
		//}
		//user, err := userService.NewUserService().Get(ctx, id)
		//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		//	RespErr(ctx, err)
		//	ctx.Abort()
		//	return false, [12]byte{}, err
		//}
		//_ = user
		//if isSupplier(user.ObjectTypeList) {
		//	return true, [12]byte{}, err
		//}
		//
		//// 是否是采购商-有效期
		//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, id)
		//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		//	RespErr(ctx, err)
		//	ctx.Abort()
		//	return false, [12]byte{}, err
		//}
		//
		//if buyer.AuditStatus == model.AuditStatusTypePass && buyer.AccountStatus == model.AccountStatusTypeNormal {
		valid = true
		//} else {
		//	valid = false
		//}
		//
		//bID = buyer.ID
	}

	return valid, bID, nil
}

func isSupplier(list []model.ObjectType) bool {
	for _, i := range list {
		if i == model.ObjectTypeSupplier {
			return true
		}
	}
	return false
}

//
//func CheckBuyer(ctx *gin.Context) (primitive.ObjectID, error) {
//	userIDStr := ctx.GetString("user_id")
//	objectID, err := util.ConvertToObject(userIDStr)
//	if err != nil {
//		RespErr(ctx, err)
//		return primitive.NilObjectID, err
//	}
//
//	// 是否是采购商-有效期
//	buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, objectID)
//	if errors.Is(err, mongo.ErrNoDocuments) {
//		err = xerr.NewErr(xerr.ErrParamError, nil, "请先进行会员认证")
//		RespErr(ctx, err)
//		return primitive.NilObjectID, err
//	}
//
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		RespErr(ctx, err)
//		return primitive.NilObjectID, err
//	}
//
//	//if buyer.ActiveExpire > time.Now().UnixMilli() {
//	if buyer.AuditStatus == model.AuditStatusTypeDoing {
//		err = xerr.NewErr(xerr.ErrParamError, nil, "会员认证中，请稍等")
//		RespErr(ctx, err)
//		return primitive.NilObjectID, err
//	}
//	if buyer.AuditStatus == model.AuditStatusTypeNotPass {
//		err = xerr.NewErr(xerr.ErrParamError, nil, "请重新进行会员认证")
//		RespErr(ctx, err)
//		return primitive.NilObjectID, err
//	}
//	//}
//	//else {
//	//	err = xerr.NewErr(xerr.ErrParamError, nil, "账号已休眠")
//	//	RespErr(ctx, err)
//	//	return primitive.NilObjectID, err
//	//}
//
//	return buyer.ID, nil
//}
