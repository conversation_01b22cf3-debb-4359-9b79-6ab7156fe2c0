package browse

import (
	"base/core/xhttp"
	"base/service/browseService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id" validate:"len=24"`
		ProductID string `json:"product_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "buyer_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithNote(req.ProductID, "product_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = browseService.NewBrowseService().Create(ctx, buyerID, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
