package multiUserDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.MultiUser) error
	Upsert(ctx context.Context, data model.MultiUser) error
	Delete(ctx context.Context, filter bson.M) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.MultiUser, error)
	Get(ctx context.Context, filter bson.M) (model.MultiUser, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.MultiUser, int64, error)
}

type multiUserDao struct {
	db *mongo.Collection
}

func (s multiUserDao) Create(ctx context.Context, data model.MultiUser) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s multiUserDao) Upsert(ctx context.Context, data model.MultiUser) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func (s multiUserDao) Delete(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s multiUserDao) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.MultiUser, error) {
	var data model.MultiUser
	err := s.db.FindOne(ctx, bson.M{"user_id": userID}).Decode(&data)
	if err != nil {
		return model.MultiUser{}, err
	}
	return data, nil
}

func (s multiUserDao) Get(ctx context.Context, filter bson.M) (model.MultiUser, error) {
	var data model.MultiUser
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.MultiUser{}, err
	}
	return data, nil
}

// List 查询
func (s multiUserDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.MultiUser, int64, error) {
	var list []model.MultiUser
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func NewMultiUserDao(collect string) DaoInt {
	return multiUserDao{
		db: global.MDB.Collection(collect),
	}
}
