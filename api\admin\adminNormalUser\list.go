package adminNormalUser

import (
	"base/core/xhttp"
	"base/service/userService"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page" validate:"min=1"`
		Limit int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, count, err := userService.NewUserService().List(ctx, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

func ListByRegexMobile(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
		Page   int64  `json:"page" validate:"min=1"`
		Limit  int64  `json:"limit" validate:"min=10"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, count, err := userService.NewUserService().ListByRegexMobile(ctx, req.Mobile, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}
