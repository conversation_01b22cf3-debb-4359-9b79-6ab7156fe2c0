package service

import "github.com/cnbattle/allinpay/core"

type MerchantService interface {
	// QueryMerchantBalance 平台账户集余额查询
	QueryMerchantBalance(req QueryMerchantBalanceReq) (QueryMerchantBalanceRes, error)
	QueryReserveFundBalance(req QueryReserveFundBalanceReq) (QueryReserveFundBalanceRes, error)
}

type merchant struct {
	cli *core.Client
}

func NewMerchant(cli *core.Client) MerchantService {
	return &merchant{
		cli: cli,
	}

}

// QueryMerchantBalance 平台账户集余额查询
func (s merchant) QueryMerchantBalance(req QueryMerchantBalanceReq) (QueryMerchantBalanceRes, error) {
	params := map[string]interface{}{}
	params["accountSetNo"] = req.AccountSetNo

	method := "allinpay.yunst.merchantService.queryMerchantBalance"
	var res QueryMerchantBalanceRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

// QueryReserveFundBalance 平台头寸查询
func (s merchant) QueryReserveFundBalance(req QueryReserveFundBalanceReq) (QueryReserveFundBalanceRes, error) {
	params := map[string]interface{}{}

	method := "allinpay.yunst.merchantService.queryReserveFundBalance"
	var res QueryReserveFundBalanceRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return QueryReserveFundBalanceRes{}, err
	}
	return res, nil
}
