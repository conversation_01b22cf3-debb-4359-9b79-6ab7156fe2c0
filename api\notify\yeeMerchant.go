package notify

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func YeeRegister(ctx *gin.Context) {
	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("%s", err.<PERSON><PERSON><PERSON>())
		return
	}
	_ = call

	m := make(map[string]string)

	err = json.Unmarshal([]byte(call), &m)
	if err != nil {
		zap.S().Erro<PERSON>("解析回调失败：%s", err.Error())
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().RegisterResultNotify(ctx, m)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("处理失败：%s", err.Error())
		return
	}

	xhttp.NotifyYeeSuccess(ctx)
}

func YeeBusiness(ctx *gin.Context) {
	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("%s", err.<PERSON>rror())
		return
	}
	_ = call

	m := make(map[string]string)

	err = json.Unmarshal([]byte(call), &m)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

}
