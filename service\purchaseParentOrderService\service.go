package purchaseParentOrderService

import (
	"base/dao"
	"base/dao/purchaseParentOrderDao"
	"base/global"
	"base/model"
	"base/util"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/cnbattle/allinpay"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 支付父单
type ServiceInterface interface {
	Create(ctx context.Context, data model.PurchaseParentOrder) error
	Get(ctx context.Context, id primitive.ObjectID) (model.PurchaseParentOrder, error)
	List(ctx context.Context, filter bson.M) ([]model.PurchaseParentOrder, error)
	GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.PurchaseParentOrder, error)
	UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error
	UpdatePayStatusNotify(ctx context.Context, res allinpay.NotifyPay) error
	UpdatePayStatusNotifyCoupon(ctx context.Context, res allinpay.NotifyPay) error
	UpdatePayStatusByID(ctx context.Context, id primitive.ObjectID, payStatus model.PayStatusType) error
	UpdateCreatePayBizOrderNO(ctx context.Context, parentOrderID primitive.ObjectID, payMethod model.PayMethodType) (string, error) // 更新和创建父单支付订单号
	UpdateCreatePayBizOrderCouponNO(ctx context.Context, parentOrderID primitive.ObjectID) (string, error)                          //  更新和创建父单支付订单号-代金券
	UpdateBizOrderNoResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error
	UpdateBizOrderNoCouponResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PurchaseParentOrder, int64, error)
}
type purchaseParentOrderService struct {
	mdb                    *mongo.Database
	rdb                    *redis.Client
	purchaseParentOrderDao purchaseParentOrderDao.DaoInt
	l                      *zap.SugaredLogger
}

func NewPurchaseParentOrderService() ServiceInterface {
	return purchaseParentOrderService{
		mdb:                    global.MDB,
		rdb:                    global.RDBDefault,
		purchaseParentOrderDao: dao.PurchaseParentOrderDao,
		l:                      global.OrderLogger.Sugar(),
	}
}

func (s purchaseParentOrderService) UpdateBizOrderNoResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"biz_order_no": bizOrderNo}
		err = s.purchaseParentOrderDao.UpdateOne(sessCtx, filter, bson.M{"$set": bson.M{"biz_order_no_result": ps}})
		if err != nil {
			s.l.Error("更新父单状态错误:", err)
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseParentOrderService) UpdateBizOrderNoCouponResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"biz_order_coupon_no": bizOrderNo}
		err = s.purchaseParentOrderDao.UpdateOne(sessCtx, filter, bson.M{"$set": bson.M{"biz_order_coupon_no_result": ps}})
		if err != nil {
			s.l.Error("更新父单代金券状态错误:", err)
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseParentOrderService) UpdateCreatePayBizOrderNO(ctx context.Context, parentOrderID primitive.ObjectID, payMethod model.PayMethodType) (string, error) {
	filter := bson.M{
		"_id": parentOrderID,
	}

	no := util.NewUUID()
	update := bson.M{
		"biz_order_no": no,
		"pay_method":   payMethod,
		"pay_status":   model.PayStatusTypePending,
		"updated_at":   time.Now().UnixMilli(),
	}
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return "", err
	}

	return no, nil
}

func (s purchaseParentOrderService) UpdateCreatePayBizOrderCouponNO(ctx context.Context, parentOrderID primitive.ObjectID) (string, error) {
	filter := bson.M{
		"_id": parentOrderID,
	}
	no := util.NewUUID()
	update := bson.M{
		"biz_order_coupon_no": no,
		"updated_at":          time.Now().UnixMilli(),
	}
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return "", err
	}
	return no, nil
}

func (s purchaseParentOrderService) UpdatePayStatusByID(ctx context.Context, id primitive.ObjectID, payStatus model.PayStatusType) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"pay_status": payStatus,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseParentOrderService) UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	update := bson.M{
		"pay_status": payStatus,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseParentOrderService) UpdatePayStatusNotify(ctx context.Context, res allinpay.NotifyPay) error {
	filter := bson.M{
		"biz_order_no": res.BizOrderNo,
	}
	update := bson.M{
		"pay_status":                      model.PayStatusTypePaid,
		"pay_result.status":               res.Status,
		"pay_result.channel_paytime":      res.ChannelPaytime,
		"pay_result.cusid":                res.Cusid,
		"pay_result.biz_order_no":         res.BizOrderNo,
		"pay_result.channel_fee":          res.ChannelFee,
		"pay_result.pay_interfacetrxcode": res.PayInterfacetrxcode,
		"pay_result.pay_datetime":         res.PayDatetime,
		"pay_result.acct":                 res.Acct,
		"updated_at":                      time.Now().UnixMilli(),
	}
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseParentOrderService) UpdatePayStatusNotifyCoupon(ctx context.Context, res allinpay.NotifyPay) error {
	filter := bson.M{
		"biz_order_coupon_no": res.BizOrderNo,
	}

	update := bson.M{
		"biz_order_coupon_no_result.amount":       res.Amount,
		"biz_order_coupon_no_result.biz_order_no": res.BizOrderNo,
		"biz_order_coupon_no_result.extendInfo":   res.ExtendInfo,
		"biz_order_coupon_no_result.pay_datetime": res.PayDatetime,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseParentOrderService) Create(ctx context.Context, data model.PurchaseParentOrder) error {
	err := s.purchaseParentOrderDao.Create(ctx, data)

	return err
}

func (s purchaseParentOrderService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.PurchaseParentOrder, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	data, err := s.purchaseParentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.PurchaseParentOrder{}, err
	}
	return data, nil
}

func (s purchaseParentOrderService) Get(ctx context.Context, id primitive.ObjectID) (model.PurchaseParentOrder, error) {
	filter := bson.M{
		"_id": id,
	}
	payOrder, err := s.purchaseParentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.PurchaseParentOrder{}, err
	}
	return payOrder, nil
}

func (s purchaseParentOrderService) List(ctx context.Context, filter bson.M) ([]model.PurchaseParentOrder, error) {
	list, err := s.purchaseParentOrderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s purchaseParentOrderService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PurchaseParentOrder, int64, error) {
	list, count, err := s.purchaseParentOrderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s purchaseParentOrderService) UpdateOne(ctx context.Context, filter, update bson.M) error {
	err := s.purchaseParentOrderDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
