package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type Category struct {
	ID          primitive.ObjectID   `bson:"_id" json:"id"`
	ParentID    primitive.ObjectID   `bson:"parent_id" json:"parent_id"`     // 所属父节点
	Path        []primitive.ObjectID `bson:"path" json:"path"`               // 所属父节点
	Name        string               `bson:"name" json:"name"`               // 分类名称
	Img         FileInfo             `bson:"img" json:"img"`                 // 图标（三级）
	Level       int                  `bson:"level" json:"level"`             // 当前第几层
	Sort        int                  `bson:"sort" json:"sort"`               // 排序
	Visible     bool                 `bson:"visible" json:"visible"`         // 是否展示
	IsSpecial   bool                 `bson:"is_special" json:"is_special"`   // 特殊分类为true
	ProductIDs  []primitive.ObjectID `bson:"product_ids" json:"product_ids"` // 特殊分类拥有商品
	MigratePID  string               `bson:"migrate_pid" json:"migrate_pid"` //
	MigratePath []string             `bson:"migrate_path" json:"migrate_path"`
}
