package orderServicePoint

import (
	"base/core/xhttp"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Arrive 确认到货
func Arrive(ctx *gin.Context) {
	var req = struct {
		OrderIDList []string `json:"order_id_list" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	var ids []primitive.ObjectID
	for _, i := range req.OrderIDList {
		id, err := util.ConvertToObjectWithNote(i, "发货订单ID")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	err = orderPointService.NewOrderPointService().DoArrive(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
