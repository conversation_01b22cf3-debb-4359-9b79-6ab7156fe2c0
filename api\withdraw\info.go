package withdraw

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Info 提现信息
func Info(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id"`
		WarehouseID    string `json:"warehouse_id"`
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	env := xhttp.GetEnv(ctx)

	var id primitive.ObjectID
	switch env {
	case model.ObjectTypeSupplier:
		id, err = util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeWarehouse:
		id, err = util.ConvertToObjectWithCtx(ctx, req.WarehouseID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeServicePoint:
		id, err = util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	default:
		break
	}

	if id == primitive.NilObjectID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误"))
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByObject(ctx, id, env)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res := infoRes{
		CompanyType:    auth.Company.CompanyType,
		AcctProtocolNo: auth.AcctProtocolNo,
		SignAcctName:   auth.SignAcctName,
		SignResult:     auth.SignResult,
		CardNumber:     auth.BankAccount.CardNumber,
		SignPersonal:   auth.SignPersonal,
		PersonalBank:   auth.PersonalBankAccount,
	}

	xhttp.RespSuccess(ctx, res)
}

type infoRes struct {
	CompanyType    model.CompanyType  `json:"company_type" bson:"company_type"`         // 企业类型
	AcctProtocolNo string             `json:"acct_protocol_no" bson:"acct_protocol_no"` //  必填  账户提现协议编号 商户端需保存
	SignAcctName   string             `json:"sign_acct_name" bson:"sign_acct_name"`     //  必填  签约户名
	SignResult     string             `json:"sign_result" bson:"sign_result"`           //  必填  签订结果	成功：
	CardNumber     string             `json:"card_number" bson:"card_number"`           // 账号
	SignPersonal   model.SignPersonal `json:"sign_personal"`                            // 个人
	PersonalBank   model.BankAccount  `json:"personal_bank"`                            // 个人
}
