package supplier

import (
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/servicePointService"
	"base/service/stationService"
	"base/service/supplierService"
	"base/service/supplierStatService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/mongo"
)

type res struct {
	model.Supplier
	IsMobileVerify   bool   `json:"is_mobile_verify"`
	PayMobile        string `json:"pay_mobile"`
	ServicePointName string `json:"service_point_name"`
	StationName      string `json:"station_name"`
}

// GetByUser 查询申请信息
func GetByUser(ctx *gin.Context) {
	var req = struct {
		UserID string `uri:"user_id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	objectID, err := util.ConvertToObject(req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	info, err := supplierService.NewSupplierService().GetByUser(objectID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespSuccess(ctx, nil)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	authentication, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, info.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := res{
		Supplier:       info,
		IsMobileVerify: authentication.IsMobileVerify,
		PayMobile:      authentication.Mobile,
	}

	xhttp.RespSuccess(ctx, r)

}

// GetByUserPost 查询申请信息
func GetByUserPost(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	info, err := supplierService.NewSupplierService().GetByUser(id)
	if err != nil && errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}

	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//authentication, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, info.ID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	r := res{
		Supplier: info,
		//IsMobileVerify: authentication.IsMobileVerify,
		//PayMobile:      authentication.Mobile,
	}

	point, err := servicePointService.NewServicePointService().Get(ctx, info.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r.ServicePointName = point.Name

	if info.Level == "station" {
		station, err := stationService.NewStationService().Get(ctx, info.StationID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		r.StationName = station.Name
	}

	xhttp.RespSuccess(ctx, r)

}

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	info, err := supplierService.NewSupplierService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//authentication, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, info.ID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//r := res{
	//	Supplier:       info,
	//	IsMobileVerify: authentication.IsMobileVerify,
	//	PayMobile:      authentication.Mobile,
	//}

	xhttp.RespSuccess(ctx, info)
}

func GetByWeb(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	info, err := supplierService.NewSupplierService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := supplierWeb{
		Supplier: info,
	}

	point, err := servicePointService.NewServicePointService().Get(ctx, info.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r.ServicePointName = point.Name

	if info.Level == "station" {
		station, err := stationService.NewStationService().Get(ctx, info.StationID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		r.StationName = station.Name
	}

	xhttp.RespSuccess(ctx, r)
}

type supplierWeb struct {
	model.Supplier
	ServicePointName string `json:"service_point_name"`
	StationName      string `json:"station_name"`
}

func GetByPost(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	info, err := supplierService.NewSupplierService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//authentication, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, info.ID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//r := res{
	//	Supplier:       info,
	//	IsMobileVerify: authentication.IsMobileVerify,
	//	PayMobile:      authentication.Mobile,
	//}

	xhttp.RespSuccess(ctx, info)
}

func GetStatsForUser(ctx *gin.Context) {
	var req = struct {
		SupplierId string `json:"supplier_id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.SupplierId, "GetStatsForUser supplier_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	supplier, err := supplierService.NewSupplierService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplierStat, err := supplierStatService.NewSupplierStatService().GetBySupplierID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := stats{
		MonthSold:          supplierStat.SaleNumMonthly,
		ReBuyPercent:       dealPercent(supplierStat.ReBuyRate),
		AfterSaleRate:      supplierStat.AfterSaleRate,
		OrderNum:           supplierStat.OrderNum,
		Fans:               supplierStat.FansNum,
		Star:               dealOne(supplierStat.Star),
		SupplierSimpleName: supplier.ShopSimpleName,
		SupplierAvatarImg:  supplier.AvatarImg,
		Location:           supplier.Location,
	}

	xhttp.RespSuccess(ctx, r)
}

func dealPercent(num int) float64 {
	f, exact := decimal.NewFromInt(int64(num)).Div(decimal.NewFromInt(100)).Round(2).Float64()
	_ = exact
	return f
}

func dealOne(num int) float64 {
	f, exact := decimal.NewFromInt(int64(num)).Div(decimal.NewFromInt(10)).Round(1).Float64()
	_ = exact
	return f
}

type stats struct {
	Star               float64        `json:"star"`                 // 星级
	MonthSold          int            `json:"month_sold"`           // 月销
	ReBuyPercent       float64        `json:"re_buy_percent"`       // 回头率
	AfterSaleRate      float64        `json:"after_sale_rate"`      // 售后率
	OrderNum           int            `json:"order_num"`            // 订单数
	Fans               int            `json:"fans"`                 // 粉丝
	SupplierSimpleName string         `json:"supplier_simple_name"` // 简称
	SupplierAvatarImg  model.FileInfo `json:"supplier_avatar_img"`  // 商家头像
	Location           model.Location `json:"location"`             // 地区
}
