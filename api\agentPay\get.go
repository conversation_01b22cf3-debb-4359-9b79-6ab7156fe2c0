package agentpay

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetNotPayAmount 待结算金额
func GetNotPayAmount(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"has_agent_pay":    false,
		"order_refund_all": false,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"created_at": bson.M{
			"$gte": 1732696200000, // 2024-11-27 16:30:00
		},
		"supplier_id": supplierID,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, o := range orders {
		orderIDs = append(orderIDs, o.ID)
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
			//"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}
	_ = refunds

	//resList := make([]NotPayRes, 0)

	var amount int

	for _, order := range orders {
		var refundList []model.OrderRefund
		var refundProductAmount int
		for _, refund := range refunds {
			if refund.OrderID == order.ID && refund.AuditStatus == model.AuditStatusTypePass {
				refundList = append(refundList, refund)
				refundProductAmount += refund.AuditAmount
			}
		}

		amount += order.ProductTotalAmount - refundProductAmount
		//item := NotPayRes{
		//	Order:      order,
		//	RefundList: refundList,
		//	Amount:     amount,
		//}
		//resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, amount)

}

// ListNotPay 待结算金额记录
func ListNotPay(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Page       int64  `json:"page"`
		Limit      int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"has_agent_pay":    false,
		"order_refund_all": false,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"created_at": bson.M{
			"$gte": 1732696200000, // 2024-11-27 16:30:00
		},
		"supplier_id": supplierID,
	}

	orders, count, err := orderService.NewOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, o := range orders {
		orderIDs = append(orderIDs, o.ID)
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
			//"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}
	_ = refunds

	resList := make([]NotPayRes, 0)

	for _, order := range orders {
		var refundList []model.OrderRefund
		var refundProductAmount int
		var existAfterSale bool
		for _, refund := range refunds {
			if refund.OrderID == order.ID && refund.AuditStatus == model.AuditStatusTypePass {
				refundList = append(refundList, refund)
				refundProductAmount += refund.AuditAmount
			}
			if refund.OrderID == order.ID && refund.AuditStatus == model.AuditStatusTypeDoing {
				existAfterSale = true
			}
		}

		amount := order.ProductTotalAmount - refundProductAmount
		item := NotPayRes{
			Order:          order,
			ExistAfterSale: existAfterSale,
			RefundList:     refundList,
			Amount:         amount,
		}
		resList = append(resList, item)
	}

	xhttp.RespSuccessList(ctx, resList, count)
}

type NotPayRes struct {
	Order          model.Order         `json:"order"`
	ExistAfterSale bool                `json:"exist_after_sale"`
	RefundList     []model.OrderRefund `json:"refund_list"`
	Amount         int                 `json:"amount"`
}
