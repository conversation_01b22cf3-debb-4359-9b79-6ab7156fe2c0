package handler

import (
	"base/api/orderFinalSettle"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// orderFinalSettleRouter 订单最终结算记录路由
func orderFinalSettleRouter(r *gin.RouterGroup) {
	r = r.Group("/order/final/settle")
	r.Use(middleware.CheckToken)

	// 根据订单ID查询订单最终结算记录
	r.POST("/get/by/order", orderFinalSettle.GetByOrderID)

	// 根据供应商ID查询最终结算记录列表
	r.POST("/list/by/supplier", orderFinalSettle.ListBySupplierID)

	// 按月查询订单最终结算记录
	r.POST("/list/by/month", orderFinalSettle.ListByMonth)

	// 获取月度最终结算统计
	r.POST("/stats/monthly", orderFinalSettle.GetMonthlyStats)

	// 结算利润
	r.POST("/transfer/profit", orderFinalSettle.TransferProfit)
	r.POST("/list/transfer/by/supplier", orderFinalSettle.ListSettlementBySupplier)
}
