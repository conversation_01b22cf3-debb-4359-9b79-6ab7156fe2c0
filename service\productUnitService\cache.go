package productUnitService

import (
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 缓存
var cache = "product-unit:"

func get(r *redis.Client, id primitive.ObjectID) string {
	key := cache + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		s := r.Get(ctx, key).Val()
		return s
	}
	return ""
}

func set(r *redis.Client, info model.ProductUnit) {
	key := cache + info.ID.Hex()
	r.Set(context.Background(), key, info.Name, 0)
}

func del(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cache+id.Hex())
}
