package orderDeliverNote

import (
	"base/core/xhttp"
	"base/model"
	"base/service/deliverNoteService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID     string            `json:"buyer_id"`
		Timestamp   int64             `json:"timestamp"`
		DeliverType model.DeliverType `json:"deliver_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	zeroTimestamp, err := util.DayStartZeroTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 查询发票信息
	filter := bson.M{
		"deleted_at": 0,
	}

	id, err := util.ConvertToObjectWithNote(req.BuyerID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["buyer_id"] = id

	data, err := deliverNoteService.NewDeliverNoteService().GetByBuyer(ctx, id, zeroTimestamp, req.DeliverType)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}
