package sys

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/userAddrService"
	"bytes"
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"log"
	"strconv"
	"time"
)

func ExportYHT(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"user_type": model.UserTypeYHT,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	bufferFile, err := toExcel(ctx, buyers)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = bufferFile

	xhttp.RespSuccess(ctx, nil)
}

func setColorStyle(f *excelize.File, sheetName string, cellList []string) {
	var err error
	_ = err
	for _, s := range cellList {
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold:   true,
				Family: "宋体",
				Size:   10,
				Color:  "#ff0000",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "000000", Style: 1},
				{Type: "top", Color: "000000", Style: 1},
				{Type: "bottom", Color: "000000", Style: 1},
				{Type: "right", Color: "000000", Style: 1},
			},
			Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		})
		_ = f.SetCellStyle(sheetName, s, s, style)
	}
}

func toExcel(ctx context.Context, list []model.Buyer) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	title(f, sheetName, i)

	//category(f, sheetName, i+5)

	var ids []primitive.ObjectID
	for _, buyer := range list {
		ids = append(ids, buyer.ID)
	}

	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
		"buyer_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		return nil, err
	}

	i = 3

	for num, buyer := range list {
		col := strconv.Itoa(i)
		numStr := strconv.Itoa(num + 1)

		//err = f.MergeCell(sheetName, "A"+strconv.Itoa(deliverMergeBegin), "A"+strconv.Itoa(i-1))
		err = f.SetCellValue(sheetName, "A"+col, numStr)
		err = f.SetCellValue(sheetName, "B"+col, buyer.BuyerName)
		err = f.SetCellValue(sheetName, "C"+col, buyer.ContactUser)
		err = f.SetCellValue(sheetName, "D"+col, buyer.Mobile)

		for _, address := range addresses {
			if address.BuyerID == buyer.ID {
				err = f.SetCellValue(sheetName, "E"+col, address.Address)
			}
		}

		i++
	}

	f.SetColWidth(sheetName, "A", "A", 6)
	f.SetColWidth(sheetName, "B", "B", 28)
	f.SetColWidth(sheetName, "C", "C", 16)
	f.SetColWidth(sheetName, "D", "D", 20)
	f.SetColWidth(sheetName, "E", "E", 28)

	//style, err := f.NewStyle(&excelize.Style{
	//	Font: &excelize.Font{
	//		Bold:   true,
	//		Family: "宋体",
	//		Size:   10,
	//	},
	//	Border: []excelize.Border{
	//		{Type: "left", Color: "000000", Style: 1},
	//		{Type: "top", Color: "000000", Style: 1},
	//		{Type: "bottom", Color: "000000", Style: 1},
	//		{Type: "right", Color: "000000", Style: 1},
	//	},
	//	Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	//})
	//
	//content2 := strconv.Itoa(i)
	//err = f.SetCellStyle(sheetName, "A2", "G"+content2, style)
	////err = f.SetRowStyle(sheetName, 6, i, style)

	setProductStyle(f, sheetName, 3, i)
	// 汇总表
	i += 11
	//summary(f, sheetName, i, res, pAllFinalAmount, totalDeliverFee)

	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	f.SaveAs("./api/sys/益禾堂客户.xlsx")

	return toBuffer, nil
}

// 其他
func setOther(f *excelize.File, sheetName string, index, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount int) {
	originIndex := index
	row := strconv.Itoa(index)
	var err error

	err = f.SetCellValue(sheetName, "B"+row, "其他")

	colNum := 3
	colValueNum := 17
	// 配送费
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "配送费")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "配送费补贴")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "配送费实付")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalDeliverFeeAll))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalDeliverFeeSubsidy))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), dealMoney(totalDeliverFee))

	// 服务费
	index += 2
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "服务费")
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "服务费平台补贴")
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "服务费实付")
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalServiceFee))
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalServiceFee))
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), 0)

	// 优惠券
	//index += 3
	//err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "优惠券使用")
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), 0)

	//index += 1
	err = f.MergeCell(sheetName, "B"+strconv.Itoa(originIndex), "B"+strconv.Itoa(index))
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	//err = f.MergeCell(sheetName, "B"+strconv.Itoa(index+1), "B"+strconv.Itoa(index))
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}

	index += 1
	err = f.SetCellValue(sheetName, "B"+strconv.Itoa(index), "合计")
	finalAmount := pAllFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(finalAmount))
	//
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(originIndex), convertToCol(colValueNum)+strconv.Itoa(index), style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(index), convertToCol(colValueNum)+strconv.Itoa(index), style2)
	//zap.S().Info("originIndex::", originIndex)
	//err = f.SetRowHeight(sheetName, originIndex, 20)
	//err = f.SetRowHeight(sheetName, originIndex+1, 20)
	//err = f.SetRowHeight(sheetName, originIndex+2, 20)
	//err = f.SetRowHeight(sheetName, originIndex+3, 20)
	//err = f.SetRowHeight(sheetName, originIndex+4, 20)
	//err = f.SetRowHeight(sheetName, originIndex+5, 20)
	//err = f.SetRowHeight(sheetName, originIndex+6, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+7, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+8, 26)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "A"+cell1, "E"+cell2, style)
}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.14),
		Right:  float64Ptr(0.14),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func note(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "备注说明：")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+1), "M"+strconv.Itoa(index+1))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+1), "补差未支付时，补差金额不计入金额小计；")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+2), "M"+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+2), "售后存在审核中时，售后金额不计入金额小计；")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "C"+strconv.Itoa(index+2), style)

}

func orderTime(f *excelize.File, sheetName string, index int, min, max, exportBegin, exportEnd int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "Q"+row)
	if err != nil {

	}

	exportBeginFormat := time.UnixMilli(exportBegin).Format("2006/01/02 15:04:05")
	exportEndFormat := time.UnixMilli(exportEnd).Format("2006/01/02 15:04:05")

	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ], 导出区间：[ %s , %s ]", minT, MaxT, exportBeginFormat, exportEndFormat))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

// 导出区间
func exportTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("导出区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func title(f *excelize.File, sheetName string, index int) {
	_ = index
	err := f.MergeCell(sheetName, "A1", "E1")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetRowHeight(sheetName, 1, 30)

	err = f.SetCellValue(sheetName, "A1", "客户列表")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   24,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {

	}
	err = f.SetCellStyle(sheetName, "A1", "A1", titleStyle)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	err = f.MergeCell(sheetName, "A"+row, "A"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "A"+row, "")
	err = f.SetColWidth(sheetName, "A", "A", 4)

	colNum := 2
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 14)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "下单时间")

	// 供应商
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "供应商")

	// 商品
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 32)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "商品")

	// 计价方式
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "计价\n方式")

	// 订单信息
	colNum += 1
	colBegin := colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+4)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "订单信息")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "单价\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "订单数量\n（件）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "商品金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+3)+row2, "订单重量\n（kg）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+4)+row2, "仓配费\n（元）")

	err = f.SetColWidth(sheetName, convertToCol(colBegin+2), convertToCol(colBegin+2), 14)
	err = f.SetColWidth(sheetName, convertToCol(colBegin+4), convertToCol(colBegin+4), 14)
	//err = f.SetCellValue(sheetName, "I"+row2, "配送费\n（元）")

	// 发货信息
	colNum += 5
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+2)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "发货信息")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "发货数量\n（件）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "发货重量\n（kg）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "重量误差\n（kg）")

	// 退款补差
	colNum += 3
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+2)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "退款补差")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "发货退款\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "售后金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "补差金额\n（元）")

	// 金额小计
	colNum += 3
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetColWidth(sheetName, convertToCol(colBegin), convertToCol(colBegin), 14)
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "金额小计\n（元）")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, convertToCol(colBegin)+row2, style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "F"+row2, "P"+row2, style2)

	err = f.SetRowHeight(sheetName, index, 20)
	err = f.SetRowHeight(sheetName, index+1, 26)
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

func convertToCol(columnNumber int) string {
	var res []byte
	for columnNumber > 0 {
		a := columnNumber % 26
		if a == 0 {
			a = 26
		}
		res = append(res, 'A'+byte(a-1))
		columnNumber = (columnNumber - a) / 26
	}
	// 上面输出的res是反着的，前后交换
	for i, n := 0, len(res); i < n/2; i++ {
		res[i], res[n-1-i] = res[n-1-i], res[i]
	}
	return string(res)
}
