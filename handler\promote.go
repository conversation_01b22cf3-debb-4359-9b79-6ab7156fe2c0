package handler

import (
	"base/api/promote"
	"github.com/gin-gonic/gin"
)

// 推广
func promoteRouter(r *gin.RouterGroup) {
	r = r.Group("/promote")

	r.POST("/list", promote.List)
	//r2 := r.Use(middleware.CheckToken, middleware.CheckUID)

	//r2.POST("/create", promote.Create)
	//r2.POST("/update/data", promote.UpdateData)
	//r2.POST("/update/status", promote.UpdateStatus)
	//r2.POST("/delete", promote.Delete)
}
