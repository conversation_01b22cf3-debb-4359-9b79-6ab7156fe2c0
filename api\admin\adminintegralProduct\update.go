package adminIntegralProduct

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/integralProductService"
	"base/util"
	"github.com/gin-gonic/gin"
	"time"
)

// UpdateData 整体更新
func UpdateData(ctx *gin.Context) {
	var req = struct {
		ID            string                      `json:"id"`
		Title         string                      `json:"title"`          // 商品名称
		CostNum       int                         `json:"cost_num"`       // 消耗积分数
		Price         int                         `json:"price"`          // 原价
		DiscountPrice int                         `json:"discount_price"` // 折扣价
		ImageCover    model.FileInfo              `json:"image_cover"`    // 商品封面图片
		ImageDisplay  []model.FileInfo            `json:"image_display"`  // 轮播展示图
		ImageDesc     []model.FileInfo            `json:"image_desc"`     // 详情图
		Stock         int                         `json:"stock"`          // 库存
		TagList       []model.TagPer              `json:"tag_list"`       // 标签
		Desc          string                      `json:"desc"`           // 描述
		Status        model.IntegralProductStatus `json:"status"`         // 上下架  1 上架 2 下架
		Target        model.Target                `json:"target"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.CostNum <= 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "兑换消耗积分需大于0"))
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = check(req.Price, req.DiscountPrice)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	product, err := integralProductService.NewIntegralProductService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	product.Title = req.Title
	product.Desc = req.Desc
	product.TagList = req.TagList
	product.Price = req.Price
	product.DiscountPrice = req.DiscountPrice
	product.ImageCover = req.ImageCover
	product.ImageDisplay = req.ImageDisplay
	product.ImageDesc = req.ImageDesc
	product.CostNum = req.CostNum
	product.Stock = req.Stock
	product.Status = req.Status
	product.Target = req.Target
	product.Status = req.Status

	now := time.Now().UnixMilli()

	product.UpdatedAt = now

	err = integralProductService.NewIntegralProductService().UpdateData(ctx, product)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func check(price, discountPrice int) error {
	if price == 0 || discountPrice == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "价格不能为0")
	}
	if discountPrice != 0 && discountPrice >= price {
		return xerr.NewErr(xerr.ErrParamError, nil, "折扣价不能高于原价")
	}

	return nil
}

// UpdateStock 更新库存
func UpdateStock(ctx *gin.Context) {
	var req = struct {
		ID    string `json:"id"`
		Stock int    `json:"stock"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = integralProductService.NewIntegralProductService().UpdateStock(ctx, id, req.Stock)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateSort 更新顺序
func UpdateSort(ctx *gin.Context) {
	var req = struct {
		ID   string `json:"id"`
		Sort int    `json:"sort"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = integralProductService.NewIntegralProductService().UpdateSort(ctx, id, req.Sort)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateStatus 更新状态
func UpdateStatus(ctx *gin.Context) {
	var req = struct {
		ID     string                      `json:"id"`
		Status model.IntegralProductStatus `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = integralProductService.NewIntegralProductService().UpdateStatus(ctx, id, req.Status)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
