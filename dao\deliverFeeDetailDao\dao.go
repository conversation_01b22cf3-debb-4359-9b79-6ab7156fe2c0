package deliverFeeDetailDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 配送费明细DAO接口
type DaoInt interface {
	Create(ctx context.Context, data model.DeliverFeeDetail) error
	Get(ctx context.Context, filter bson.M) (model.DeliverFeeDetail, error)
	List(ctx context.Context, filter bson.M) ([]model.DeliverFeeDetail, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliverFeeDetail, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type deliverFeeDetailDao struct {
	db *mongo.Collection
}

// NewDeliverFeeDetailDao 创建配送费明细DAO实例
func NewDeliverFeeDetailDao(collectionName string) DaoInt {
	return &deliverFeeDetailDao{
		db: global.MDB.Collection(collectionName),
	}
}

// Create 创建配送费明细记录
func (d *deliverFeeDetailDao) Create(ctx context.Context, data model.DeliverFeeDetail) error {
	_, err := d.db.InsertOne(ctx, data)
	return err
}

// Get 获取配送费明细记录
func (d *deliverFeeDetailDao) Get(ctx context.Context, filter bson.M) (model.DeliverFeeDetail, error) {
	var result model.DeliverFeeDetail
	err := d.db.FindOne(ctx, filter).Decode(&result)
	return result, err
}

// List 获取配送费明细记录列表
func (d *deliverFeeDetailDao) List(ctx context.Context, filter bson.M) ([]model.DeliverFeeDetail, error) {
	var result []model.DeliverFeeDetail
	opts := options.Find().SetSort(bson.D{{"order_created_at", -1}})
	cursor, err := d.db.Find(ctx, filter, opts)
	if err != nil {
		return result, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &result)
	return result, err
}

// ListByPage 分页获取配送费明细记录列表
func (d *deliverFeeDetailDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliverFeeDetail, int64, error) {
	var result []model.DeliverFeeDetail

	total, err := d.Count(ctx, filter)
	if err != nil {
		return result, 0, err
	}

	opts := options.Find().
		SetSkip((page - 1) * limit).
		SetLimit(limit).
		SetSort(bson.D{{"order_created_at", -1}})

	cursor, err := d.db.Find(ctx, filter, opts)
	if err != nil {
		return result, total, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &result)
	return result, total, err
}

// Count 获取配送费明细记录数量
func (d *deliverFeeDetailDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	return d.db.CountDocuments(ctx, filter)
}
