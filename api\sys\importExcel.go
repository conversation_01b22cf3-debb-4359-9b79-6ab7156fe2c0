package sys

import (
	"base/core/xhttp"
	"base/model"
	"base/types"
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

func ImportExcel(ctx *gin.Context) {
	var req = struct {
		P1 string  `json:"p1"`
		P2 string  `json:"p2"`
		P3 int     `json:"p3"`
		P4 int64   `json:"p4"`
		P5 float64 `json:"p5"`
		P6 float64 `json:"p6"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//dealExcel(ctx)
}

func dealExcel(ctx context.Context) {
	f, err := excelize.OpenFile("./api/sys/yht.xlsx")
	if err != nil {
		fmt.Println(err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
		return
	}

	var list []sourceData

	for i, row := range rows {
		if i == 0 {
			continue
		}
		item := sourceData{
			ShopName: row[4] + "益禾堂",
			User:     row[7],
			Mobile:   row[8],
			Address:  row[9],
		}
		list = append(list, item)
		//fmt.Println(item)
	}
	_ = list

	//mMobile := make(map[string]int)
	for i, data := range list {
		//if i > 0 {
		//	break
		//}
		req := types.BuyerApplyReq{
			BuyerName:     data.ShopName,
			ContactUser:   data.User,
			ContactMobile: data.Mobile,
			ApplyReason:   "益禾堂",
			Entity:        2,
			Location:      model.Location{},
			Address:       data.Address,
			DeliverType:   []model.DeliverType{model.DeliverTypeLogistics},
		}

		Apply(ctx, req)
		zap.S().Infof("index %d mobile %s", i+1, data.Mobile)
	}

}

type sourceData struct {
	ShopName string
	User     string
	Mobile   string
	Address  string
}

// Apply 采购商
func Apply(ctx context.Context, req types.BuyerApplyReq) {
	//
	//user, err := userService.NewUserService().GetOrCreateUser(ctx, req.ContactMobile, "", "")
	//if err != nil {
	//
	//}
	//
	//byUser, err := buyerService.NewBuyerService().GetByUserID(ctx, user.ID)
	//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//	zap.S().Errorf("%s", err.Error())
	//	return
	//}
	//
	//if byUser.ID != primitive.NilObjectID {
	//	return
	//}
	//
	//pointLongitude := 102.746418
	//pointLatitude := 25.025472
	//km, deliverFee, person, track := util.CalcDeliverFee(pointLongitude, pointLatitude, req.Location)
	//_ = km
	//
	//now := time.Now().UnixMilli()
	//instantDeliver := make([]model.InstantDeliver, 0, 2)
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     1,
	//	Name:   "跑腿",
	//	Amount: person,
	//})
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     2,
	//	Name:   "货拉拉",
	//	Amount: track,
	//})
	//
	//data := model.Buyer{
	//	ID:        primitive.NewObjectID(),
	//	UserID:    user.ID,
	//	BuyerName: req.BuyerName,
	//	//BuyerType:          req.BuyerType,
	//	MemberType:    pays.MemberTypeIndividual,
	//	ContactUser:   req.ContactUser,
	//	ContactMobile: req.ContactMobile,
	//	//RegionID:           rid,
	//	Location:           req.Location,
	//	Address:            req.Address,
	//	BusinessLicenseImg: req.BusinessLicenseImg,
	//	ShopHeadImg:        req.ShopHeadImg,
	//	AuditStatus:        model.AuditStatusTypePass,
	//	AccountStatus:      model.AccountStatusTypeNormal,
	//	LicenseStatus:      model.LicenseStatusNo,
	//	Note:               req.BuyerNote,
	//	ApplyReason:        req.ApplyReason,
	//	Entity:             2,
	//	AddressNote:        req.AddressNote,
	//	DeliverType:        req.DeliverType,
	//	DeliverFee:         deliverFee,
	//	SubsidyAmount:      50000,
	//	SubsidyPercent:     50,
	//	LogisticsNote:      "快递",
	//	InstantDeliver:     instantDeliver,
	//	UserType:           "YHT",
	//	CreatedAt:          now,
	//	UpdatedAt:          now,
	//}
	//
	//pointID, _ := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
	//data.ServicePointID = pointID
	//data.IsAssignServicePoint = true
	//data.ServicePointName = "昆明城市服务仓"
	//
	//data.ApplyReason = req.ApplyReason
	//
	//err = buyerService.NewBuyerService().Create(ctx, data)
	//
	//err = userAddrService.NewUserAddrService().InitByBuyer(ctx, data)
	//if err != nil {
	//	return
	//}
}
