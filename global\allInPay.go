package global

import (
	"base/core/config"
	"github.com/cnbattle/allinpay"
	"github.com/cnbattle/allinpay/core"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 跳转协议路径
var SignAcctUrl = "https://test.allinpay.com/yungateway/member/signContract.html"
var SignContractPageUrl = "https://fintech.allinpay.com/yungateway/member/signContract.html"

// CDNUrl 对象存储cdn
var CDNUrl = "http://image.guoshut.com/"

var backHostDev = "http://agent.e1.luyouxia.top:14085"
var BackHost = "https://api.guoshut.com"

// BackUrl 支付异步通知
var BackUrl = "/api/pay/back/notify"
var BackUrlSignAcctProtocol = "/api/pay/back/notify/sign/acct"
var BackUrlSignAcctProtocolPersonal = "/api/pay/back/notify/sign/acct/personal"
var BackUrlWithdraw = "/api/pay/back/notify/withdraw"
var BackUrlWithdrawBuyer = "/api/pay/back/notify/withdraw/buyer"
var BackUrlDeposit = "/api/pay/back/notify/deposit"
var BackUrlDepositBuyerBalance = "/api/pay/back/notify/deposit/buyer/balance"
var BackUrlDepositMarketing = "/api/pay/back/notify/deposit/marketing"
var BackUrlAgentCollect = "/api/pay/back/notify/agent/collect"               // 托管代收
var BackUrlAgentCollectDebt = "/api/pay/back/notify/agent/collect/debt"      // 托管代收
var BackUrlAgentCollectCoupon = "/api/pay/back/notify/agent/collect/coupon"  // 托管代收
var BackUrlSignalAgentPay = "/api/pay/back/notify/agent/pay/signal"          // 托管单笔代付
var BackUrlSignalAgentPayTemp = "/api/pay/back/notify/agent/pay/signal/temp" // 托管单笔代付
var BackUrlCancel = "/api/pay/back/notify/cancel"                            // 取消订单
var BackUrlRefund = "/api/pay/back/notify/refund"                            // 退款
var BackUrlRefundManual = "/api/pay/back/notify/refund/manual"               // 手动取消整个订单 退款
var BackUrlRefundDeliver = "/api/pay/back/notify/refund/deliver"             //  退款  配送费

var BackUrlIntegralPay = "/api/pay/back/notify/integral/pay"
var BackUrlIntegralCancel = "/api/pay/back/notify/integral/cancel"            // 取消订单
var BackUrlIntegralSignalAgentPay = "/api/pay/back/notify/integral/agent/pay" // 托管单笔代付

var BackUrlLargeTransfer = "/api/pay/back/notify/large/transfer" // 大额转账，暂不部署
//https://www.guoshut.com/api/pay/back/notify/large/transfer

// AllinPayClient 通联支付
var AllinPayClient *allinpay.AllInPay

var IndustryCode = "2311" // 支付-行业代码
var IndustryName = "食品饮料"

var TradeCodeCollect = "3001"    // 电商及其它 代收消费金
var TradeCodeCollectPay = "4001" // 电商及其它 代付购买金

// AllInPayAccountSetInfo 支付账户集信息
var AllInPayAccountSetInfo config.AllInPayAccountSetInfo

func InitAllinPay(c *config.Config, log *zap.Logger) {
	if gin.Mode() == gin.DebugMode {
		BackHost = backHostDev
	}

	AllinPayClient = newCli(c.AllinPayProd, log, true)

	AllInPayAccountSetInfo = config.AllInPayAccountSetInfo{
		PlatformBizUserId: "#yunBizUserId_B2C#",
		// 招商银行昆明东站支行
		PlatformCardNumber: "***************",
		//标准余额账户集
		StandardBalanceNo: "100001",
		//标准保证金账户集
		StandardDepositNo: "100002",
		//准备金额度账户集
		ReserveLimitNo: "100003",
		//标准营销账户集
		MarketNo: "2000000",
		//托管账户集
		EscrowUserNo: "401876", // 正式
	}

}

func newCli(c config.AllinPay, log *zap.Logger, isProd bool) *allinpay.AllInPay {
	conf := core.Config{
		//SysID:        c.SysID,
		AppID:        c.AppID,
		AppSecretKey: c.AppSecretKey,
		//AppAccountID: c.AppAccountID,
		PfxPath: c.PfxPath,
		TLCert:  c.TLCert,
		PfxPwd:  c.PfxPwd,
		IsProd:  isProd,
		Debug:   c.Debug,
	}

	allInPay := allinpay.NewAllInPay(conf, log)

	return allInPay
}
