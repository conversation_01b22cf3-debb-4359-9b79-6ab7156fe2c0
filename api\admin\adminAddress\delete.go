package adminAddress

import (
	"base/core/xhttp"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Del(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = userAddrService.NewUserAddrService().DelByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, "删除成功")
}
