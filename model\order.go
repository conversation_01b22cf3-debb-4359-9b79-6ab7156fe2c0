package model

import (
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Order 订单
type Order struct {
	ID                       primitive.ObjectID `json:"id" bson:"_id"`
	IDNum                    string             `json:"id_num" bson:"id_num"`                   // 订单编号
	ParentOrderID            primitive.ObjectID `json:"parent_order_id" bson:"parent_order_id"` // 父单ID
	PayMethod                PayMethodType      `json:"pay_method" bson:"pay_method"`           // 支付方式
	OrderType                OrderType          `json:"order_type" bson:"order_type"`           // 订单类型
	SupplierLevel            SupplierLevel      `json:"supplier_level" bson:"supplier_level"`   // 供应商类型
	UserID                   primitive.ObjectID `json:"user_id" bson:"user_id"`                 // 用户ID
	BuyerID                  primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`               // 采购商ID
	BuyerName                string             `json:"buyer_name" bson:"buyer_name"`           // 采购商名称
	UserType                 UserType           `json:"user_type" bson:"user_type"`
	SupplierID               primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`                                 // 供应商ID
	SupplierName             string             `json:"supplier_name" bson:"supplier_name"`                             // 供应商名称
	PayBizUserID             string             `json:"pay_biz_user_id" bson:"pay_biz_user_id"`                         // 付款人-采购商
	ReceiverBizUserID        string             `json:"receiver_biz_user_id" bson:"receiver_biz_user_id"`               // 收款人-供应商
	TotalAmount              int                `json:"total_amount" bson:"total_amount"`                               // 总金额
	PaidAmount               int                `json:"paid_amount" bson:"paid_amount"`                                 // 实付金额
	ProductTotalAmount       int                `json:"product_total_amount" bson:"product_total_amount"`               // 商品总金额
	OriginProductTotalAmount int                `json:"origin_product_total_amount" bson:"origin_product_total_amount"` // 商品原总金额
	OfficialSubsidyAmount    int                `json:"official_subsidy_amount" bson:"official_subsidy_amount"`         // 补贴额
	DiscountAmount           int                `json:"discount_amount" bson:"discount_amount"`                         // 折扣立减额
	CouponUserID             primitive.ObjectID `json:"coupon_user_id" bson:"coupon_user_id"`                           // 用户优惠券ID
	CouponAmount             int                `json:"coupon_amount" bson:"coupon_amount"`                             // 用户优惠券使用金额
	CouponMinAmount          int                `json:"coupon_min_amount" bson:"coupon_min_amount"`                     // 优惠券-门槛金额
	CouponSplitAmount        int                `json:"coupon_split_amount" bson:"coupon_split_amount"`                 // 优惠券分摊金额
	CouponTitle              string             `json:"coupon_title" bson:"coupon_title"`                               // 优惠券-标题
	TotalTransportFee        int                `json:"total_transport_fee" bson:"total_transport_fee"`                 // 干线费
	TransportFeePerKG        int                `json:"transport_fee_per_kg" bson:"transport_fee_per_kg"`               // 干线费单价-分/kg
	TotalServiceFee          int                `json:"total_service_fee" bson:"total_service_fee"`                     // 服务费
	UnitWarehouseLoadFee     int                `json:"unit_warehouse_load_fee" bson:"unit_warehouse_load_fee"`         // 仓配费单价
	TotalWarehouseLoadFee    int                `json:"total_warehouse_load_fee" bson:"total_warehouse_load_fee"`       // 仓配费总价
	ProductList              []ProductOrder     `json:"product_list" bson:"product_list"`                               // 产品列表
	Address                  OrderAddress       `json:"address" bson:"address"`                                         // 地址
	WarehouseID              primitive.ObjectID `json:"warehouse_id" bson:"warehouse_id"`                               // 集中仓ID
	WarehouseName            string             `json:"warehouse_name" bson:"warehouse_name"`                           // 集中仓名称
	ServicePointID           primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`                       // 服务点ID
	ServicePointName         string             `json:"service_point_name" bson:"service_point_name"`                   // 服务点名称
	SecondPointID            primitive.ObjectID `json:"second_point_id" bson:"second_point_id"`                         // 服务点ID
	SecondPointName          string             `json:"second_point_name" bson:"second_point_name"`                     // 服务点名称
	StationID                primitive.ObjectID `json:"station_id" bson:"station_id"`                                   // 服务点ID
	StationName              string             `json:"station_name" bson:"station_name"`
	PayStatus                PayStatusType      `json:"pay_status" bson:"pay_status"`                     // 支付状态
	OrderStatus              OrderStatusType    `json:"order_status" bson:"order_status"`                 // 订单状态
	OrderRefundAll           bool               `json:"order_refund_all" bson:"order_refund_all"`         // 订单全缺
	HasCheckAfterShip        bool               `json:"has_check_after_ship" bson:"has_check_after_ship"` // 是否已经进行发货检查
	HasDebtOrder             bool               `json:"has_debt_order" bson:"has_debt_order"`             // 是否补差订单
	HasDebtOrderPaid         bool               `json:"has_debt_order_paid" bson:"has_debt_order_paid"`   // 是否补差订单已支付
	DeliverType              DeliverType        `json:"deliver_type" bson:"deliver_type"`                 // 配送方式
	DeliverFeeRes            DeliverFeeRes      `json:"deliver_fee_res" bson:"deliver_fee_res"`           // 配送费
	InstantDeliverType       int                `json:"instant_deliver_type" bson:"instant_deliver_type"` // 配送方式
	InstantDeliverName       string             `json:"instant_deliver_name" bson:"instant_deliver_name"`
	OrderNote                string             `json:"order_note" bson:"order_note"`                                   // 订单备注
	ServicePointNote         string             `json:"service_point_note" bson:"service_point_note"`                   // 服务仓备注
	CancelResult             RefundResult       `json:"cancel_result" bson:"cancel_result"`                             // 取消订单结果
	WXCancelResult           WXRefundResult     `json:"wx_cancel_result" bson:"wx_cancel_result"`                       // jsapi
	CancelResultCoupon       RefundResult       `json:"cancel_result_coupon" bson:"cancel_result_coupon"`               // 取消订单结果-代金券
	OrderStatusRecord        OrderStatusRecord  `json:"order_status_record" bson:"order_status_record"`                 // 订单状态记录
	Expire                   int64              `json:"expire" bson:"expire"`                                           // 订单超时
	HasComment               bool               `json:"has_comment" bson:"has_comment"`                                 // 是否已经评论
	HasAgentPay              bool               `json:"has_agent_pay" bson:"has_agent_pay"`                             // 分账-标识
	DeliveryAllotHas         bool               `json:"delivery_allot_has" bson:"delivery_allot_has"`                   // 配送-已经分派
	DeliveryUserID           primitive.ObjectID `json:"delivery_user_id" bson:"delivery_user_id"`                       // 配送-配送员ID  是userID
	DeliveryUserName         string             `json:"delivery_user_name" bson:"delivery_user_name"`                   // 配送-配送员名称
	DeliveryImgList          []FileInfo         `json:"delivery_img_list" bson:"delivery_img_list"`                     // 配送-图片
	StockUpNo                int                `json:"stock_up_no" bson:"stock_up_no"`                                 // 备货 批次
	StockUpDayTime           int64              `json:"stock_up_day_time" bson:"stock_up_day_time"`                     // 备货 日期
	InvoiceStatus            InvoiceStatusType  `json:"invoice_status" bson:"invoice_status"`                           // 发票状态
	LogisticsCompanyID       primitive.ObjectID `json:"logistics_company_id" bson:"logistics_company_id"`               // 物流公司ID
	LogisticsAutoReceiveHour int                `json:"logistics_auto_receive_hour" bson:"logistics_auto_receive_hour"` // 物流自动收货时间   12  24  36
	LogisticsNoList          []string           `json:"logistics_no_list" bson:"logistics_no_list"`                     // 物流单号
	LogisticsImageList       []FileInfo         `json:"logistics_image_list" bson:"logistics_image_list"`               // 物流单图片
	LogisticsTime            int64              `json:"logistics_time" bson:"logistics_time"`                           // 物流单上传时间
	LogisticsName            string             `json:"logistics_name" bson:"logistics_name"`                           // 物流名称
	YeeRefundResult          YeeRefundResult    `json:"yee_refund_result" bson:"yee_refund_result"`                     // 易宝
	CreatedAt                int64              `bson:"created_at" json:"created_at"`
	UpdatedAt                int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt                int64              `bson:"deleted_at" json:"deleted_at"`
}

/*
	OrderStatusTypeToComment: "待评价",
	OrderStatusTypeAfterSale: "售后中",
	OrderStatusTypeCommented: "已评价",
*/

//OrderProductStatusType OrderProductStatusType `json:"order_product_status_type" bson:"order_product_status_type"` // 订单产品状态
//BizOrderNoResult       PayResult              `json:"biz_order_no_result" bson:"biz_order_no_result"`               // 微信支付结果
//BizOrderNoCloseResult  PayCloseResult         `json:"biz_order_no_close_result" bson:"biz_order_no_close_result"`   // 关闭订单结果
//BizOrderCouponNoResult PayResult              `json:"biz_order_coupon_no_result" bson:"biz_order_coupon_no_result"` // 代金券支付结果
//PlatformAmount         int                    `json:"platform_amount" bson:"platform_amount"`                       // 平台
//WarehouseAmount        int                    `json:"warehouse_amount" bson:"warehouse_amount"`                     // 集中仓
//ServicePointAmount     int                    `json:"service_point_amount" bson:"service_point_amount"`             // 服务点
//SupplierAmount         int                    `json:"supplier_amount" bson:"supplier_amount"`                       // 供应商

// ProductOrder 商品订单
type ProductOrder struct {
	ProductID                     primitive.ObjectID   `json:"product_id" bson:"product_id"`                   // 商品ID
	IsShipRefundAll               bool                 `json:"is_ship_refund_all" bson:"is_ship_refund_all"`   //  发货退款 是否退全部 2 // 发货退款
	CategoryIDs                   []primitive.ObjectID `bson:"category_ids" json:"category_ids"`               // 商品分类信息
	ProductTitle                  string               `json:"product_title" bson:"product_title"`             // 商品标题
	ProductCoverImg               FileInfo             `json:"product_cover_img" bson:"product_cover_img"`     // 封面
	ProductImageID                primitive.ObjectID   `json:"product_image_id" bson:"product_image_id"`       // 商品镜像ID
	OriginPrice                   int                  `json:"origin_price" bson:"origin_price"`               // 原价
	CostPrice                     int                  `json:"cost_price" bson:"cost_price"`                   // 成本价
	DiscountList                  []ProductDiscount    `json:"discount_list" bson:"discount_list"`             // 折扣信息 若符合
	DiscountPriceList             []DiscountPrice      `json:"discount_price_list" bson:"discount_price_list"` // 折扣价格
	Price                         int                  `json:"price" bson:"price"`                             // 单价
	SettleUnitPrice               int                  `json:"settle_unit_price" bson:"settle_unit_price"`     // 单价
	IsCheckWeight                 bool                 `json:"is_check_weight" bson:"is_check_weight"`         // 分拣检查重量  是/否
	SkuIDCode                     string               `json:"sku_id_code" bson:"sku_id_code"`
	SkuName                       string               `json:"sku_name" bson:"sku_name"`
	HasParam                      bool                 `json:"has_param" bson:"has_param"` // 规格参数 有/无
	ProductParamType              ProductParamType     `json:"product_param_type" bson:"product_param_type"`
	StandardAttr                  StandardAttr         `json:"standard_attr" bson:"standard_attr"`                                             // 标品参数
	NonStandardAttr               NonStandardAttr      `json:"non_standard_attr" bson:"non_standard_attr"`                                     // 非标品参数
	RoughWeight                   int                  `json:"rough_weight" bson:"rough_weight"`                                               // 毛重
	OutWeight                     int                  `bson:"out_weight" json:"out_weight"`                                                   // 皮重
	NetWeight                     int                  `bson:"net_weight" json:"net_weight"`                                                   // 净重
	ProductRoughWeightUnitPriceKG int                  `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"`   // 商品毛重单价/kg
	UserPayRoughWeightUnitPriceKG int                  `json:"user_pay_rough_weight_unit_price_kg" bson:"user_pay_rough_weight_unit_price_kg"` // 用户支付（不包含运费）毛重单价/kg
	CouponRoughWeightUnitPriceKG  int                  `json:"coupon_rough_weight_unit_price_kg" bson:"coupon_rough_weight_unit_price_kg"`     // 优惠券毛重单价/kg
	Num                           int                  `json:"num" bson:"num"`                                                                 // 数量
	TotalWeight                   int                  `json:"total_weight" bson:"total_weight"`                                               // 总重量
	DueWeight                     int                  `json:"due_weight" bson:"due_weight"`                                                   // 实际数量对应的应有重量
	SortNum                       int                  `json:"sort_num" bson:"sort_num"`                                                       // 分拣数量
	SortWeight                    int                  `json:"sort_weight" bson:"sort_weight"`                                                 // 分拣重量
	SortUserID                    primitive.ObjectID   `json:"sort_user_id" bson:"sort_user_id"`                                               // 分拣人ID
	SortUserName                  string               `json:"sort_user_name" bson:"sort_user_name"`                                           //
	SortUserMobile                string               `json:"sort_user_mobile" bson:"sort_user_mobile"`                                       //
	TotalTransportFee             int                  `json:"total_transport_fee" bson:"total_transport_fee"`                                 // 干线费
	TransportFeePerKG             int                  `json:"transport_fee_per_kg" bson:"transport_fee_per_kg"`
	TotalServiceFee               int                  `json:"total_service_fee" bson:"total_service_fee"`               // 服务费
	UnitWarehouseLoadFee          int                  `json:"unit_warehouse_load_fee" bson:"unit_warehouse_load_fee"`   // 仓配费单价
	TotalWarehouseLoadFee         int                  `json:"total_warehouse_load_fee" bson:"total_warehouse_load_fee"` // 仓配费总价
	TotalAmount                   int                  `json:"total_amount" bson:"total_amount"`                         // 总价
	ProductAmount                 int                  `json:"product_amount" bson:"product_amount"`                     // 商品总价
	OriginProductAmount           int                  `json:"origin_product_amount" bson:"origin_product_amount"`       // 原商品总价
	CouponSplitAmount             int                  `json:"coupon_split_amount" bson:"coupon_split_amount"`           // 优惠金额
	PaidAmount                    int                  `json:"paid_amount" bson:"paid_amount"`                           // 实付
	CommissionPercent             int                  `bson:"commission_percent" json:"commission_percent"`             // 商品抽成
	AfterSaleStatus               AfterSaleStatusType  `json:"after_sale_status" bson:"after_sale_status"`               // 售后状态 not 未售后 pending 售后中 finish 已售后
	Photo                         FileInfo             `json:"photo" bson:"photo"`                                       // 分拣图
	PhotoList                     []FileInfo           `json:"photo_list" bson:"photo_list"`                             // 品控图列表
	ReasonType                    RefundReasonType     `json:"reason_type" bson:"reason_type"`                           // 原因-类型   1  质量 2 缺货
	ReasonImg                     FileInfo             `json:"reason_img" bson:"reason_img"`                             // 原因-图片
	HasComment                    bool                 `json:"has_comment" bson:"has_comment"`                           // 是否已经评论
	IsAutoComment                 bool                 `json:"is_auto_comment" bson:"is_auto_comment"`                   // 是否自动评论
	PurchaseNote                  string               `json:"purchase_note" bson:"purchase_note"`                       // 采购说明-采购门店
	LinkBrandStatus               int                  `json:"link_brand_status" bson:"link_brand_status"`               // 关联品牌状态  2 关联
	LinkBrandID                   primitive.ObjectID   `json:"link_brand_id" bson:"link_brand_id"`                       // 关联品牌ID
	LinkBrandName                 string               `json:"link_brand_name" bson:"link_brand_name"`                   // 关联品牌名称
}

// AfterSaleStatusType  售后状态
type AfterSaleStatusType string

const (
	// AfterSaleStatusTypeNot 未发生售后
	AfterSaleStatusTypeNot AfterSaleStatusType = "not"
	// AfterSaleStatusTypePending 售后中
	AfterSaleStatusTypePending AfterSaleStatusType = "pending"
	// AfterSaleStatusTypeFinish 售后结束
	AfterSaleStatusTypeFinish AfterSaleStatusType = "finish"
)

//OrderProductStatusType OrderProductStatusType `json:"order_product_status_type" bson:"order_product_status_type"` // 订单产品状态
//

type OrderAddress struct {
	AddressID primitive.ObjectID `json:"address_id" bson:"address_id"`
	Contact   Contact            `json:"contact" bson:"contact"`   // 联系人信息
	Location  Location           `json:"location" bson:"location"` // 地址经纬度
	Address   string             `json:"address" bson:"address"`   // 详细地址
	UserType  string             `json:"user_type" bson:"user_type"`
}

// OrderStatusRecord 状态记录
type OrderStatusRecord struct {
	CreateOrderTime int64 `json:"create_order_time" bson:"create_order_time"` // 下单时间
	PayTime         int64 `json:"pay_time" bson:"pay_time"`                   // 支付时间
	StockUpTime     int64 `json:"stock_up_time" bson:"stock_up_time"`         // 备货
	QualityTime     int64 `json:"quality_time" bson:"quality_time"`           // 品控
	SortTime        int64 `json:"sort_time" bson:"sort_time"`                 // 分拣
	ShipTime        int64 `json:"ship_time" bson:"ship_time"`                 // 发货
	ArriveTime      int64 `json:"arrive_time" bson:"arrive_time"`             // 到货
	ReceiveTime     int64 `json:"receive_time" bson:"receive_time"`           // 收货
}

type WXPayResult struct {
	OutTradeNo     string      `json:"out_trade_no" bson:"out_trade_no"`         //
	PayOpenID      string      `json:"pay_open_id" bson:"pay_open_id"`           //
	PayFailMessage string      `json:"pay_fail_message" bson:"pay_fail_message"` //
	PrepayId       string      `json:"prepay_id" bson:"prepay_id"`               // 预支付交易会话标识。用于后续接口调用中使用，该值有效期为2小时
	Appid          string      `json:"app_id" bson:"app_id"`
	TimeStamp      string      `json:"timestamp" bson:"timestamp"`
	NonceStr       string      `json:"nonce_str" bson:"nonce_str"`
	Package        string      `json:"package" bson:"package"`
	SignType       string      `json:"sign_type" bson:"sign_type"`
	PaySign        string      `json:"pay_sign" bson:"pay_sign"`
	TransactionID  string      `json:"transaction_id" bson:"transaction_id"`     // 微信支付系统生成的订单号
	TradeState     string      `json:"trade_state" bson:"trade_state"`           // 交易状态
	TradeStateDesc string      `json:"trade_state_desc" bson:"trade_state_desc"` // 交易状态描述
	BankType       string      `json:"bank_type" bson:"bank_type"`               // 银行类型，采用字符串类型的银行标识。银行标识请参考《银行类型对照表》
	SuccessTime    string      `json:"success_time" bson:"success_time"`         // 支付完成时间，遵循rfc3339标准格式
	TotalAmount    int         `json:"total_amount" bson:"total_amount"`
	PayerAmount    int         `json:"payer_amount" bson:"payer_amount"`
	Source         interface{} `json:"source" bson:"source"`
}

type YeeWechatResult struct {
	OrderCreatedAt             int64              `json:"order_created_at" bson:"order_created_at"`       //
	OrderID                    string             `json:"order_id" bson:"order_id"`                       //
	UniqueOrderNo              string             `json:"unique_order_no" bson:"unique_order_no"`         //
	ParentMerchantNo           string             `json:"parent_merchant_no" bson:"parent_merchant_no"`   //
	MerchantNo                 string             `json:"merchant_no" bson:"merchant_no"`                 //
	Token                      string             `json:"token" bson:"token"`                             //
	AggOpenID                  string             `json:"agg_open_id" bson:"agg_open_id"`                 //
	AggOrderId                 string             `json:"agg_order_id" bson:"agg_order_id"`               //
	AggUniqueOrderNo           string             `json:"agg_unique_order_no" bson:"agg_unique_order_no"` //
	AggCreatedAt               int64              `json:"agg_created_at" bson:"agg_created_at"`           //
	PrePayTn                   string             `json:"pre_pay_tn" bson:"pre_pay_tn"`                   //
	AppId                      string             `json:"app_id" bson:"app_id"`                           //
	MiniProgramPath            string             `json:"mini_program_path" bson:"mini_program_path"`     // 跳转小程序的路径
	MiniProgramOrgId           string             `json:"mini_program_org_id" bson:"mini_program_org_id"` // 小程序原始id
	NotifyPayWay               string             `json:"notify_pay_way" bson:"notify_pay_way"`
	NotifyPaySuccessDate       string             `json:"notify_pay_success_date" bson:"notify_pay_success_date"`                 //
	NotifyChannelTrxId         string             `json:"notify_channel_trx_id" bson:"notify_channel_trx_id"`                     // 微信/支付宝订单号
	NotifyStatus               string             `json:"notify_status" bson:"notify_status"`                                     //
	NotifyChannelOrderId       string             `json:"notify_channel_order_id" bson:"notify_channel_order_id"`                 // 该笔订单在微信、支付宝或银行侧系统生成的单号
	NotifyPayAmount            int                `json:"notify_pay_amount" bson:"notify_pay_amount"`                             // 支付金额，单位:元
	NotifyOrderAmount          int                `json:"notify_order_amount" bson:"notify_order_amount"`                         //
	NotifyPayerUserID          string             `json:"notify_payer_user_id" bson:"notify_payer_user_id"`                       //  openID
	NotifyPayerYpAccountBookNo string             `json:"notify_payer_yp_account_book_no" bson:"notify_payer_yp_account_book_no"` // 记账簿
	NotifySubOrderList         []SubOrderInfoList `json:"notify_sub_order_list" bson:"notify_sub_order_list"`                     // 子单
}

type WXRefundResult struct {
	RefundID            string                      `json:"refund_id" bson:"refund_id"`                         // 微信支付退款单号
	OutRefundNo         string                      `json:"out_refund_no" bson:"out_refund_no"`                 // 商户系统内部的退款单号
	OutTradeNo          string                      `json:"out_trade_no" bson:"out_trade_no"`                   // out_trade_no
	TransactionId       string                      `json:"transaction_id" bson:"transaction_id"`               // 微信支付交易订单号
	Channel             refunddomestic.Channel      `json:"channel" bson:"channel"`                             // 退款渠道
	UserReceivedAccount string                      `json:"user_received_account" bson:"user_received_account"` // 取当前退款单的退款入账方
	SuccessTime         string                      `json:"success_time" bson:"success_time"`                   // 退款成功时间，当退款状态为退款成功时有返回。
	CreateTime          string                      `json:"create_time" bson:"create_time"`                     // 退款受理时间
	Status              refunddomestic.Status       `json:"status" bson:"status"`                               // 退款状态 退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往商户平台-交易中心，手动处理此笔退款。
	FundsAccount        refunddomestic.FundsAccount `json:"funds_account" bson:"funds_account"`                 // 退款所使用资金对应的资金账户类型
	Amount              RefundAmount                `json:"amount" bson:"amount"`                               // 金额信息
	Source              interface{}                 `json:"source" bson:"source"`
}

type YeeRefundResult struct {
	OrderId                 string `json:"order_id" bson:"order_id"`                                     //
	RefundRequestId         string `json:"refund_request_id" bson:"refund_request_id"`                   //
	ParentMerchantNo        string `json:"parent_merchant_no" bson:"parent_merchant_no"`                 //
	MerchantNo              string `json:"merchant_no" bson:"merchant_no"`                               // 收款商户编号
	RefundAmount            int    `json:"refund_amount" bson:"refund_amount"`                           //
	Status                  string `json:"status" bson:"status"`                                         //  退款状态
	NotifyPaymentMethod     string `json:"notify_payment_method" bson:"notify_payment_method"`           //
	NotifyRefundSuccessDate string `json:"notify_refund_success_date" bson:"notify_refund_success_date"` //
	NotifyErrorMessage      string `json:"notify_error_message" bson:"notify_error_message"`             //
	NotifyUniqueRefundNo    string `json:"notify_unique_refund_no" bson:"notify_unique_refund_no"`       //
}

type RefundAmount struct {
	Total            int          `json:"total" bson:"total"`                         // 订单金额
	Refund           int          `json:"refund" bson:"refund"`                       // 退款金额
	PayerTotal       int          `json:"payer_total" bson:"payer_total"`             // 用户支付金额
	PayerRefund      int          `json:"payer_refund" bson:"payer_refund"`           // 用户退款金额
	SettlementRefund int          `json:"settlement_refund" bson:"settlement_refund"` // 应结退款金额
	SettlementTotal  int          `json:"settlement_total" bson:"settlement_total"`   // 应结订单金额
	DiscountRefund   int          `json:"discount_refund" bson:"discount_refund"`     // 优惠退款金额
	Currency         string       `json:"currency" bson:"currency"`                   // 退款币种
	RefundFee        int          `json:"refund_fee" bson:"refund_fee"`               // 手续费退款金额
	From             []RefundFrom `json:"from" bson:"from"`                           // 退款出资账户及金额
}

type RefundFrom struct {
	Account string `json:"account" bson:"account"` // 出资账户类型
	Amount  int    `json:"amount" bson:"amount"`   // 出资金额
}

type RefundNotify struct {
	Mchid               string             `json:"mchid"`
	RefundStatus        string             `json:"refund_status"`
	SuccessTime         string             `json:"success_time"`
	UserReceivedAccount string             `json:"user_received_account"`
	OutTradeNo          string             `json:"out_trade_no"`
	TransactionId       string             `json:"transaction_id"`
	OutRefundNo         string             `json:"out_refund_no"`
	RefundId            string             `json:"refund_id"`
	Amount              RefundNotifyAmount `json:"amount"`
}

type RefundNotifyAmount struct {
	Total       int `json:"total"`
	Refund      int `json:"refund"`
	PayerTotal  int `json:"payer_total"`
	PayerRefund int `json:"payer_refund"`
}
