package servicePointService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/servicePointDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/allInPayUserService"
	"base/service/authenticationService"
	"base/service/bankAccountService"
	"base/service/entityService"
	"base/service/messageService"
	"base/service/routeService"
	"base/service/servicePointCommissionService"
	"base/service/userService"
	"base/service/yeeMerchantService"
	"base/types"
	"base/util"
	"context"
	"errors"
	"time"

	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 服务点
type ServiceInterface interface {
	Create(ctx context.Context, req types.ServicePointApplyReq) error
	CreateSecond(ctx context.Context, userID, parentPointID primitive.ObjectID, req types.ServicePointSecondCreateReq) error
	GetByUser(ctx context.Context, userID primitive.ObjectID, level model.ServicePointLevel) (model.ServicePoint, error)
	ListByLocation(ctx context.Context, longitude, latitude float64) ([]model.ServicePoint, error)
	ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, isOpen bool) ([]model.ServicePoint, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.ServicePoint, error)
	UpdateOpen(ctx context.Context, id primitive.ObjectID, isOpen bool) error
	UpdateServiceFee(ctx context.Context, id primitive.ObjectID, supplierServiceFee, deliverServiceFee float64) error
	UpdateHeadImg(ctx context.Context, id primitive.ObjectID, shopHeadImg model.FileInfo) error
	UpdateScope(ctx context.Context, id primitive.ObjectID, scope []model.PointLocation) error
	UpdateCenterLocation(ctx context.Context, id primitive.ObjectID, lng, lat float64) error
	List(filter bson.M, page, limit int64) ([]model.ServicePoint, int64, error)
	ListCus(ctx context.Context, filter bson.M) ([]model.ServicePoint, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.ServicePoint, error)
	CheckOneExist(ctx context.Context, id primitive.ObjectID) error
	InitScope(ctx context.Context, centerPoint model.PointLocation) ([]model.PointLocation, error)

	CalcTransportFee(ctx context.Context, weight int, unitPrice int) (int, error)
}

type servicePointService struct {
	mdb                     *mongo.Database
	rdb                     *redis.Client
	servicePointDao         servicePointDao.DaoInt
	msg                     messageService.ServiceInterface
	servicePointCommissions servicePointCommissionService.ServiceInterface
	routeService            routeService.ServiceInterface
	entityService           entityService.ServiceInterface
	bankAccountS            bankAccountService.ServiceInterface
	authenticationS         authenticationService.ServiceInterface
	yeeMerchantS            yeeMerchantService.ServiceInterface
	UserS                   userService.ServiceInterface

	AllInPayS     payModule.MemberService
	AllInPayUserS allInPayUserService.ServiceInterface
}

func NewServicePointService() ServiceInterface {
	return servicePointService{
		mdb:                     global.MDB,
		rdb:                     global.RDBDefault,
		servicePointDao:         dao.ServicePointDao,
		msg:                     messageService.NewMessageService(),
		servicePointCommissions: servicePointCommissionService.NewPartnerCommissionService(),
		routeService:            routeService.NewTransportFeeService(),
		entityService:           entityService.NewEntityService(),
		bankAccountS:            bankAccountService.NewBankCardService(),
		UserS:                   userService.NewUserService(),
		authenticationS:         authenticationService.NewAuthenticationService(),
		yeeMerchantS:            yeeMerchantService.NewYeeMerchantService(),

		AllInPayS:     payModule.NewMember(),
		AllInPayUserS: allInPayUserService.NewAllInPayUserService(),
	}
}

func (s servicePointService) UpdateOpen(ctx context.Context, id primitive.ObjectID, isOpen bool) error {
	err := s.servicePointDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{"is_open": isOpen}})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}
func (s servicePointService) UpdateServiceFee(ctx context.Context, id primitive.ObjectID, supplierServiceFee, deliverServiceFee float64) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"supplier_service_fee": supplierServiceFee,
		"deliver_service_fee":  deliverServiceFee,
		"updated_at":           milli,
	}
	err := s.servicePointDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

func (s servicePointService) UpdateHeadImg(ctx context.Context, id primitive.ObjectID, shopHeadImg model.FileInfo) error {
	now := time.Now().UnixMilli()
	update := bson.M{
		"updated_at":    now,
		"shop_head_img": shopHeadImg,
	}
	err := s.servicePointDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

func (s servicePointService) UpdateScope(ctx context.Context, id primitive.ObjectID, scope []model.PointLocation) error {
	now := time.Now().UnixMilli()
	update := bson.M{
		"updated_at":     now,
		"delivery_scope": scope,
	}
	err := s.servicePointDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

func (s servicePointService) UpdateCenterLocation(ctx context.Context, id primitive.ObjectID, lng, lat float64) error {
	update := bson.M{
		"location.longitude": lng,
		"location.latitude":  lat,
	}
	err := s.servicePointDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

func (s servicePointService) CheckOneExist(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}
	count, err := s.servicePointDao.Count(ctx, filter)
	if err != nil {
		return err
	}
	if count != 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "供应商不存在")
	}
	return nil
}

func (s servicePointService) Create(ctx context.Context, req types.ServicePointApplyReq) error {
	if len(req.AuthenticationReq.UserID) != 24 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请选择负责人")
	}

	userID, err := util.ConvertToObjectWithCtx(ctx, req.AuthenticationReq.UserID)
	if err != nil {
		return err
	}
	user, err := s.UserS.Get(ctx, userID)
	if err != nil {
		return err
	}

	getByUser, err := s.GetByUser(ctx, userID, model.ServicePointLevelFirst)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if getByUser.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在信息，请勿重复申请")
	}

	now := time.Now().UnixMilli()

	data := model.ServicePoint{
		ID:          primitive.NewObjectID(),
		UserID:      userID,
		Name:        req.Name,
		DeliverType: req.DeliverType,
		ContactUser: req.AuthenticationReq.ContactUserName,
		Location:    req.AuthenticationReq.Location,
		Address:     req.AuthenticationReq.Address,
		ShopHeadImg: req.ShopHeadImg,
		IsOpen:      false,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err = s.authenticationS.CheckParam(ctx, req.AuthenticationReq)
	if err != nil {
		return err
	}

	err = s.servicePointDao.Create(ctx, data)
	if err != nil {
		return err
	}

	err = s.authenticationS.Create(ctx, user.Mobile, req.AuthenticationReq, userID, data.ID, model.ObjectTypeServicePoint, pays.MemberTypeCompany)
	if err != nil {
		return err
	}

	return nil
}

func (s servicePointService) CreateSecond(ctx context.Context, userID, parentPointID primitive.ObjectID, req types.ServicePointSecondCreateReq) error {
	getByUser, err := s.GetByUser(ctx, userID, model.ServicePointLevelSecond)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if getByUser.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在信息，请勿重复申请")
	}

	now := time.Now().UnixMilli()

	scope, err := s.InitScope(ctx, model.PointLocation{
		Longitude: req.Location.Longitude,
		Latitude:  req.Location.Latitude,
	})
	if err != nil {
		return err
	}

	data := model.ServicePoint{
		ID:                   primitive.NewObjectID(),
		UserID:               userID,
		ParentPointID:        parentPointID,
		ParentPointName:      "云南中心仓",
		Name:                 req.Name,
		DeliverType:          req.DeliverType,
		ContactUser:          req.UserName,
		ContactMobile:        req.Mobile,
		Location:             req.Location,
		Address:              req.Address,
		Level:                model.ServicePointLevelSecond,
		ServiceChargePercent: req.ServiceChargePercent,
		TransportUnitPrice:   req.TransportUnitPrice,
		DeliveryScope:        scope,
		IsOpen:               false,
		CreatedAt:            now,
		UpdatedAt:            now,
	}

	err = s.servicePointDao.Create(ctx, data)
	if err != nil {
		return err
	}

	err = s.yeeMerchantS.CreateByPoint(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s servicePointService) InitScope(ctx context.Context, centerPoint model.PointLocation) ([]model.PointLocation, error) {
	points := []model.PointLocation{
		{
			Longitude: centerPoint.Longitude + 0.0015,
			Latitude:  centerPoint.Latitude,
		},
		{
			Longitude: centerPoint.Longitude,
			Latitude:  centerPoint.Latitude + 0.0015,
		},
		{
			Longitude: centerPoint.Longitude - 0.0015,
			Latitude:  centerPoint.Latitude - 0.0015,
		},
	}

	return points, nil
}

func (s servicePointService) ListByLocation(ctx context.Context, longitude, latitude float64) ([]model.ServicePoint, error) {
	list, err := s.servicePointDao.List(ctx, bson.M{
		"is_open":    true,
		"deleted_at": 0,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s servicePointService) ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, isOpen bool) ([]model.ServicePoint, error) {
	list, err := s.servicePointDao.List(ctx, bson.M{
		"warehouse_id": warehouseID,
		"is_open":      isOpen,
		"deleted_at":   0,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s servicePointService) Get(ctx context.Context, id primitive.ObjectID) (model.ServicePoint, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		filter := bson.M{
			"_id": id,
		}
		data, err := s.servicePointDao.Get(ctx, filter)
		if err != nil {
			return model.ServicePoint{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s servicePointService) GetByUser(ctx context.Context, userID primitive.ObjectID, level model.ServicePointLevel) (model.ServicePoint, error) {
	filter := bson.M{
		"user_id": userID,
		"level":   level,
	}
	i, err := s.servicePointDao.Get(ctx, filter)
	if err != nil {
		return model.ServicePoint{}, err
	}
	return i, nil
}

func (s servicePointService) List(filter bson.M, page, limit int64) ([]model.ServicePoint, int64, error) {
	list, i, err := s.servicePointDao.ListByPage(context.Background(), filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s servicePointService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.ServicePoint, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"deleted_at": 0,
	}
	list, err := s.servicePointDao.List(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s servicePointService) ListCus(ctx context.Context, filter bson.M) ([]model.ServicePoint, error) {
	list, err := s.servicePointDao.List(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s servicePointService) CalcTransportFee(ctx context.Context, weight int, unitPrice int) (int, error) {
	weightDe := decimal.NewFromInt(int64(weight)).Div(decimal.NewFromInt(1000))
	unitPriceDe := decimal.NewFromInt(int64(unitPrice))

	amountNum := weightDe.Mul(unitPriceDe).Round(0)
	intPart := int(amountNum.IntPart())

	return intPart, nil
}
