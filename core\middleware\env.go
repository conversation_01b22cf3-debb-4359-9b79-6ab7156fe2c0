package middleware

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"github.com/gin-gonic/gin"
	"strconv"
)

func CheckEnv(ctx *gin.Context) {
	e := ctx.GetHeader("X-Env")
	if len(e) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrEnvMissing, nil))
		ctx.Abort()
		return
	}
	i, err := strconv.Atoi(e)
	if err != nil {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrEnvMissing, nil))
		ctx.Abort()
		return
	}
	if _, ok := model.ObjectTypeMsg[model.ObjectType(i)]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrEnvValue, nil))
		ctx.Abort()
		return
	}

	ctx.Set("X-Env", i)
	ctx.Next()
}

func getEnv(ctx *gin.Context) model.ObjectType {
	i := ctx.GetInt("X-Env")
	return model.ObjectType(i)
}
