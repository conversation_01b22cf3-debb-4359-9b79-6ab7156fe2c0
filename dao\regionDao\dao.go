package regionDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Region) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Region, error)
	List(ctx context.Context, filter bson.M) ([]model.Region, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Region, int64, error)
}

type regionDao struct {
	db *mongo.Collection
}

func (s regionDao) Create(ctx context.Context, data model.Region) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s regionDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s regionDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s regionDao) Get(ctx context.Context, filter bson.M) (model.Region, error) {
	var data model.Region
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Region{}, err
	}
	return data, nil
}

// List 查询
func (s regionDao) List(ctx context.Context, filter bson.M) ([]model.Region, error) {
	var list []model.Region

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// ListByPage 查询
func (s regionDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Region, int64, error) {
	var list []model.Region
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func NewRegionDao(collect string) DaoInt {
	return regionDao{
		db: global.MDB.Collection(collect),
	}
}
