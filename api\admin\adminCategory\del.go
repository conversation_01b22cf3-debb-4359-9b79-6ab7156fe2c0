package adminCategory

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/productService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
)

// Del 分类删除
func Del(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ID, "id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := productService.NewProductService().DeleteCategory(ctx, id)
	if errors.Is(err, xerr.XerrCategoryExistProduct) {
		xhttp.RespSuccess(ctx, list)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = list

	xhttp.RespSuccess(ctx, nil)
}
