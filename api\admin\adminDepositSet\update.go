package adminDeposiSet

import (
	"base/core/xhttp"
	"base/service/depositSetService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Update 更新
func Update(ctx *gin.Context) {
	var req = struct {
		ID     string `json:"id" validate:"len=24"`
		Amount int    `json:"amount" validate:"min=0"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = depositSetService.NewDepositSetService().Update(ctx, id, req.Amount)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
