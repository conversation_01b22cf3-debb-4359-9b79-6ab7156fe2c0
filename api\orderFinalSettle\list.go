package orderFinalSettle

import (
	"base/core/xhttp"
	"base/service/orderFinalSettleService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// ListBySupplierID 根据供应商ID查询最终结算记录列表
func ListBySupplierID(c *gin.Context) {
	var req struct {
		SupplierID string `json:"supplier_id"`
		Page       int64  `json:"page"`
		Limit      int64  `json:"limit"`
	}

	err := xhttp.Parse(c, &req)
	if err != nil {
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	// 转换ObjectID
	supplierID, err := util.ConvertToObjectWithCtx(c, req.SupplierID)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	// 构建查询条件
	filter := bson.M{
		"supplier_id": supplierID,
	}

	// 分页查询
	list, total, err := orderFinalSettleService.NewService().ListByPage(c, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	xhttp.RespSuccess(c, gin.H{
		"list":  list,
		"total": total,
		"page":  req.Page,
		"limit": req.Limit,
	})
}

// ListByMonth 按月查询订单最终结算记录
func ListByMonth(c *gin.Context) {
	var req struct {
		SupplierID     string `json:"supplier_id"`
		MonthTimestamp int64  `json:"month_timestamp"` // 开始时间戳
		Page           int64  `json:"page"`
		Limit          int64  `json:"limit"`
	}

	err := xhttp.Parse(c, &req)
	if err != nil {
		return
	}

	// 构建查询条件
	filter := bson.M{}

	// 供应商ID过滤
	if req.SupplierID != "" {
		supplierID, err := util.ConvertToObjectWithCtx(c, req.SupplierID)
		if err != nil {
			xhttp.RespErr(c, err)
			return
		}
		filter["supplier_id"] = supplierID
	}

	beginTimestamp, endTimestamp, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	// 时间范围过滤
	filter["order_created_at"] = bson.M{
		"$gte": beginTimestamp,
		"$lte": endTimestamp,
	}

	// 分页查询
	list, total, err := orderFinalSettleService.NewService().ListByPage(c, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	xhttp.RespSuccessList(c, list, total)
}
