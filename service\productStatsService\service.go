package productStatsService

import (
	"base/global"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/productService"
	"context"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Get(ctx context.Context, id primitive.ObjectID) (model.ProductStats, error)
	Remove(ctx context.Context, id primitive.ObjectID) error
}

type productStatsService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	orderS   orderService.ServiceInterface
	productS productService.ServiceInterface
	refundS  orderRefundService.ServiceInterface
}

func NewProductStatsService() ServiceInterface {
	return productStatsService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		orderS:   orderService.NewOrderService(),
		productS: productService.NewProductService(),
		refundS:  orderRefundService.NewOrderRefundService(),
	}
}

func (s productStatsService) Get(ctx context.Context, id primitive.ObjectID) (model.ProductStats, error) {
	m := get(s.rdb, id)
	if m.BuyerID == primitive.NilObjectID {

		var data model.ProductStats
		data.BuyerID = id
		// 购买量
		countProduct, countBuyer, orderNum, err := s.orderS.CountBuyer(ctx, id)
		if err != nil {
			return model.ProductStats{}, err
		}
		data.TotalNum = countProduct
		data.TotalBuyerNum = countBuyer
		data.TotalOrderNum = orderNum

		// 售后次数
		countByProduct, err := s.refundS.CountByProduct(ctx, id)
		if err != nil {
			return model.ProductStats{}, err
		}
		data.AfterSaleOrderNum = countByProduct

		// 售后率

		totalProduct := decimal.NewFromInt(countProduct)
		totalRefundProduct := decimal.NewFromInt(countByProduct)
		var rate float64
		if countProduct != 0 && countByProduct != 0 {
			f, exact := totalRefundProduct.Div(totalProduct).Mul(decimal.NewFromInt(100)).Round(2).Float64()
			_ = exact
			rate = f
		}

		data.AfterSaleRate = rate

		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s productStatsService) Remove(ctx context.Context, id primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("buyerStatsService error:%v", err)
			return
		}
	}()

	del(s.rdb, id.Hex())

	return nil
}
