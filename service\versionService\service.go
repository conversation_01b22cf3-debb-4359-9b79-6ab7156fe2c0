package versionService

import (
	"base/dao"
	"base/dao/versionDao"
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	GetByVersion(ctx context.Context, version string) (model.Version, error)
	Create(ctx context.Context, version string, forceUpdate bool) error
	List(ctx context.Context, page, limit int64) ([]model.Version, int64, error)
}

type versionService struct {
	mdb        *mongo.Database
	rdb        *redis.Client
	versionDao versionDao.DaoInt
}

func NewVersionService() ServiceInterface {
	return versionService{
		mdb:        global.MDB,
		rdb:        global.RDBDefault,
		versionDao: dao.VersionDao,
	}
}

func (s versionService) Create(ctx context.Context, version string, forceUpdate bool) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		now := time.Now().UnixMilli()
		if forceUpdate {
			err = s.versionDao.UpdateMany(sessCtx, bson.M{}, bson.M{
				"$set": bson.M{
					"force_update": true,
					"deleted_at":   now,
				},
			})
			if err != nil {
				return nil, err
			}
		}

		data := model.Version{
			ID:          primitive.NewObjectID(),
			Version:     version,
			ForceUpdate: forceUpdate,
			CreatedAt:   now,
		}
		err = s.versionDao.Create(ctx, data)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s versionService) GetByVersion(ctx context.Context, version string) (model.Version, error) {
	filter := bson.M{
		"version": version,
	}
	get, err := s.versionDao.Get(ctx, filter)
	if err != nil {
		return model.Version{}, err
	}
	return get, nil
}

func (s versionService) List(ctx context.Context, page, limit int64) ([]model.Version, int64, error) {
	versions, count, err := s.versionDao.ListByPage(ctx, bson.M{}, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return versions, count, nil
}
