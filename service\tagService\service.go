package tagService

import (
	"base/dao"
	"base/dao/tagDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Create(ctx context.Context, categoryID primitive.ObjectID, title string) error
	List(ctx context.Context, categoryID primitive.ObjectID) ([]model.Tag, error)
	Delete(ctx context.Context, ids []primitive.ObjectID) error
	UpdateTitle(ctx context.Context, id primitive.ObjectID, title string) error
	UpdateStatus(ctx context.Context, id primitive.ObjectID, valid bool) error
}

type tagService struct {
	tagDao tagDao.DaoInt
}

func (s tagService) Create(ctx context.Context, categoryID primitive.ObjectID, title string) error {
	data := model.Tag{
		ID:         primitive.NewObjectID(),
		CategoryID: categoryID,
		Title:      title,
		Valid:      true,
		CreatedAt:  time.Now().UnixMilli(),
	}
	err := s.tagDao.Create(ctx, data)
	return err
}

func (s tagService) List(ctx context.Context, categoryID primitive.ObjectID) ([]model.Tag, error) {
	filter := bson.M{
		"category_id": categoryID,
	}
	list, err := s.tagDao.List(ctx, filter)
	return list, err
}

func (s tagService) Delete(ctx context.Context, ids []primitive.ObjectID) error {
	err := s.tagDao.Delete(ctx, ids)
	return err
}

func (s tagService) UpdateTitle(ctx context.Context, id primitive.ObjectID, title string) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"$set": bson.M{
			"title": title,
		},
	}
	err := s.tagDao.Update(ctx, filter, update)
	return err
}

func (s tagService) UpdateStatus(ctx context.Context, id primitive.ObjectID, valid bool) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"$set": bson.M{
			"valid": valid,
		},
	}
	err := s.tagDao.Update(ctx, filter, update)
	return err
}

func NewTagService() ServiceInterface {
	return tagService{
		tagDao: dao.TagDao,
	}
}
