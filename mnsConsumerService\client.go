package mnsConsumerService

import (
	"base/api/product"
	"base/core/xerr"
	"base/global"
	"base/model"
	"base/service/buyerStatsService"
	"base/service/couponUserService"
	"base/service/deliverFeeDetailService"
	"base/service/deliverNoteService"
	"base/service/fileservice"
	"base/service/fruitClassService"
	"base/service/integralOrderService"
	"base/service/integralRecordService"
	"base/service/orderAdjustSettleService"
	"base/service/orderAgentPayService"
	"base/service/orderDebtService"
	"base/service/orderFinalSettleService"
	"base/service/orderPointService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/orderWarehouseService"
	"base/service/payOrderService"
	"base/service/productOfflineService"
	"base/service/productStatsService"
	"base/service/supplierService"
	"base/service/withdrawApplyOrderService"
	"base/util"
	"context"
	"encoding/base64"
	"errors"
	_ "net/http/pprof"
	"strings"

	ali_mns "github.com/aliyun/aliyun-mns-go-sdk"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func InitMNSQueue() {
	c := global.MNSConf
	cli := ali_mns.NewAliMNSClient(c.Url, c.AccessKeyId, c.AccessKeySecret)
	queue := ali_mns.NewMNSQueue(global.QueueOrder, cli)

	go func() {
		// 收
		for {
			endChan := make(chan int)
			respChan := make(chan ali_mns.MessageReceiveResponse)
			errChan := make(chan error)
			go func() {
				select {
				case resp := <-respChan:
					{
						//marshal, _ := json.Marshal(resp)
						//zap.S().Infof("接收消息:%v", string(marshal))
						//zap.S().Info("change the visibility: ", resp.ReceiptHandle)
						if ret, e := queue.ChangeMessageVisibility(resp.ReceiptHandle, 30); e != nil {
							zap.S().Errorf("mns ChangeMessageVisibility error,%v", e)
						} else {
							e = DealMessage(resp.MessageBody, resp.MessageId)
							if e != nil {
								zap.S().Errorf("mns 处理消息错误,消息id：%s，错误：%v", resp.MessageId, e.Error())
								endChan <- 1
							} else {
								//zap.S().Infof("visibility changed,%v", ret)
								//zap.S().Infof("delete it now: %v", ret.ReceiptHandle)
								if e := queue.DeleteMessage(ret.ReceiptHandle); e != nil {
									zap.S().Errorf("mns 删除失败,%v", e)
								}
								endChan <- 1
							}
						}
					}
				case err := <-errChan:
					_ = err
					//zap.S().Infof("mns no message:%v", err)
					endChan <- 1
				}
			}()
			//接收消息。
			queue.ReceiveMessage(respChan, errChan, 30)
			<-endChan
		}
	}()
}

func DealMessage(msg, id string) error {
	decodeString, err := base64.StdEncoding.DecodeString(msg)
	if err != nil {
		zap.Error(err)
		return err
	}
	s := string(decodeString)
	if strings.Contains(s, "@") {
		split := strings.Split(s, "@")
		if len(split) != 2 {
			return nil
		}

		content := split[1]

		if content == "eyJidXllcl9pZCI6IjY4NTc0ZTUwMThmYmYxYzExNGIyOWVkMyJ9" || content == "eyJwcm9kdWN0X2lkIjoiNjdlOGU4MWE2NjViOTdhZWY3YTY4YjBiIn0=" {
			return nil
		}

		switch split[0] {
		case "close":
			//	 关闭父单
			err := CloseOrder(split[1])
			if err != nil {
				return err
			}
			return nil
		case "deleteProduct":
			err := product.DeleteProduct(split[1])
			if err != nil {
				return err
			}
			return nil
		case "checkWithdraw":
			err := withdrawApplyOrderService.NewWithdrawApplyOrderService().ActiveUpdateStatus(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case "removeDebtPayInfo": // 移除补差单支付串
			err := orderDebtService.NewOrderDebtService().RemoveDebtPayInfo(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case "recoverProductStock":
			err := orderService.NewOrderService().RecoverStockByOrder(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case "recoverProductStockByParent":
			err := orderService.NewOrderService().RecoverStockByParentOrder(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil

		case model.MNSProductUp:
			var data model.MNSProduct
			err := util.DecodeMNSInfo(id, split[1], &data)
			if err != nil {
				return err
			}
			err = product.UpProduct(context.Background(), data.ProductID)
			if err != nil {
				return err
			}
			return nil

		case model.MNSProductDown:
			var data model.MNSProduct
			err := util.DecodeMNSInfo(id, split[1], &data)
			if err != nil {
				return err
			}
			err = product.DownProduct(context.Background(), data.ProductID)
			if err != nil {
				return err
			}
			return nil
		//case model.MNSSyncSortData: // 同步分拣数据
		//	err := orderWarehouseService.NewOrderWarehouseService().SyncSortData(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		//case "debtAgentPay": // 补差单代付
		//	err := orderAgentPayService.NewOrderAgentPayService().ToAgentPayForDebt(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		//case model.MNSCheckAfterShip:
		//	err := orderWarehouseService.NewOrderWarehouseService().CheckAfterShip(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		//case model.MNSDoAfterShipRefund: // 执行发货退款
		//	err := orderRefundService.NewOrderRefundService().DoAfterShipRefund(context.Background(), split[1])
		//	if err != nil {
		//		zap.S().Errorf("doAfterShipRefund 错误%v", err)
		//		return err
		//	}
		//	return nil
		case model.MNSSyncRefundData: // 同步订单的退款数据
			err := orderRefundService.NewOrderRefundService().SyncRefundData(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil

		case model.MNSSyncQualityRefundData: // 同步订单的品控退款数据
			err := orderRefundService.NewOrderRefundService().SyncQualityRefundData(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		//case "checkOrderHasRefundAll": // 检查是否全退
		//	err := orderRefundService.NewOrderRefundService().CheckOrderHasRefundAll(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		case "closeAllInPayBizOrderNo":
			err := payOrderService.NewPayOrderService().CloseByBizOrderNo(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case "createFruitClass":
			err := fruitClassService.NewFruitClassService().CreateFruitClassBatch(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		//case model.MNSRefundDeliverAlone:
		//	err := orderRefundService.NewOrderRefundService().RefundDeliver(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		//case model.MNSCouponActiveReward:
		//	// 激活代金券
		//	err := couponAccountService.NewCouponAccountService().ActiveByNewUser(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		//case model.MNSCancelOrderCoupon:
		//	// 订单取消-代金券
		//	//err := orderRefundService.NewOrderRefundService().CancelOrderCoupon(context.Background(), split[1])
		//	//if err != nil {
		//	//	return err
		//	//}
		//	return nil
		//case model.MNSCouponCheckAccountValid:
		//	// 检查代金券有效性
		//	err := couponAccountService.NewCouponAccountService().CheckAccountValid(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		//case model.MNSInviteUpdateStatus:
		//	// 检查代金券有效性
		//	err := inviteService.NewInviteService().UpdateStatus(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		case model.MNSOrderAutoReceive:
			// 检查代金券有效性
			err := orderPointService.NewOrderPointService().AutoReceive(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		//case model.MNSOrderCheckRemoveCommission:
		//	err := orderService.NewOrderService().CheckRemoveCommission(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		case model.MNSOrderDivideNormal:
			// 订单-分账
			var data model.MNSOrder
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			objectID, err := util.ConvertToObjectWithCtx(context.Background(), data.OrderID)
			if err != nil {
				return err
			}

			err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(context.Background(), objectID)
			if err != nil {
				return err
			}
			return nil
		case model.MNSOrderDivideFlatCheck:
			// 订单-分账
			var data model.MNSAgentPay
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			objectID, err := util.ConvertToObjectWithCtx(ctx, data.AgentPayID)
			if err != nil {
				return err
			}
			err = orderAgentPayService.NewOrderAgentPayService().YeeCheckDivideFlat(ctx, objectID)
			if err != nil {
				return err
			}
			return nil

		case model.MNSOrderDivideEndCheck:
			// 订单-分账
			var data model.MNSAgentPay
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			objectID, err := util.ConvertToObjectWithCtx(ctx, data.AgentPayID)
			if err != nil {
				return err
			}
			err = orderAgentPayService.NewOrderAgentPayService().YeeCheckDivideEnd(ctx, objectID)
			if err != nil {
				return err
			}
			return nil
		case model.MNSOrderDivideDeliver:
			// 配送费-分账
			var data model.MNSParentOrder
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			objectID, err := util.ConvertToObjectWithCtx(ctx, data.ParentOrderID)
			if err != nil {
				return err
			}
			err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayDeliver(ctx, objectID)
			if err != nil {
				return err
			}
			return nil
		case model.MNSOrderDivideDebt:
			// 补差-订单分账
			var data model.MNSDebtOrder
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			objectID, err := util.ConvertToObjectWithCtx(ctx, data.DebtOrderID)
			if err != nil {
				return err
			}
			err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayDebtOrder(ctx, objectID)
			if err != nil {
				return err
			}
			return nil
		case model.MNSOrderCalcProfit:
			// 订单-利润核算
			return nil
		case model.MNSOrderCloseParent:
			//	 关闭父单
			var data model.MNSOrder
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			objectID, err := util.ConvertToObjectWithCtx(ctx, data.OrderID)
			if err != nil {
				return err
			}

			err = orderService.NewOrderService().Close(context.Background(), objectID)
			if err != nil {
				return err
			}

			return nil

		case model.MNSOrderShipSettle:
			// 发货结算
			var data model.MNSOrderList
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}
			var ids []primitive.ObjectID
			ctx := context.Background()
			for _, v := range data.OrderIDList {
				objectID, err := util.ConvertToObjectWithCtx(ctx, v)
				if err != nil {
					return err
				}
				ids = append(ids, objectID)
			}
			err = orderDebtService.NewOrderDebtService().DoShipSettleRefund(ctx, ids)
			if err != nil {
				return err
			}
			return nil
		case model.MNSIntegralGenerate:
			// 积分
			err := integralRecordService.NewIntegralRecordService().Generate(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case model.MNSIntegralConsume:
			// 积分
			err := integralRecordService.NewIntegralRecordService().Consume(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case model.MNSDeliverNoteGenerate:
			// 配送单生成
			err := deliverNoteService.NewDeliverNoteService().Generate(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil

		//case model.MNSCloseDepositOrder:
		//	// 关闭充值单
		//	err := buyerBalanceOrderService.NewBuyerBalanceOrderService().CloseByBizOrderNo(context.Background(), split[1])
		//	if err != nil {
		//		return err
		//	}
		//	return nil
		case model.MNSCloseIntegralOrder:
			// 关闭积分订单
			err := integralOrderService.NewIntegralOrderService().CloseByBizOrderNo(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case model.MNSIntegralAgentPay:
			// 关闭积分订单
			err := integralOrderService.NewIntegralOrderService().AgentPay(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil
		case model.MNSCheckOverWeight:
			err := orderWarehouseService.NewOrderWarehouseService().CheckOverWeight(context.Background(), split[1])
			if err != nil {
				return err
			}
			return nil

		case model.MNSRefundChangeAuditor:
			err := orderRefundService.NewOrderRefundService().ChangeRefundAuditor(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSRefundComplete:
			err := orderRefundService.NewOrderRefundService().CompleteRefund(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSOrderAdjustSettleRefund:
			err := orderAdjustSettleService.NewService().ProcessRefund(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSProductOfflineCheck:
			err := productOfflineService.NewProductOfflineService().OfflineCheck(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSDeliverFeeDetailGenerate:
			var data model.MNSDeliverFeeDetail
			err := util.DecodeMNSInfo(id, split[1], &data)
			if err != nil {
				return err
			}
			parentOrderID, err := primitive.ObjectIDFromHex(data.ParentOrderID)
			if err != nil {
				zap.S().Errorf("配送费明细生成父订单ID：%s转换失败：%s", data.ParentOrderID, err.Error())
				return err
			}
			err = deliverFeeDetailService.NewDeliverFeeDetailService().CreateFromParentOrder(context.Background(), parentOrderID)
			if err != nil {
				zap.S().Errorf("配送费明细生成失败:%s,父单：%s", err.Error(), data.ParentOrderID)
				return err
			}

			_ = content
			return nil

		case model.MNSCouponUserRefresh:
			err = couponUserService.NewCouponUserService().Refresh(context.Background(), content)
			if err != nil {
				return err
			}
			return nil
		case model.MNSCouponUserRestore:
			err = couponUserService.NewCouponUserService().Restore(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSCouponUserUsed:
			err = couponUserService.NewCouponUserService().SendCouponUserUsed(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSProductSaleStatsExport:
			err := fileservice.NewFileService().ProductSaleStatsExport(context.Background(), content)
			if err != nil {
				return err
			}
			return nil

		case model.MNSOrderFinalSettle:
			// 订单最终结算
			var data model.MNSOrder
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			objectID, err := util.ConvertToObjectWithCtx(ctx, data.OrderID)
			if err != nil {
				return err
			}

			err = orderFinalSettleService.NewService().CreateFromOrder(ctx, objectID)
			if err != nil {
				return err
			}
			return nil

		case model.MNSSupplierFreezeAmount:
			// 供应商冻结金额
			var data model.MNSSupplierFreezeAmountInfo
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			supplierID, err := util.ConvertToObjectWithCtx(ctx, data.SupplierID)
			if err != nil {
				return err
			}

			err = supplierService.NewSupplierService().AddFrozenBalanceAmount(ctx, supplierID, data.Amount)
			if err != nil {
				return err
			}
			return nil
		case model.MNSRemoveBuyerStats:
			var data model.MNSID
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			id, err := util.ConvertToObjectWithCtx(ctx, data.ID)
			if err != nil {
				return err
			}

			buyerStatsService.NewBuyerStatsService().Remove(ctx, id)
			return nil
		case model.MNSRemoveProductStats:
			var data model.MNSID
			err := util.DecodeMNSInfo(id, content, &data)
			if err != nil {
				return err
			}

			ctx := context.Background()

			id, err := util.ConvertToObjectWithCtx(ctx, data.ID)
			if err != nil {
				return err
			}

			productStatsService.NewProductStatsService().Remove(ctx, id)
			return nil
		}
		zap.S().Errorf("no case: %s", s)
		return nil
	}

	zap.S().Errorf("no enter: %s", s)
	return nil
}

// CloseOrder 关闭父单
func CloseOrder(parentOrderID string) error {
	id, err := util.ConvertToObjectWithNote(parentOrderID, "parentOrderID")
	if err != nil {
		zap.S().Errorf("关闭失败%v", err)
		return err
	}
	err = orderService.NewOrderService().Close(context.Background(), id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil
	}
	if err != nil {
		var v *xerr.Err
		ok := errors.As(err, &v)
		if ok && v.Code == xerr.ErrOrderHasPaid {
			zap.S().Infof("关闭订单忽略")
			return nil
		}
		return err
	}

	return nil
}
