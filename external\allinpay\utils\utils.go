package utils

import (
	"crypto/sha1"
	"errors"
	"fmt"
	"regexp"
)

// AesSha1PRNG aes_sha1_prng加密
func AesSha1PRNG(keyBytes []byte, encryptLength int) ([]byte, error) {
	hash := SHA1(SHA1(keyBytes))
	maxLen := len(hash)
	realLen := encryptLength / 8
	if realLen > maxLen {
		return nil, fmt.Errorf("Not Support %d, Only Support Lower then %d [% x]", realLen, maxLen, hash)
	}
	return hash[0:realLen], nil
}

// SHA1 SHA1加密
func SHA1(data []byte) []byte {
	h := sha1.New()
	h.Write(data)
	return h.Sum(nil)
}

func CheckPhone(phone string) error {
	// 匹配规则
	// ^1第一位为一
	// [345789]{1} 后接一位345789 的数字
	// \\d \d的转义 表示数字 {9} 接9位
	// $ 结束符
	regRuler := "^1[23456789]{1}\\d{9}$"

	// 正则调用规则
	reg := regexp.MustCompile(regRuler)
	if reg.MatchString(phone) {
		return nil
	}
	return errors.New("请输入11位手机号")
}
