package notify

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sync"
)

var debtLock sync.Mutex

// YeeTradeOrderDebt 交易下单-补差
func YeeTradeOrderDebt(ctx *gin.Context) {
	debtLock.Lock()
	defer debtLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("交易下单-补差：%s", call)

	var r model.YeeTradeOrderPaySingleNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	var payer model.YeePayerInfo

	err = json.Unmarshal([]byte(r.PayerInfo), &payer)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	r.PayerInfoFormat = payer

	//// 子单信息
	//var subOrders []model.SubOrderInfoList
	//
	//err = json.Unmarshal([]byte(r.SubOrderInfoList), &subOrders)
	//if err != nil {
	//	zap.S().Errorf("解析回调失败：%s", err.Error())
	//	return
	//}
	//
	//for i, order := range subOrders {
	//	yuanInt := util.DealMoneyFloatToYuanInt(order.OrderAmount)
	//	subOrders[i].OrderAmountInt = yuanInt
	//}
	//
	//r.SubOrderInfoListFormat = subOrders

	err = orderDebtService.NewOrderDebtService().YeeNotifyTradeOrderPay(ctx, r)
	if err != nil {
		PayWarning("补差支付", r.OrderId)
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}
	xhttp.NotifyYeeSuccess(ctx)
}

// YeeAccountBookPayDebt 记账簿
func YeeAccountBookPayDebt(ctx *gin.Context) {
	debtLock.Lock()
	defer debtLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("记账簿-补差：%s", call)

	var r model.YeeTradeOrderPaySingleNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	var payer model.YeePayerInfo

	err = json.Unmarshal([]byte(r.PayerInfo), &payer)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	r.PayerInfoFormat = payer

	err = orderDebtService.NewOrderDebtService().YeeNotifyAccountBookPay(ctx, r)
	if err != nil {
		PayWarning("记账簿-补差支付", r.OrderId)
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}
	xhttp.NotifyYeeSuccess(ctx)
}
