package sys

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/jwtService"
	"base/service/userService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ByToken(ctx *gin.Context) {
	var req struct {
		Token string `json:"token" validate:"required"`
	}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	claims, err := jwtService.NewJwtService().ParseToken(req.Token)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	userID := claims.ID
	id, err := util.ConvertToObjectWithNote(userID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	user, err := userService.NewUserService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, id)
	if err != nil {

	}

	r := res{
		UserID:    user.ID,
		Mobile:    user.Mobile,
		BuyerID:   buyer.ID,
		BuyerName: buyer.BuyerName,
		Address:   buyer.Address,
		Location:  buyer.Location.Address,
	}

	xhttp.RespSuccess(ctx, r)
}

type res struct {
	UserID    primitive.ObjectID `json:"user_id"`
	BuyerID   primitive.ObjectID `json:"buyer_id"`
	Mobile    string             `json:"mobile"`
	BuyerName string             `json:"buyer_name"`
	Address   string             `json:"address"`
	Location  string             `json:"location"`
}
