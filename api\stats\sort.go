package stats

type SingleList []SingleByCategory

func (array SingleList) Len() int {
	return len(array)
}

func (array SingleList) Less(i, j int) bool {
	return array[i].CategoryName < array[j].CategoryName //从小到大， 若为大于号，则从大到小
}

func (array SingleList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

type SingleListQuality []SingleByCategoryQuality

func (array SingleListQuality) Len() int {
	return len(array)
}

func (array SingleListQuality) Less(i, j int) bool {
	return array[i].CategoryName < array[j].CategoryName //从小到大， 若为大于号，则从大到小
}

func (array SingleListQuality) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

// supplierMonthSort 供应商月销售额统计
type supplierMonthSort []PerSupplier

func (array supplierMonthSort) Len() int {
	return len(array)
}

func (array supplierMonthSort) Less(i, j int) bool {
	return array[i].TotalAmount > array[j].TotalAmount //从小到大， 若为大于号，则从大到小
}

func (array supplierMonthSort) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}
