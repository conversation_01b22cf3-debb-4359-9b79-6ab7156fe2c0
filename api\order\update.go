package order

import (
	"base/core/xhttp"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// UpdateOrderNote 更新订单备注
func UpdateOrderNote(ctx *gin.Context) {
	var req = struct {
		OrderID   string `json:"order_id"`
		OrderNote string `json:"order_note"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"_id": id,
	}

	update := bson.M{
		"order_note": req.OrderNote,
	}

	err = orderService.NewOrderService().UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateServicePointOrderNote 更新服务仓订单备注
func UpdateServicePointOrderNote(ctx *gin.Context) {
	var req = struct {
		OrderID          string `json:"order_id"`
		ServicePointNote string `json:"service_point_note"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"_id": id,
	}

	update := bson.M{
		"service_point_note": req.ServicePointNote,
	}

	err = orderService.NewOrderService().UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
