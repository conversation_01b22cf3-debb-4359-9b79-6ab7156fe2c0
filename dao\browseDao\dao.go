package browseDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"log"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Browse) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	DeleteOne(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Browse, error)
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.Browse, int64, error)
	ListForBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.BrowseStats, int64, error)
}

type browseDao struct {
	db *mongo.Collection
}

func (s browseDao) ListForBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.BrowseStats, int64, error) {
	pip := bson.A{
		bson.D{{"$match", bson.D{{"buyer_id", buyerID}}}},
		bson.D{{"$sort", bson.D{{"created_at", -1}}}},
		bson.D{
			{"$group",
				bson.D{
					{"_id",
						bson.D{
							{"buyer_id", "$buyer_id"},
							{"product_id", "$product_id"},
						},
					},
					{"time", bson.D{{"$first", "$created_at"}}},
					{"count", bson.D{{"$sum", 1}}},
				},
			},
		},
		bson.D{
			{"$project",
				bson.D{
					{"product_id", "$_id.product_id"},
					{"time", "$time"},
					{"count", "$count"},
				},
			},
		},
		bson.D{{"$sort", bson.D{{"time", -1}}}},
		bson.D{{"$skip", page - 1}},
		bson.D{{"$limit", limit}},
	}

	aggregate, err := s.db.Aggregate(ctx, pip)
	if err != nil {
		log.Println(err)
	}
	_ = aggregate
	var list []model.BrowseStats
	err = aggregate.All(ctx, &list)
	if err != nil {
		log.Println(err)
	}

	countPip := bson.A{
		bson.D{{"$match", bson.D{{"buyer_id", buyerID}}}},
		bson.D{
			{"$group",
				bson.D{
					{"_id",
						bson.D{
							{"buyer_id", "$buyer_id"},
							{"product_id", "$product_id"},
						},
					},
					{"time", bson.D{{"$first", "$created_at"}}},
					{"count", bson.D{{"$sum", 1}}},
				},
			},
		},
		bson.D{{"$count", "total"}},
	}
	c, err := s.db.Aggregate(ctx, countPip)
	if err != nil {
		return nil, 0, err
	}
	_ = c
	var a bson.A
	err = c.All(ctx, &a)
	if err != nil {
		return nil, 0, err
	}
	var total int64
	if len(a) > 0 {
		count := a[0].(bson.D)
		b := count[0]
		finalCount, ok := b.Value.(int32)
		if !ok {
			log.Println("采购商浏览统计，总数转换错误")
			return nil, 0, nil
		}
		total = int64(finalCount)
	}

	return list, total, nil
}

func (s browseDao) Create(ctx context.Context, data model.Browse) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s browseDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
func (s browseDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s browseDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s browseDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s browseDao) Get(ctx context.Context, filter bson.M) (model.Browse, error) {
	var data model.Browse
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Browse{}, err
	}
	return data, nil
}

// List 查询
func (s browseDao) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.Browse, int64, error) {
	var list []model.Browse

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func NewBrowseDao(collect string) DaoInt {
	return browseDao{
		db: global.MDB.Collection(collect),
	}
}
