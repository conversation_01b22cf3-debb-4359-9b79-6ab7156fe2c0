package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DeliveryMan 配送员
type DeliveryMan struct {
	ID             primitive.ObjectID `bson:"_id"  json:"id"`
	UserID         primitive.ObjectID `bson:"user_id" json:"user_id"`
	ServicePointID primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`
	UserName       string             `json:"user_name" bson:"user_name"`
	Desc           string             `json:"desc" bson:"desc"` // 说明
	CreatedAt      int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt      int64              `bson:"updated_at"  json:"updated_at"`
}

// DeliverFeeRule 下单配送费收取规则
type DeliverFeeRule struct {
	ID                   primitive.ObjectID    `bson:"_id"  json:"id"`
	ServicePointID       primitive.ObjectID    `bson:"service_point_id" json:"service_point_id"`
	Rules                []DistanceWithNumRule `json:"rules" bson:"rules"`                                       // 规则
	BaseDeliverFee       int                   `json:"base_deliver_fee" bson:"base_deliver_fee"`                 // 基础配送费
	BaseDeliverDistance  int                   `json:"base_deliver_distance" bson:"base_deliver_distance"`       // 基础配送距离
	FeePerKm             int                   `json:"fee_per_km" bson:"fee_per_km"`                             // 里程价每km
	SubsidyFeePerKm      int                   `json:"subsidy_fee_per_km" bson:"subsidy_fee_per_km"`             // 平台补贴-里程价每km
	BaseDeliverWeight    int                   `json:"base_deliver_weight" bson:"base_deliver_weight"`           // 基础配送重量
	OverWeightFeePerUnit int                   `json:"over_weight_fee_per_unit" bson:"over_weight_fee_per_unit"` // 超重价格每单位
	OverWeightKGPerUnit  int                   `json:"over_weight_kg_per_unit" bson:"over_weight_kg_per_unit"`   // 超重单位
	CreatedAt            int64                 `bson:"created_at"  json:"created_at"`
	UpdatedAt            int64                 `bson:"updated_at"  json:"updated_at"`
}

// DistanceWithNumRule 距离对应的商品件数
type DistanceWithNumRule struct {
	Distance int `json:"distance" bson:"distance"` // 距离
	Num      int `json:"num" bson:"num"`           // 件数
}

// WarehouseLoadFee 仓配费，集中仓装配
type WarehouseLoadFee struct {
	ID          primitive.ObjectID `bson:"_id"  json:"id"`
	WarehouseID primitive.ObjectID `bson:"warehouse_id" json:"warehouse_id"`
	FeePerKG    int                `json:"fee_per_kg" bson:"fee_per_kg"` // 分/kg
	CreatedAt   int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt   int64              `bson:"updated_at"  json:"updated_at"`
}

type DeliverFeeRes struct {
	IsOverScope          bool        `json:"is_over_scope" bson:"is_over_scope"`                     // 超出范围
	CalcDistance         int         `json:"calc_distance" bson:"calc_distance"`                     // 计算距离
	NeedAmountForSubsidy int         `json:"need_amount_for_subsidy" bson:"need_amount_for_subsidy"` // 差多少满足补贴规则
	TotalDeliverFee      int         `json:"total_deliver_fee" bson:"total_deliver_fee"`             // 总配送费
	SubsidyDeliverFee    int         `json:"subsidy_deliver_fee" bson:"subsidy_deliver_fee"`         // 补贴配送费
	FinalDeliverFee      int         `json:"final_deliver_fee" bson:"final_deliver_fee"`             // 最终配送费
	TotalTransportFee    int         `json:"total_transport_fee" bson:"total_transport_fee"`         // 干线费
	DeliverType          DeliverType `json:"deliver_type" bson:"deliver_type"`                       // 配送方式
	InstantDeliverType   int         `json:"instant_deliver_type" bson:"instant_deliver_type"`       // 即时配送方式
	InstantDeliverName   string      `json:"instant_deliver_name" bson:"instant_deliver_name"`       // 即时配送方式
}
