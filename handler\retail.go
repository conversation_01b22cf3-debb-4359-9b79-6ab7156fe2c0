package handler

import (
	"base/api/retailOrder"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 零售
func retailRouter(r *gin.RouterGroup) {
	r = r.Group("/retail")

	r.Use(middleware.CheckToken)

	r.POST("/order/calc", retailOrder.AmountCalc)
	//r.POST("/order/create", retailOrder.Create)
	//r.POST("/order/list/supplier", purchaseOrder.ListBySupplier)
	//r.POST("/order/list", purchaseOrder.List)
	//r.POST("/order/parent/list", purchaseOrder.ListParent)
	//r.POST("/order/get", purchaseOrder.Get)
	//r.POST("/order/close", purchaseOrder.Close)
	//r.POST("/order/delete", purchaseOrder.Delete)
	//r.POST("/order/confirm", purchaseOrder.ConfirmOrder)
	//r.POST("/order/ship", purchaseOrder.ShipOrder)
	//r.POST("/order/finish", purchaseOrder.FinishOrder)
	//r.POST("/order/sort", purchaseOrder.UpdateSort)

	r.POST("/address/create", retailOrder.CreateAddress)
	r.POST("/address/update", retailOrder.UpdateAddress)
	r.POST("/address/get", retailOrder.GetAddress)
}
