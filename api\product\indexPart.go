package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/indexPartProductService"
	"base/service/indexPartService"
	"base/service/productService"
	"base/service/supplierService"
	"base/types"
	"base/util"
	"context"
	"sort"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// ListPart 查询
func ListPart(ctx *gin.Context) {
	var req = struct {
		IndexPartID string `json:"index_part_id" validate:"len=24"`
		Page        int64  `json:"page" validate:"min=1"`
		Limit       int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, count, err := indexPartProductService.NewIndexPartProductService().List(ctx, id, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var ids []primitive.ObjectID
	for _, i := range list {
		ids = append(ids, i.ProductID)
	}

	resList, err := DealProductList(ctx, primitive.NilObjectID, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f && req.IndexPartID != "6644725f95727c00226dc3dc" {
		for j, _ := range resList {
			resList[j].Price = 0
			resList[j].StartPrice = 0
			for k, _ := range resList[j].Product.SkuList {
				resList[j].Product.SkuList[k].Price = 0
			}
		}
	}

	sort.Sort(ByIDList(resList))

	xhttp.RespSuccessList(ctx, resList, count)
}

// ListPartBySupplier 查询
func ListPartBySupplier(ctx *gin.Context) {
	var req = struct {
		IndexPartID string `json:"index_part_id" validate:"len=24"`
		SupplierID  string `json:"supplier_id" validate:"len=24"`
		//Page        int64  `json:"page"`
		//Limit       int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.IndexPartID, "adminIndexPartProduct List IndexPartID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplierID, err := util.ConvertToObjectWithNote(req.SupplierID, "adminIndexPartProduct List SupplierID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"index_part_id": id,
		"supplier_id":   supplierID,
	}

	list, err := indexPartProductService.NewIndexPartProductService().ListByCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, i := range list {
		ids = append(ids, i.ProductID)
	}

	//resList, err := DealProductList(ctx, primitive.NilObjectID, ids)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	products, err := productService.NewProductService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, products)
}

func ListPartBySupplierStation(ctx *gin.Context) {
	var req = struct {
		StationID  string `json:"station_id" validate:"len=24"`
		SupplierID string `json:"supplier_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	stationID, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	indexPart, err := indexPartService.NewIndexPartService().GetLocalByStation(ctx, stationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"index_part_id": indexPart.ID,
		"supplier_id":   supplierID,
	}

	list, err := indexPartProductService.NewIndexPartProductService().ListByCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, i := range list {
		ids = append(ids, i.ProductID)
	}

	//resList, err := DealProductList(ctx, primitive.NilObjectID, ids)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	products, err := productService.NewProductService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, products)
}

func UpdateLocalBySupplier(ctx *gin.Context) {
	var req = struct {
		StationID     string   `json:"station_id" validate:"len=24"`
		SupplierID    string   `json:"supplier_id" validate:"len=24"`
		ProductIDList []string `json:"product_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	stationID, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	indexPart, err := indexPartService.NewIndexPartService().GetLocalByStation(ctx, stationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var pIDs []primitive.ObjectID
	for _, idStr := range req.ProductIDList {
		i, err := util.ConvertToObjectWithCtx(ctx, idStr)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		pIDs = append(pIDs, i)
	}

	err = indexPartProductService.NewIndexPartProductService().UpdateByStation(ctx, indexPart.ID, supplierID, pIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateSort 更新商品排序
func UpdateSort(ctx *gin.Context) {
	var req = struct {
		IndexPartID   string   `json:"index_part_id" validate:"len=24"`
		ProductIDList []string `json:"product_id_list" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var pIDs []primitive.ObjectID
	for _, s := range req.ProductIDList {
		i, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		pIDs = append(pIDs, i)
	}

	err = indexPartProductService.NewIndexPartProductService().UpdateSort(ctx, id, pIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func DealProductList(ctx context.Context, buyerID primitive.ObjectID, ids []primitive.ObjectID) ([]types.ProductRes, error) {
	if len(ids) < 1 {
		return nil, nil
	}

	list := make([]types.ProductRes, 0, len(ids))
	mSort := make(map[string]int)
	for i, id := range ids {
		mSort[id.Hex()] = i
	}

	products, err := productService.NewProductService().ListByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	var supplierIDs []primitive.ObjectID
	for _, product := range products {
		supplierIDs = append(supplierIDs, product.SupplierID)

	}
	supplier, err := supplierService.NewSupplierService().ListByIDs(ctx, supplierIDs)
	if err != nil {
		zap.S().Error("查询供应商信息错误", err)
	}
	ms := make(map[primitive.ObjectID]model.Supplier)
	for _, m := range supplier {
		ms[m.ID] = m
	}

	for _, v := range products {
		if !v.Sale {
			// 删除
			//indexPartProductService.NewIndexPartProductService().DownProduct(ctx, v.ID, true)

			continue
		}
		list = append(list, types.ProductRes{
			Sort:                mSort[v.ID.Hex()],
			Product:             v,
			SupplierTagListInfo: ms[v.SupplierID].TagList,
		})
	}

	return list, nil
}
