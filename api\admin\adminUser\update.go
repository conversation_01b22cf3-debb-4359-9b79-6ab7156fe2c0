package adminUser

import (
	"base/core/xhttp"
	"base/model"
	"base/service/adminService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Update 更新管理员
func Update(ctx *gin.Context) {
	var req = struct {
		ID       string           `json:"id"`
		Note     string           `json:"note"`
		RoleList []model.RoleInfo `json:"role_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = adminService.NewAdminService().Update(ctx, id, req.Note, req.RoleList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
