package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ServicePointLevel string

const (
	ServicePointLevelFirst  = "first"
	ServicePointLevelSecond = "second"
)

// ServicePoint 服务点
type ServicePoint struct {
	ID                   primitive.ObjectID    `bson:"_id" json:"id"`
	UserID               primitive.ObjectID    `bson:"user_id" json:"user_id"` // 用户ID
	ParentPointID        primitive.ObjectID    `bson:"parent_point_id" json:"parent_point_id"`
	ParentPointName      string                `bson:"parent_point_name" json:"parent_point_name"`
	Name                 string                `bson:"name" json:"name"`                                       // 服务点名称
	DeliverType          []DeliverType         `json:"deliver_type" bson:"deliver_type"`                       // 配送方式
	ContactUser          string                `json:"contact_user"  bson:"contact_user"  validate:"required"` // 联系人
	ContactMobile        string                `json:"contact_mobile" bson:"contact_mobile"`                   // 联系人
	Location             Location              `bson:"location" json:"location"`                               // 定位地址
	Address              string                `bson:"address" json:"address"`                                 // 详细地址
	ShopHeadImg          FileInfo              `bson:"shop_head_img" json:"shop_head_img"`                     // 门头照
	IsOpen               bool                  `json:"is_open" bson:"is_open"`                                 // 是否开启，需要先设置路线
	Level                string                `json:"level" bson:"level"`                                     // 类型
	ServiceChargePercent int                   `json:"service_charge_percent" bson:"service_charge_percent"`   // 服务费分成
	TransportUnitPrice   int                   `json:"transport_unit_price" bson:"transport_unit_price"`       // 干线费单价
	DeliveryScope        []PointLocation       `json:"delivery_scope" bson:"delivery_scope"`                   // 配送范围
	DeliveryUnitPrice    int                   `json:"delivery_unit_price" bson:"delivery_unit_price"`         // 配送费单价
	DeliverySubsidyRule  []DeliverySubsidyRule `json:"delivery_subsidy_rule" bson:"delivery_subsidy_rule"`     // 配送费补贴条件
	CreatedAt            int64                 `bson:"created_at" json:"created_at"`
	UpdatedAt            int64                 `bson:"updated_at" json:"updated_at"`
	DeletedAt            int64                 `bson:"deleted_at" json:"deleted_at"`
}

type DeliverySubsidyRule struct {
	Amount  int `json:"amount" bson:"amount"`
	Percent int `json:"percent" bson:"percent"`
}

type PointLocation struct {
	Longitude float64 `json:"longitude" bson:"longitude"` // 经度
	Latitude  float64 `json:"latitude" bson:"latitude"`   // 维度
}

//ServiceAbility     []ServiceAbilityType `json:"service_ability" bson:"service_ability"`           // 服务能力-仓储/配送
//ShopImgList        []FileInfo           `bson:"shop_img_list" json:"shop_img_list"`               // 经营场所-图片列表
//WarehouseID        primitive.ObjectID   `bson:"warehouse_id" json:"warehouse_id"`                 // 接收的中心仓
//RunningBeginTime   string               `bson:"running_begin_time" json:"running_begin_time"`     // 营业时间-开始
//RunningEndTime     string               `bson:"running_end_time" json:"running_end_time"`         // 营业时间-结束
