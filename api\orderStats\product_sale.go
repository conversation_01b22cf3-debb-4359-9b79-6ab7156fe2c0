package orderStats

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/mnsSendService"

	"github.com/gin-gonic/gin"
)

// ProductSaleStats 商品销售统计
func ProductSaleStats(ctx *gin.Context) {
	var req = struct {
		BeginTime int64 `json:"begin_time"`
		EndTime   int64 `json:"end_time"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if req.BeginTime == 0 || req.EndTime == 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "时间不能为0"))
		return
	}

	mnsSendService.NewMNSClient().SendProductSaleStatsExport(req.BeginTime, req.EndTime)

	xhttp.RespSuccess(ctx, nil)
}
