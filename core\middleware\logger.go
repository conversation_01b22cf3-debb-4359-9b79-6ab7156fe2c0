package middleware

import (
	"base/global"
	"base/util"
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"io"
	"net"
	"net/http"
	"net/http/httputil"
	"os"
	"runtime/debug"
	"strings"
	"time"
)

type CustomResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w CustomResponseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

type logReq struct {
	RequestID  string            `json:"request_id"`
	Method     string            `json:"method"`
	Header     map[string]string `json:"header"`
	IP         string            `json:"ip"`
	RequestURI string            `json:"request_uri"`
	Auth       string            `json:"auth"`
	Env        string            `json:"env"`
	ReqBody    interface{}       `json:"req_body"`
}

type logRes struct {
	RequestID string        `json:"request_id"`
	RespBody  interface{}   `json:"resp_body"`
	Time      time.Duration `json:"time"`
}

// GinLogger 用于替换gin框架的Logger中间件，不传参数，直接这样写
func GinLogger(c *gin.Context) {
	l := global.SysLogger.Sugar()

	// 记录请求时间
	start := time.Now()

	// 使用自定义 ResponseWriter
	crw := &CustomResponseWriter{
		body:           &bytes.Buffer{},
		ResponseWriter: c.Writer,
	}
	c.Writer = crw

	// 打印请求信息
	reqBody, _ := c.GetRawData()

	if len(reqBody) > 0 {
		c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody))
	}

	auth := c.Request.Header.Get("Authorization")
	env := c.Request.Header.Get("X-Env")
	rid := c.Request.Header.Get("X-Request-Id")
	if rid == "" {
		// 不存在
		rid = "backReqId:" + util.NewUUID()
	}
	c.Set("rid", rid)
	p := c.Request.RequestURI

	fullPath := c.FullPath()
	//
	ip := c.Request.Header.Get("X-Real-IP")
	if ip == "" {
		ip = c.Request.Header.Get("X-Forwarded-For")
	}
	if ip == "" {
		ip = c.Request.RemoteAddr
	}

	h := make(map[string]string)
	h["X-Point"] = c.GetHeader("X-Point")
	h["X-Login"] = c.GetHeader("X-Login")
	//h["X-Station"] = c.GetHeader("X-Station")

	req := logReq{
		RequestID:  c.GetString("rid"),
		Method:     c.Request.Method,
		Header:     h,
		IP:         ip,
		RequestURI: p,
		Auth:       auth,
		Env:        env,
	}

	if !strings.Contains(p, "/api/pay/back/notify") && p != "/api/yee/merchant/upload" {
		// 跳过协议查询
		//if !isExclude(p, fullPath) {
		if len(reqBody) > 0 {
			req.ReqBody = json.RawMessage(reqBody)
		}
		// 非回调
		marshal, err := json.Marshal(req)
		if err != nil {
			l.Errorf("请求%s，json错误,%v", c.GetString("rid"), err)
		}
		l.Info(string(marshal))
		//}
	}

	if strings.Contains(p, "/api/pay/back/notify") {
		// 回调
		marshal, err := json.Marshal(req)
		if err != nil {
			l.Errorf("支付notify请求%s，json错误,%v", c.GetString("rid"), err)
		}
		l.Info(string(marshal))
	}

	//l.Infof("Request: %s %s %s %s %s %s %s", rid, c.Request.Method, c.RemoteIP(), c.Request.RequestURI, auth, env, reqBody)

	// 执行请求处理程序和其他中间件函数
	c.Next()

	// 记录回包内容和处理时间
	end := time.Now()
	latency := end.Sub(start)
	if !strings.Contains(p, "/api/pay/back/notify") && !strings.Contains(p, "/api/invoice/down/excel") && !strings.Contains(p, "/api/admin/order/list/yht/export") {
		if !isExclude(p, fullPath) {
			respBody := crw.body.Bytes()
			res := logRes{
				RequestID: c.GetString("rid"),
				RespBody:  json.RawMessage(respBody),
				Time:      latency,
			}
			resByte, err := json.Marshal(res)
			if err != nil {
				l.Errorf("响应%s，json错误,%v", c.GetString("rid"), err)
			}
			l.Info(string(resByte))
		}
	}

	if strings.Contains(p, "/api/pay/back/notify") {
		s := crw.body.String()
		res := logRes{
			RequestID: c.GetString("rid"),
			RespBody:  s,
			Time:      latency,
		}
		resByte, err := json.Marshal(res)
		if err != nil {
			l.Errorf("支付notify响应%s，json错误,%v", c.GetString("rid"), err)
		}
		l.Info(string(resByte))
	}

}

// GinRecovery 用于替换gin框架的Recovery中间件，因为传入参数，再包一层
func GinRecovery(stack bool) gin.HandlerFunc {
	logger := global.SysLogger.Sugar()
	return func(c *gin.Context) {
		defer func() {
			// defer 延迟调用，出了异常，处理并恢复异常，记录日志
			if err := recover(); err != nil {
				//  这个不必须，检查是否存在断开的连接(broken pipe或者connection reset by peer)---------开始--------
				var brokenPipe bool
				if ne, ok := err.(*net.OpError); ok {
					if se, ok := ne.Err.(*os.SyscallError); ok {
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") || strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
				}
				//httputil包预先准备好的DumpRequest方法
				httpRequest, _ := httputil.DumpRequest(c.Request, false)
				if brokenPipe {
					logger.Error(c.Request.URL.Path,
						zap.Any("error", err),
						zap.String("request", string(httpRequest)),
					)
					// 如果连接已断开，我们无法向其写入状态
					_ = c.Error(err.(error))
					c.Abort()
					return
				}
				//  这个不必须，检查是否存在断开的连接(broken pipe或者connection reset by peer)---------结束--------

				// 是否打印堆栈信息，使用的是debug.Stack()，传入false，在日志中就没有堆栈信息
				if stack {
					logger.Error("[Recovery from panic]",
						zap.Any("error", err),
						zap.String("request", string(httpRequest)),
						zap.String("stack", string(debug.Stack())),
					)
				} else {
					logger.Error("[Recovery from panic]",
						zap.Any("error", err),
						zap.String("request", string(httpRequest)),
					)
				}
				// 有错误，直接返回给前端错误，前端直接报错
				//c.AbortWithStatus(http.StatusInternalServerError)
				// 该方式前端不报错
				c.AbortWithStatusJSON(http.StatusInternalServerError, "系统忙")
			}
		}()
		c.Next()
	}
}
