package protocolService

import (
	"base/dao"
	"base/dao/protocolDao"
	"base/global"
	"base/model"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Update(ctx context.Context, pType string, content string) error
	//List(ctx context.Context) ([]model.Protocol, error) // 不适用
	Get(ctx context.Context, pType string) (model.Protocol, error)
}

type protocolService struct {
	mdb         *mongo.Database
	rdb         *redis.Client
	protocolDao protocolDao.DaoInt
}

func (s protocolService) Update(ctx context.Context, pType string, content string) error {
	data := model.Protocol{
		ID:        primitive.NewObjectID(),
		Type:      pType,
		Content:   content,
		CreatedAt: time.Now().UnixMilli(),
	}
	get, err := s.Get(ctx, pType)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		data.ID = get.ID
		data.UpdatedAt = time.Now().UnixMilli()
		data.CreatedAt = get.CreatedAt
	}
	err = s.protocolDao.Upsert(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s protocolService) List(ctx context.Context) ([]model.Protocol, error) {
	list, err := s.protocolDao.List(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s protocolService) Get(ctx context.Context, pType string) (model.Protocol, error) {
	filter := bson.M{
		"type": pType,
	}
	data, err := s.protocolDao.Get(ctx, filter)
	if err != nil {
		return model.Protocol{}, err
	}
	return data, nil
}

func NewProtocolService() ServiceInterface {
	return protocolService{
		mdb:         global.MDB,
		rdb:         global.RDBDefault,
		protocolDao: dao.ProtocolDao,
	}
}
