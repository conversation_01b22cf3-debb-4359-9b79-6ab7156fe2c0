package invoiceService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/invoiceDao"
	"base/global"
	"base/model"
	"base/service/aesService"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, buyerID primitive.ObjectID, applyNote string, title model.InvoiceTitle) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Invoice, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invoice, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Cancel(ctx context.Context, id primitive.ObjectID) error

	Audit(ctx context.Context, id primitive.ObjectID, status model.InvoiceStatusType, failNote string) error
	Issue(ctx context.Context, id primitive.ObjectID, issueFile, orderFile, issueNote string) error
}

type invoiceService struct {
	rdb        *redis.Client
	invoiceDao invoiceDao.DaoInt
	aesS       aesService.ServiceInterface
}

func NewInvoiceService() ServiceInterface {
	return invoiceService{
		rdb:        global.RDBDefault,
		invoiceDao: dao.InvoiceDao,
		aesS:       aesService.NewAesService(),
	}
}

func (s invoiceService) Create(ctx context.Context, buyerID primitive.ObjectID, applyNote string, title model.InvoiceTitle) error {
	now := time.Now().UnixMilli()

	data := model.Invoice{
		ID:               primitive.NewObjectID(),
		BuyerID:          buyerID,
		InvoiceTitleType: title.InvoiceTitleType,
		InvoiceTitle:     title.InvoiceTitle,
		TaxNumber:        title.TaxNumber,
		Address:          title.Address,
		PhoneNumber:      title.PhoneNumber,
		BankName:         title.BankName,
		BankAccount:      title.BankAccount,
		ApplyNote:        applyNote,
		Status:           model.InvoiceStatusTypeAuditing,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	err := s.invoiceDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s invoiceService) GetByID(ctx context.Context, id primitive.ObjectID) (model.Invoice, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.invoiceDao.Get(ctx, filter)
	if err != nil {
		return model.Invoice{}, err
	}
	return fee, nil
}

func (s invoiceService) Cancel(ctx context.Context, id primitive.ObjectID) error {
	invoice, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if invoice.Status == model.InvoiceStatusTypeCanceled {
		return xerr.NewErr(xerr.ErrParamError, nil, "发票申请已取消")
	}

	if invoice.Status != model.InvoiceStatusTypeAuditing {
		return xerr.NewErr(xerr.ErrParamError, nil, "发票未处于审核中，不能取消")
	}

	filter := bson.M{
		"_id": id,
	}
	err = s.invoiceDao.UpdateOne(ctx, filter, bson.M{
		"$set": bson.M{
			"status":     model.InvoiceStatusTypeCanceled,
			"updated_at": time.Now().UnixMilli(),
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceService) Audit(ctx context.Context, id primitive.ObjectID, status model.InvoiceStatusType, failNote string) error {
	filter := bson.M{
		"_id": id,
	}

	update := bson.M{
		"status":     status,
		"updated_at": time.Now().UnixMilli(),
	}
	if status == model.InvoiceStatusTypeAuditFail {
		update["fail_note"] = failNote
		//	短信通知

	}

	err := s.invoiceDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s invoiceService) Issue(ctx context.Context, id primitive.ObjectID, issueFile, orderFile, issueNote string) error {
	filter := bson.M{
		"_id": id,
	}

	now := time.Now().UnixMilli()
	err := s.invoiceDao.UpdateOne(ctx, filter, bson.M{
		"$set": bson.M{
			"status":      model.InvoiceStatusTypeIssued,
			"issued_file": issueFile,
			"order_file":  orderFile,
			"issued_note": issueNote,
			"issued_at":   now,
			"updated_at":  now,
		},
	})
	if err != nil {
		return err
	}

	// 短信通知

	return nil
}

func (s invoiceService) Delete(ctx context.Context, id primitive.ObjectID) error {
	invoice, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if !(invoice.Status == model.InvoiceStatusTypeCanceled || invoice.Status == model.InvoiceStatusTypeAuditFail) {
		return xerr.NewErr(xerr.ErrParamError, nil, "发票未处于已取消，不能删除")
	}

	filter := bson.M{
		"_id": id,
	}
	err = s.invoiceDao.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invoice, int64, error) {
	list, count, err := s.invoiceDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s invoiceService) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.invoiceDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}
