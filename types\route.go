package types

import (
	"base/model"
)

// RouteCreateReq 路线-集中仓至服务点
type RouteCreateReq struct {
	FromWarehouseID  string              `json:"from_warehouse_id" validate:"len=24"` // 集中仓
	RouteType        model.RouteType     `json:"route_type" validate:"oneof=1 2"`     // 线路类型
	ToWarehouseID    string              `json:"to_warehouse_id"`                     // 集中仓
	ToServicePointID string              `json:"to_service_point_id"`                 // 服务点
	ActualDistance   int                 `json:"actual_distance" validate:"min=0"`    // 实际距离/m
	FeePerKG         int                 `json:"fee_per_kg" validate:"min=0"`         // 分/kg
	Note             string              `json:"note" validate:"-"`                   // 备注
	DeliverTime      []model.DeliverTime `json:"deliver_time" validate:"min=1"`
}
