package adminPromote

import (
	"base/core/xhttp"
	"base/service/promoteService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = promoteService.NewPromoteService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
