package cart

import (
	"base/core/xhttp"
	"base/model"
	"base/service/cartService"
	"base/service/productService"
	"base/service/supplierService"
	"base/util"
	"errors"
	"sort"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// List 购物车列表
func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := cartService.NewCartService().List(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID][]model.Cart)
	var pidList []primitive.ObjectID
	for _, cart := range list {
		m[cart.SupplierID] = append(m[cart.SupplierID], cart)
		var f bool
		for _, i := range pidList {
			if i == cart.ProductID {
				f = true
				continue
			}
		}
		if !f {
			pidList = append(pidList, cart.ProductID)
		}
	}

	products, err := productService.NewProductService().ListByIDs(ctx, pidList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mp := make(map[primitive.ObjectID]model.Product)
	for _, i2 := range products {
		mp[i2.ID] = i2
	}

	var needDelList []primitive.ObjectID

	var resList []Res
	for k, vList := range m {
		supplier, err := supplierService.NewSupplierService().Get(ctx, k)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			zap.S().Error("查询供应商非空报错")
		}

		var pList []perProduct
		for _, v := range vList {
			product := mp[v.ProductID]
			count := v.Count

			sku := getSkuName(v.SkuIDCode, product.SkuList)

			if sku.IDCode == "" {
				needDelList = append(needDelList, v.ID)
				continue
			}

			pList = append(pList, perProduct{
				ID:                  v.ID,
				ProductID:           v.ProductID,
				ProductCover:        product.CoverImg,
				ProductTitle:        product.Title,
				Price:               sku.Price,
				Count:               count,
				IsCheckWeight:       product.IsCheckWeight,
				RoughWeight:         sku.RoughWeight,
				Sale:                product.Sale,
				DeletedAt:           product.DeletedAt,
				ProductUnitTypeName: product.ProductUnitTypeName,
				SkuName:             sku.Name,
				SkuIDCode:           v.SkuIDCode,
				CreatedAt:           v.CreatedAt,
				Stock:               sku.Stock,
			})
		}

		resList = append(resList, Res{
			SupplierID:   k,
			SupplierName: supplier.ShopSimpleName,
			List:         pList,
		})
	}

	sort.Sort(CartList(resList))

	if len(needDelList) > 0 {
		cartService.NewCartService().DeleteByIDList(ctx, needDelList)
	}

	xhttp.RespSuccess(ctx, resList)
}

func getSkuName(skuIDCode string, skuList []model.Sku) model.Sku {
	for _, i := range skuList {
		if i.IDCode == skuIDCode {
			return i
		}
	}
	return model.Sku{}
}

// ListForProduct 购物车列表
func ListForProduct(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	cartList, err := cartService.NewCartService().ListByProduct(ctx, buyerID, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, cartList)
}

// Res 购物车列表
type Res struct {
	SupplierID   primitive.ObjectID `json:"supplier_id"`
	SupplierName string             `json:"supplier_name"`
	List         []perProduct       `json:"list"`
}

// perProduct 购物车商品
type perProduct struct {
	ID           primitive.ObjectID `json:"id"`
	ProductID    primitive.ObjectID `json:"product_id"`
	ProductCover model.FileInfo     `json:"product_cover"`
	ProductTitle string             `json:"product_title"`
	// OriginPrice         int                     `json:"origin_price"`        // 原价
	Price int `json:"price"` // 价格
	// DiscountList        []model.ProductDiscount `json:"discount_list"`       // 折扣列表
	// DiscountPriceList   []model.DiscountPrice   `json:"discount_price_list"` // 折扣价
	// Stock               int                     `json:"stock"`               // 库存
	// BuyMinLimit         int                     `json:"buy_min_limit"`       // 起购
	// BuyMaxLimit         int                     `json:"buy_max_limit"`       // 限购
	IsCheckWeight       bool   `json:"is_check_weight"`
	RoughWeight         int    `json:"rough_weight"` // 毛重
	Count               int    `json:"count"`
	Sale                bool   `json:"sale"`                   // 上架
	DeletedAt           int64  `json:"deleted_at"`             // 软删
	ProductUnitTypeName string `json:"product_unit_type_name"` // 产品单位名称
	// LinkBrandStatus     int                     `json:"link_brand_status"`      // 关联品牌状态  2 关联
	// LinkBrandID         primitive.ObjectID      `json:"link_brand_id"`          // 关联品牌ID
	// LinkBrandName       string                  `json:"link_brand_name"`        // 关联品牌名称
	SkuName   string `json:"sku_name"`    // sku名称
	SkuIDCode string `json:"sku_id_code"` // sku编号
	CreatedAt int64  `json:"created_at"`
	Stock     int    `json:"stock"`
}

//PriceList           []model.PerPrice   `json:"price_list"` // 价格列表
