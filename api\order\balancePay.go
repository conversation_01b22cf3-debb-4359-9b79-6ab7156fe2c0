package order

import (
	"base/core/xhttp"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func BalancePayConfirm(ctx *gin.Context) {
	payLock.Lock()
	defer payLock.Unlock()
	var req = struct {
		ParentOrderID    string `json:"parent_order_id"`
		VerificationCode string `json:"verification_code"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ParentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ip := xhttp.IP(ctx)

	data, err := parentOrderService.NewParentOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderService.NewOrderService().BalancePayConfirm(ctx, data, req.VerificationCode, ip)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func ResendBalancePaySMS(ctx *gin.Context) {
	payLock.Lock()
	defer payLock.Unlock()
	var req = struct {
		ParentOrderID string `json:"parent_order_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ParentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := parentOrderService.NewParentOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mobile, err := orderService.NewOrderService().ResendBalancePaySMS(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, mobile)
}
