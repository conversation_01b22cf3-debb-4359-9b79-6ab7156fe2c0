package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// SupplierBill 供应商订单
type SupplierBill struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	SupplierID    primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`         // 供应商ID
	FilePath      string             `json:"file_path" bson:"file_path"`             // 文件路径
	OrderBeginAt  int64              `json:"order_begin_at" bson:"order_begin_at"`   // 订单区间
	OrderEndAt    int64              `json:"order_end_at" bson:"order_end_at"`       // 订单区间
	ExportBeginAt int64              `json:"export_begin_at" bson:"export_begin_at"` // 导出区间
	ExportEndAt   int64              `json:"export_end_at" bson:"export_end_at"`     // 导出区间
	GeneratedAt   int64              `json:"generated_at" bson:"generated_at"`       // 生成时间
	Remark        string             `json:"remark" bson:"remark"`                   // 备注
	CreatedAt     int64              `json:"created_at" bson:"created_at"`           // 创建时间
	UpdatedAt     int64              `json:"updated_at" bson:"updated_at"`           // 更新时间
	DeletedAt     int64              `json:"deleted_at" bson:"deleted_at"`           // 删除时间
}
