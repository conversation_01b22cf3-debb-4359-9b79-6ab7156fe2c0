package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// Duplicate 复制
func Duplicate(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" `
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ProductID, "Duplicate product_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	product, err := productService.NewProductService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data := product

	now := time.Now().UnixMilli()
	data.ID = primitive.NewObjectID()
	data.Title = "【复制】" + product.Title
	data.Sale = false
	data.BuyMinLimit = 0
	data.BuyMaxLimit = 0
	data.LinkBrandID = primitive.NilObjectID
	data.LinkBrandStatus = 1
	data.LinkBrandName = ""
	data.PurchaseNote = ""
	data.OriginID = ""
	data.CreatedAt = now
	data.UpdatedAt = now
	data.Version = now

	err = productService.NewProductService().Duplicate(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
