package supplier

import (
	"base/core/xhttp"
	"base/service/supplierService"
	"base/service/userService"
	"base/types"
	"github.com/gin-gonic/gin"
)

// Apply 申请
func Apply(ctx *gin.Context) {
	var req types.SupplierApplyReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	user, err := userService.NewUserService().GetOrCreateUser(ctx, req.Mobile, "", "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = supplierService.NewSupplierService().Create(ctx, req, user.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
	return
}

//
//// UpdateApply 更新申请
//func UpdateApply(ctx *gin.Context) {
//	var req types.SupplierApplyReq
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	err = supplierService.NewSupplierService().UpdateApply(ctx, req)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, nil)
//	return
//}
