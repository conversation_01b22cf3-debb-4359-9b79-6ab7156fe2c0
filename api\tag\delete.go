package tag

import (
	"base/core/xhttp"
	"base/service/tagService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		IDs []string `json:"ids"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, i := range req.IDs {
		objectID, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, objectID)
	}

	err = tagService.NewTagService().Delete(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
