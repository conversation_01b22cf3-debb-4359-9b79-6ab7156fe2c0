package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func UpdateRecommend(ctx *gin.Context) {
	var req = struct {
		ProductID string   `json:"product_id"`
		IDList    []string `json:"id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, idStr := range req.IDList {
		id, err := util.ConvertToObjectWithCtx(ctx, idStr)
		if err != nil {
			return
		}
		ids = append(ids, id)
	}

	err = productService.NewProductService().UpdateRecommend(ctx, productID, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func ListRecommend(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	product, err := productService.NewProductService().Get(ctx, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	recommendProductList := product.RecommendProductList

	var ids []primitive.ObjectID
	for _, id := range recommendProductList {
		if id == productID {
			continue
		}
		ids = append(ids, id)
	}

	if len(ids) < 1 {
		xhttp.RespSuccess(ctx, []interface{}{})
		return
	}

	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"sale":       true,
		"deleted_at": 0,
	}

	products, _, err := productService.NewProductService().ListByCus(ctx, filter, 1, 110)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range products {
			products[j].Price = 0
			products[j].OriginPrice = 0
		}
	}

	var resList []productSortRes
	for _, id := range recommendProductList {
		for _, p := range products {
			if id == p.ID {
				resList = append(resList, productSortRes{
					Product: p,
				})
			}
		}
	}

	xhttp.RespSuccess(ctx, resList)
}

type productSortRes struct {
	model.Product
	Sort int `json:"sort"`
}
