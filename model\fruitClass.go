package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// FruitClass 水果等级
type FruitClass struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CategoryID primitive.ObjectID `json:"category_id" bson:"category_id"` // 二级分类ID
	Name       string             `json:"name" bson:"name"`               // 名称
	CreatedAt  int64              `json:"created_at" bson:"created_at"`
	UpdatedAt  int64              `json:"updated_at" bson:"updated_at"`
}
