package orderQualityDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 品控
type DaoInt interface {
	Create(ctx context.Context, data model.OrderQuality) error
	CreateMany(ctx context.Context, data []model.OrderQuality) error
	Get(ctx context.Context, filter bson.M) (model.OrderQuality, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderQuality, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderQuality, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type orderQualityDao struct {
	db *mongo.Collection
}

func (s orderQualityDao) CreateMany(ctx context.Context, list []model.OrderQuality) error {
	data := make([]interface{}, len(list))
	for i, v := range list {
		data[i] = v
	}

	_, err := s.db.InsertMany(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s orderQualityDao) Create(ctx context.Context, data model.OrderQuality) error {
	_, err := s.db.InsertOne(ctx, data)

	return err
}

func (s orderQualityDao) Get(ctx context.Context, filter bson.M) (model.OrderQuality, error) {
	var data model.OrderQuality
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s orderQualityDao) List(ctx context.Context, filter bson.M) ([]model.OrderQuality, error) {
	var list []model.OrderQuality
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts.SetSort(sort)
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderQualityDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderQuality, int64, error) {
	var list []model.OrderQuality
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s orderQualityDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s orderQualityDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s orderQualityDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

func NewOrderQualityDao(collect string) DaoInt {
	return orderQualityDao{
		db: global.MDB.Collection(collect),
	}
}
