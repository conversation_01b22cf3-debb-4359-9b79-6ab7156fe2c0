package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// IndexPart 首页专区
type IndexPart struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	ServicePointID primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`
	StationID      primitive.ObjectID `bson:"station_id" json:"station_id"`
	Level          string             `bson:"level" json:"level"`
	Title          string             `json:"title" bson:"title"`             // 标题
	Style          string             `json:"style" bson:"style"`             // limitPrice 限量特价团, hotSale 当季热卖
	Visible        bool               `json:"visible" bson:"visible"`         // 是否显示
	Sort           int                `json:"sort" bson:"sort"`               // 顺序
	DisplayNum     int                `json:"display_num" bson:"display_num"` // 展示数
	TopImg         FileInfo           `json:"top_img" bson:"top_img"`         // 顶图
	Condition      []string           `json:"condition" bson:"condition"`     // 条件
	CreatedAt      int64              `json:"created_at" bson:"created_at"`
	UpdatedAt      int64              `json:"updated_at" bson:"updated_at"`
}

// IndexPartProduct 首页专区-商品
type IndexPartProduct struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	IndexPartID primitive.ObjectID `json:"index_part_id" bson:"index_part_id"` // 专区ID
	ProductID   primitive.ObjectID `json:"product_id" bson:"product_id"`
	SupplierID  primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`
	Sort        int                `json:"sort" bson:"sort"` // 顺序
	CreatedAt   int64              `json:"created_at" bson:"created_at"`
	UpdatedAt   int64              `json:"updated_at" bson:"updated_at"`
}

// IndexPartProductApply  专区-商品申请
type IndexPartProductApply struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	IndexPartID    primitive.ObjectID `json:"index_part_id" bson:"index_part_id"` // 专区ID
	ProductID      primitive.ObjectID `json:"product_id" bson:"product_id"`
	ApplyPriceList []PerPrice         `json:"apply_price_list" bson:"apply_price_list"` // 申请价格列表
	SupplierID     primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`
	AuditStatus    AuditStatusType    `json:"audit_status" bson:"audit_status"`
	AuditNote      string             `json:"audit_note" bson:"audit_note"`
	CreatedAt      int64              `json:"created_at" bson:"created_at"`
	UpdatedAt      int64              `json:"updated_at" bson:"updated_at"`
	DeletedAt      int64              `json:"deleted_at" bson:"deleted_at"`
}
