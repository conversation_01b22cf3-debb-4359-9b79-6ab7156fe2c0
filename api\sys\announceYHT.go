package sys

import (
	"base/core/xhttp"
	"base/service/announceService"
	"github.com/gin-gonic/gin"
)

func YHTAnnounceUpsert(ctx *gin.Context) {
	var req = struct {
		Content string `json:"content"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = announceService.NewAnnounceService().UpsertYHT(ctx, req.Content)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func YHTAnnounceGet(ctx *gin.Context) {
	v, err := announceService.NewAnnounceService().GetYHT(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, v)
}
