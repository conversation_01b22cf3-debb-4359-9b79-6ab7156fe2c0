package depositSetService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/depositSetDao"
	"base/model"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, objectType model.ObjectType, amount int) error
	List(ctx context.Context) ([]model.DepositSet, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.DepositSet, error)
	GetByObjectType(ctx context.Context, objectType model.ObjectType) (model.DepositSet, error)
	Update(ctx context.Context, id primitive.ObjectID, amount int) error
}

type depositSetService struct {
	depositSetDao depositSetDao.DaoInt
}

func (s depositSetService) Create(ctx context.Context, objectType model.ObjectType, amount int) error {
	depositSet, err := s.GetByObjectType(ctx, objectType)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if depositSet.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "该类型已存在保证金")
	}
	data := model.DepositSet{
		ID:         primitive.NewObjectID(),
		ObjectType: objectType,
		Amount:     amount,
		CreatedAt:  time.Now().UnixMilli(),
	}
	err = s.depositSetDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil

}

func (s depositSetService) List(ctx context.Context) ([]model.DepositSet, error) {
	list, err := s.depositSetDao.List(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s depositSetService) Get(ctx context.Context, id primitive.ObjectID) (model.DepositSet, error) {
	filter := bson.M{
		"_id": id,
	}
	depositSet, err := s.depositSetDao.Get(ctx, filter)
	if err != nil {
		return model.DepositSet{}, err
	}
	return depositSet, nil
}

func (s depositSetService) GetByObjectType(ctx context.Context, objectType model.ObjectType) (model.DepositSet, error) {
	filter := bson.M{
		"object_type": objectType,
	}
	depositSet, err := s.depositSetDao.Get(ctx, filter)
	if err != nil {
		return model.DepositSet{}, err
	}
	return depositSet, nil
}

func (s depositSetService) Update(ctx context.Context, id primitive.ObjectID, amount int) error {
	update := bson.M{
		"amount":     amount,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.depositSetDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func NewDepositSetService() ServiceInterface {
	return depositSetService{
		depositSetDao: dao.DepositSetDao,
	}
}
