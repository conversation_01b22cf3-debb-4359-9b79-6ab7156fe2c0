package categoryDao

import (
	"base/core/xerr"
	"base/global"
	"base/model"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CategoryDaoInt interface {
	Find(ctx context.Context, id primitive.ObjectID) (model.Category, error)
	Get(ctx context.Context, filter bson.M) (model.Category, error)
	Create(ctx context.Context, category model.Category) (primitive.ObjectID, error)
	Update(ctx context.Context, category model.Category) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, where bson.M) ([]model.Category, error)
	Del(ctx context.Context, filter bson.M) error
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type categoryDao struct {
	db *mongo.Collection
}

func NewCategoryDao(collect string) CategoryDaoInt {
	return categoryDao{
		db: global.MDB.Collection(collect),
	}
}

func (s categoryDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s categoryDao) Get(ctx context.Context, filter bson.M) (model.Category, error) {
	var category model.Category

	err := s.db.FindOne(ctx, filter).Decode(&category)
	if err != nil {
		return model.Category{}, err
	}

	return category, nil
}

func (s categoryDao) Find(ctx context.Context, id primitive.ObjectID) (model.Category, error) {
	category := model.Category{}

	if err := s.db.FindOne(ctx, bson.M{"_id": id}).Decode(&category); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return category, xerr.NewErr(xerr.ErrNoDocument, nil, "分类所选节点不存在")
		}
		return category, err
	}
	return category, nil
}

func (s categoryDao) Create(ctx context.Context, category model.Category) (primitive.ObjectID, error) {
	res, err := s.db.InsertOne(ctx, category)
	if err != nil {
		return primitive.NilObjectID, err
	}

	objectID, ok := res.InsertedID.(primitive.ObjectID)
	if !ok {
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrSysBusy, nil, "新增失败")
	}

	return objectID, nil
}

func (s categoryDao) Update(ctx context.Context, category model.Category) error {
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": category.ID}, bson.M{"$set": category})
	if err != nil {
		return err
	}
	return nil
}

func (s categoryDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s categoryDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s categoryDao) List(ctx context.Context, where bson.M) ([]model.Category, error) {
	list := make([]model.Category, 0)
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}

	opts := &options.FindOptions{
		Sort: sort,
	}

	res, err := s.db.Find(ctx, where, opts)
	if err != nil {
		return list, err
	}

	if err = res.All(ctx, &list); err != nil {
		return list, err
	}

	return list, nil
}

func (s categoryDao) Del(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}
