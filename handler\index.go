package handler

import (
	"base/api/index"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func indexRouter(r *gin.RouterGroup) {
	r = r.Group("/index")

	//轮播图
	r.GET("/swipe/list", index.ListSwipe)
	r.GET("/swipe/get/:id", index.GetSwipe)

	//快捷栏
	r.GET("/shortcut/list", index.ListShortcut)
	r.POST("/shortcut/get", index.GetShortcut)

	//	主题
	r.GET("/topic/list", index.ListTopic)
	r.GET("/topic/get/:id", index.GetTopic)

	r.GET("/part/list", index.ListPart)
	r.POST("/part/get", index.GetPart)

	r2 := r.Use(middleware.CheckToken)

	r2.POST("/part/product/apply", index.Apply)
	r2.POST("/part/product/apply/check", index.ApplyCheck)
	r2.POST("/part/product/apply/list", index.ApplyList)
	r2.POST("/part/product/apply/list/supplier", index.ApplyListBySupplier)
	r2.POST("/part/product/apply/audit", index.ApplyAudit)
	r2.POST("/part/product/exit", index.ExitPart)
}
