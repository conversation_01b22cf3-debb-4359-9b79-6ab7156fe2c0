package integralOrderService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/integralOrderDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/integralProductService"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	CreateAndPay(ctx context.Context, buyerID, productID primitive.ObjectID, openID string, integralAccount model.IntegralAccount) (interface{}, error)
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.IntegralOrderStatus) error
	Get(ctx context.Context, id primitive.ObjectID) (model.IntegralOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralOrder, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)

	Ship(ctx context.Context, id primitive.ObjectID) error

	Cancel(ctx context.Context, id primitive.ObjectID) error

	AgentPay(ctx context.Context, content string) error

	NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error

	NotifyCancelStatus(ctx context.Context, res allinpay.NotifyPay) error

	CloseByBizOrderNo(ctx context.Context, content string) error

	NotifyPaySignalAgentPayStatus(ctx context.Context, res allinpay.NotifyPay) error
}

type integralOrderService struct {
	mdb *mongo.Database
	rdb *redis.Client

	l *zap.SugaredLogger

	integralProductS integralProductService.ServiceInterface
	authenticationS  authenticationService.ServiceInterface
	allInPayOrderS   payModule.OrderService
	IntegralOrderDao integralOrderDao.DaoInt
}

func NewIntegralOrderService() ServiceInterface {
	return integralOrderService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
		l:   global.OrderLogger.Sugar(),

		integralProductS: integralProductService.NewIntegralProductService(),
		authenticationS:  authenticationService.NewAuthenticationService(),
		allInPayOrderS:   payModule.NewOrderS(),
		IntegralOrderDao: dao.IntegralOrderDao,
	}
}

func (s integralOrderService) CreateAndPay(ctx context.Context, buyerID, productID primitive.ObjectID, openID string, integralAccount model.IntegralAccount) (interface{}, error) {
	key := "existIntegralOrder:" + buyerID.Hex()
	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "操作频繁，10秒后重试")
	} else {
		set := s.rdb.Set(ctx, key, "", time.Second*10)
		_ = set
	}

	integralProduct, err := s.integralProductS.Get(ctx, productID)
	if err != nil {
		return nil, err
	}

	if integralAccount.Num < integralProduct.CostNum {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "积分不足")
	}

	now := time.Now().UnixMilli()
	data := model.IntegralOrder{
		ID:            primitive.NewObjectID(),
		BuyerID:       buyerID,
		ProductID:     productID,
		ProductTitle:  integralProduct.Title,
		ImageCover:    integralProduct.ImageCover,
		CostNum:       integralProduct.CostNum,
		Price:         integralProduct.Price,
		DiscountPrice: integralProduct.DiscountPrice,
		BizOrderNo:    util.NewUUID(),
		PayStatus:     model.PayStatusTypeToPay,
		Status:        model.IntegralOrderStatusToShip,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	err = s.IntegralOrderDao.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	//authentication, err := s.authenticationS.GetBuyerByUser(ctx, userID)
	//if err != nil {
	//	return nil, err
	//}

	amount := data.DiscountPrice

	miniMethod := allinpay.PayMethodMiniProgramBack(amount, global.WechatAppID, openID)

	var rList []pays.RecieverItem
	item := pays.RecieverItem{
		BizUserId: "********-2adc-4c14-9e14-1b7b24354c07",
		Amount:    amount,
	}
	rList = append(rList, item)

	validateType := 0

	req := pays.AgentCollectApplyReq{
		BizOrderNo: data.BizOrderNo,
		//PayerId:             authentication.PayBizUserId,
		RecieverList:        rList,
		TradeCode:           global.TradeCodeCollect,
		Amount:              amount,
		Fee:                 0,            // 分账再收
		ValidateType:        validateType, // 无验证	0	整型	仅渠道验证，通商云不做交易验证   1 短信
		BackUrl:             global.BackHost + global.BackUrlIntegralPay,
		OrderExpireDatetime: util.ExpirePayOrderTime(5), // 支付订单过期
		PayMethod:           miniMethod,
		IndustryCode:        global.IndustryCode,
		IndustryName:        global.IndustryName,
		Source:              pays.SourceMobile,
		Summary:             "",
		ExtendInfo:          "integral pay",
	}

	res, err := s.allInPayOrderS.AgentCollectApplyS(req)
	if err != nil {
		s.l.Error("充值支付错误", err)
		return "", err
	}

	ps := model.PayResult{}
	ps.PayStatus = res.PayStatus
	ps.PayOpenID = openID
	ps.PayFailMessage = res.PayFailMessage
	ps.OrderNo = res.OrderNo
	ps.BizUserId = res.BizUserId
	ps.BizOrderNo = res.BizOrderNo
	ps.ReqPayInterfaceNo = res.ReqPayInterfaceNo
	ps.PayInterfaceOutTradeNo = res.PayInterfaceOutTradeNo
	ps.PayInterfacetrxcode = res.PayInterfacetrxcode
	ps.ChannelFee = res.ChannelFee
	ps.Chnldata = res.Chnldata
	ps.ChannelPaytime = res.ChannelPaytime
	ps.Cusid = res.Cusid
	ps.Acct = res.Acct
	ps.TradeNo = res.TradeNo
	ps.ValidationType = res.ValidationType
	ps.MiniprogrampayinfoVsp = res.MiniprogrampayinfoVsp
	ps.ExtendInfo = res.ExtendInfo

	m := make(map[string]interface{})
	err = json.Unmarshal([]byte(res.PayInfo), &m)
	if err != nil {
		s.l.Errorf("积分订单解析pay_info错误:%v", err)
		return nil, err
	}

	ps.PayInfo = m

	err = s.IntegralOrderDao.UpdateOne(ctx, bson.M{"biz_order_no": res.BizOrderNo}, bson.M{"$set": bson.M{
		"pay_status": model.PayStatusTypePending,
		"pay_result": ps,
		"updated_at": now,
	}})
	if err != nil {
		s.l.Errorf("更新积分调起支付异常%v", err)
		return "", err
	}

	mnsSendService.NewMNSClient().SendCloseIntegralOrder(model.MNSBizOrderNo{
		BizOrderNo: data.BizOrderNo,
	})

	return m, nil
}

func (s integralOrderService) UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.IntegralOrderStatus) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"status":     status,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.IntegralOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s integralOrderService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralOrder, int64, error) {
	list, i, err := s.IntegralOrderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s integralOrderService) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.IntegralOrderDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return i, nil
}

func (s integralOrderService) Get(ctx context.Context, id primitive.ObjectID) (model.IntegralOrder, error) {
	filter := bson.M{
		"_id": id,
	}
	i, err := s.IntegralOrderDao.Get(ctx, filter)
	if err != nil {
		return model.IntegralOrder{}, err
	}
	return i, nil
}

func (s integralOrderService) Ship(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.IntegralOrderDao.UpdateOne(ctx, filter, bson.M{
		"$set": bson.M{
			"status":     model.IntegralOrderStatusFinish,
			"updated_at": time.Now().UnixMilli(),
		},
	})
	if err != nil {
		return err
	}

	// 分账
	mnsSendService.NewMNSClient().SendIntegralAgentPay(model.MNSIntegralAgentPayOrder{OrderID: id.Hex()})
	return nil
}

func (s integralOrderService) AgentPay(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("integralOrderService AgentPay error:%v", err)
			return
		}
	}()

	var data model.MNSIntegralAgentPayOrder
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	id, err := util.ConvertToObjectWithCtx(ctx, data.OrderID)
	if err != nil {
		return err
	}

	order, err := s.Get(ctx, id)
	if err != nil {
		return err
	}
	_ = order

	var cPayList []pays.CollectPayItem
	cPayList = append(cPayList, pays.CollectPayItem{
		BizOrderNo: order.PayResult.BizOrderNo,
		Amount:     order.PayResult.Amount, // 采购商实付  金额，单位：分；部分代付时，可以少于或等于托管代收订单金额
	})

	bizOrderNo := util.NewUUID()

	// 更新分账信息
	//	AgentPayBizOrderNo        string              `json:"agent_pay_biz_order_no" bson:"agent_pay_biz_order_no"`                 // 代付
	//	AgentPayReceiverBizUserID string              `json:"agent_pay_receiver_biz_user_id" bson:"agent_pay_receiver_biz_user_id"` //
	//	AgentPayAmount            int                 `json:"agent_pay_amount" bson:"agent_pay_amount"`                             //
	//	AgentPayFee               int                 `json:"agent_pay_fee" bson:"agent_pay_fee"`                                   //
	//	AgentPayResult            AgentPayResult      `json:"agent_pay_result" bson:"agent_pay_result"`                             //

	err = s.IntegralOrderDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"agent_pay_biz_order_no":         bizOrderNo,
			"agent_pay_receiver_biz_user_id": "********-2adc-4c14-9e14-1b7b24354c07",
			"agent_pay_amount":               order.PayResult.Amount,
			"agent_pay_fee":                  0,
		},
	})
	if err != nil {
		return err
	}

	orderData, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	// 提起分账

	// 分账
	var splitList []pays.SplitRuleItem

	req := pays.SignalAgentPayReq{
		BizOrderNo:     orderData.AgentPayBizOrderNo,
		CollectPayList: cPayList,
		BizUserId:      orderData.AgentPayReceiverBizUserID,                    // 收款
		AccountSetNo:   global.AllInPayAccountSetInfo.EscrowUserNo,             // 托管账户集合
		BackUrl:        global.BackHost + global.BackUrlIntegralSignalAgentPay, // 单笔代付
		Amount:         orderData.AgentPayAmount,
		Fee:            orderData.AgentPayFee,
		SplitRuleList:  splitList,
		TradeCode:      global.TradeCodeCollectPay,
		Summary:        "",
		ExtendInfo:     "积分订单代付",
	}

	res, err := s.allInPayOrderS.SignalAgentPayS(req)
	if err != nil {
		return err
	}
	ps := model.AgentPayResult{
		PayStatus:      res.PayStatus,
		PayFailMessage: res.PayFailMessage,
		OrderNo:        res.OrderNo,
		BizOrderNo:     res.BizOrderNo,
		PayWhereabouts: res.PayWhereabouts,
		ExtendInfo:     res.ExtendInfo,
	}

	s.IntegralOrderDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"agent_pay_biz_order_no":         bizOrderNo,
			"agent_pay_receiver_biz_user_id": "********-2adc-4c14-9e14-1b7b24354c07",
			"agent_pay_amount":               order.PayResult.Amount,
			"agent_pay_fee":                  0,
		},
	})

	err = s.IntegralOrderDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"agent_pay_result": ps,
		},
	})
	if err != nil {
		s.l.Errorf("更新代付响应错误%v", err)
		return err
	}

	return nil
}

func (s integralOrderService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.IntegralOrder, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	data, err := s.IntegralOrderDao.Get(ctx, filter)
	if err != nil {
		return model.IntegralOrder{}, err
	}
	return data, nil
}

func (s integralOrderService) CloseByBizOrderNo(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("deliverNoteService Generate error:%v", err)
			return
		}
	}()

	var data model.MNSBizOrderNo

	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}
	marshal, _ := json.Marshal(data)

	zap.S().Infof("关闭积分订单信息：%s", string(marshal))

	depositOrder, err := s.GetByBizOrderNo(ctx, data.BizOrderNo)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Infof("查询充值单信息：%s，不存在，跳过", string(marshal))
		return nil
	}

	if depositOrder.PayStatus == model.PayStatusTypePending {
		//	 关闭订单
		req := pays.CloseOrderReq{
			BizOrderNo: data.BizOrderNo,
		}
		resClose, err := s.allInPayOrderS.CloseOrderS(req)
		if err != nil {
			return err
		}
		bytes, _ := json.Marshal(resClose)
		zap.S().Infof("关闭信息：%s", string(bytes))

		filter := bson.M{
			"biz_order_no": data.BizOrderNo,
		}

		err = s.IntegralOrderDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
			"pay_status": model.PayStatusTypeClose,
			"status":     model.IntegralOrderStatusClosed,
			"updated_at": time.Now().UnixMilli(),
		}})
	}

	return nil
}

// Cancel 取消订单-已付款
func (s integralOrderService) Cancel(ctx context.Context, id primitive.ObjectID) error {
	order, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	now := time.Now()
	created := time.UnixMilli(order.CreatedAt)
	if now.Sub(created).Seconds() < 60 {
		d := 60 - now.Sub(created).Seconds()
		return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("请%d秒后取消", int(d)))
	}

	if order.Status == model.IntegralOrderStatusFinish {
		//已经备货了
		return xerr.NewErr(xerr.ErrParamError, nil, "订单已发货")
	}
	if order.Status == model.IntegralOrderStatusCancel {
		return xerr.NewErr(xerr.ErrParamError, nil, "订单已取消，请刷新")
	}

	amount := order.DiscountPrice

	var rList []pays.RefundItem
	rList = append(rList, pays.RefundItem{
		//AccountSetNo:  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）
		BizUserId: "********-2adc-4c14-9e14-1b7b24354c07",
		Amount:    amount,
	})
	req := pays.RefundReq{
		BizOrderNo:    util.NewUUID(),
		OriBizOrderNo: order.BizOrderNo,
		OriOrderNo:    order.PayResult.OrderNo,
		BizUserId:     order.PayResult.BizUserId,
		RefundType:    "D0",
		RefundList:    rList,
		BackUrl:       global.BackHost + global.BackUrlIntegralCancel,
		Amount:        amount,
		//CouponAmount:  order.PaidAmount,
		CouponAmount:  0,
		FeeAmount:     0,
		RefundAccount: "TLT",
		ExtendInfo:    "cancel integral order",
	}
	res, err := s.allInPayOrderS.RefundS(req)
	if err != nil {
		s.l.Errorf("执行取消订单退款请求错误%v", err)
		return err
	}

	ps := model.RefundResult{
		//PayStatus:      res.PayStatus,
		PayFailMessage: res.PayFailMessage,
		OrderNo:        res.OrderNo,
		BizOrderNo:     res.BizOrderNo,
		Amount:         res.Amount,
		CouponAmount:   res.CouponAmount,
		FeeAmount:      res.FeeAmount,
		ExtendInfo:     res.ExtendInfo,
	}

	if res.PayStatus == "fail" {
		return xerr.NewErr(xerr.ErrOrder, nil, "退款失败，请联系客服")
	}

	err = s.IntegralOrderDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"cancel_result": ps,
			"status":        model.IntegralOrderStatusCancel,
			"updated_at":    time.Now().UnixMilli(),
		},
	})

	if err != nil {
		s.l.Errorf("积分，执行取消订单更新请求结果错误%v", err)
		return err
	}
	return nil
}

// NotifyPayStatus 回调
func (s integralOrderService) NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知
	if res.Status == "OK" {
		//“OK”标识支付成功；
		filter := bson.M{
			"biz_order_no": res.BizOrderNo,
		}

		order, err := s.GetByBizOrderNo(ctx, res.BizOrderNo)
		if err != nil {
			return err
		}

		if order.PayStatus == model.PayStatusTypePaid {
			return nil
		}

		err = s.IntegralOrderDao.UpdateOne(ctx, filter, bson.M{
			"$set": bson.M{
				"pay_status":                      model.PayStatusTypePaid,
				"pay_result.status":               res.Status,
				"pay_result.amount":               res.Amount,
				"pay_result.channel_paytime":      res.ChannelPaytime,
				"pay_result.cusid":                res.Cusid,
				"pay_result.biz_order_no":         res.BizOrderNo,
				"pay_result.channel_fee":          res.ChannelFee,
				"pay_result.pay_interfacetrxcode": res.PayInterfacetrxcode,
				"pay_result.pay_datetime":         res.PayDatetime,
				"pay_result.acct":                 res.Acct,
				"status":                          model.IntegralOrderStatusToShip,
				"updated_at":                      time.Now().UnixMilli(),
			},
		})
		if err != nil {
			s.l.Error("debt 更新支付成功状态错误：", err)
			return err
		}

		s.l.Infof("NotifyPayStatus %v", res)
		// 扣减积分
		mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeExchange, order.BuyerID, order.ID, order.CostNum)
	}

	return nil
}

func (s integralOrderService) NotifyCancelStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知  退款到银行账户/微信/支付宝成功
	if res.Status == "OK" {
		//“OK”标识支付成功；
		filter := bson.M{
			"biz_order_no": res.OriBizOrderNo,
			//"cancel_result.order_no":     res.OrderNo,
		}

		order, err := s.GetByBizOrderNo(ctx, res.OriBizOrderNo)
		if err != nil {
			return err
		}

		if order.CancelResult.PayStatus == "success" {
			return nil
		}

		update := bson.M{
			"$set": bson.M{
				"cancel_result.pay_status":                 "success",
				"cancel_result.pay_interface_out_trade_no": res.PayInterfaceOutTradeNo,
				"status":     model.IntegralOrderStatusCancel,
				"updated_at": time.Now().UnixMilli(),
			},
		}

		err = s.IntegralOrderDao.UpdateOne(ctx, filter, update)
		if err != nil {
			return err
		}

		zap.S().Infof("恢复积分：%s", res.BizOrderNo)
		// 恢复积分
		mnsSendService.NewMNSClient().SendGenerateIntegral(model.RecordTypeExchange, order.BuyerID, order.ID, order.CostNum)
	}
	return nil
}

// NotifyPaySignalAgentPayStatus 单笔代付
func (s integralOrderService) NotifyPaySignalAgentPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知
	if res.Status == "OK" {
		//“OK”标识支付成功；
		now := time.Now().UnixMilli()

		err := s.IntegralOrderDao.UpdateOne(ctx, bson.M{"biz_order_no": res.BizOrderNo}, bson.M{"$set": bson.M{
			"agent_pay_result.pay_status": "success",
			"agent_pay_result.status":     "OK",
			"updated_at":                  now,
		}})
		if err != nil {
			return err
		}

		s.l.Infof("代付回调成功%s", res.BizOrderNo)
		return nil
	}
	return nil
}
