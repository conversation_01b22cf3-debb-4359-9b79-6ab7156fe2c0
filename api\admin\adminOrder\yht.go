package adminOrder

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListYHT(ctx *gin.Context) {
	var req = struct {
		//PayStatus   int   `json:"pay_status"`
		//OrderStatus int   `json:"order_status"`
		Month int64 `json:"month"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}
	filter["order_type"] = model.OrderTypeWholeSale
	filter["user_type"] = "YHT"
	filter["deleted_at"] = 0

	//if req.OrderStatus != 0 {
	//	filter["order_status"] = req.OrderStatus
	//}

	//if req.PayStatus != 0 {
	//	filter["pay_status"] = model.PayStatusTypePaid
	//}

	begin, end, err := util.MonthScopeTimestamp(req.Month)
	filter["created_at"] = bson.M{
		"$gte": begin,
		"$lte": end,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id":    bson.M{"$in": orderIDs},
			"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	var list []orderRes
	for _, order := range orders {
		var refundApplyAmount int
		var refundAuditAmount int

		for _, refund := range refunds {
			if refund.OrderID == order.ID && !refund.IsWithdraw {
				refundApplyAmount += refund.Amount
				refundAuditAmount += refund.AuditAmount
			}
		}

		item := orderRes{
			Order:             order,
			RefundApplyAmount: refundApplyAmount,
			RefundAuditAmount: refundAuditAmount,
		}
		list = append(list, item)

	}

	xhttp.RespSuccess(ctx, list)
}

type orderRes struct {
	model.Order
	RefundApplyAmount int `json:"refund_apply_amount"`
	RefundAuditAmount int `json:"refund_audit_amount"`
}

func ListRefundOrderYHT(ctx *gin.Context) {
	var req = struct {
		RefundType     int   `json:"refund_type" validate:"required"`
		AuditStatus    int   `json:"audit_status" validate:"required"`
		WithdrawStatus int   `json:"withdraw_status"` // 撤销状态   0 所有 1 未撤销 2 已撤销
		Page           int64 `json:"page" validate:"min=1"`
		Limit          int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	statusType, err := model.BackAuditStatusType(req.AuditStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	refundType, err := model.BackRefundType(req.RefundType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, i, err := orderRefundService.NewOrderRefundService().ListByPageYHT(ctx, refundType, statusType, req.Page, req.Limit, req.WithdrawStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}
