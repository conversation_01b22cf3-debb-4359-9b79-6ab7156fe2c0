package middleware

//
//func CheckUID(ctx *gin.Context) {
//	auth := ctx.GetHeader("Authorization")
//	auth = strings.TrimSpace(auth)
//	if len(auth) > 0 {
//		myClaims, err := jwtService.NewJwtService().ParseToken(auth)
//		if err != nil {
//			zap.S().Warnf("解析token错误:%v,token:%v", err.Error(), auth)
//			//reqBody, _ := ctx.GetRawData()
//			//
//			//zap.S().Errorf("解析token错误请求%v", string(reqBody))
//			//xhttp.RespErr(ctx, err)
//			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
//			ctx.Abort()
//			return
//		}
//		if myClaims.ID == "" {
//			zap.S().<PERSON><PERSON><PERSON>("userID缺失%v", auth)
//			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
//			ctx.Abort()
//			return
//		}
//		ctx.Set("user_id", myClaims.ID)
//	}
//	ctx.Next()
//}
