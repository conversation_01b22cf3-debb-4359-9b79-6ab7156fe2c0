package sys

import (
	"base/core/xhttp"
	"base/model"
	"base/service/announceService"
	"base/service/servicePointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AnnounceUpsert 公告
func AnnounceUpsert(ctx *gin.Context) {
	var req = struct {
		ServicePointID string   `json:"service_point_id"`
		Content        []string `json:"content"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = announceService.NewAnnounceService().Upsert(ctx, pointID, req.Content)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// AnnounceGet 公告
func AnnounceGet(ctx *gin.Context) {
	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	get, err := announceService.NewAnnounceService().Get(ctx, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, get)
}

// AnnounceGetByWeb 公告
func AnnounceGetByWeb(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	get, err := announceService.NewAnnounceService().Get(ctx, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, get)
}

func ListAnnounce(ctx *gin.Context) {
	list, err := announceService.NewAnnounceService().List(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = list

	var ids []primitive.ObjectID
	for _, a := range list {
		ids = append(ids, a.ServicePointID)
	}

	points, err := servicePointService.NewServicePointService().ListCus(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"is_open":    true,
		"deleted_at": 0,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]AnnounceRes, 0, len(ids))

	for _, announce := range list {
		var servicePointName string
		for _, point := range points {
			if announce.ServicePointID == point.ID {
				servicePointName = point.Name
			}
		}

		resList = append(resList, AnnounceRes{
			Announce:         announce,
			ServicePointName: servicePointName,
		})
	}

	xhttp.RespSuccess(ctx, resList)
}

type AnnounceRes struct {
	model.Announce
	ServicePointName string `json:"service_point_name"`
}
