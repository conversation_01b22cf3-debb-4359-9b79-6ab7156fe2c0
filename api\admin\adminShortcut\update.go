package adminShortcut

import (
	"base/core/xhttp"
	"base/service/shortcutService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func UpdateSort(ctx *gin.Context) {
	req := types.SwipeUpdateSort{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = shortcutService.NewShortcutService().UpdateSort(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

func Update(ctx *gin.Context) {
	req := types.ShortcutUpdate{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = shortcutService.NewShortcutService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

func UpdateProduct(ctx *gin.Context) {
	var req = struct {
		ID         string   `json:"id" validate:"len=24"`
		ProductIDs []string `json:"product_ids" validate:"min=0"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var ids []primitive.ObjectID
	for _, s := range req.ProductIDs {
		i, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, i)
	}

	err = shortcutService.NewShortcutService().UpdateProduct(ctx, id, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}
