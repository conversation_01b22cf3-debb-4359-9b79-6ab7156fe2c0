package types

import (
	"base/model"
)

// OrderRefundReq 退款
type OrderRefundReq struct {
	OrderID          string                 `json:"order_id" validate:"len=24"`
	RefundType       model.RefundType       `json:"refund_type" validate:"required"`        // 退款类型
	RefundReasonType model.RefundReasonType `json:"refund_reason_type" validate:"required"` // 退款原因类型
	Reason           string                 `json:"reason" validate:"required"`             // 原因
	Amount           int                    `json:"amount"  validate:"required"`            // 金额 不退运费
	ProductID        string                 `json:"product_id" validate:"len=24"`           // 商品ID
	SkuIDCode        string                 `json:"sku_id_code"`                            // 商品编码
	SkuName          string                 `json:"sku_name"`                               // 商品名称
	RefundNum        int                    `json:"refund_num"`                             // 退款数量
	RefundWeight     int                    `json:"refund_weight"`                          // 退款重量
	ImageList        []model.FileInfo       `json:"image_list"`                             // 图片列表
	ImageListOne     []model.FileInfo       `json:"image_list_one"`                         // 图片列表
	ImageListTwo     []model.FileInfo       `json:"image_list_two"`                         // 图片列表
	ImageListThree   []model.FileInfo       `json:"image_list_three"`                       // 图片列表
	Video            model.FileInfo         `json:"video"`                                  // 视频
}
