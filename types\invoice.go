package types

import "base/model"

type InvoiceTitleUpsert struct {
	ID               string                 `json:"id" `
	InvoiceTitleType model.InvoiceTitleType `json:"invoice_title_type"`
	InvoiceTitle     string                 `json:"invoice_title"`
	TaxNumber        string                 `json:"tax_number"`
	Address          string                 `json:"address"`
	PhoneNumber      string                 `json:"phone_number"`
	BankName         string                 `json:"bank_name"`
	BankAccount      string                 `json:"bank_account"`
	Remark           string                 `json:"remark"`
}

type InvoiceUpsert struct {
	Remark string `json:"remark"`
}
