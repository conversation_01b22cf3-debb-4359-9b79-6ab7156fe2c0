package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/categoryService"
	"base/service/orderQualityService"
	"base/service/productBuyPriceService"
	"base/util"
	"sort"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetMonthlyProfit(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Begin      int64  `json:"begin"`
		End        int64  `json:"end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//begin, end, err := util.MonthScopeTimestamp(req.Timestamp)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//_ = begin
	//_ = end

	filter := bson.M{
		"stock_up_day_time": bson.M{
			"$gte": req.Begin,
			"$lte": req.End,
		},
	}

	if len(req.SupplierID) == 24 {
		supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = supplierID
	}

	list, err := productBuyPriceService.NewProductBuyPriceService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mSupplier := make(map[primitive.ObjectID][]model.ProductBuyPrice)
	for _, price := range list {
		mSupplier[price.SupplierID] = append(mSupplier[price.SupplierID], price)
	}

	mProfit := make(map[primitive.ObjectID]int)
	for supplierID, priceList := range mSupplier {
		for _, price := range priceList {
			var p int
			profitAmountDe := decimal.NewFromInt(int64(price.ProfitAmount))

			if price.IsCheckWeight {
				//	重量*单价差价
				weightDe := decimal.NewFromInt(int64(price.TotalSortWeight)).Div(decimal.NewFromInt(1000))
				intPart := weightDe.Mul(profitAmountDe).IntPart()
				p = int(intPart)
			} else {
				//	 数量*单价差价
				buyNumDe := decimal.NewFromInt(int64(price.BuyNum))
				intPart := buyNumDe.Mul(profitAmountDe).IntPart()
				p = int(intPart)
			}
			mProfit[supplierID] += p
		}
	}

	var resList []profitList

	for id, i := range mProfit {
		resList = append(resList, profitList{
			SupplierID:   id,
			ProfitAmount: i,
		})
	}

	xhttp.RespSuccess(ctx, resList)
}

type profitList struct {
	SupplierID   primitive.ObjectID `json:"supplier_id"`
	ProfitAmount int                `json:"profit_amount"`
}

// GetBuyStats 采购商数据
func GetBuyStats(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Timestamp  int64  `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"stock_up_day_time": ts,
	}

	if len(req.SupplierID) == 24 {
		supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = supplierID
	}

	list, err := productBuyPriceService.NewProductBuyPriceService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mCategory := make(map[primitive.ObjectID][]model.ProductBuyPrice)
	for _, i := range list {
		if len(i.CategoryIDs) > 2 {
			mCategory[i.CategoryIDs[1]] = append(mCategory[i.CategoryIDs[1]], i)
		}
	}
	var ids []primitive.ObjectID
	for cid, _ := range mCategory {
		ids = append(ids, cid)
	}

	categories, err := categoryService.NewCategoryService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	mc := make(map[primitive.ObjectID]string)
	for _, v := range categories {
		mc[v.ID] = v.Name
	}

	var resList []ByCategory

	for cid, ups := range mCategory {
		j := ByCategory{
			CategoryID:   cid,
			CategoryName: mc[cid],
			List:         ups,
		}
		resList = append(resList, j)
	}

	sort.Sort(byCategoryList(resList))

	totalProfit, totalOrderProductAmount, totalBuyProductAmount := dealStats(resList)
	totalProfitRate := calcProfitRate(totalProfit, totalOrderProductAmount)
	resBuyStats := resBuyStats{
		List:                    resList,
		TotalProfit:             totalProfit,
		TotalOrderProductAmount: totalOrderProductAmount,
		TotalBuyProductAmount:   totalBuyProductAmount,
		TotalProfitRate:         totalProfitRate,
	}

	xhttp.RespSuccess(ctx, resBuyStats)
}

func dealStats(list []ByCategory) (int, int, int) {
	totalProfit := 0
	totalOrderProductAmount := 0
	totalBuyProductAmount := 0
	for _, perCategory := range list {
		for _, perProduct := range perCategory.List {

			if perProduct.IsCheckWeight {
				// 重量*单价
				totalOrderProductAmount += calcWeightPrice(perProduct.TotalSortWeight, perProduct.AverageUnitPrice)

				// totalBuyProductAmount += calcWeightPrice(perProduct.TotalSortWeight, perProduct.AverageBuyUnitPrice)

				totalProfit += calcWeightPrice(perProduct.TotalSortWeight, perProduct.ProfitAmount)

			} else {
				// 数量*单价
				totalOrderProductAmount += perProduct.BuyNum * perProduct.AverageUnitPrice

				// totalBuyProductAmount += perProduct.BuyNum * perProduct.AverageBuyUnitPrice

				totalProfit += perProduct.ProfitAmount * perProduct.BuyNum

			}
			totalBuyProductAmount += perProduct.BuyAmount
		}
	}
	return totalProfit, totalOrderProductAmount, totalBuyProductAmount
}

// 计算重量*单价
func calcWeightPrice(weight int, price int) int {
	weightDe := decimal.NewFromInt(int64(weight)).Div(decimal.NewFromInt(1000))
	intPart := weightDe.Mul(decimal.NewFromInt(int64(price))).IntPart()
	return int(intPart)
}

// 百分比计算，保留一位小数
func calcProfitRate(totalProfit, totalOrderProductAmount int) float64 {
	if totalOrderProductAmount == 0 {
		return 0
	}
	totalProfitDe := decimal.NewFromInt(int64(totalProfit))

	totalOrderProductAmountDe := decimal.NewFromInt(int64(totalOrderProductAmount))

	percentDe := totalProfitDe.Div(totalOrderProductAmountDe)

	profitRate, _ := percentDe.Mul(decimal.NewFromInt(100)).Round(1).Float64()
	return profitRate
}

// 采购统计
type resBuyStats struct {
	List                    []ByCategory `json:"list"`
	TotalProfit             int          `json:"total_profit"`
	TotalOrderProductAmount int          `json:"total_order_product_amount"`
	TotalBuyProductAmount   int          `json:"total_buy_product_amount"`
	TotalProfitRate         float64      `json:"total_profit_rate"` // 总利润率
}

// ByCategory 采购统计-分类
type ByCategory struct {
	CategoryID   primitive.ObjectID      `json:"category_id"`
	CategoryName string                  `json:"category_name"`
	List         []model.ProductBuyPrice `json:"list"`
}

// RefreshBuyStats 刷新采购总价
func RefreshBuyStats(ctx *gin.Context) {
	var req = struct {
		ProductID      string `json:"product_id"`
		StockUpNo      int    `json:"stock_up_no"`
		StockUpDayTime int64  `json:"stock_up_day_time"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	quality, err := orderQualityService.NewOrderQualityService().GetByProduct(ctx, productID, req.StockUpNo, req.StockUpDayTime)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productBuyPriceService.NewProductBuyPriceService().Upsert(ctx, quality)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// 排序
type byCategoryList []ByCategory

func (array byCategoryList) Len() int {
	return len(array)
}

func (array byCategoryList) Less(i, j int) bool {
	return array[i].CategoryName < array[j].CategoryName //从小到大， 若为大于号，则从大到小
}

func (array byCategoryList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}
