package withdraw

import (
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/service/withdrawService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// SignAcctProtocol 账户提现协议签约
func SignAcctProtocol(ctx *gin.Context) {
	//var req = struct {
	//	SignType string `json:"sign_type"` // company personal
	//}{}
	//userID, err := xhttp.ParseUser(ctx, &req)
	//if err != nil {
	//	return
	//}
	//env, err := xhttp.GetEnv(ctx)
	//if err != nil {
	//	return
	//}
	//
	//auth, err := authenticationService.NewAuthenticationService().GetByUserAndObject(ctx, userID, env)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//url, err := withdrawService.NewWithdrawService().SignAcctProtocol(ctx, auth, req.SignType)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, url)
}

func SignAcctProtocolBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	auth, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	url, err := withdrawService.NewWithdrawService().SignAcctProtocolBuyer(ctx, auth)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, url)
}

// SignContractFrontBack 账户协议签约前端主动返回
func SignContractFrontBack(ctx *gin.Context) {
	var req = struct {
		BizUserID      string `json:"biz_user_id" validate:"required"`
		Result         string `json:"result" validate:"required"`
		AcctProtocolNo string `json:"acct_protocol_no" validate:"required"` // 账户提现协议编号
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = withdrawService.NewWithdrawService().UpdateSignAcctProtocolFrontBack(ctx, req.BizUserID, req.Result, req.AcctProtocolNo)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// SignContractQuery 账户协议签约查询
func SignContractQuery(ctx *gin.Context) {
	userID, err := xhttp.UserID(ctx)
	if err != nil {
		return
	}
	env := xhttp.GetEnv(ctx)

	auth, err := authenticationService.NewAuthenticationService().GetByUserAndObject(ctx, userID, env)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	url, err := withdrawService.NewWithdrawService().SignAcctProtocolQuery(ctx, auth)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, url)
}
