package servicePoint

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/deliveryManService"
	"base/service/userService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
)

func CreateDeliveryMan(ctx *gin.Context) {
	var req = struct {
		Mobile         string `json:"mobile"`
		ServicePointID string `json:"service_point_id"`
		UserName       string `json:"user_name"`
		Desc           string `json:"desc"` // 说明
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	userName := strings.TrimSpace(req.UserName)

	if userName == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入姓名"))
		return
	}

	if len(req.Mobile) != 11 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入正确手机号"))
		return
	}

	byMobile, err := userService.NewUserService().GetByMobile(ctx, req.Mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		err = nil
		//  新建
		byMobile, err = userService.NewUserService().GetOrCreateUser(ctx, req.Mobile, "", "")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	deliveryMan, err := deliveryManService.NewDeliveryManService().GetByUserID(ctx, byMobile.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}

	if deliveryMan.ID != primitive.NilObjectID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该用户已是配送员"))
		return
	}

	err = deliveryManService.NewDeliveryManService().Create(ctx, byMobile.ID, servicePointID, req.UserName, req.Desc)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func GetDeliveryManByUser(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	userID, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	deliveryMan, err := deliveryManService.NewDeliveryManService().GetByUserID(ctx, userID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, deliveryMan)
}

func UpdateDeliveryMan(ctx *gin.Context) {
	var req = struct {
		ID       string `json:"id"`
		UserName string `json:"user_name"`
		Desc     string `json:"desc"` // 说明
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	userName := strings.TrimSpace(req.UserName)

	if userName == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入姓名"))
		return
	}

	err = deliveryManService.NewDeliveryManService().Update(ctx, id, req.UserName, req.Desc)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func DeleteDeliveryMan(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = deliveryManService.NewDeliveryManService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

func ListDeliveryMan(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := deliveryManService.NewDeliveryManService().ListByServicePoint(ctx, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, man := range list {
		ids = append(ids, man.UserID)
	}

	users, err := userService.NewUserService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]resMan, 0, len(list))

	for _, man := range list {
		var mobile string

		for _, user := range users {
			if man.UserID == user.ID {
				mobile = user.Mobile
			}
		}
		resList = append(resList, resMan{
			DeliveryMan: man,
			Mobile:      mobile,
		})
	}

	xhttp.RespSuccess(ctx, resList)

}

type resMan struct {
	model.DeliveryMan
	Mobile string `json:"mobile"`
}
