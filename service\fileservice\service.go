package fileservice

import (
	"base/global"
	"base/model"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"github.com/go-redis/redis/v8"
)

var cacheKey = "files"

// Service 文件服务接口
type Service interface {
	// ProductSaleStatsExport 商品销售统计导出
	ProductSaleStatsExport(ctx context.Context, content string) error
	// SaveFileInfo 保存文件信息
	SaveFileInfo(ctx context.Context, fileName, fileURL string) error
	// GetFileList 获取文件列表
	GetFileList(ctx context.Context) ([]*model.File, error)
}

// fileService 文件服务实现
type fileService struct {
	rdb *redis.Client
}

// NewFileService 创建新的文件服务实例
func NewFileService() Service {
	return &fileService{
		rdb: global.RDBDefault,
	}
}

// SaveFileInfo 保存文件信息
func (s *fileService) SaveFileInfo(ctx context.Context, fileName, fileURL string) error {
	nowMilli := time.Now().UnixMilli()

	validTime := time.Duration(7 * 24 * time.Hour)

	info := &model.File{
		FileName:  fileName,
		FileURL:   fileURL,
		CreatedAt: nowMilli,
		ExpireAt:  nowMilli + validTime.Milliseconds(),
	}

	// 将文件信息序列化为JSON
	data, err := json.Marshal(info)
	if err != nil {
		return err
	}

	key := fmt.Sprintf("%s:%s", cacheKey, fileURL)

	exists, err := s.rdb.Exists(ctx, key).Result()
	if err != nil {
		return err
	}
	if exists == 1 {
		return nil
	}

	_, err = s.rdb.Set(ctx, key, data, validTime).Result()
	if err != nil {
		return err
	}

	return nil
}

// GetFileList 获取文件列表
func (s *fileService) GetFileList(ctx context.Context) ([]*model.File, error) {
	// 使用KEYS命令获取所有文件key
	pattern := fmt.Sprintf("%s:*", cacheKey)
	keys, err := s.rdb.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, err
	}

	var files []*model.File
	for _, key := range keys {
		// 获取文件信息
		data, err := s.rdb.Get(ctx, key).Result()
		if err != nil {
			continue
		}

		var file model.File
		if err := json.Unmarshal([]byte(data), &file); err != nil {
			continue
		}

		files = append(files, &file)
	}

	// 按创建时间降序排序
	sort.Slice(files, func(i, j int) bool {
		return files[i].CreatedAt > files[j].CreatedAt
	})

	return files, nil
}
