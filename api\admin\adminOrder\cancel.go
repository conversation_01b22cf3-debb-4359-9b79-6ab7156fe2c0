package adminOrder

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"sync"
)

var orderLock sync.Mutex

// Cancel 取消订单
func Cancel(ctx *gin.Context) {
	orderLock.Lock()
	defer orderLock.Unlock()

	var req = struct {
		OrderID string `json:"order_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	order, err := orderService.NewOrderService().Get(ctx, orderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if order.OrderStatus < model.OrderStatusTypeToStockUp {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单已取消"))
		return
	}

	if order.OrderStatus > model.OrderStatusTypeToQuality {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单已确认，不可取消"))
		return
	}

	if order.OrderStatus == model.OrderStatusTypeToStockUp && order.OrderType == model.OrderTypeRetail {
		// 1. 取消订单
		err = orderRefundService.NewOrderRefundService().CancelOrder(ctx, orderID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		xhttp.RespSuccess(ctx, nil)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
