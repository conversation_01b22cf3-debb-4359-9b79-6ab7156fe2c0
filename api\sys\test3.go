package sys

import (
	"base/core/xhttp"
	"base/dao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/orderAgentPayService"
	"base/service/orderDebtService"
	"base/service/orderQualityService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/orderWarehouseService"
	"base/service/parentOrderService"
	"base/service/productBuyPriceService"
	"base/service/supplierService"
	"base/service/yeeMerchantService"
	"base/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func Test3(ctx *gin.Context) {
	var req = struct {
		P1 string `json:"p1"`
		P2 string `json:"p2"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 利润同步
	// list, err := orderAgentPayService.NewOrderAgentPayService().List(ctx, bson.M{
	// 	"created_at": bson.M{
	// 		"$gte": 1748707200000, // 2025-06-01 00:00:00
	// 	},
	// })
	// if err != nil {
	// 	return
	// }

	// var ids []primitive.ObjectID
	// for _, agent := range list {
	// 	_ = agent
	// 	ids = append(ids, agent.ID)

	// }

	// orders, err := orderService.NewOrderService().ListByOrderIDs(ctx, ids)
	// if err != nil {
	// 	return
	// }

	// for _, order := range orders {
	// 	if order.CreatedAt > 1748707200000 {
	// 		//id, _ := primitive.ObjectIDFromHex("6841bd45f530d0df92f0f72b")
	// 	}
	// }

	// id, _ := primitive.ObjectIDFromHex("682142fb1238fb73bf56c467")
	// product, err := productService.NewProductService().Get(ctx, id)
	// if err != nil {
	// 	return
	// }

	// productAuditService.NewProductAuditService().Create(ctx, product, true)
	// TestOrder(ctx)
}

// func TestOrder(ctx *gin.Context) {
// 	filter := bson.M{
// 		"created_at": bson.M{
// 			"$gte": *************, // 2024-01-01 00:00:00
// 			"$lt":  1735574400000, // 2024-12-31 23:59:59
// 		},
// 		"pay_status": model.PayStatusTypePaid,
// 	}
// 	orders, err := orderService.NewOrderService().List(ctx, filter)
// 	if err != nil {
// 		return
// 	}

// 	var amount int
// 	for _, order := range orders {
// 		amount += order.PaidAmount
// 	}

// 	zap.S().Infof("amount: %d", amount)
// }

func TestRefund(ctx *gin.Context) {
	filter := bson.M{
		"created_at": bson.M{
			"$gt": 1741572117000,
			"$lt": 1744855317305,
		},
	}
	refunds, err := dao.OrderRefundDao.List(ctx, filter)
	if err != nil {
		return
	}

	for _, refund := range refunds {
		if refund.IsComplete && refund.AuditStatus == model.AuditStatusTypePass && refund.YeeRefundResult.Status == "" {
			format := time.UnixMilli(refund.CreatedAt).Format(time.RFC3339)
			zap.S().Infof("id:%s,buyerName:%s,time:%s", refund.ID.Hex(), refund.BuyerName, format)

		}
	}

	return
	//
	//idStrList := []string{"67f537ddbcc0be6c2e18d1c4", "67f8ef41215ea6c87da5eaf6", "67f8efcd215ea6c87da5eb48", "67fccff79448f5f91fce70f4", "67fde658a6282ffa891bca0d", "67fe1e548963fd23b0289f6b", "67fe50b823a4a3d9f89dc038"}
	//_ = idStrList
	//
	//for _, s := range idStrList {
	//	id, _ := primitive.ObjectIDFromHex(s)
	//	time.Sleep(time.Second * 3)
	//	err = orderRefundService.NewOrderRefundService().ConfirmRefund(ctx, id, model.ConfirmTypeAgree, "")
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//}

}

func changeProductOrigin(ctx *gin.Context) {
	var ids []primitive.ObjectID
	idStr := []string{"67cce4c49d6347fa287f728b", "67b934265cbfcbdff907cda4", "67c79c708d3d84b1dfb88c45", "67da1cf67451e93858ee009e", "67cf86ce04fc6cedf9c7354c", "673403e57e9e71ee77820727",
		"66e79b904cd9992b70c49bb5", "66ff49f83806fd1c13398b42", "66e79b874cd9992b70c49bb2"}
	for _, s := range idStr {
		id, _ := util.ConvertToObjectWithCtx(ctx, s)
		ids = append(ids, id)
	}

	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}

	dao.ProductDao.UpdateMany(ctx, filter, bson.M{
		"$set": bson.M{
			"product_origin_type": model.ProductOriginTypeForeign,
		},
	})

	rdb := global.RDBDefault
	for _, id := range ids {
		rdb.Del(ctx, "product:"+id.Hex())
	}
}

func buyCheck(ctx *gin.Context) {
	filter := bson.M{
		"stock_up_day_time": bson.M{
			"$gte": 1741104000000, // 2025-03-05 00:00:00
			"$lte": 1742486399000,
		},
		//	只需要部分字段
	}
	supplierID, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b529f")

	filter["supplier_id"] = supplierID

	list, err := productBuyPriceService.NewProductBuyPriceService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = list

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
	}
	_ = index

	err = f.SetCellValue(sheetName, "B1", "商品")
	err = f.SetCellValue(sheetName, "C1", "数量")
	//err = f.SetCellValue(sheetName, "D1", "重量")
	err = f.SetCellValue(sheetName, "E1", "总金额")

	f.SetColWidth(sheetName, "B", "B", 40)
	f.SetColWidth(sheetName, "A", "A", 16)

	offset := 2

	// 按天进行分组

	mALl := make(map[int64][]model.ProductBuyPrice)

	for _, price := range list {
		timestamp, err := util.DayStartZeroTimestamp(price.StockUpDayTime)
		if err != nil {
			return
		}
		mALl[timestamp] = append(mALl[timestamp], price)
	}

	var total int

	for dayAt, prices := range mALl {
		ll := len(prices)

		f.MergeCell(sheetName, "A"+strconv.Itoa(offset), "A"+strconv.Itoa(offset+ll-1))

		format := time.UnixMilli(dayAt).Format("2006-01-02")
		f.SetCellValue(sheetName, "A"+strconv.Itoa(offset), format)

		for i, price := range prices {
			f.SetRowHeight(sheetName, i+offset, 22)
			yuanStr := util.DealMoneyToYuanStr(price.BuyAmount)
			err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i+offset), price.ProductTitle)

			err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i+offset), price.BuyNum)
			err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i+offset), yuanStr)

			total += price.BuyAmount
		}

		offset += ll

	}

	f.SetCellValue(sheetName, "F"+strconv.Itoa(offset), util.DealMoneyToYuanStr(total))

	f.SaveAs("a.xlsx")

}

func manualAdjustAfterSale(ctx *gin.Context) {

	refunds, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"refund_type": model.RefundTypeAfterSale,
		"created_at": bson.M{
			//"$lte": 1742523647000,
			"$gte": 1742523647000,
		},
	})
	if err != nil {
		return
	}

	refundDao := dao.OrderRefundDao

	for i, refund := range refunds {
		_ = refund

		err := refundDao.UpdateOne(ctx, bson.M{
			"_id": refund.ID,
		}, bson.M{
			"$set": bson.M{
				"auditor_type": model.AuditorTypeSupplier,
				"is_complete":  false,
			},
		})
		if err != nil {
			return
		}
		zap.S().Infof("index: %d", i)
	}

}

func manualWithdraw(ctx *gin.Context) {
	var err error
	id, _ := primitive.ObjectIDFromHex("6745c8ad99e7921cdcfe9985") // 服务仓
	//id, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b52a0")

	merchant, err := yeeMerchantService.NewYeeMerchantService().GetByID(ctx, id)
	if err != nil {
		return
	}
	_ = merchant

	err = yeeMerchantService.NewYeeMerchantService().AccountWithDraw(ctx, merchant, 68672, merchant.SettlementAccountInfo.BankCardNo)
	if err != nil {
		return
	}
}

func tempBuyerStats(ctx *gin.Context) {
	buyerID, _ := primitive.ObjectIDFromHex("665ffa358532b6ef301943dc")
	orders, err := orderService.NewOrderService().List(ctx,
		bson.M{
			"created_at": bson.M{
				"$gte": *************,
				"$lte": *************,
			},
			"order_status": model.OrderStatusTypeFinish,
			"pay_status":   model.PayStatusTypePaid,
			"buyer_id":     buyerID,
		})
	if err != nil {
		return
	}

	var totalProductAmount int
	var totalRefundProductAmount int
	var totalDebtProductAmount int

	var ids []primitive.ObjectID

	for _, order := range orders {
		for _, productOrder := range order.ProductList {
			totalProductAmount += productOrder.ProductAmount
		}
		ids = append(ids, order.ID)
	}

	refunds, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		return
	}

	for _, refund := range refunds {
		totalRefundProductAmount += refund.AuditAmount
	}

	debts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		return
	}

	for _, d := range debts {
		totalDebtProductAmount += d.TotalProductAmount
	}

	yuanStr := util.DealMoneyToYuanStr(totalProductAmount)
	refundStr := util.DealMoneyToYuanStr(totalRefundProductAmount)
	debtStr := util.DealMoneyToYuanStr(totalDebtProductAmount)

	zap.S().Infof("商品金额：%v", yuanStr)
	zap.S().Infof("退款金额：%v", refundStr)
	zap.S().Infof("补差金额：%v", debtStr)

}

func checkRefund(ctx *gin.Context) {
	refunds, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": *************, // 2025-2-1 00:00:00
			"$lte": 1743436800000, // 2025-4-1 00:00:00
		},
		"audit_status": model.AuditStatusTypePass,
	})
	if err != nil {
		return
	}

	for _, refund := range refunds {
		if refund.YeeRefundResult.Status != "SUCCESS" {
			zap.S().Errorf("------------------------:%s", refund.ID.Hex())
		}
	}

}

func checkDebt(ctx *gin.Context) {
	var err error
	_ = err
	//id, _ := primitive.ObjectIDFromHex("6745c8ad99e7921cdcfe9985")
	//
	//merchant, err := yeeMerchantService.NewYeeMerchantService().GetByID(ctx, id)
	//if err != nil {
	//	return
	//}
	//_ = merchant
	//
	//accountQuery, err := yeeMerchantService.NewYeeMerchantService().BalanceAccountQuery(ctx, merchant)
	//if err != nil {
	//	return
	//}
	//_ = accountQuery
	//
	//xhttp.RespSuccess(ctx, accountQuery)

	//supplierID, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b529f")
	//debts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
	//	"supplier_id": supplierID,
	//	"pay_status":  model.PayStatusTypePaid,
	//	"created_at": bson.M{
	//		"$lte": *************,
	//	},
	//})
	//if err != nil {
	//	return
	//}
	//
	//for _, d := range debts {
	//	if d.YeeWechatResult.OrderID == "9c86a7f780e94b1eaf76a67fd5680c7a" {
	//		queryRes, err := orderService.NewOrderService().YeeTradeOrderQuery(ctx, d.YeeWechatResult.OrderID, d.YeeWechatResult.ParentMerchantNo, d.YeeWechatResult.MerchantNo)
	//		if err != nil {
	//			return
	//		}
	//		_ = queryRes
	//
	//		if queryRes.UnSplitAmount != 0 {
	//			zap.S().Errorf("id:%s,金额：%v，未分账金额：%v", d.ID.Hex(), queryRes.OrderAmount, queryRes.UnSplitAmount)
	//		}
	//		//if queryRes.TotalRefundAmount+queryRes.TotalDivideAmount != queryRes.OrderAmount {
	//		//	zap.S().Errorf("parent id:%s,订单：%s，金额：%v，未分账金额：%d", parent.ID.Hex(), sub.OrderId, queryRes.OrderAmount, queryRes.UnSplitAmount)
	//		//}
	//	}
	//}

	//agentID, _ := primitive.ObjectIDFromHex("67cec90f04fc6cedf9c7183d")
	//agentPay, err := orderAgentPayService.NewOrderAgentPayService().Get(ctx, agentID)
	//if err != nil {
	//	return
	//}
	//
	//err = orderAgentPayService.NewOrderAgentPayService().YeeDivide(ctx, agentPay)
	//if err != nil {
	//	return
	//}
	//// 执行完结分账
	//if agentPay.PayMethod == model.PayMethodTypeYeeWechat {
	//	agentPay2, err := orderAgentPayService.NewOrderAgentPayService().Get(ctx, agentID)
	//	if err != nil {
	//		return
	//	}
	//	err = orderAgentPayService.NewOrderAgentPayService().YeeDivideComplete(ctx, agentPay2)
	//	if err != nil {
	//		return
	//	}
	//}
}

func checkDivide(ctx *gin.Context) {
	var err error
	id, _ := primitive.ObjectIDFromHex("6745c8ad99e7921cdcfe9985") // 服务仓
	//id, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b52a0")

	merchant, err := yeeMerchantService.NewYeeMerchantService().GetByID(ctx, id)
	if err != nil {
		return
	}
	_ = merchant

	accountQuery, err := yeeMerchantService.NewYeeMerchantService().BalanceAccountQuery(ctx, merchant)
	if err != nil {
		return
	}
	_ = accountQuery

	xhttp.RespSuccess(ctx, accountQuery)

	supplierID, _ := primitive.ObjectIDFromHex("67c53f69c7374310352b529f")
	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		"supplier_id": supplierID,
		"pay_status":  model.PayStatusTypePaid,
		"created_at": bson.M{
			"$lte": *************,
		},
	})
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, order := range orders {
		//if order.PayMethod == model.PayMethodTypeYeeBalance {
		ids = append(ids, order.ParentOrderID)
		//}
	}

	parentOrders, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{
		//"pay_status": model.PayStatusTypePaid,
		"_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		return
	}

	for _, parent := range parentOrders {
		var f bool
		for _, sub := range parent.YeeWechatResult.NotifySubOrderList {
			// && sub.OrderId == "637ce1b6afd44823a0513d7c1b227484"
			if sub.MerchantNo == "***********" && sub.OrderId == "9c86a7f780e94b1eaf76a67fd5680c7a" {
				f = true
				queryRes, err := orderService.NewOrderService().YeeTradeOrderQuery(ctx, sub.OrderId, parent.YeeWechatResult.ParentMerchantNo, sub.MerchantNo)
				if err != nil {
					return
				}
				_ = queryRes

				if queryRes.UnSplitAmount != 0 {
					zap.S().Errorf("parent id:%s,订单：%s，金额：%v，未分账金额：%d", parent.ID.Hex(), sub.OrderId, queryRes.OrderAmount, queryRes.UnSplitAmount)
				}
				//if queryRes.TotalRefundAmount+queryRes.TotalDivideAmount != queryRes.OrderAmount {
				//	zap.S().Errorf("parent id:%s,订单：%s，金额：%v，未分账金额：%d", parent.ID.Hex(), sub.OrderId, queryRes.OrderAmount, queryRes.UnSplitAmount)
				//}
			}

		}
		if !f {
			zap.S().Errorf("ffffffffffffffffffff:::%s", parent.ID.Hex())
		}
	}

	//orderID, _ := primitive.ObjectIDFromHex("67cdc7c09eca22d458e64862")
	//err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(ctx, orderID)
	//if err != nil {
	//	zap.S().Infof("%s", err.Error())
	//	return
	//}

	//id, _ := primitive.ObjectIDFromHex("67cfb6b67cd45a740fcf07f0")
	//refund, err := orderRefundService.NewOrderRefundService().GetByID(ctx, id)
	//if err != nil {
	//	return
	//}
	//err = orderRefundService.NewOrderRefundService().DoRefundYee(ctx, refund)
	//if err != nil {
	//	zap.S().Infof("%s", err.Error())
	//	return
	//}

}

func checkPointDivide(ctx *gin.Context) {
	var err error
	id, _ := primitive.ObjectIDFromHex("6745c8ad99e7921cdcfe9985") // 服务仓  ***********

	merchant, err := yeeMerchantService.NewYeeMerchantService().GetByID(ctx, id)
	if err != nil {
		return
	}
	_ = merchant

	accountQuery, err := yeeMerchantService.NewYeeMerchantService().BalanceAccountQuery(ctx, merchant)
	if err != nil {
		return
	}
	_ = accountQuery

	xhttp.RespSuccess(ctx, accountQuery)
	return

	parentOrders, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{
		"yee_wechat_result.notify_sub_order_list.merchantno": "***********",
		"pay_status": model.PayStatusTypePaid,
		"created_at": bson.M{
			//"$gte": *************, // 2025-03-01 00:00:00
			//"$lte": *************,

			//"$gte": *************, // 2025-02-20 00:00:00
			//"$lte": *************, // 2025-03-01 00:00:00

			//"$gte": *************, // 2025-02-1 00:00:00
			//"$lte": *************, // 2025-02-20 00:00:00

			"$gte": *************, // 2025-01-1 00:00:00
			"$lte": *************, // 2025-02-1 00:00:00
		},
	})
	if err != nil {
		return
	}

	//var ids []primitive.ObjectID
	//for _, order := range orders {
	//	//if order.PayMethod == model.PayMethodTypeYeeBalance {
	//	ids = append(ids, order.ParentOrderID)
	//	//}
	//}

	// ***********

	notMap := make(map[string]string)
	var amount float64

	for _, parent := range parentOrders {
		for _, sub := range parent.YeeWechatResult.NotifySubOrderList {
			// && sub.OrderId == "637ce1b6afd44823a0513d7c1b227484"
			if sub.MerchantNo == "***********" {
				queryRes, err := orderService.NewOrderService().YeeTradeOrderQuery(ctx, sub.OrderId, parent.YeeWechatResult.ParentMerchantNo, sub.MerchantNo)
				if err != nil {
					return
				}
				_ = queryRes

				if queryRes.UnSplitAmount != 0 {
					zap.S().Errorf("parent id:%s,订单：%s，金额：%v，未分账金额：%d", parent.ID.Hex(), sub.OrderId, queryRes.OrderAmount, queryRes.UnSplitAmount)
					format := time.UnixMilli(parent.CreatedAt).Format("2006-01-02 15:04:05")
					notMap[parent.ID.Hex()] = format
					amount += queryRes.UnSplitAmount
				}
			}

		}
	}

	marshal, _ := json.Marshal(notMap)
	zap.S().Infof("check ids: %v", string(marshal))
	zap.S().Infof("amount  :%v", amount)

}

func doDivideDeliver(ctx *gin.Context, p1 string) {
	id, _ := primitive.ObjectIDFromHex(p1)

	orderAgentPayService.NewOrderAgentPayService().ToAgentPayDeliver(ctx, id)

}

func doRefund(ctx *gin.Context) {

	id, _ := primitive.ObjectIDFromHex("67af186f1581a20c028db5f7")
	refund, err := orderRefundService.NewOrderRefundService().GetByID(ctx, id)
	if err != nil {
		return
	}

	err = orderRefundService.NewOrderRefundService().DoRefundYee(ctx, refund)
	if err != nil {
		return
	}
}

func testDivideDeliver(ctx *gin.Context) {
	list, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1732695037000,
			//"$gte": 1732695037000,
		},
		"pay_status": model.PayStatusTypePaid,
		"deliver_type": bson.M{
			"$in": bson.A{model.DeliverTypeDoor, model.DeliverTypeInstantDeliver},
		},
	})
	if err != nil {
		return
	}
	_ = list

	for i, parent := range list {
		if parent.DeliverFeeRes.FinalDeliverFee == 0 {
			continue
		}
		err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayDeliver(ctx, parent.ID)
		if err != nil {
			zap.S().Infof("%s", err.Error())
		}
		zap.S().Infof("%d", i)
	}
}

func GetMonthlyProfit(ctx *gin.Context) {
	begin := 1732694400000 // 2024-11-27 16:00:00
	end := 1732982399000   // 2024-11-30 23:59:59

	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"order_status": model.OrderStatusTypeFinish,
	})
	if err != nil {
		return
	}

	t0 := orders[0].StockUpDayTime
	minT := t0
	maxT := t0

	var ids []primitive.ObjectID

	for _, order := range orders {
		t := order.StockUpDayTime

		if maxT < t {
			maxT = t
		}

		if minT > t {
			minT = t
		}

		ids = append(ids, order.ID)
	}

	zap.S().Infof("%d,%d", minT, maxT)

	filter := bson.M{
		"stock_up_day_time": bson.M{
			"$gte": minT,
			"$lte": maxT,
		},
		"order_list.order_id": bson.M{
			"$in": ids,
		},
	}

	list, err := productBuyPriceService.NewProductBuyPriceService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mSupplier := make(map[primitive.ObjectID][]model.ProductBuyPrice)
	for _, price := range list {
		mSupplier[price.SupplierID] = append(mSupplier[price.SupplierID], price)
	}

	mProfit := make(map[primitive.ObjectID]int)
	for supplierID, priceList := range mSupplier {

		for _, price := range priceList {
			for _, o := range price.OrderList {
				var exist bool
				for _, order := range orders {
					if o.OrderID == order.ID {
						exist = true
						break
					}
				}
				if !exist {
					zap.S().Infof("不存在：%s", o.OrderID.Hex())
				}

			}
		}

		for _, price := range priceList {
			var p int
			profitAmountDe := decimal.NewFromInt(int64(price.ProfitAmount))

			if price.IsCheckWeight {
				//	重量*单价差价
				weightDe := decimal.NewFromInt(int64(price.TotalSortWeight)).Div(decimal.NewFromInt(1000))
				intPart := weightDe.Mul(profitAmountDe).IntPart()
				p = int(intPart)
			} else {
				//	 数量*单价差价
				buyNumDe := decimal.NewFromInt(int64(price.BuyNum))
				intPart := buyNumDe.Mul(profitAmountDe).IntPart()
				p = int(intPart)
			}
			mProfit[supplierID] += p
		}
	}

	var resList []profitList

	for id, i := range mProfit {

		supplier, _ := supplierService.NewSupplierService().Get(ctx, id)

		money := dealMoney(i)

		resList = append(resList, profitList{
			SupplierID:   id,
			SupplierName: supplier.ShopSimpleName,
			ProfitAmount: money,
		})
	}

	marshal, err := json.Marshal(resList)
	if err != nil {
		return
	}
	zap.S().Infof("利润：%s", string(marshal))

	stockOrder(ctx)

}

func GetMonthlyProfit11(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Begin      int64  `json:"begin"`
		End        int64  `json:"end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//begin, end, err := util.MonthScopeTimestamp(req.Timestamp)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//_ = begin
	//_ = end

	filter := bson.M{
		"stock_up_day_time": bson.M{
			"$gte": req.Begin,
			"$lte": req.End,
		},
	}

	if len(req.SupplierID) == 24 {
		supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = supplierID
	}

	list, err := productBuyPriceService.NewProductBuyPriceService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mSupplier := make(map[primitive.ObjectID][]model.ProductBuyPrice)
	for _, price := range list {
		mSupplier[price.SupplierID] = append(mSupplier[price.SupplierID], price)
	}

	mProfit := make(map[primitive.ObjectID]int)
	for supplierID, priceList := range mSupplier {
		for _, price := range priceList {
			var p int
			profitAmountDe := decimal.NewFromInt(int64(price.ProfitAmount))

			if price.IsCheckWeight {
				//	重量*单价差价
				weightDe := decimal.NewFromInt(int64(price.TotalSortWeight)).Div(decimal.NewFromInt(1000))
				intPart := weightDe.Mul(profitAmountDe).IntPart()
				p = int(intPart)
			} else {
				//	 数量*单价差价
				buyNumDe := decimal.NewFromInt(int64(price.BuyNum))
				intPart := buyNumDe.Mul(profitAmountDe).IntPart()
				p = int(intPart)
			}
			mProfit[supplierID] += p
		}
	}

	var resList []profitList

	for id, i := range mProfit {
		_ = i

		resList = append(resList, profitList{
			SupplierID: id,
			//SupplierName: id,
			//ProfitAmount: i,
		})
	}

	//xhttp.RespSuccess(ctx, resList)

}

type profitList struct {
	SupplierID   primitive.ObjectID `json:"supplier_id"`
	SupplierName string             `json:"supplier_name"`
	//ProfitAmount int                `json:"profit_amount"`
	ProfitAmount float64 `json:"profit_amount"`
}

func stockOrder(ctx *gin.Context) {
	idListStr := []string{"674bb3a23d03455f5c045d1c", "6746d07a0b25f5b93f77d338", "674b394be4636d23151d664d"}
	for _, s := range idListStr {
		id, _ := util.ConvertToObjectWithCtx(ctx, s)
		order, _ := orderService.NewOrderService().Get(ctx, id)
		_ = order

		format := time.UnixMilli(order.CreatedAt).Format(time.RFC3339)

		zap.S().Infof("订单：%s,下单时间：%s", order.BuyerName, format)

		for _, productOrder := range order.ProductList {
			money := dealMoney(productOrder.ProductAmount)
			zap.S().Infof("商品：%v，单价：，%v分拣数量：%d", productOrder.ProductTitle, money, productOrder.SortNum)
		}

	}
}

func divideOrder(ctx *gin.Context, p1, p2 string) {
	var err error
	//pID, _ := util.ConvertToObjectWithCtx(ctx, p2)
	//query, err := orderService.NewOrderService().YeeTradeOrderQuery(ctx, pID)
	//if err != nil {
	//	return
	//}
	//_ = query

	id, _ := util.ConvertToObjectWithCtx(ctx, p1)

	err = orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(ctx, id)
	if err != nil {
		zap.S().Errorf("分账异常：%s", err.Error())
		return
	}
}

//
//func divideOrder(ctx *gin.Context, p1, p2 string) {
//	var err error
//	//pID, _ := util.ConvertToObjectWithCtx(ctx, p2)
//	//query, err := orderService.NewOrderService().YeeTradeOrderQuery(ctx, pID)
//	//if err != nil {
//	//	return
//	//}
//	//_ = query
//
//	id, _ := util.ConvertToObjectWithCtx(ctx, p1)
//
//	orderByOrder, err := orderAgentPayService.NewOrderAgentPayService().GetOrderByOrder(ctx, id)
//	if err != nil {
//		return
//	}
//
//	err = orderAgentPayService.NewOrderAgentPayService().YeeDivide(ctx, orderByOrder)
//	if err != nil {
//		zap.S().Errorf("分账异常：%s", err.Error())
//		return
//	}
//}

func refundDeliver(ctx *gin.Context) {
	id, _ := util.ConvertToObjectWithCtx(ctx, "67908f605b5a1a6e8ed5a0cd")
	err := orderRefundService.NewOrderRefundService().YeeRefundDeliver(ctx, id)
	if err != nil {
		zap.S().Errorf("检查订单退配送费异常：%s", err.Error())
	}
}

// 原来结算订单
func checkSettle(ctx *gin.Context) {

	filter := bson.M{
		//"order_status": bson.M{
		//	"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		//},
		"created_at": bson.M{
			//"$gte": 1727712000000, // 2024-10-1 00:00:00
			//"$lte": 1730390400000, // 2024-11-1 00:00:00

			//"$gte": 1732982400000, // 2024-12-1 00:00:00
			//"$lte": *************, // 2025-01-01 00:00:00

			"$gte": *************, // 2025-01-01 00:00:00
			"$lte": *************, // 2025-02-01 00:00:00
		},
	}

	orderList, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, order := range orderList {
		for _, per := range order.ProductList {
			if per.SettleUnitPrice == 0 {
				zap.S().Warnf("结算单价为0：%s", order.ID.Hex())
			}

		}
	}

}

func testOrderSettle(ctx *gin.Context, p1 string) {
	id, _ := primitive.ObjectIDFromHex(p1)
	orderIDs := []primitive.ObjectID{id}

	qualities, err := orderQualityService.NewOrderQualityService().List(ctx, bson.M{
		"order_list.order_id": bson.M{
			"$in": orderIDs,
		},
	})
	if err != nil {
		return
	}

	err = orderWarehouseService.NewOrderWarehouseService().SyncSortData(ctx, qualities, orderIDs)
	if err != nil {
		zap.S().Errorf("SyncSortData error: %s", err.Error())
		return
	}

	//id, _ := primitive.ObjectIDFromHex(p1)
	//orderIDs := []primitive.ObjectID{id}
	//queueProduceService.NewQueueProduceService().SendShipSettle(ctx, orderIDs, 3)

}

func orderDivide(ctx *gin.Context, p1 string) {

	id, _ := util.ConvertToObjectWithCtx(ctx, p1)

	err := orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(ctx, id)
	if err != nil {
		zap.S().Errorf("err:%s", err.Error())
		return
	}
}

func testGenDeliverBill(ctx *gin.Context) {

	var ids []model.MNSBuyer
	ids = append(ids, model.MNSBuyer{
		BuyerID:     "6470708ced36e747d0ffd941",
		DeliverType: model.DeliverTypeDoor,
	})

	data := model.MNSGenDeliverNote{
		BuyerList: ids,
		Timestamp: 1737184759565,
	}
	mnsSendService.NewMNSClient().SendDeliverNoteGenerate(data)
}

func checkSettleList(ctx *gin.Context, p1 string) {
	id, err := util.ConvertToObjectWithCtx(ctx, p1)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orderIDs := []primitive.ObjectID{id}

	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
		//"order_status":         model.OrderStatusTypeToArrive,
		//"has_check_after_ship": false,
	}
	orderList, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	zap.S().Infof("CheckAfterShip order list len::%d", len(orderList))

	var debtList []model.OrderDebt
	for _, order := range orderList {
		pSettleList, err := orderRefundService.NewOrderRefundService().BackShipSettle(ctx, order)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		debt, err := orderDebtService.NewOrderDebtService().Create(ctx, order, pSettleList)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		if debt.ID != primitive.NilObjectID {
			debtList = append(debtList, debt)
		}

		marshal, err := json.Marshal(debt)
		if err != nil {
			return
		}
		fmt.Println(string(marshal))
	}
}

// 原来结算订单
func migrateSettle(ctx *gin.Context) {

	filter := bson.M{
		//"has_agent_pay":    false,
		//"order_refund_all": false,
		//"order_status": bson.M{
		//	"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		//},
		"created_at": bson.M{
			//"$gte": 1727712000000, // 2024-10-1 00:00:00
			//"$lte": 1730390400000, // 2024-11-1 00:00:00

			"$gte": 1732982400000, // 2024-12-1 00:00:00
			"$lte": *************, // 2025-01-01 00:00:00

			//"$gte": *************, // 2025-01-01 00:00:00
			//"$lte": *************, // 2025-02-01 00:00:00
		},
		//"_id": orderID,
	}

	orderList, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	d := dao.OrderDebtDao
	_ = d

	for index, order := range orderList {
		debt, err := orderDebtService.NewOrderDebtService().GetByOrderID(ctx, order.ID)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return
		}
		if debt.ID == primitive.NilObjectID {
			continue
		}

		_ = debt

		var settleProductList []model.ProductSettle

		for _, per := range debt.ProductList {
			var settleUnit int

			var orderProductAmount int
			var settleProductAmount int
			var dueWeight int
			var num int
			var sortWeight int
			for _, productOrder := range order.ProductList {
				if productOrder.ProductID == per.ProductID {
					orderProductAmount = productOrder.ProductAmount
					settleProductAmount = productOrder.ProductAmount
					dueWeight = productOrder.DueWeight
					num = productOrder.Num
					sortWeight = productOrder.SortWeight
					settleUnit = productOrder.SettleUnitPrice
				}
			}

			item := model.ProductSettle{
				ProductID:                     per.ProductID,
				ProductImageID:                per.ProductImageID,
				ProductTitle:                  per.ProductTitle,
				ProductCover:                  per.ProductCover,
				IsCheckWeight:                 per.IsCheckWeight,
				Price:                         per.Price,
				Num:                           num,
				SettleUnitPrice:               settleUnit,
				ProductRoughWeightUnitPriceKG: per.ProductRoughWeightUnitPriceKG,
				OrderProductAmount:            orderProductAmount,
				SettleProductAmount:           settleProductAmount,
				DiffProductAmount:             per.Amount,
				DueWeight:                     dueWeight,
				SortWeight:                    sortWeight,
				SettleResultType:              model.SettleResultTypeDebt,
			}
			settleProductList = append(settleProductList, item)
		}

		for _, per := range debt.RefundProductList {
			var settleUnit int

			var orderProductAmount int
			var settleProductAmount int
			var dueWeight int
			var num int
			var sortWeight int
			for _, productOrder := range order.ProductList {
				if productOrder.ProductID == per.ProductID {
					orderProductAmount = productOrder.ProductAmount
					settleProductAmount = productOrder.ProductAmount
					dueWeight = productOrder.DueWeight
					num = productOrder.Num
					sortWeight = productOrder.SortWeight
					settleUnit = productOrder.SettleUnitPrice
				}
			}

			item := model.ProductSettle{
				ProductID:                     per.ProductID,
				ProductImageID:                per.ProductImageID,
				ProductTitle:                  per.ProductTitle,
				ProductCover:                  per.ProductCover,
				IsCheckWeight:                 per.IsCheckWeight,
				Price:                         per.Price,
				Num:                           num,
				RoughWeight:                   per.RoughWeight,
				OutWeight:                     per.OutWeight,
				NetWeight:                     per.NetWeight,
				SettleUnitPrice:               settleUnit,
				ProductRoughWeightUnitPriceKG: per.ProductRoughWeightUnitPriceKG,
				OrderProductAmount:            orderProductAmount,
				SettleProductAmount:           settleProductAmount,
				DiffProductAmount:             per.ProductAmount,
				DueWeight:                     dueWeight,
				SortWeight:                    sortWeight,
				SortNum:                       per.SortNum,
				TotalServiceFee:               per.TotalServiceFee,
				UnitTransportFee:              per.UnitTransportFee,
				TotalTransportFee:             per.TotalTransportFee,
				TotalWarehouseLoadFee:         per.TotalWarehouseLoadFee,
				SettleResultType:              model.SettleResultTypeRefund,
			}
			settleProductList = append(settleProductList, item)
		}

		d.UpdateOne(ctx, bson.M{
			"_id": debt.ID,
		}, bson.M{
			"$set": bson.M{
				"settle_product_list": settleProductList,
			},
		})

		zap.S().Infof("index:%d", index+1)

	}

}

// 订单结算价
func dealOrderSettleUnitPrice(ctx *gin.Context) {
	//orderID, _ := util.ConvertToObjectWithCtx(ctx, "678afc008d942716c7a608ea")

	filter := bson.M{
		//"has_agent_pay":    false,
		//"order_refund_all": false,
		//"order_status": bson.M{
		//	"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		//},
		"created_at": bson.M{
			//"$gte": 1685548800000, // 2023-06-1 00:00:00
			//"$lte": 1696089600000, // 2023-10-1 00:00:00

			//"$gte": 1696089600000, // 2023-10-1 00:00:00
			//"$lte": *************, // 2024-1-1 00:00:00

			//"$gte": *************, // 2024-1-1 00:00:00
			//"$lte": 1709222400000, // 2024-3-1 00:00:00

			//"$gte": 1711900800000, // 2024-4-1 00:00:00
			//"$lte": 1714492800000, // 2024-5-1 00:00:00

			//"$gte": 1714492800000, // 2024-5-1 00:00:00
			//"$lte": 1717171200000, // 2024-6-1 00:00:00

			//"$gte": 1717171200000, // 2024-6-1 00:00:00
			//"$lte": 1722441600000, // 2024-8-1 00:00:00

			//"$gte": 1722441600000, // 2024-8-1 00:00:00
			//"$lte": 1725120000000, // 2024-9-1 00:00:00

			//"$gte": 1725120000000, // 2024-9-1 00:00:00
			//"$lte": 1727712000000, // 2024-10-1 00:00:00

			//"$gte": 1727712000000, // 2024-10-1 00:00:00
			//"$lte": 1730390400000, // 2024-11-1 00:00:00

			//"$gte": 1732982400000, // 2024-12-1 00:00:00
			//"$lte": *************, // 2025-01-01 00:00:00

			"$gte": *************, // 2025-01-01 00:00:00
			"$lte": *************, // 2025-02-01 00:00:00
		},
		//"_id": orderID,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		return
	}

	for index, order := range orders {

		for _, productOrder := range order.ProductList {

			settleUnitPrice := productOrder.Price
			if productOrder.IsCheckWeight {
				settleUnitPrice = productOrder.ProductRoughWeightUnitPriceKG
			}

			if productOrder.SettleUnitPrice != 0 {
				continue
			}

			orderService.NewOrderService().UpdateOne(ctx, bson.M{
				"_id":                     order.ID,
				"product_list.product_id": productOrder.ProductID,
			}, bson.M{
				"$set": bson.M{
					"product_list.$.settle_unit_price": settleUnitPrice,
				},
			})

		}
		zap.S().Infof("index:%d", index+1)
	}

}
