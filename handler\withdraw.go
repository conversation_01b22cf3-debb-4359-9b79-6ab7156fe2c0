package handler

import (
	"base/api/withdraw"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 提现
func withdrawRouter(r *gin.RouterGroup) {
	r = r.Group("/withdraw")

	r.Use(middleware.CheckToken)
	r.POST("/apply", withdraw.Apply)
	r.POST("/export", withdraw.Export)
	r.POST("/account/info", withdraw.Info)
	r.POST("/apply/list", withdraw.ListApplyByObject)
	r.POST("/apply/list/by/station", withdraw.ListApplyByStation)
}
