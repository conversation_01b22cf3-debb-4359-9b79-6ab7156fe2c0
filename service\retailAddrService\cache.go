package retailAddrService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var cache = "retailAddress:"

func get(r *redis.Client, userID primitive.ObjectID) model.RetailAddress {
	key := cache + userID.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.RetailAddress{}
		}
		var i model.RetailAddress
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.RetailAddress{}
		}
		return i
	}
	return model.RetailAddress{}
}

func set(r *redis.Client, info model.RetailAddress) {
	key := cache + info.UserID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().Error("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, 0)
}

func del(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cache+id.Hex())
}
