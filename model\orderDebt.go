package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type SettleResultType string

const SettleResultTypeNone = "none"
const SettleResultTypeRefund = "refund"
const SettleResultTypeDebt = "debt"

// OrderDebt 补差订单
type OrderDebt struct {
	ID                       primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID                  primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	BuyerName                string             `json:"buyer_name" bson:"buyer_name"`                       // 采购商名称
	SupplierID               primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`                     // 供应商ID
	SupplierName             string             `json:"supplier_name" bson:"supplier_name"`                 // 供应商名称
	OrderID                  primitive.ObjectID `json:"order_id" bson:"order_id"`                           // 订单ID
	ParentOrderID            primitive.ObjectID `json:"parent_order_id" bson:"parent_order_id"`             // 父单
	PayMethod                PayMethodType      `json:"pay_method" bson:"pay_method"`                       // 支付方式
	TotalProductAmount       int                `json:"total_product_amount" bson:"total_product_amount"`   // 商品总金额
	OffsetProductAmount      int                `json:"offset_product_amount" bson:"offset_product_amount"` // 商品总金额
	PaidProductAmount        int                `json:"paid_product_amount" bson:"paid_product_amount"`     // 商品总金额
	ServicePointID           primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`           // 服务点ID
	ServicePointName         string             `json:"service_point_name" bson:"service_point_name"`       // 服务点名称
	ProductList              []ProductDebt      `json:"product_list" bson:"product_list"`                   // 产品列表
	PayStatus                PayStatusType      `json:"pay_status" bson:"pay_status"`                       // 支付状态
	PayResult                PayResult          `json:"pay_result" bson:"pay_result"`                       // 支付结果
	ExpireAt                 int64              `json:"expire_at" bson:"expire_at"`                         // 过期时间
	HasAgentPay              bool               `json:"has_agent_pay" bson:"has_agent_pay"`
	WXPayResult              WXPayResult        `json:"wx_pay_result" bson:"wx_pay_result"`                             // 支付结果
	YeeWechatResult          YeeWechatResult    `json:"yee_wechat_result" bson:"yee_wechat_result"`                     // 易宝支付-微信
	RefundUnitTransportFee   int                `json:"refund_unit_transport_fee" bson:"refund_unit_transport_fee"`     // 干线费单价
	RefundTotalTransportFee  int                `json:"refund_total_transport_fee" bson:"refund_total_transport_fee"`   // 干线费
	RefundTotalServiceFee    int                `json:"refund_total_service_fee" bson:"refund_total_service_fee"`       // 服务费
	RefundTotalProductAmount int                `json:"refund_total_product_amount" bson:"refund_total_product_amount"` // 商品总额
	RefundFinalAmount        int                `json:"refund_final_amount" bson:"refund_final_amount"`                 // 最终总退款额
	RefundProductList        []ProductQuality   `json:"refund_product_list" bson:"refund_product_list"`                 // 产品列表
	RefundYeeRefundResult    YeeRefundResult    `json:"refund_yee_refund_result" bson:"refund_yee_refund_result"`       // 易宝微信退款
	PaidRefundYeeResult      YeeRefundResult    `json:"paid_refund_yee_result" bson:"paid_refund_yee_result"`           // 补差已支付，易宝微信退款

	SettleProductList []ProductSettle `json:"settle_product_list" bson:"settle_product_list"` // 产品列表

	CreatedAt int64 `bson:"created_at" json:"created_at"`
	UpdatedAt int64 `bson:"updated_at" json:"updated_at"`
	DeletedAt int64 `bson:"deleted_at" json:"deleted_at"`
}

// ProductSettle 商品结算
type ProductSettle struct {
	ProductID                     primitive.ObjectID `json:"product_id" bson:"product_id"`                                                 // 商品ID
	SkuIDCode                     string             `json:"sku_id_code" bson:"sku_id_code"`                                             // 商品ID
	SkuName                       string             `json:"sku_name" bson:"sku_name"`                                                   // 商品名称
	ProductImageID                primitive.ObjectID `json:"product_image_id" bson:"product_image_id"`                                     // 商品镜像ID
	ProductTitle                  string             `json:"product_title" bson:"product_title"`                                           // 商品标题
	ProductCover                  FileInfo           `json:"product_cover" bson:"product_cover"`                                           // 商品封面
	IsCheckWeight                 bool               `json:"is_check_weight" bson:"is_check_weight"`                                       // 分拣检查重量  是/否
	Price                         int                `json:"price" bson:"price"`                                                           // 单价
	SettleUnitPrice               int                `json:"settle_unit_price" bson:"settle_unit_price"`                                   // 单价
	Num                           int                `json:"num" bson:"num"`                                                               // 退款数量
	RoughWeight                   int                `json:"rough_weight" bson:"rough_weight"`                                             // 毛重
	OutWeight                     int                `bson:"out_weight" json:"out_weight"`                                                 // 皮重
	NetWeight                     int                `bson:"net_weight" json:"net_weight"`                                                 // 净重
	ProductRoughWeightUnitPriceKG int                `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"` // 商品毛重单价/kg
	CouponRoughWeightUnitPriceKG  int                `json:"coupon_rough_weight_unit_price_kg" bson:"coupon_rough_weight_unit_price_kg"`   // 优惠券毛重单价/kg
	CouponSplitAmount             int                `json:"coupon_split_amount" bson:"coupon_split_amount"`                               // 优惠券金额
	RefundCouponAmount            int                `json:"refund_coupon_amount" bson:"refund_coupon_amount"`                             // 退款优惠券金额
	OrderProductAmount            int                `json:"order_product_amount" bson:"order_product_amount"`                             // 金额
	SettleProductAmount           int                `json:"settle_product_amount" bson:"settle_product_amount"`                           // 金额
	DiffProductAmount             int                `json:"diff_product_amount" bson:"diff_product_amount"`                               // 金额
	DueWeight                     int                `json:"due_weight" bson:"due_weight"`                                                 // 分拣重量
	SortWeight                    int                `json:"sort_weight" bson:"sort_weight"`                                               // 分拣重量
	SortNum                       int                `json:"sort_num" bson:"sort_num"`                                                     // 分拣数量
	UnitTransportFee              int                `json:"unit_transport_fee" bson:"unit_transport_fee"`                                 // 运费单价
	TotalTransportFee             int                `json:"total_transport_fee" bson:"total_transport_fee"`                               // 总运费
	TotalServiceFee               int                `json:"total_service_fee" bson:"total_service_fee"`                                   // 服务费
	TotalWarehouseLoadFee         int                `json:"total_warehouse_load_fee" bson:"total_warehouse_load_fee"`
	SettleResultType              SettleResultType   `json:"settle_result_type" bson:"settle_result_type"`
}

//RefundWeight                  int                `json:"refund_weight" bson:"refund_weight"`                                           // 退款重量g
//ProductID                     primitive.ObjectID `json:"product_id" bson:"product_id"`                                                 // 商品ID
//ProductImageID                primitive.ObjectID `json:"product_image_id" bson:"product_image_id"`                                     // 商品镜像ID
//ProductTitle                  string             `json:"product_title" bson:"product_title"`                                           // 商品标题
//ProductCover                  FileInfo           `json:"product_cover" bson:"product_cover"`                                           // 商品封面
//IsCheckWeight                 bool               `json:"is_check_weight" bson:"is_check_weight"`                                       // 分拣检查重量  是/否
//Price                         int                `json:"price" bson:"price"`                                                           // 单价
//SettleUnitPrice               int                `json:"settle_unit_price" bson:"settle_unit_price"`                                   // 单价
//ProductRoughWeightUnitPriceKG int                `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"` // 商品毛重单价/kg
//OverWeight                    int                `json:"over_weight" bson:"over_weight"`                                               // 超重

type ProductQuality struct {
	ProductID                     primitive.ObjectID `json:"product_id" bson:"product_id"`                                                 // 商品ID
	ProductImageID                primitive.ObjectID `json:"product_image_id" bson:"product_image_id"`                                     // 商品镜像ID
	ProductTitle                  string             `json:"product_title" bson:"product_title"`                                           // 商品标题
	ProductCover                  FileInfo           `json:"product_cover" bson:"product_cover"`                                           // 商品封面
	IsCheckWeight                 bool               `json:"is_check_weight" bson:"is_check_weight"`                                       // 分拣检查重量  是/否
	Price                         int                `json:"price" bson:"price"`                                                           // 单价
	SettleUnitPrice               int                `json:"settle_unit_price" bson:"settle_unit_price"`                                   // 单价
	Num                           int                `json:"num" bson:"num"`                                                               // 退款数量
	RoughWeight                   int                `json:"rough_weight" bson:"rough_weight"`                                             // 毛重
	OutWeight                     int                `bson:"out_weight" json:"out_weight"`                                                 // 皮重
	NetWeight                     int                `bson:"net_weight" json:"net_weight"`                                                 // 净重
	ProductRoughWeightUnitPriceKG int                `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"` // 商品毛重单价/kg
	ProductAmount                 int                `json:"product_amount" bson:"product_amount"`                                         // 金额
	SortWeight                    int                `json:"sort_weight" bson:"sort_weight"`                                               // 分拣重量
	SortNum                       int                `json:"sort_num" bson:"sort_num"`                                                     // 分拣数量
	RefundWeight                  int                `json:"refund_weight" bson:"refund_weight"`                                           // 退款重量g
	UnitTransportFee              int                `json:"unit_transport_fee" bson:"unit_transport_fee"`                                 // 运费单价
	TotalTransportFee             int                `json:"total_transport_fee" bson:"total_transport_fee"`                               // 总运费
	TotalServiceFee               int                `json:"total_service_fee" bson:"total_service_fee"`                                   // 服务费
	TotalWarehouseLoadFee         int                `json:"total_warehouse_load_fee" bson:"total_warehouse_load_fee"`
}

// ProductDebt 补差商品
type ProductDebt struct {
	ProductID                     primitive.ObjectID `json:"product_id" bson:"product_id"`                                                 // 商品ID
	ProductImageID                primitive.ObjectID `json:"product_image_id" bson:"product_image_id"`                                     // 商品镜像ID
	ProductTitle                  string             `json:"product_title" bson:"product_title"`                                           // 商品标题
	ProductCover                  FileInfo           `json:"product_cover" bson:"product_cover"`                                           // 商品封面
	IsCheckWeight                 bool               `json:"is_check_weight" bson:"is_check_weight"`                                       // 分拣检查重量  是/否
	Price                         int                `json:"price" bson:"price"`                                                           // 单价
	SettleUnitPrice               int                `json:"settle_unit_price" bson:"settle_unit_price"`                                   // 单价
	ProductRoughWeightUnitPriceKG int                `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"` // 商品毛重单价/kg
	OverWeight                    int                `json:"over_weight" bson:"over_weight"`                                               // 超重
	Amount                        int                `json:"amount" bson:"amount"`                                                         // 金额
}
