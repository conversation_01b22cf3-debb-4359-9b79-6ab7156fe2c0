package config

var Conf *Config

type Config struct {
	Mode     string  `json:"mode" validate:"required"`
	App      App     `yaml:"app" validate:"dive"`
	Auth     Auth    `json:"auth" validate:"dive"`
	Mini     Mini    `json:"mini" validate:"dive"`
	Official Mini    `json:"official" validate:"dive"`
	<PERSON><PERSON><PERSON>  `json:"AliOss" validate:"dive"`
	AliOcr   AliOcr  `json:"AliOcr" validate:"dive"`
	Mongo    Mongo   `json:"mongo" validate:"dive"`
	Redis    Redis   `json:"redis" validate:"dive"`
	Message  Message `json:"message"  validate:"dive"`
	// 正式
	AllinPayProd AllinPay `json:"AllinPayProd"  validate:"dive"`
	YeePay       YeePay   `json:"YeePay"  validate:"dive"`
}

type App struct {
	ServerName string `mapstructure:"server_name" validate:"required"`
	Port       string `json:"port" validate:"required"`
	AesKey     string `mapstructure:"aes_key" yaml:"aes_key" validate:"required"` // 对称加密
}

// Auth jwt
type Auth struct {
	AccessSecret string `mapstructure:"access_secret" validate:"required"`
	AccessExpire int    `mapstructure:"access_expire" validate:"required"`
}

// Mini 小程序appID
type Mini struct {
	AppID     string `mapstructure:"app_id" validate:"required"`
	AppSecret string `mapstructure:"app_secret" validate:"required"`
}

// Public 公众号
type Public struct {
	AppID     string `mapstructure:"app_id" validate:"required"`
	AppSecret string `mapstructure:"app_secret" validate:"required"`
}

type Redis struct {
	Host     string `json:"host" validate:"required"`
	Port     string `json:"port" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type Mysql struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Database string `json:"database"`
	UserName string `mapstructure:"user_name"`
	Password string `json:"password"`
}

type Mongo struct {
	//Hosts    []string `json:"hosts" validate:"dive,min=1"`
	Database string `json:"database" validate:"required"`
	Url      string `json:"url" validate:"required"`
	//UserName string   `mapstructure:"user_name" validate:"required"`
	//Password string   `json:"password" validate:"required"`
}

// AliOss 存储
type AliOss struct {
	AccessKeyID     string `json:"AccessKeyID" validate:"required"`
	AccessKeySecret string `json:"AccessKeySecret" validate:"required"`
	//Region          string `json:"region" validate:"required"`
	EndPoint string `json:"EndPoint" validate:"required"` // 节点
	Host     string `json:"Host" validate:"required"`     // 访问节点
	Bucket   string `json:"bucket" validate:"required"`   // 桶
	//STSExpire        int    `json:"STSExpire" validate:"min=900"`       // 临时凭证失效时间/s
	SignUploadExpire int64 `json:"SignUploadExpire" validate:"min=60"` // 上传签名有效时间/s
}

type AliOcr struct {
	AccessKeyID     string `json:"AccessKeyID" validate:"required"`
	AccessKeySecret string `json:"AccessKeySecret" validate:"required"`
	////Region          string `json:"region" validate:"required"`
	//EndPoint string `json:"EndPoint" validate:"required"` // 节点
	//Host     string `json:"Host" validate:"required"`     // 访问节点
	//Bucket   string `json:"bucket" validate:"required"`   // 桶
	////STSExpire        int    `json:"STSExpire" validate:"min=900"`       // 临时凭证失效时间/s
	//SignUploadExpire int64 `json:"SignUploadExpire" validate:"min=60"` // 上传签名有效时间/s
}

type Message struct {
	AccessKey       string `mapstructure:"access_key" validate:"required"`
	AccessKeySecret string `mapstructure:"access_key_secret" validate:"required"`
	EndPoint        string `mapstructure:"end_point" validate:"required"`
}

// AllinPay 通联支付
type AllinPay struct {
	//SysID        string `json:"SysID" validate:"required"` // 对应平台账户
	AppID        string `json:"AppID" validate:"required"`
	AppSecretKey string `json:"AppSecretKey" validate:"required"`
	//AppAccountID string `json:"AppAccountID" validate:"required"`
	PfxPath string `json:"PfxPath" validate:"required"`
	TLCert  string `json:"TLCert" validate:"required"`
	PfxPwd  string `json:"PfxPwd" validate:"required"`
	Debug   bool   `json:"Debug"`
}

// YeePay 易宝支付
type YeePay struct {
	AppID    string `json:"AppID" validate:"required"`
	Cer      string `json:"Cer" validate:"required"`
	Public   string `json:"Public" validate:"required"`
	Platform string `json:"Platform" validate:"required"`
}

type AllInPayAccountSetInfo struct {
	PlatformBizUserId string
	// 平台银行卡卡号
	PlatformCardNumber string
	//标准余额账户集
	StandardBalanceNo string
	//标准保证金账户集
	StandardDepositNo string
	//准备金额度账户集
	ReserveLimitNo string
	//标准营销账户集
	MarketNo string
	//托管账户集
	EscrowUserNo string
}
