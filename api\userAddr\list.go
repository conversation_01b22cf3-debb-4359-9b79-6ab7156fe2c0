package userAddr

import (
	"base/core/xhttp"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	userId, err := xhttp.UserID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := userAddrService.NewUserAddrService().List(ctx, userId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, res)
}

func ListByUser(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		return
	}

	res, err := userAddrService.NewUserAddrService().List(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, res)
}

func ListByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		return
	}

	filter := bson.M{
		"buyer_id": id,
	}

	res, err := userAddrService.NewUserAddrService().ListByCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, res)
}

func SearchByAddress(ctx *gin.Context) {
	var req = struct {
		Content string `json:"content" validate:"required"`
		Page    int64  `uri:"page" validate:"min=1"`
		Limit   int64  `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"address": bson.M{
			"$regex": req.Content,
		},
	}

	res, count, err := userAddrService.NewUserAddrService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, res, count)
}
