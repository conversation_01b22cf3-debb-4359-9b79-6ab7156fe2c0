package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// ServicePointCommission 服务点佣金
type ServicePointCommission struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`
	ServicePointID   primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`   // 服务点ID
	WarehousePercent int                `json:"warehouse_percent" bson:"warehouse_percent"` // 集中仓-百分比
	PointPercent     int                `json:"point_percent" bson:"point_percent"`         // 服务点百分比
	PlatformPercent  int                `json:"platform_percent" bson:"platform_percent"`   // 平台百分比
	CreatedAt        int64              `bson:"created_at" json:"created_at"`
	UpdatedAt        int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt        int64              `bson:"deleted_at" json:"deleted_at"`
}
