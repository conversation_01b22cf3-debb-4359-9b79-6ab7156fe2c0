package cartDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 购物车dao接口
type DaoInt interface {
	Count(ctx context.Context, filter bson.M) (int64, error)
	Delete(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	Create(ctx context.Context, data model.Cart) error
	CreateMany(ctx context.Context, data []model.Cart) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.Cart, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Cart, int64, error)
	Get(ctx context.Context, filter bson.M) (model.Cart, error)
}

type cartDao struct {
	db *mongo.Collection
}

// Create 创建购物车
func (s cartDao) Create(ctx context.Context, data model.Cart) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

// CreateMany 创建多个购物车
func (s cartDao) CreateMany(ctx context.Context, data []model.Cart) error {
	interfaceData := make([]interface{}, len(data))
	for i, v := range data {
		interfaceData[i] = v
	}
	_, err := s.db.InsertMany(ctx, interfaceData)
	if err != nil {
		return err
	}
	return nil
}

// List 查询购物车列表
func (s cartDao) List(ctx context.Context, filter bson.M) ([]model.Cart, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.Cart
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// ListByPage 分页查询购物车列表
func (s cartDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Cart, int64, error) {
	var list []model.Cart
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

	// Get 查询购物车
func (s cartDao) Get(ctx context.Context, filter bson.M) (model.Cart, error) {
	var cart model.Cart
	err := s.db.FindOne(ctx, filter).Decode(&cart)
	if err != nil {
		return model.Cart{}, err
	}
	return cart, nil
}

// DeleteMany 删除多个购物车
func (s cartDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

// Delete 删除一个购物车
func (s cartDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// Update 更新一个购物车
func (s cartDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

// UpdateMany 更新多个购物车
func (s cartDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

// Count 查询购物车数量
func (s cartDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	return count, err
}

// NewCartDao 创建购物车dao
func NewCartDao(collect string) DaoInt {
	return cartDao{
		db: global.MDB.Collection(collect),
	}
}
