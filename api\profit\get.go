package profit

import (
	"base/core/xhttp"
	"base/types"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/gin-gonic/gin"
)

// GetRes 获取利润
func GetRes(ctx *gin.Context) {
	var req = struct {
		SupplierID     primitive.ObjectID `json:"supplier_id"`
		MonthTimestamp int64              `json:"month_timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var res types.ProfitRes

	xhttp.RespSuccess(ctx, res)
}
