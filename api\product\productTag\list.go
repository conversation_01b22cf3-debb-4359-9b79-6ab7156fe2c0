package productTag

import (
	"base/core/xhttp"
	"base/service/productTagService"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		TagType int `json:"tag_type" validate:"oneof=1 2"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	list, err := productTagService.NewProductTagService().ListByType(ctx, req.TagType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)

}
