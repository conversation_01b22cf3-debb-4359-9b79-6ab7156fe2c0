package inviteDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Invite) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.Invite, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invite, int64, error)
	Get(ctx context.Context, filter bson.M) (model.Invite, error)
}

type inviteDao struct {
	db *mongo.Collection
}

func NewInviteDao(collect string) DaoInt {
	return inviteDao{
		db: global.MDB.Collection(collect),
	}
}

func (s inviteDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invite, int64, error) {
	var list []model.Invite
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s inviteDao) Get(ctx context.Context, filter bson.M) (model.Invite, error) {
	var data model.Invite
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Invite{}, err
	}
	return data, nil
}

func (s inviteDao) List(ctx context.Context, filter bson.M) ([]model.Invite, error) {
	opts := options.Find()

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	var list []model.Invite
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s inviteDao) Create(ctx context.Context, data model.Invite) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s inviteDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s inviteDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}
