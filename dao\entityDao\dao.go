package entityDao

import (
	"base/global"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	//Upsert(ctx context.Context, data model.Entity) error
	//GetByObject(objectType int, objectID primitive.ObjectID) (model.Entity, error)
}

type entityDao struct {
	db *mongo.Collection
}

//
//func (s entityDao) GetByObject(objectType int, objectID primitive.ObjectID) (model.Entity, error) {
//	var data model.Entity
//	filter := bson.M{
//		"object_type": objectType,
//		"object_id":   objectID,
//	}
//	a := objectID.Hex()
//	_ = a
//	err := s.db.FindOne(context.Background(), filter).Decode(&data)
//	if err != nil {
//		return model.Entity{}, err
//	}
//	return data, nil
//}
//
//func (s entityDao) Upsert(ctx context.Context, data model.Entity) error {
//	opts := options.Update().SetUpsert(true)
//	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
//	if err != nil {
//		return err
//	}
//	return nil
//}

func NewEntityDao(collect string) DaoInt {
	return entityDao{
		db: global.MDB.Collection(collect),
	}
}
