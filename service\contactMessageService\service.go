package contactMessageService

import (
	"base/dao"
	"base/dao/contactMessageDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.ContactMessage) error
	List(ctx context.Context) ([]model.ContactMessage, error)
	CountByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int64, error)
}

type contactMessageService struct {
	contactMessageDao contactMessageDao.DaoInt
}

func NewContactMessageService() ServiceInterface {
	return contactMessageService{
		contactMessageDao: dao.ContactMessageDao,
	}
}

func (s contactMessageService) Create(ctx context.Context, data model.ContactMessage) error {
	err := s.contactMessageDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s contactMessageService) List(ctx context.Context) ([]model.ContactMessage, error) {
	list, i, err := s.contactMessageDao.ListByPage(ctx, bson.M{}, 1, 20)
	if err != nil {
		return nil, err
	}
	_ = i

	return list, nil
}

func (s contactMessageService) CountByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int64, error) {
	i, err := s.contactMessageDao.Count(ctx, bson.M{
		"buyer_id": buyerID,
	})
	if err != nil {
		return 0, err
	}

	return i, nil
}
