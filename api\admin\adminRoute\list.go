package adminRoute

import (
	"base/core/xhttp"
	"base/model"
	"base/service/routeService"
	"base/service/servicePointService"
	"base/service/warehouseService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func ListByWarehouse(ctx *gin.Context) {
	var req = struct {
		FromWarehouseID string `json:"from_warehouse_id" validate:"len=24"`
		Page            int64  `json:"page" validate:"min=1"`
		Limit           int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.FromWarehouseID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list, i, err := routeService.NewTransportFeeService().ListByPage(ctx, id, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]RouteRes, 0, len(list))
	for _, v := range list {
		temp := RouteRes{
			Route:         v,
			RouteTypeName: model.RouteTypeMsg[v.RouteType],
		}

		if v.RouteType == model.RouteTypeWarehouse {
			info, err := warehouseService.NewWarehouseServiceService().Get(ctx, v.ToWarehouseID)
			if err != nil {
				xhttp.RespErr(ctx, err)
				return
			}
			temp.ToWarehouseName = info.Name
		}

		if v.RouteType == model.RouteTypeServicePoint {
			point, err := servicePointService.NewServicePointService().Get(ctx, v.ToServicePointID)
			if err != nil {
				xhttp.RespErr(ctx, err)
				return
			}
			temp.ServicePointName = point.Name
		}

		resList = append(resList, temp)
	}

	xhttp.RespSuccessList(ctx, resList, i)
}

type RouteRes struct {
	model.Route
	RouteTypeName    string `json:"route_type_name"`    // 路线类型名称
	ToWarehouseName  string `json:"to_warehouse_name"`  // 集中仓名称
	ServicePointName string `json:"service_point_name"` // 服务点名称
}
