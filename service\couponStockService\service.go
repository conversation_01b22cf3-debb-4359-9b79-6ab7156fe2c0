package couponStockService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/couponStockDao"
	"base/global"
	"base/model"
	"base/types"
	"base/util"
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 优惠券
type ServiceInterface interface {
	Create(ctx context.Context, req types.CouponCreateReq) error
	Get(ctx context.Context, id primitive.ObjectID) (model.CouponStock, error)
	Update(ctx context.Context, req types.CouponStockUpdateReq) error
	UpdateOpen(ctx context.Context, id primitive.ObjectID, isOpen bool) error
	UpdateSendedNum(ctx context.Context, id primitive.ObjectID, num int, isAdd bool) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponStock, int64, error)
}

type couponStockService struct {
	mdb            *mongo.Database
	couponStockDao couponStockDao.DaoInt
}

// NewService 创建优惠券批次服务
func NewService() ServiceInterface {
	return couponStockService{
		mdb:            global.MDB,
		couponStockDao: dao.CouponStockDao,
	}
}

func (s couponStockService) UpdateOpen(ctx context.Context, id primitive.ObjectID, isOpen bool) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"is_open":    isOpen,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.couponStockDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s couponStockService) UpdateSendedNum(ctx context.Context, id primitive.ObjectID, num int, isAdd bool) error {
	filter := bson.M{
		"_id": id,
	}
	couponStock, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	if isAdd {
		couponStock.SendedNum += num
	} else {
		couponStock.SendedNum -= num
	}

	update := bson.M{
		"sended_num": couponStock.SendedNum,
	}
	err = s.couponStockDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s couponStockService) Get(ctx context.Context, id primitive.ObjectID) (model.CouponStock, error) {
	filter := bson.M{
		"_id": id,
	}
	coupon, err := s.couponStockDao.Get(ctx, filter)
	if err != nil {
		return model.CouponStock{}, err
	}
	return coupon, nil
}

func (s couponStockService) Create(ctx context.Context, req types.CouponCreateReq) error {
	var err error

	// 校验参数
	if req.Title == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "标题不能为空")
	}

	if req.AvailableBeginTime == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "可用时间不能为空")
	}

	if req.AvailableEndTime == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "可用时间不能为空")
	}

	if req.MaxSendNum == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "最大发放数量不能为空")
	}

	if req.MaxPerUserNum == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "最大领取数量不能为空")
	}

	if req.CouponAmount == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "优惠券面额不能为空")
	}

	if req.MinAmount == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "门槛金额不能为空")
	}

	if req.ValidDays == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "生效时长不能为空")
	}

	now := time.Now().UnixMilli()
	data := model.CouponStock{
		ID:                 primitive.NewObjectID(),
		CouponStockType:    req.CouponStockType,
		Title:              req.Title,
		Description:        req.Description,
		AvailableBeginTime: req.AvailableBeginTime,
		AvailableEndTime:   req.AvailableEndTime,
		MaxSendNum:         req.MaxSendNum,
		MaxPerUserNum:      req.MaxPerUserNum,
		CouponAmount:       req.CouponAmount,
		MinAmount:          req.MinAmount,
		ValidDays:          req.ValidDays,
		CouponStatus:       model.CouponStockStatusValid,
		CreatedAt:          now,
		UpdatedAt:          now,
	}

	err = s.couponStockDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s couponStockService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponStock, int64, error) {
	if page == 0 {
		page = 1
	}
	if limit == 0 {
		limit = 10
	}

	list, i, err := s.couponStockDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}

	return list, i, nil
}

func (s couponStockService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}

	var err error

	// couponStock, err := s.Get(ctx, id)
	// if err != nil {
	// 	return err
	// }
	// if couponStock.CouponStatus == model.CouponStockStatusValid {
	// 	return xerr.NewErr(xerr.ErrParamError, nil, "请先关闭优惠券")
	// }

	update := bson.M{
		"deleted_at": time.Now().UnixMilli(),
	}
	err = s.couponStockDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s couponStockService) Update(ctx context.Context, req types.CouponStockUpdateReq) error {
	// 校验参数
	if req.Title == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "标题不能为空")
	}

	if req.Description == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "描述不能为空")
	}

	if req.AvailableBeginTime == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "可用时间不能为空")
	}

	if req.AvailableEndTime == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "可用时间不能为空")
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		return err
	}
	filter := bson.M{
		"_id": id,
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"title":                req.Title,
		"description":          req.Description,
		"available_begin_time": req.AvailableBeginTime,
		"available_end_time":   req.AvailableEndTime,
		"updated_at":           now,
	}
	err = s.couponStockDao.Update(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}
