package sys

import (
	"base/core/xhttp"
	"base/service/generalImgService"

	"github.com/gin-gonic/gin"
)

// UpdateProductCommonImg 商品公共图片更新
func UpdateProductCommonImg(ctx *gin.Context) {
	var req = struct {
		Path string `json:"path"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = generalImgService.NewGeneralImgService().UpdateProductCommonImg(ctx, req.Path)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// GetProductCommonImg 商品公共图片查询
func GetProductCommonImg(ctx *gin.Context) {
	data, err := generalImgService.NewGeneralImgService().GetProductCommonImg(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}
