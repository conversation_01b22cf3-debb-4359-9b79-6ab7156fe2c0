package orderFinalSettleDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 订单最终结算记录
type DaoInt interface {
	Create(ctx context.Context, data model.OrderFinalSettle) error
	Get(ctx context.Context, filter bson.M) (model.OrderFinalSettle, error)
	GetByID(ctx context.Context, id primitive.ObjectID) (model.OrderFinalSettle, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderFinalSettle, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderFinalSettle, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateByID(ctx context.Context, id primitive.ObjectID, update bson.M) error
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderFinalSettle, error)
	DeleteByOrderID(ctx context.Context, orderID primitive.ObjectID) error
}

type orderFinalSettleDao struct {
	db *mongo.Collection
}

// NewDao 创建订单最终结算记录DAO
func NewDao() DaoInt {
	return &orderFinalSettleDao{
		db: global.MDB.Collection("order_final_settle"),
	}
}

// Create 创建订单最终结算记录
func (s orderFinalSettleDao) Create(ctx context.Context, data model.OrderFinalSettle) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

// Get 获取订单最终结算记录
func (s orderFinalSettleDao) Get(ctx context.Context, filter bson.M) (model.OrderFinalSettle, error) {
	var result model.OrderFinalSettle
	err := s.db.FindOne(ctx, filter).Decode(&result)
	return result, err
}

// GetByID 根据ID获取订单最终结算记录
func (s orderFinalSettleDao) GetByID(ctx context.Context, id primitive.ObjectID) (model.OrderFinalSettle, error) {
	filter := bson.M{
		"_id":        id,
	}
	return s.Get(ctx, filter)
}

// List 获取订单最终结算记录列表
func (s orderFinalSettleDao) List(ctx context.Context, filter bson.M) ([]model.OrderFinalSettle, error) {
	var results []model.OrderFinalSettle
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return results, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &results)
	return results, err
}

// ListByPage 分页获取订单最终结算记录列表
func (s orderFinalSettleDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderFinalSettle, int64, error) {
	var results []model.OrderFinalSettle

	// 计算总数
	total, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return results, 0, err
	}

	// 分页查询
	opts := options.Find()
	opts.SetSkip((page - 1) * limit)
	opts.SetLimit(limit)
	opts.SetSort(bson.D{{"created_at", -1}})

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return results, total, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &results)
	return results, total, err
}

// Count 获取订单最终结算记录总数
func (s orderFinalSettleDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	return s.db.CountDocuments(ctx, filter)
}

// UpdateOne 更新订单最终结算记录
func (s orderFinalSettleDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

// UpdateByID 根据ID更新订单最终结算记录
func (s orderFinalSettleDao) UpdateByID(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	filter := bson.M{
		"_id":        id,
	}
	return s.UpdateOne(ctx, filter, update)
}

// GetByOrderID 根据订单ID获取订单最终结算记录
func (s orderFinalSettleDao) GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderFinalSettle, error) {
	filter := bson.M{
		"order_id": orderID,
	}
	return s.Get(ctx, filter)
}

// DeleteByOrderID 根据订单ID删除订单最终结算记录
func (s orderFinalSettleDao) DeleteByOrderID(ctx context.Context, orderID primitive.ObjectID) error {
	filter := bson.M{
		"order_id": orderID,
	}
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}
