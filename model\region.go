package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// Region 区域
type Region struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Name      string             `json:"name" bson:"name"` // 区域名称
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt int64              `bson:"deleted_at" json:"deleted_at"`
}
