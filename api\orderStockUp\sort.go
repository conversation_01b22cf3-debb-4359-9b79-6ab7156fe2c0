package orderStockUp

//
//type toStockList []toStockRes
//
//func (array toStockList) Len() int {
//	return len(array)
//}
//
//func (array toStockList) Less(i, j int) bool {
//	if array[i].DeliverType == array[j].DeliverType {
//		return array[i].BuyerName < array[j].BuyerName
//	}
//	if array[i].DeliverType > array[j].DeliverType {
//		return true
//	}
//	return false //从小到大， 若为大于号，则从大到小
//}
//
//func (array toStockList) Swap(i, j int) {
//	array[i], array[j] = array[j], array[i]
//}
