package warehouseLoadFeeDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.WarehouseLoadFee) error
	UpdateOne(filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.WarehouseLoadFee, error)
	List(ctx context.Context, filter bson.M) ([]model.WarehouseLoadFee, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.WarehouseLoadFee, int64, error)
}

type warehouseLoadFeeDao struct {
	db *mongo.Collection
}

// List 查询
func (s warehouseLoadFeeDao) List(ctx context.Context, filter bson.M) ([]model.WarehouseLoadFee, error) {
	var list []model.WarehouseLoadFee
	//skip := (page - 1) * limit
	opts := options.Find()
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s warehouseLoadFeeDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.WarehouseLoadFee, int64, error) {
	var list []model.WarehouseLoadFee
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s warehouseLoadFeeDao) UpdateOne(filter, update bson.M) error {
	_, err := s.db.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s warehouseLoadFeeDao) Create(ctx context.Context, data model.WarehouseLoadFee) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s warehouseLoadFeeDao) Get(ctx context.Context, filter bson.M) (model.WarehouseLoadFee, error) {
	var data model.WarehouseLoadFee
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.WarehouseLoadFee{}, err
	}
	return data, nil
}

func NewWarehouseLoadFeeDao(collect string) DaoInt {
	return warehouseLoadFeeDao{
		db: global.MDB.Collection(collect),
	}
}
