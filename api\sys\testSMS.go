package sys

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/messageService"
	"base/service/orderService"
	"context"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestSMS(ctx *gin.Context) {
	var req = struct {
		P1 string `json:"p1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//ids := filterBuyer(ctx)
	//sendSMSNotify(ctx, ids)

}

func filterBuyer(ctx *gin.Context) []primitive.ObjectID {
	filter := bson.M{
		"order_status": model.OrderStatusTypeFinish,
		"deliver_type": model.DeliverTypeLogistics,
		"created_at": bson.M{
			"$gte": 1730390400000, // 2024-11-1 00:00:00
			"$lte": 1738339200000, // 2025-02-01 00:00:00
		},
	}

	orderList, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return nil
	}

	m := make(map[primitive.ObjectID]int)
	for _, order := range orderList {
		m[order.BuyerID] = 0
	}

	var ids []primitive.ObjectID

	for id, _ := range m {
		ids = append(ids, id)
	}

	return ids
}

func sendSMSNotify(ctx context.Context, ids []primitive.ObjectID) {

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		return
	}
	for _, buyer := range buyers {
		messageService.NewMessageService().SendOrderEndNotify(buyer.Mobile)
	}

}
