package supplier

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/service/supplierService"
	"base/service/supplierTagService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BindTag 标签绑定
func BindTag(ctx *gin.Context) {
	var req types.BindSupplierTagReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	tagID, err := util.ConvertToObjectWithNote(req.TagID, "BindTag")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	tag, err := supplierTagService.NewSupplierTagService().Get(ctx, tagID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, i := range req.SupplierIDs {
		id, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	err = supplierService.NewSupplierService().BindTag(ctx, tag, ids, req.UpdateType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateAvatar 更新头像
func UpdateAvatar(ctx *gin.Context) {
	var req = struct {
		AvatarImg model.FileInfo `json:"avatar_img" validate:"-"` // 头像
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	supplier, err := xhttp.CheckSupplier(ctx)
	if err != nil {
		return
	}

	if req.AvatarImg.Name == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "图片地址缺失"))
		return
	}

	err = supplierService.NewSupplierService().UpdateAvatar(ctx, supplier.ID, req.AvatarImg)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateStatus 更新状态
func UpdateStatus(ctx *gin.Context) {
	var req = struct {
		SupplierID string                  `json:"supplier_id"`
		Status     model.AccountStatusType `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	count, err := productService.NewProductService().Count(ctx, bson.M{"supplier_id": id,
		"sale":       true,
		"deleted_at": 0,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if count > 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请先下架该供应商的商品"))
		return
	}

	if req.Status != 1 && req.Status != 2 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "状态参数错误"))
		return
	}

	err = supplierService.NewSupplierService().UpdateStatus(ctx, id, req.Status)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateIsBusinessManage 更新是否经营管理
func UpdateIsBusinessManage(ctx *gin.Context) {
	var req = struct {
		SupplierID       string `json:"supplier_id"`
		IsBusinessManage bool   `json:"is_business_manage"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = supplierService.NewSupplierService().UpdateIsBusinessManage(ctx, id, req.IsBusinessManage)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
