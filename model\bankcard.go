package model

import (
	"encoding/json"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BankAccount1 银行账户
type BankAccount1 struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	ObjectType ObjectType         `json:"object_type" bson:"object_type"` // 对象类型
	ObjectID   primitive.ObjectID `json:"object_id" bson:"object_id"`     // 对象ID

	AccountType int    `json:"account_type" validate:"oneof=0 1"` // 银行账户类型-1对公/0对私
	CardNumber  string `json:"card_number"  validate:"required"`  // 账号

	// 对公
	ParentBankName string `json:"parent_bank_name"  validate:"required"` // 开户银行名称
	BankName       string `json:"bank_name"  validate:"required"`        // 开户行支行名称
	UnionBank      string `json:"union_bank"  validate:"required"`       // 支付行号，12位数字

	// 对私
	BankReservedMobile string `json:"bank_reserved_mobile"  validate:"required"` // 银行预留手机号

	BankcardImg FileInfo  `json:"bankcard_img" bson:"bankcard_img"`     // 银行卡图片
	Bankcard    Bankcard2 `json:"bankcard_basic" bson:"bankcard_basic"` // 银行识别

	CreatedAt int64 `bson:"created_at" json:"created_at"`
	UpdatedAt int64 `bson:"updated_at" json:"updated_at"`
	DeletedAt int64 `bson:"deleted_at" json:"deleted_at"`
}

// Bankcard 银行卡
type Bankcard2 struct {
	BankName   string          `json:"bank_name" bson:"bank_name"`     // 发卡行
	CardNumber string          `json:"card_number" bson:"card_number"` // 账号
	IssueDate  string          `json:"issue_date" bson:"issue_date"`   // 签发时间
	ExpireData string          `json:"expire_data" bson:"expire_data"` // 过期时间
	Type       string          `json:"type" bson:"type"`               // 卡类型
	Confidence json.RawMessage `json:"confidence" bson:"confidence"`   // 识别数据
}
