package config

import (
	"base/core/validate"
	"github.com/spf13/viper"
	"log"
	"os"
)

func Init() {
	validate.InitValidate()

	path, err := os.Getwd()
	if err != nil {
		panic(err)
	}

	v := viper.New()

	v.AddConfig<PERSON>ath(path)     //设置读取的文件路径
	v.SetConfigName("config") //设置读取的文件名
	v.SetConfigType("yaml")   //设置文件的类型
	//尝试进行配置读取
	if err := v.ReadInConfig(); err != nil {
		log.Fatalln(err)
	}
	if err := v.Unmarshal(&Conf); err != nil {
		log.Fatalln("配置文件：Serialization configuration failed. ", err)
		return
	}

	//validate
	err = validate.Struct(Conf)
	if err != nil {
		log.Fatalln(err)
	}
}
