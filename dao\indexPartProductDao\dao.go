package indexPartProductDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 首页专区-商品
type DaoInt interface {
	Create(ctx context.Context, data model.IndexPartProduct) error
	CreateMany(ctx context.Context, data []model.IndexPartProduct) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	BulkWrite(ctx context.Context, operations []mongo.WriteModel) error
	Get(ctx context.Context, filter bson.M) (model.IndexPartProduct, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	List(ctx context.Context, filter bson.M) ([]model.IndexPartProduct, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IndexPartProduct, int64, error)
	Delete(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
}

type indexPartProductDao struct {
	db *mongo.Collection
}

func (s indexPartProductDao) Create(ctx context.Context, data model.IndexPartProduct) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductDao) CreateMany(ctx context.Context, data []model.IndexPartProduct) error {
	var list []interface{}
	for _, datum := range data {
		list = append(list, datum)
	}
	_, err := s.db.InsertMany(ctx, list)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductDao) Get(ctx context.Context, filter bson.M) (model.IndexPartProduct, error) {
	var data model.IndexPartProduct
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IndexPartProduct{}, err
	}
	return data, nil
}

func (s indexPartProductDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s indexPartProductDao) List(ctx context.Context, filter bson.M) ([]model.IndexPartProduct, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.IndexPartProduct
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartProductDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IndexPartProduct, int64, error) {
	var list []model.IndexPartProduct
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s indexPartProductDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductDao) BulkWrite(ctx context.Context, operations []mongo.WriteModel) error {
	_, err := s.db.BulkWrite(ctx, operations)
	if err != nil {
		return err
	}
	return nil
}

func NewIndexPartProductDao(collect string) DaoInt {
	return indexPartProductDao{
		db: global.MDB.Collection(collect),
	}
}
