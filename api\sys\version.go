package sys

import (
	"base/core/xhttp"
	"base/service/versionService"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

// CreateVersion 版本
func CreateVersion(ctx *gin.Context) {
	var req = struct {
		Version     string `json:"version" validate:"required"`
		ForceUpdate bool   `json:"force_update"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	err = versionService.NewVersionService().Create(ctx, req.Version, req.ForceUpdate)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// CheckVersion 检查版本
func CheckVersion(ctx *gin.Context) {
	var req = struct {
		Version    string `json:"version"`
		NewVersion string `json:"new_version"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	version, err := versionService.NewVersionService().GetByVersion(ctx, req.NewVersion)
	if errors.Is(err, mongo.ErrNoDocuments) {
		// 不存在 强制重启
		version.ForceUpdate = false
		err = nil
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, version.ForceUpdate)
}

// ListVersion 版本列表
func ListVersion(ctx *gin.Context) {
	var req = struct {
		Page  int64 `uri:"page" validate:"min=1"`
		Limit int64 `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	list, i, err := versionService.NewVersionService().List(ctx, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, i)
}
