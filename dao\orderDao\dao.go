package orderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 订单
type DaoInt interface {
	Create(ctx context.Context, data model.Order) error
	CreateMany(ctx context.Context, data []model.Order) error
	Get(ctx context.Context, filter bson.M) (model.Order, error)
	GetMaxStockUpNo(ctx context.Context, filter bson.M) (int, error)
	List(ctx context.Context, filter bson.M) ([]model.Order, error)
	ListWithOption(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.Order, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Order, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	CountProductNum(ctx context.Context, buyerID primitive.ObjectID) (int64, int64, error)
	CountBuyerAndProductNum(ctx context.Context, productID primitive.ObjectID) (int64, int64, int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type orderDao struct {
	db *mongo.Collection
}

func (s orderDao) CreateMany(ctx context.Context, list []model.Order) error {
	data := make([]interface{}, len(list))
	for i, v := range list {
		data[i] = v
	}

	_, err := s.db.InsertMany(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s orderDao) Create(ctx context.Context, data model.Order) error {
	_, err := s.db.InsertOne(ctx, data)

	return err
}

func (s orderDao) Get(ctx context.Context, filter bson.M) (model.Order, error) {
	var data model.Order
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s orderDao) GetMaxStockUpNo(ctx context.Context, filter bson.M) (int, error) {
	var data model.Order
	err := s.db.FindOne(ctx, filter).Decode(&data)
	//return data, err
	return 0, err
}

//db.getCollection('collection1').find().sort({ rid: -1}).limit(1)

func (s orderDao) List(ctx context.Context, filter bson.M) ([]model.Order, error) {
	var list []model.Order

	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderDao) ListWithOption(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.Order, error) {
	var list []model.Order

	//opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Order, int64, error) {
	var list []model.Order
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s orderDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderDao) CountProductNum(ctx context.Context, buyerID primitive.ObjectID) (int64, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	}
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return 0, 0, err
	}
	var list []model.Order
	err = cursor.All(ctx, &list)
	if err != nil {
		return 0, 0, err
	}
	var productNum int64
	var amount int64
	for _, order := range list {
		for _, productOrder := range order.ProductList {
			productNum += int64(productOrder.Num)
		}
		amount += int64(order.PaidAmount)
	}

	return productNum, amount, nil
}

type name struct {
	ProductSum int64 `bson:"product_sum"`
	AmountSum  int64 `bson:"amount_sum"`
}

func (s orderDao) CountBuyerAndProductNum(ctx context.Context, productID primitive.ObjectID) (int64, int64, int64, error) {
	filter := bson.M{
		"product_list.product_id": productID,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	}
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return 0, 0, 0, err
	}
	var list []model.Order
	err = cursor.All(ctx, &list)
	if err != nil {
		return 0, 0, 0, err
	}
	var productNum int64
	mBuyer := make(map[primitive.ObjectID]int64)
	for _, order := range list {
		for _, productOrder := range order.ProductList {
			if productOrder.ProductID == productID {
				productNum += int64(productOrder.Num)
			}
		}
		mBuyer[order.BuyerID] = 0
	}

	return productNum, int64(len(mBuyer)), int64(len(list)), nil
}

func (s orderDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s orderDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return err
}

func NewOrderDao(collect string) DaoInt {
	return orderDao{
		db: global.MDB.Collection(collect),
	}
}
