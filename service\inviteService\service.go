package inviteService

import (
	"base/dao"
	"base/dao/inviteDao"
	"base/global"
	"base/model"
	"base/util"
	"context"
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 邀请服务接口
type ServiceInterface interface {
	Create(ctx context.Context, data model.Invite) ([]model.CouponUser, error)
	List(ctx context.Context, filter bson.M) ([]model.Invite, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invite, int64, error)
	CheckExist(ctx context.Context, buyerID, userID primitive.ObjectID) (model.Invite, error)
	GetInviteConfig(ctx context.Context) (model.InviteConfig, error)
	UpdateInviteConfig(ctx context.Context, data model.InviteConfig) error
	UpdateStatus(ctx context.Context, content string) error
}

type inviteService struct {
	mdb       *mongo.Database
	rdb       *redis.Client
	InviteDao inviteDao.DaoInt
}

// NewInviteService 创建邀请服务
func NewInviteService() ServiceInterface {
	return inviteService{
		mdb:       global.MDB,
		rdb:       global.RDBDefault,
		InviteDao: dao.InviteDao,
	}
}

func (s inviteService) Create(ctx context.Context, data model.Invite) ([]model.CouponUser, error) {
	// exist, err := s.CheckExist(ctx, data.InviterUserID, data.InvitedUserID)

	// if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	// 	return nil, err
	// }

	// if exist.ID != primitive.NilObjectID {
	// 	zap.S().Warnf("已存在关联,会员ID：%s，来源会员ID：%s", data.InviterUserID.Hex(), data.InvitedUserID.Hex())
	// 	return nil, xerr.ErrInviteExist
	// }
	// var couponAccounts []model.CouponAccount
	// session, err := s.mdb.Client().StartSession()
	// if err != nil {
	// 	return nil, err
	// }
	// defer session.EndSession(ctx)
	// _, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
	// 	err = s.InviteDao.Create(sessCtx, data)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	// 发券
	// 	couponAccounts, err = s.couponAccountS.CreateNewUserByInvite(sessCtx, data.InviterUserID, data.InvitedUserID)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	return nil, nil
	// })
	// if err != nil {
	// 	return nil, err
	// }

	// return couponAccounts, nil
	return nil, nil
}

func (s inviteService) CheckExist(ctx context.Context, buyerID, fromBuyerID primitive.ObjectID) (model.Invite, error) {
	filter := bson.M{
		"inviter_user_id": buyerID,
		"invited_user_id": fromBuyerID,
	}
	get, err := s.InviteDao.Get(ctx, filter)
	if err != nil {
		return model.Invite{}, err
	}
	return get, nil
}

func (s inviteService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invite, int64, error) {
	list, i, err := s.InviteDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s inviteService) List(ctx context.Context, filter bson.M) ([]model.Invite, error) {
	list, err := s.InviteDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

var cache = "invite-config"

func (s inviteService) GetInviteConfig(ctx context.Context) (model.InviteConfig, error) {
	key := cache
	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		b, err := s.rdb.Get(ctx, key).Bytes()
		var i model.InviteConfig
		err = json.Unmarshal(b, &i)
		if err != nil {
			zap.S().Errorf("InviteConfig unmarshal,err:%s", err)
			return model.InviteConfig{}, nil
		}
		return i, nil
	}

	// 不存在
	inviteConfig, err := s.CreateInviteConfig(ctx)
	if err != nil {
		return model.InviteConfig{}, err
	}

	return inviteConfig, nil
}

func (s inviteService) CreateInviteConfig(ctx context.Context) (model.InviteConfig, error) {
	now := time.Now()
	begin := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).AddDate(0, 0, 1)
	end := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).AddDate(0, 0, 10)

	diff := end.Sub(now).Seconds()

	data := model.InviteConfig{
		IsOpen:    false,
		TimeBegin: begin.UnixMilli(),
		TimeEnd:   end.UnixMilli(),
		UpdatedAt: now.UnixMilli(),
	}
	bytes, err := json.Marshal(data)
	if err != nil {
		zap.S().Errorf("set GetInviteConfig marshal,%s", err.Error())
		return model.InviteConfig{}, err
	}
	key := cache

	expire := time.Second * time.Duration(int64(diff))
	s.rdb.Set(context.Background(), key, bytes, expire)

	return data, nil
}

func (s inviteService) UpdateInviteConfig(ctx context.Context, data model.InviteConfig) error {
	b, err := json.Marshal(data)
	if err != nil {
		zap.S().Errorf("set GetInviteConfig marshal,%s", err.Error())
		return err
	}
	key := cache

	now := time.Now()
	seconds := time.UnixMilli(data.TimeEnd).Sub(now).Seconds()

	expire := time.Second * time.Duration(int64(seconds))
	s.rdb.Set(context.Background(), key, b, expire)

	return nil
}

func (s inviteService) UpdateStatus(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("inviteService UpdateStatus error:%v", err)
			return
		}
	}()

	var data = struct {
		InvitedUserID string `json:"invited_user_id"`
		InviteStatus  int    `json:"invite_status"`
	}{}
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	if len(data.InvitedUserID) != 24 {
		zap.S().Errorf("UpdateStatus InvitedUserID id不符，id：%s,原数据：%s", data.InvitedUserID, content)
		return nil
	}

	invitedUserID, err := util.ConvertToObjectWithNote(data.InvitedUserID, "")
	if err != nil {
		return err
	}

	filter := bson.M{
		"invited_user_id": invitedUserID,
	}

	err = s.InviteDao.UpdateMany(ctx, filter, bson.M{"$set": bson.M{"invite_status": data.InviteStatus}})
	if err != nil {
		zap.S().Errorf("更新代金券账户有效错误，err:%s", err.Error())
		return err
	}

	return nil
}
