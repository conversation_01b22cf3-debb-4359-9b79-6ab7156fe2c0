package payModule

import (
	"base/global"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
)

// MemberService 会员
type MemberService interface {
	CreateMemberS(req pays.CreateMemberReq) (pays.CreateMemberRes, error) // CreateMemberS 创建会员
	SendCaptchaS(req pays.SendCaptchaReq) (pays.SendCaptchaRes, error)    // SendCaptchaS 发送验证码
	// BindPhoneS 绑定手机
	BindPhoneS(req pays.BindPhoneReq) (pays.BindPhoneRes, error)
	// UnbindPhoneS 解绑手机
	UnbindPhoneS(req pays.UnbindPhoneReq) (pays.UnbindPhoneRes, error)
	// SetRealNameS 个人实名认证
	SetRealNameS(req pays.SetRealNameReq) (pays.SetRealNameRes, error)
	// SetCompanyInfoS 设置企业信息
	SetCompanyInfoS(req pays.SetCompanyInfoReq) (pays.SetCompanyInfoRes, error)
	// UpdateCompanyInfoS 企业会员信息修改
	UpdateCompanyInfoS(req pays.UpdateCompanyInfoReq) (pays.UpdateCompanyInfoRes, error)
	// GetMemberInfoForIndividualS 获取个人会员信息
	GetMemberInfoForIndividualS(req pays.GetMemberInfoReq) (pays.IndividualMemberInfoGet, error)
	// GetMemberInfoForCompanyS 获取企业会员信息
	GetMemberInfoForCompanyS(req pays.GetMemberInfoReq) (pays.CompanyMemberInfoGet, error)
	// GetBankCardBinS 查询卡bin
	GetBankCardBinS(req pays.GetBankCardBinReq) (pays.GetBankCardBinRes, error)
	// SignAcctProtocolS 账户提现协议签约
	SignAcctProtocolS(req pays.SignAcctProtocolReq) (pays.SignAcctProtocolRes, error)
	// SignContractQueryS 账户协议签约查询
	SignContractQueryS(req pays.SignContractQueryReq) (pays.SignContractQueryRes, error)
	//	idcardCollect 影印件采集
	IdcardCollectS(req pays.IdcardCollectReq) (pays.IdcardCollectRes, error)
	//UnbindBankCard 解绑银行卡
	UnbindBankCard(req pays.UnbindBankCardReq) (pays.UnbindBankCardRes, error)
	//	ApplyBindBankCard 请求绑定银行卡
	ApplyBindBankCardS(req pays.ApplyBindBankCardReq) (pays.ApplyBindBankCardRes, error)
	//BindBankCard 确认绑定银行卡--银行四要素无需确认
	BindBankCard(req pays.BindBankCardReq) (pays.BindBankCardRes, error)
	//	QueryBankCard 查询绑定银行卡
	QueryBankCardS(req pays.QueryBankCardReq) (pays.QueryBankCardRes, error)

	ApplyBindAcctS(req pays.ApplyBindAcctReq) (pays.ApplyBindAcctRes, error) // 支付用户标识
}

type member struct {
	cli *allinpay.AllInPay
}

func NewMember() MemberService {
	return &member{
		cli: global.AllinPayClient,
	}
}

// CreateMemberS 创建会员
func (s member) CreateMemberS(req pays.CreateMemberReq) (pays.CreateMemberRes, error) {
	res, err := s.cli.MemberService.CreateMember(req)
	if err != nil {
		return pays.CreateMemberRes{}, err
	}

	return res, nil
}

// SendCaptchaS 发送验证码
func (s member) SendCaptchaS(req pays.SendCaptchaReq) (pays.SendCaptchaRes, error) {
	res, err := s.cli.MemberService.SendCaptcha(req)
	if err != nil {
		return pays.SendCaptchaRes{}, err
	}

	return res, nil
}

// BindPhoneS 绑定手机
func (s member) BindPhoneS(req pays.BindPhoneReq) (pays.BindPhoneRes, error) {
	res, err := s.cli.MemberService.BindPhone(req)
	if err != nil {
		return pays.BindPhoneRes{}, err
	}

	return res, nil
}

// UnbindPhoneS 解绑手机
func (s member) UnbindPhoneS(req pays.UnbindPhoneReq) (pays.UnbindPhoneRes, error) {
	res, err := s.cli.MemberService.UnbindPhone(req)
	if err != nil {
		return pays.UnbindPhoneRes{}, err
	}

	return res, nil
}

// SetRealNameS 个人实名认证
// 绑定银行卡前需先进行实名认证。
// 个人会员创建会员后即可实名认证，与是否绑定手机无关。
// 实名认证是去公安网验证这个人是真实存在的。
func (s member) SetRealNameS(req pays.SetRealNameReq) (pays.SetRealNameRes, error) {
	res, err := s.cli.MemberService.SetRealName(req)
	if err != nil {
		return pays.SetRealNameRes{}, err
	}

	return res, nil
}

// SetCompanyInfoS 设置企业信息
func (s member) SetCompanyInfoS(req pays.SetCompanyInfoReq) (pays.SetCompanyInfoRes, error) {
	res, err := s.cli.MemberService.SetCompanyInfo(req)
	if err != nil {
		return pays.SetCompanyInfoRes{}, err
	}

	return res, nil
}

// UpdateCompanyInfoS 企业会员信息修改
func (s member) UpdateCompanyInfoS(req pays.UpdateCompanyInfoReq) (pays.UpdateCompanyInfoRes, error) {
	res, err := s.cli.MemberService.UpdateCompanyInfo(req)
	if err != nil {
		return pays.UpdateCompanyInfoRes{}, err
	}

	return res, nil
}

// GetMemberInfoForIndividualS 获取个人会员信息
func (s member) GetMemberInfoForIndividualS(req pays.GetMemberInfoReq) (pays.IndividualMemberInfoGet, error) {
	res, err := s.cli.MemberService.GetMemberInfoForIndividual(req)
	if err != nil {
		return pays.IndividualMemberInfoGet{}, err
	}

	return res, nil

}

// GetMemberInfoForCompanyS 获取企业会员信息
func (s member) GetMemberInfoForCompanyS(req pays.GetMemberInfoReq) (pays.CompanyMemberInfoGet, error) {
	res, err := s.cli.MemberService.GetMemberInfoForCompany(req)
	if err != nil {
		return pays.CompanyMemberInfoGet{}, err
	}

	return res, nil
}

// GetBankCardBinS 查询卡bin
func (s member) GetBankCardBinS(req pays.GetBankCardBinReq) (pays.GetBankCardBinRes, error) {
	res, err := s.cli.MemberService.GetBankCardBin(req)
	if err != nil {
		return pays.GetBankCardBinRes{}, err
	}

	return res, nil
}

// SignAcctProtocolS 账户提现协议签约
func (s member) SignAcctProtocolS(req pays.SignAcctProtocolReq) (pays.SignAcctProtocolRes, error) {
	res, err := s.cli.MemberService.SignAcctProtocol(req)
	if err != nil {
		return pays.SignAcctProtocolRes{}, err
	}

	return res, nil
}

// SignContractQueryS 账户协议签约查询
func (s member) SignContractQueryS(req pays.SignContractQueryReq) (pays.SignContractQueryRes, error) {
	res, err := s.cli.MemberService.SignContractQuery(req)
	if err != nil {
		return pays.SignContractQueryRes{}, err
	}

	return res, nil
}

// IdcardCollectS 影印件采集
func (s member) IdcardCollectS(req pays.IdcardCollectReq) (pays.IdcardCollectRes, error) {
	res, err := s.cli.MemberService.IdcardCollect(req)
	if err != nil {
		return pays.IdcardCollectRes{}, err
	}

	return res, nil
}

func (s member) UnbindBankCard(req pays.UnbindBankCardReq) (pays.UnbindBankCardRes, error) {
	res, err := s.cli.MemberService.UnbindBankCard(req)
	if err != nil {
		return pays.UnbindBankCardRes{}, err
	}

	return res, nil
}

// ApplyBindBankCardS 请求绑定银行卡
func (s member) ApplyBindBankCardS(req pays.ApplyBindBankCardReq) (pays.ApplyBindBankCardRes, error) {
	res, err := s.cli.MemberService.ApplyBindBankCard(req)
	if err != nil {
		return pays.ApplyBindBankCardRes{}, err
	}

	return res, nil
}

func (s member) BindBankCard(req pays.BindBankCardReq) (pays.BindBankCardRes, error) {
	res, err := s.cli.MemberService.BindBankCard(req)
	if err != nil {
		return pays.BindBankCardRes{}, err
	}

	return res, nil
}

// QueryBankCardS 查询绑定银行卡
func (s member) QueryBankCardS(req pays.QueryBankCardReq) (pays.QueryBankCardRes, error) {
	res, err := s.cli.MemberService.QueryBankCard(req)
	if err != nil {
		return pays.QueryBankCardRes{}, err
	}

	return res, nil
}

// ApplyBindAcctS 支付用户标识
func (s member) ApplyBindAcctS(req pays.ApplyBindAcctReq) (pays.ApplyBindAcctRes, error) {
	res, err := s.cli.MemberService.ApplyBindAcct(req)
	if err != nil {
		return pays.ApplyBindAcctRes{}, err
	}

	return res, nil
}
