package order

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"time"
)

func ListByParentOrder(ctx *gin.Context) {
	var req = struct {
		ParentOrderID string `json:"parent_order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ParentOrderID, "parent_order_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	orders, err := orderService.NewOrderService().ListByParentOrderID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, orders)

}

func ListByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().Get(buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//id, _ := primitive.ObjectIDFromHex("6470708ced36e747d0ffd941")
	//buyer.ID = id

	orders, i, err := orderService.NewOrderService().ListByBuyer(ctx, buyer.ID, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, orders, i)

}

func ListByBuyerID(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orders, i, err := orderService.NewOrderService().ListByBuyer(ctx, buyerID, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, orders, i)

}

func ListStatusByBuyerID(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().Get(buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var res statusRes
	orders, err := orderService.NewOrderService().ListStatusByBuyer(ctx, buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, v := range orders {
		if v.PayStatus == model.PayStatusTypeToPay || v.PayStatus == model.PayStatusTypePending {
			if v.OrderStatus == model.OrderStatusTypeClosed {
				continue
			}
			res.ToPayNum++
		}
		if v.PayStatus == model.PayStatusTypePaid || v.PayStatus == model.PayStatusTypePaidButRefund {
			if v.OrderStatus == model.OrderStatusTypeCancel {
				continue
			}
			if v.OrderStatus == model.OrderStatusTypeToStockUp || v.OrderStatus == model.OrderStatusTypeToQuality || v.OrderStatus == model.OrderStatusTypeToSort || v.OrderStatus == model.OrderStatusTypeToShip {
				res.ToShipNum++
			}
			if v.OrderStatus == model.OrderStatusTypeToReceive {
				res.ToReceiveNum++
			}
		}

		if v.PayStatus == model.PayStatusTypePaid && v.OrderStatus == model.OrderStatusTypeFinish && v.HasComment == false && !v.OrderRefundAll {
			//for _, p := range v.ProductList {
			//	if !p.IsShipRefundAll {
			//		res.ToCommentNum++
			//		break
			//	}
			//}
			begin := time.Now().AddDate(0, 0, -7).UnixMilli()
			if v.CreatedAt > begin {
				res.ToCommentNum++
			}

		}

	}

	countByBuyer, err := orderRefundService.NewOrderRefundService().CountNum(ctx, buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	// 售后
	res.AfterSaleNum = int(countByBuyer)

	var debtToPayNum int

	debtList, err := orderDebtService.NewOrderDebtService().ListByBuyerID(ctx, buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	for _, i := range debtList {
		if i.PayStatus == model.PayStatusTypeToPay || i.PayStatus == model.PayStatusTypePending {
			debtToPayNum++
		}
	}

	res.DebtToPayNum += debtToPayNum

	xhttp.RespSuccess(ctx, res)
}

type statusRes struct {
	ToPayNum     int `json:"to_pay_num"` // 待付款
	DebtToPayNum int `json:"debt_to_pay_num"`
	ToShipNum    int `json:"to_ship_num"`    // 待发货
	ToReceiveNum int `json:"to_receive_num"` // 待收货
	ToCommentNum int `json:"to_comment_num"` // 待评价
	AfterSaleNum int `json:"after_sale_num"` // 售后
}
