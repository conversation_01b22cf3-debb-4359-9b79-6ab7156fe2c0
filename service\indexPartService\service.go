package indexPartService

import (
	"base/dao"
	"base/dao/indexPartDao"
	"base/global"
	"base/model"
	"base/service/productService"
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ServiceInterface interface {
	Init(ctx context.Context, data model.IndexPart) error
	Update(ctx context.Context, id primitive.ObjectID, title, style string, sort int, visible bool, displayNum int, topImg model.FileInfo, condition []string) error
	Get(ctx context.Context, id primitive.ObjectID) (model.IndexPart, error)
	GetLocalByStation(ctx context.Context, stationID primitive.ObjectID) (model.IndexPart, error)
	ListVisible(ctx context.Context, pointID primitive.ObjectID) ([]model.IndexPart, error)
	List(ctx context.Context, visibleType int) ([]model.IndexPart, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type indexPartService struct {
	mdb          *mongo.Database
	indexPartDao indexPartDao.DaoInt
	productS     productService.ServiceInterface
}

func NewIndexPartService() ServiceInterface {
	return indexPartService{
		mdb:          global.MDB,
		indexPartDao: dao.IndexPartDao,
		productS:     productService.NewProductService(),
	}
}

func (s indexPartService) Update(ctx context.Context, id primitive.ObjectID, title, style string, sort int, visible bool, displayNum int, topImg model.FileInfo, condition []string) error {
	now := time.Now().UnixMilli()
	data := model.IndexPart{
		ID:         primitive.NewObjectID(),
		Title:      title,
		Style:      style,
		Visible:    visible,
		Sort:       sort,
		DisplayNum: displayNum,
		TopImg:     topImg,
		Condition:  condition,
		CreatedAt:  now,
	}

	get, err := s.Get(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		data.ID = get.ID
		data.UpdatedAt = now
	}
	data.ServicePointID = get.ServicePointID

	err = s.indexPartDao.Upsert(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartService) Init(ctx context.Context, data model.IndexPart) error {
	err := s.indexPartDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartService) Get(ctx context.Context, id primitive.ObjectID) (model.IndexPart, error) {
	part, err := s.indexPartDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.IndexPart{}, err
	}
	return part, nil
}

func (s indexPartService) GetLocalByStation(ctx context.Context, stationID primitive.ObjectID) (model.IndexPart, error) {
	part, err := s.indexPartDao.Get(ctx, bson.M{"station_id": stationID})
	if err != nil {
		return model.IndexPart{}, err
	}
	return part, nil
}

func (s indexPartService) ListVisible(ctx context.Context, pointID primitive.ObjectID) ([]model.IndexPart, error) {
	filter := bson.M{
		"visible":          true,
		"service_point_id": pointID,
	}
	list, err := s.indexPartDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartService) List(ctx context.Context, visibleType int) ([]model.IndexPart, error) {
	filter := bson.M{}
	if visibleType == 1 {
		filter["visible"] = true
	}
	if visibleType == 2 {
		filter["visible"] = false
	}
	list, err := s.indexPartDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.indexPartDao.Delete(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}
