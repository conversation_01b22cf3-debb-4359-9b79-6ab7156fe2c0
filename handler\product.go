package handler

import (
	"base/api/cart"
	"base/api/fruitClass"
	"base/api/product"
	"base/api/product/productTag"
	"base/api/productOffline"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

func productRouter(r *gin.RouterGroup) {
	r = r.Group("/product")

	r2 := r.Group("/").Use(middleware.CheckToken)
	r2.POST("/create", product.Create)
	r2.POST("/update", product.Update)
	r2.POST("/duplicate", product.Duplicate)
	r2.POST("/stock/update", product.UpdateStock)
	r2.POST("/custom/tag/update", product.UpdateCustomTag)
	r2.POST("/buy/limit/update", product.UpdateBuyLimit)
	//r2.POST("/price/update", product.UpdatePrice)
	r2.POST("/price/single/update", product.UpdatePriceSingle)
	r2.POST("/user/type/update", product.UpdateProductUserType)
	r2.POST("/desc/update", product.UpdateDesc)
	r2.POST("/sku/desc/update", product.UpdateSkuDesc)
	r2.POST("/title/update", product.UpdateTitle)
	r2.POST("/purchase/note/update", product.UpdatePurchaseNote)
	r2.POST("/attr/update", product.UpdateAttr)
	r2.POST("/sale/update", product.UpdateSale)
	r2.POST("/delete", product.Delete)
	r2.GET("/list", product.List)
	r2.POST("/list/offline/audit", product.ListOffLineAudit)
	r2.POST("/offline/audit", product.OffLineAudit)

	r2.POST("/link/create", product.CreateLink)
	r2.POST("/link/remove", product.RemoveLink)
	r2.POST("/link/list/has", product.ListLinkedProduct)
	r2.POST("/link/check", product.CheckLink)
	r2.POST("/link/price/change/update", product.UpdateLinkPriceChange)
	r2.POST("/link/info", product.GetLinkInfo)

	r2.POST("/list/offline", productOffline.ListOffline)

	r2.POST("/recommend/update", product.UpdateRecommend)

	r4 := r.Group("/")
	r4.POST("/get", product.Get)
	r4.POST("/count/audit", product.CountAudit)
	r4.POST("/get/pure", product.GetOnly)
	r4.POST("/get/update", product.GetUpdate)
	r4.POST("/list/category", product.ListByCategory)
	r4.POST("/list/by/yht", product.ListByYHT)
	r4.POST("/list/category/web", product.ListByCategoryWeb)
	r4.POST("/list/ids", product.ListByProductIDs)
	r4.POST("/list/by/origin", product.ListByOrigin)
	r4.POST("/list/supplier/for/user", product.ListBySupplierForUser)
	r4.POST("/list/search", product.Search)
	r4.POST("/list/by/brand", product.ListByBrand)
	r4.POST("/search/top/list", product.SearchTopList)
	r4.POST("/search/top/upsert", product.SearchTopUpsert)
	r4.POST("/search/history", product.SearchHistory)
	r4.POST("/search/find/list", product.SearchFind)
	r4.POST("/list/search/supplier", product.SearchForSupplier)
	r4.POST("/list/supplier/audit", product.ListAuditBySupplierID)
	r4.POST("/list/external/sale", product.ListExternalSale)
	r4.POST("/list/latest", product.ListLatest)
	r4.POST("/audit/get", product.GetAudit)                      // 商品审核详情
	r4.POST("/audit/supplier/list", product.ListAuditBySupplier) // 商品审核列表
	r4.POST("/audit/list", product.ListAudit)                    // 商品审核列表
	r4.POST("/audit/delete", product.DeleteAudit)                // 删除商品审核记录
	r4.POST("/list/by/cover/tag", product.ListByCoverTag)
	r4.POST("/list/by/normal/tag", product.ListByNormalTag)
	//	 专区
	r4.POST("/list/index/part", product.ListPart)
	r4.POST("/list/index/part/supplier", product.ListPartBySupplier)
	r4.POST("/list/index/part/local/by/supplier", product.ListPartBySupplierStation)
	r4.POST("/update/index/part/local/by/supplier", product.UpdateLocalBySupplier)
	// 单位
	r4.POST("/unit/create", product.CreateUnit)
	r4.GET("/unit/list", product.ListUnit)

	r4.POST("/recommend/list", product.ListRecommend)

	//	购物车
	r3 := r.Group("/").Use(middleware.CheckToken)
	r3.POST("/cart/upsert", cart.Upsert)
	r3.POST("/cart/delete", cart.DeleteOne)
	r3.POST("/cart/list", cart.List)
	r3.POST("/cart/num", cart.GetNum)
	r3.POST("/cart/list/product", cart.ListForProduct)
	r3.POST("/sale/down/batch", product.DownSaleBatch)
	//r3.POST("/price/part/update", product.UpdatePricePart)

	r6 := r.Group("/")
	r6.POST("/fruit/class", fruitClass.Create)
	r6.POST("/fruit/class/list/category", fruitClass.List)
	r6.POST("/fruit/class/update", fruitClass.Update)
	r6.POST("/fruit/class/delete", fruitClass.Delete)

	//	商品标签
	r5 := r.Group("/tag")
	r5.POST("/create", productTag.Create)
	r5.POST("/list", productTag.List)
	r5.POST("/list/avilable", productTag.ListAllAvilable)
	r5.POST("/update", productTag.UpdateTitle)
	r5.POST("/delete", productTag.Delete)
	// 绑定标签，权限
	r5.POST("/bind", product.BindProductTag)
	r5.POST("/cover/bind", product.BindCoverProductTag)

	rSupplier := r.Group("/") // 供应商
	rSupplier.POST("/list/supplier", product.ListBySupplierID)
	rSupplier.POST("/low/stock/count/supplier", product.CountLowStockBySupplierID)
	rSupplier.POST("/low/stock/list/supplier", product.ListLowStockBySupplierID)
	rSupplier.POST("/list/to/update/supplier", product.ListToUpdateBySupplierID)
	rSupplier.POST("/num/by/supplier", product.GetProductNumBySupplier)
	rSupplier.POST("/num/all/supplier", product.ListProductNumAllSupplier)
	rSupplier.POST("/category/used/supplier", product.CategoryUsedBySupplier)
}
