package adminProduct

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Audit 商品审核
func Audit(ctx *gin.Context) {
	var req = struct {
		AuditID      string           `json:"audit_id"`
		SkuPriceList []types.SkuPrice `json:"sku_price_list"`
		FailReason   string           `json:"fail_reason" validate:"-"`
		Status       int              `json:"status" validate:"required"`
		// PurchaseNote string           `json:"purchase_note"` // 采购说明-采购门店
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.Status)]; !ok {
		xhttp.RespErr(ctx, xerr.New<PERSON>rr(xerr.ErrParamError, nil, "审核状态类型错误"))
		return
	}

	auditID, err := util.ConvertToObject(req.AuditID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().Audit(ctx, auditID, model.AuditStatusType(req.Status), req.FailReason, req.SkuPriceList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
