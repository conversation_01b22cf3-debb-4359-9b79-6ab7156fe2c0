package supplierCollect

import (
	"base/core/xhttp"
	"base/service/supplierCollectService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Upsert(ctx *gin.Context) {
	var req = struct {
		BuyerID    string `json:"buyer_id"`
		SupplierID string `json:"supplier_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	supplierID, err := util.ConvertToObjectWithNote(req.SupplierID, "supplier_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = supplierCollectService.NewSupplierCollectService().Create(ctx, id, supplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
