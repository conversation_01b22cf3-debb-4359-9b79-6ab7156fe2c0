package sys

import (
	"base/core/xhttp"
	"base/service/ossService"
	"bytes"
	"encoding/base64"
	"github.com/gin-gonic/gin"
	"github.com/nfnt/resize"
	"image/png"
	"log"
)

// GetOssUploadSign oss上传签名
func GetOssUploadSign(ctx *gin.Context) {
	var req = struct {
		Dir string `uri:"dir" validate:"oneof=user buyer product certificate swipe shortcut topic order sys integral promote brand invoice yeeGuoshut"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	info, err := ossService.NewOssService().GetSignUpload(req.Dir)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, info)
	return
}

func Down(ctx *gin.Context) {
	var req = struct {
		Url string `json:"url"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	b, err := ossService.NewOssService().DownLoad(req.Url)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	decode, err := png.Decode(bytes.NewBuffer(b))

	i := resize.Resize(0, 0, decode, resize.Lanczos3)
	newI := bytes.NewBuffer(nil)
	err = png.Encode(newI, i)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	toString := "data:supplierImage/png;base64," + base64.StdEncoding.EncodeToString(newI.Bytes())

	log.Println("大小", float64(len(newI.Bytes()))/1024/1024)

	xhttp.RespSuccess(ctx, toString)
}
