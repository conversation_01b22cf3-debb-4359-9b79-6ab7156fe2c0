package adminRoute

import (
	"base/core/xhttp"
	"base/model"
	"base/service/routeService"
	"base/util"
	"github.com/gin-gonic/gin"
	"time"
)

func Update(ctx *gin.Context) {
	var req = struct {
		ID              string              `json:"id" validate:"len=24"`
		ActualDistance  int                 `json:"actual_distance" validate:"min=1"`
		FeePerKG        int                 `json:"fee_per_kg" validate:"min=0"`
		DeliverTimeList []model.DeliverTime `json:"deliver_time_list"` // 配送时效
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r, err := routeService.NewTransportFeeService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r.ActualDistance = req.ActualDistance
	r.FeePerKG = req.FeePerKG
	r.DeliverTime = req.DeliverTimeList
	r.UpdatedAt = time.Now().UnixMilli()

	err = routeService.NewTransportFeeService().Upsert(ctx, r)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
