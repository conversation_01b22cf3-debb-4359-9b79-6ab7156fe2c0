package routeService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/routeDao"
	"base/global"
	"base/model"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, data model.Route) error
	Update(ctx context.Context, id primitive.ObjectID, feePerKG, actualDistance int) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Route, error)
	GetServicePoint(ctx context.Context, servicePointID primitive.ObjectID) (model.Route, error)
	GetToWarehouse(ctx context.Context, id primitive.ObjectID) (model.Route, error)
	List(ctx context.Context, fromID primitive.ObjectID) ([]model.Route, error)
	ListByPage(ctx context.Context, fromID primitive.ObjectID, page, limit int64) ([]model.Route, int64, error)
	CheckExist(ctx context.Context, warehouseID, cityPartnerID primitive.ObjectID) error
}

type routeService struct {
	rdb      *redis.Client
	routeDao routeDao.DaoInt
}

func (s routeService) Update(ctx context.Context, id primitive.ObjectID, feePerKG, actualDistance int) error {
	route, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	err = s.routeDao.Update(bson.M{"_id": id}, bson.M{"$set": bson.M{
		"actual_distance": actualDistance,
		"fee_per_kg":      feePerKG,
	}})
	if err != nil {
		return err
	}

	del(s.rdb, route.ToServicePointID)
	return nil
}

func (s routeService) GetByID(ctx context.Context, id primitive.ObjectID) (model.Route, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.routeDao.Get(ctx, filter)
	if err != nil {
		return model.Route{}, err
	}
	return fee, nil
}

func (s routeService) GetServicePoint(ctx context.Context, servicePointID primitive.ObjectID) (model.Route, error) {
	m := get(s.rdb, servicePointID)
	if m.ID == primitive.NilObjectID {
		filter := bson.M{
			"route_type":          model.RouteTypeServicePoint,
			"to_service_point_id": servicePointID,
		}
		data, err := s.routeDao.Get(ctx, filter)
		if err != nil {
			return model.Route{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s routeService) GetToWarehouse(ctx context.Context, id primitive.ObjectID) (model.Route, error) {
	filter := bson.M{
		"route_type":      model.RouteTypeWarehouse,
		"to_warehouse_id": id,
	}
	fee, err := s.routeDao.Get(ctx, filter)
	if err != nil {
		return model.Route{}, err
	}
	return fee, nil
}

func (s routeService) List(ctx context.Context, fromID primitive.ObjectID) ([]model.Route, error) {
	filter := bson.M{
		"from_warehouse_id": fromID,
	}
	list, err := s.routeDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s routeService) ListByPage(ctx context.Context, fromID primitive.ObjectID, page, limit int64) ([]model.Route, int64, error) {
	filter := bson.M{
		"from_warehouse_id": fromID,
	}
	list, i, err := s.routeDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s routeService) CheckExist(ctx context.Context, warehouseID, cityPartnerID primitive.ObjectID) error {
	// 线路唯一性
	get, err := s.routeDao.Get(ctx, bson.M{
		"warehouse_id": warehouseID,
		"partner_id":   cityPartnerID,
	})
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil
	}
	if err != nil {
		return err
	}
	if get.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "创建失败，该线路费用已存在")
	}
	return nil
}

func (s routeService) Upsert(ctx context.Context, data model.Route) error {
	if data.ActualDistance < 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "实际距离数值不合理")
	}
	//if data.FeePerKG < 1 {
	//	return xerr.NewErr(xerr.ErrParamError, nil, "路线运费数值不合理")
	//}

	err := s.routeDao.Upsert(ctx, data)
	if err != nil {
		return err
	}
	del(s.rdb, data.ToServicePointID)
	return nil
}

func NewTransportFeeService() ServiceInterface {
	return routeService{
		rdb:      global.RDBDefault,
		routeDao: dao.RouteDao,
	}
}
