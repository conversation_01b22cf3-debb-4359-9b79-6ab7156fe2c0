package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/categoryService"
	"base/service/productService"
	"base/service/supplierService"
	"base/types"
	"base/util"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sort"
	"strconv"
	"time"
)

// ListBySupplierID 根据供应商ID
func ListBySupplierID(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"min=1"`
		CategoryID string `json:"category_id" validate:"-"`
		Sale       bool   `json:"sale" validate:"-"`
		Page       int64  `json:"page" validate:"min=1"`
		Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"supplier_id": id,
		"sale":        req.Sale,
		"deleted_at":  0,
	}

	if len(req.CategoryID) == 24 {
		cid, err := util.ConvertToObject(req.CategoryID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["category_ids.1"] = cid
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	xhttp.RespSuccessList(ctx, list, count)

}

// CountLowStockBySupplierID 供应商低库存-数
func CountLowStockBySupplierID(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"supplier_id": id,
		"sale":        true,
		"deleted_at":  0,
		"stock": bson.M{
			"$lt": 10,
		},
	}

	count, err := productService.NewProductService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, count)

}

// ListLowStockBySupplierID 供应商低库存
func ListLowStockBySupplierID(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"min=1"`
		//Page       int64  `json:"page" validate:"min=1"`
		//Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"supplier_id": id,
		"sale":        true,
		"deleted_at":  0,
		"stock": bson.M{
			"$lt": 10,
		},
	}

	//if len(req.CategoryID) == 24 {
	//	cid, err := util.ConvertToObject(req.CategoryID)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	filter["category_ids.1"] = cid
	//}

	products, err := productService.NewProductService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, products)

}

func ListToUpdateBySupplierID(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"min=1"`
		//Page       int64  `json:"page" validate:"min=1"`
		//Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	t := time.Now().Add(time.Hour * time.Duration(int64(-24*3)))

	filter := bson.M{
		"supplier_id": id,
		"sale":        true,
		"deleted_at":  0,
		"version": bson.M{
			"$lte": t.UnixMilli(),
		},
	}

	//if len(req.CategoryID) == 24 {
	//	cid, err := util.ConvertToObject(req.CategoryID)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	filter["category_ids.1"] = cid
	//}

	products, err := productService.NewProductService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, products)

}

// ListBySupplierForUser 供应商
func ListBySupplierForUser(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"min=1"`
		Page       int64  `json:"page" validate:"min=1"`
		Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"supplier_id": id,
		"sale":        true,
		"deleted_at":  0,
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	if len(list) > 1 {
		f, _, err := xhttp.CheckPrice(ctx)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		if !f {
			for j, _ := range list {
				list[j].Price = 0
				list[j].OriginPrice = 0
			}
		}
	}

	xhttp.RespSuccessList(ctx, list, count)

}

// GetProductNumBySupplier 商品数
func GetProductNumBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := productService.NewProductService().StatsNumBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

func ListProductNumAllSupplier(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, err := productService.NewProductService().StatsNumAllSupplier(ctx, bson.M{})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, stats := range list {
		ids = append(ids, stats.SupplierID)
	}

	suppliers, err := supplierService.NewSupplierService().ListByIDs(ctx, ids)

	var resList []NumAllSupplier
	for _, i := range list {
		var sName string
		for _, supplier := range suppliers {
			if i.SupplierID == supplier.ID {
				sName = supplier.ShopSimpleName
				break
			}
		}
		resList = append(resList, NumAllSupplier{
			SupplierName:  sName,
			SupplierStats: i,
		})
	}

	// 排序
	sort.Slice(resList, func(i, j int) bool {
		return resList[i].SupplierStats.SaleTrueCount > resList[j].SupplierStats.SaleTrueCount
	})

	xhttp.RespSuccess(ctx, resList)
}

type NumAllSupplier struct {
	SupplierName string `json:"supplier_name"`
	model.SupplierStats
}

// CategoryUsedBySupplier 分类
func CategoryUsedBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
		SaleStatus int    `json:"sale_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	categoryList, err := productService.NewProductService().CategoryUsedBySupplier(ctx, req.SaleStatus, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var ids []primitive.ObjectID
	mAll := make(map[primitive.ObjectID]int)
	m := make(map[primitive.ObjectID][]model.CategoryOnly)
	for _, v := range categoryList {
		for _, categoryId := range v.CategoryIds {
			mAll[categoryId] = 0
		}
		m[v.CategoryIds[0]] = append(m[v.CategoryIds[0]], v)
	}
	for objectID, _ := range mAll {
		ids = append(ids, objectID)
	}

	byIDs, err := categoryService.NewCategoryService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	mName := make(map[primitive.ObjectID]string)
	mSort := make(map[primitive.ObjectID]int)
	for _, v := range byIDs {
		mName[v.ID] = v.Name
		mSort[v.ID] = v.Sort
	}

	var list []Category
	for objectID, cList := range m {
		text := fmt.Sprintf("%s-%s", mName[objectID], strconv.Itoa(len(cList)))
		item := Category{
			Text:  text,
			Value: objectID,
			Sort:  mSort[objectID],
		}
		childrenLevelTwo := make([]Category, 0)
		// 二级分组
		mTwo := make(map[primitive.ObjectID][]model.CategoryOnly)
		for _, v := range cList {
			mTwo[v.CategoryIds[1]] = append(mTwo[v.CategoryIds[1]], v)
		}
		for objectIDTwo, cListTwo := range mTwo {
			textTwo := fmt.Sprintf("%s-%s", mName[objectIDTwo], strconv.Itoa(len(cListTwo)))
			itemTwo := Category{
				Text:  textTwo,
				Value: objectIDTwo,
				Sort:  mSort[objectIDTwo],
			}
			childrenLevelTwo = append(childrenLevelTwo, itemTwo)
		}

		item.Children = childrenLevelTwo
		list = append(list, item)
	}

	for k := 0; k < len(list); k++ {
		sort.Slice(list[k].Children, func(i, j int) bool {
			return list[k].Children[i].Sort < list[k].Children[j].Sort
		})
	}

	sort.Slice(list, func(i, j int) bool {
		return list[i].Sort < list[j].Sort
	})

	xhttp.RespSuccess(ctx, list)
}

type Category struct {
	Text     string             `json:"text"`
	Value    primitive.ObjectID `json:"value"`
	Sort     int                `json:"sort"`
	Children []Category         `json:"children"`
}
