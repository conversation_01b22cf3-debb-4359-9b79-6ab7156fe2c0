package orderFinalSettleService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderFinalSettleDao"
	"base/mnsSendService"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/productBuyPriceService"
	"context"
	"errors"
	"time"

	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInt 订单最终结算记录服务接口
type ServiceInt interface {
	Get(ctx context.Context, settleID primitive.ObjectID) (model.OrderFinalSettle, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderFinalSettle, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderFinalSettle, int64, error)
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderFinalSettle, error)
	CreateFromOrder(ctx context.Context, orderID primitive.ObjectID) error
}

type orderFinalSettleService struct {
	dao          orderFinalSettleDao.DaoInt
	orderS       orderService.ServiceInterface
	orderRefundS orderRefundService.ServiceInterface
	orderDebtS   orderDebtService.ServiceInterface
	productBuyS  productBuyPriceService.ServiceInterface
}

// NewService 创建订单最终结算记录服务
func NewService() ServiceInt {
	return &orderFinalSettleService{
		dao:          dao.OrderFinalSettleDao,
		orderS:       orderService.NewOrderService(),
		orderRefundS: orderRefundService.NewOrderRefundService(),
		orderDebtS:   orderDebtService.NewOrderDebtService(),
		productBuyS:  productBuyPriceService.NewProductBuyPriceService(),
	}
}

// Get 获取订单最终结算记录
func (s *orderFinalSettleService) Get(ctx context.Context, settleID primitive.ObjectID) (model.OrderFinalSettle, error) {
	result, err := s.dao.GetByID(ctx, settleID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return result, xerr.NewErr(xerr.ErrNoDocument, nil, "订单最终结算记录不存在")
		}
		return result, xerr.NewErr(xerr.ErrSysBusy, err, "查询订单最终结算记录失败")
	}
	return result, nil
}

// List 获取订单最终结算记录列表
func (s *orderFinalSettleService) List(ctx context.Context, filter bson.M) ([]model.OrderFinalSettle, error) {
	result, err := s.dao.List(ctx, filter)
	if err != nil {
		return result, xerr.NewErr(xerr.ErrSysBusy, err, "查询订单最终结算记录列表失败")
	}
	return result, nil
}

// ListByPage 分页获取订单最终结算记录列表
func (s *orderFinalSettleService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderFinalSettle, int64, error) {
	result, total, err := s.dao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return result, total, xerr.NewErr(xerr.ErrSysBusy, err, "分页查询订单最终结算记录失败")
	}
	return result, total, nil
}

// GetByOrderID 根据订单ID获取订单最终结算记录
func (s *orderFinalSettleService) GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderFinalSettle, error) {
	result, err := s.dao.GetByOrderID(ctx, orderID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return result, xerr.NewErr(xerr.ErrNoDocument, nil, "订单最终结算记录不存在")
		}
		return result, xerr.NewErr(xerr.ErrSysBusy, err, "查询订单最终结算记录失败")
	}
	return result, nil
}

// CreateFromOrder 创建最终结算记录
func (s *orderFinalSettleService) CreateFromOrder(ctx context.Context, orderID primitive.ObjectID) error {
	data, err := s.dao.GetByOrderID(ctx, orderID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if data.ID != primitive.NilObjectID {
		s.dao.DeleteByOrderID(ctx, orderID)
		zap.S().Infof("删除订单最终结算记录: %v", orderID)
	}

	// 1. 获取订单信息
	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return xerr.NewErr(xerr.ErrSysBusy, err, "获取订单信息失败")
	}

	filterBuyPrice := bson.M{
		"order_list.order_id": order.ID,
	}
	buyPrices, err := s.productBuyS.List(ctx, filterBuyPrice)
	if err != nil {
		return err
	}

	var totalQualityRefundAmount int
	var totalDebtAmount int
	var totalRefundAmount int
	var totalBuyAmount int

	var totalBuyPriceProfit int
	var totalFinalProfit int

	// 获取品控金额（从debt表）
	debt, err := s.orderDebtS.GetByOrderID(ctx, orderID)
	if err != nil {
		return err
	}

	// 获取售后退款金额
	refunds, err := s.orderRefundS.ListByOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 2. 计算订单商品总金额
	var productList []model.ProductFinalSettle

	for _, product := range order.ProductList {
		buyPriceProfit, costAmount, err := s.calcBuyPriceProfit(ctx, buyPrices, product.ProductID, product.SkuIDCode, product.SortNum, product.SortWeight)
		if err != nil {
			return err
		}

		var qualityRefundAmount int
		var debtAmount int
		for _, debtProduct := range debt.SettleProductList {
			if debtProduct.ProductID != product.ProductID || debtProduct.SkuIDCode != product.SkuIDCode {
				continue
			}

			if debtProduct.SettleResultType == model.SettleResultTypeRefund {
				// 退款
				qualityRefundAmount = debtProduct.DiffProductAmount
			}
			if debtProduct.SettleResultType == model.SettleResultTypeDebt {
				// 补差
				debtAmount = debtProduct.DiffProductAmount
			}
		}

		// 售后
		var afterSaleAmount int
		for _, refund := range refunds {
			if refund.RefundType == model.RefundTypeAfterSale && refund.ProductID == product.ProductID && refund.SkuIDCode == product.SkuIDCode {
				afterSaleAmount += refund.AuditAmount
				break
			}
		}

		finalProfit := buyPriceProfit - afterSaleAmount

		productList = append(productList, model.ProductFinalSettle{
			ProductID:                  product.ProductID,
			ProductTitle:               product.ProductTitle,
			ProductCoverImg:            product.ProductCoverImg.Name,
			SkuIDCode:                  product.SkuIDCode,
			SkuName:                    product.SkuName,
			TotalProductAmount:         product.ProductAmount,
			TotalQualityRefundAmount:   qualityRefundAmount,
			TotalAfterSaleRefundAmount: afterSaleAmount,
			TotalDebtAmount:            debtAmount,
			TotalBuyPriceAmount:        costAmount,
			BuyPriceProfit:             buyPriceProfit,
			FinalProfit:                finalProfit,
		})

		totalQualityRefundAmount += qualityRefundAmount
		totalDebtAmount += debtAmount
		totalRefundAmount += afterSaleAmount
		totalBuyAmount += costAmount

		totalBuyPriceProfit += buyPriceProfit
		totalFinalProfit += finalProfit
	}

	// 计算总利润 = 订单商品总金额 - 售后金额 - 品控金额 - 采购金额

	milli := time.Now().UnixMilli()
	// 构建订单最终结算记录
	orderFinalSettle := &model.OrderFinalSettle{
		ID:                         primitive.NewObjectID(),
		OrderID:                    orderID,
		SupplierID:                 order.SupplierID,
		SupplierName:               order.SupplierName,
		TotalProductAmount:         order.ProductTotalAmount,
		TotalProductBuyPriceAmount: totalBuyAmount,
		TotalAdjustSettleAmount:    0, // 如果需要调价结算金额，可以从相关表获取
		TotalQualityRefundAmount:   totalQualityRefundAmount,
		TotalAfterSaleRefundAmount: totalRefundAmount,
		TotalDebtAmount:            totalDebtAmount,
		TotalBuyPriceProfit:        totalBuyPriceProfit,
		TotalFinalProfit:           totalFinalProfit,
		ProductList:                productList,
		OrderCreatedAt:             order.CreatedAt, // 设置订单创建时间
		CreatedAt:                  milli,
		UpdatedAt:                  milli,
	}

	// 创建最终结算记录
	err = s.dao.Create(ctx, *orderFinalSettle)
	if err != nil {
		return err
	}

	// 冻结金额 - 发送MNS消息冻结供应商利润金额
	if totalFinalProfit > 0 {
		mnsSendService.NewMNSClient().SendSupplierFreezeAmount(ctx, order.SupplierID, totalFinalProfit, 5)
	}

	return nil
}

func (s *orderFinalSettleService) calcBuyPriceProfit(ctx context.Context, buyPrices []model.ProductBuyPrice, productID primitive.ObjectID, skuIDCode string, sortNum, sortWeight int) (int, int, error) {

	//  订单利润
	var totalProfitAmount int
	var totalBuyPriceAmount int
	var f bool
	for _, buyPrice := range buyPrices {
		if buyPrice.ProductID == productID && buyPrice.SkuIDCode == skuIDCode {

			unitPrice := buyPrice.AverageUnitPrice
			purchaseUnitPrice := buyPrice.AverageBuyUnitPrice

			diffUnitPrice := unitPrice - purchaseUnitPrice

			var perProfitAmount int

			if buyPrice.IsCheckWeight {
				// 计重
				diffUnitPriceDecimal := decimal.NewFromInt(int64(diffUnitPrice))
				sortWeightDecimal := decimal.NewFromInt(int64(sortWeight)).Div(decimal.NewFromInt(1000))
				perProfitAmount = int(diffUnitPriceDecimal.Mul(sortWeightDecimal).Round(0).IntPart())

				purchaseUnitPriceDecimal := decimal.NewFromInt(int64(purchaseUnitPrice))
				totalBuyPriceAmount = int(purchaseUnitPriceDecimal.Mul(sortWeightDecimal).Round(0).IntPart())
			}

			if !buyPrice.IsCheckWeight {
				// 计件
				perProfitAmount = diffUnitPrice * sortNum

				totalBuyPriceAmount = purchaseUnitPrice * sortNum
			}

			totalProfitAmount = perProfitAmount
			f = true
			break
		}
	}

	if !f {
		return 0, 0, xerr.NewErr(xerr.ErrParamError, nil, "采购价不存在")
	}

	return totalProfitAmount, totalBuyPriceAmount, nil
}
