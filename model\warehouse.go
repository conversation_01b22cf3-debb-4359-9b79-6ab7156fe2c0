package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Warehouse 集中仓
type Warehouse struct {
	ID          primitive.ObjectID `bson:"_id" json:"id"`
	UserID      primitive.ObjectID `bson:"user_id" json:"user_id"`             // 用户ID
	Name        string             `bson:"name" json:"name"`                   // 中心仓名称
	ContactUser string             `json:"contact_user"   validate:"required"` // 联系人
	Location    Location           `bson:"location" json:"location"`           // 定位地址
	Address     string             `json:"address"  validate:"required"`       // 详细地址
	Note        string             `bson:"note" json:"note"`                   // 备注
	CreatedAt   int64              `bson:"created_at" json:"created_at"`
	UpdatedAt   int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt   int64              `bson:"deleted_at" json:"deleted_at"`
}
