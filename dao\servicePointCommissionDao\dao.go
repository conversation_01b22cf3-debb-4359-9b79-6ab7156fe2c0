package servicePointCommissionDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 服务点佣金
type DaoInt interface {
	Upsert(ctx context.Context, data model.ServicePointCommission) error
	Find(ctx context.Context, filter bson.M) ([]model.ServicePointCommission, error)
	Get(ctx context.Context, filter bson.M) (model.ServicePointCommission, error)
}

type servicePointCommissionDao struct {
	db *mongo.Collection
}

func (s servicePointCommissionDao) Upsert(ctx context.Context, data model.ServicePointCommission) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func (s servicePointCommissionDao) Get(ctx context.Context, filter bson.M) (model.ServicePointCommission, error) {
	var data model.ServicePointCommission
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ServicePointCommission{}, err
	}
	return data, nil
}

func (s servicePointCommissionDao) Find(ctx context.Context, filter bson.M) ([]model.ServicePointCommission, error) {
	var list []model.ServicePointCommission

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewServicePointCommissionDao(collect string) DaoInt {
	return servicePointCommissionDao{
		db: global.MDB.Collection(collect),
	}
}
