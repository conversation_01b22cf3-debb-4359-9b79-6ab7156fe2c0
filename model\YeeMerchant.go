package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BusinessRole string

const (
	BusinessRolePLATFORMMERCHANT BusinessRole = "PLATFORM_MERCHANT" // 平台商
	BusinessRoleSETTLEDMERCHANT  BusinessRole = "SETTLED_MERCHANT"  // 入驻商户
	BusinessRoleMicroMERCHANT    BusinessRole = "MICRO_MERCHANT"    // 个人
)

type SignType string

const (
	SignTypeINDIVIDUAL SignType = "INDIVIDUAL" // 个体工商户
	SignTypeENTERPRISE SignType = "ENTERPRISE" // 企业
)

type BankAccountType string

const (
	BankAccountTypeENTERPRISEACCOUNT BankAccountType = "ENTERPRISE_ACCOUNT" // 对公账户
	BankAccountTypeDEBITCARD         BankAccountType = "DEBIT_CARD"         // 借记卡
)

// YeeMerchant 易宝商户
type YeeMerchant struct {
	ID                           primitive.ObjectID      `json:"id" bson:"_id"`
	UserID                       primitive.ObjectID      `json:"user_id" bson:"user_id"`                                                   // 用户ID
	ObjectType                   ObjectType              `json:"object_type" bson:"object_type"`                                           // 对象类型
	ObjectID                     primitive.ObjectID      `json:"object_id" bson:"object_id"`                                               // 对象ID
	RequestNo                    string                  `json:"request_no" bson:"request_no"`                                             // 入网请求号
	BusinessRole                 BusinessRole            `json:"business_role" bson:"business_role"`                                       // 入网商户业务角色
	ParentMerchantNo             string                  `json:"parent_merchant_no" bson:"parent_merchant_no"`                             // 如果该入网商户是标准商户或者平台商，则需要上送已入网完成的SAAS服务商编号；如果该入网商户是入驻商户，则需要上送对应平台商的商户编号。
	MerchantNo                   string                  `json:"merchant_no" bson:"merchant_no"`                                           // 商户号
	ApplicationNo                string                  `json:"application_no" bson:"application_no"`                                     // 申请单编号
	ApplicationStatus            string                  `json:"application_status" bson:"application_status"`                             // 申请单状态
	AuditOpinion                 string                  `json:"audit_opinion" bson:"audit_opinion"`                                       // 申请单状态
	ProgressDescription          string                  `json:"progress_description" bson:"progress_description"`                         // 申请单状态
	AgreementSignUrl             string                  `json:"agreement_sign_url" bson:"agreement_sign_url"`                             // 申请单状态
	IntentionAuthUrl             string                  `json:"intention_auth_url" bson:"intention_auth_url"`                             // 申请单状态
	WechatAuthApplyID            string                  `json:"wechat_auth_apply_id" bson:"wechat_auth_apply_id"`                         // 申请单编号
	WechatAuthApplyState         string                  `json:"wechat_auth_apply_state" bson:"wechat_auth_apply_state"`                   // 申请单状态
	MerchantSubjectInfo          MerchantSubjectInfo     `json:"merchant_subject_info" bson:"merchant_subject_info"`                       // 主体信息
	MerchantCorporationInfo      MerchantCorporationInfo `json:"merchant_corporation_info" bson:"merchant_corporation_info"`               // 商户法人信息
	MerchantContactInfo          MerchantContactInfo     `json:"merchant_contact_info" bson:"merchant_contact_info"`                       // 商户联系人
	BusinessAddressInfo          BusinessAddressInfo     `json:"business_address_info" bson:"business_address_info"`                       // 经营地址信息
	SettlementAccountInfo        SettlementAccountInfo   `json:"settlement_account_info" bson:"settlement_account_info"`                   // 结算账户信息
	AccountYpAccountBookNo       string                  `json:"account_yp_account_book_no" bson:"account_yp_account_book_no"`             // 记账簿
	AccountMerchantAccountBookNo string                  `json:"account_merchant_account_book_no" bson:"account_merchant_account_book_no"` //
	AccountStatus                string                  `json:"account_status" bson:"account_status"`                                     //
	WithdrawCardList             []WithdrawCard          `json:"withdraw_card_list" bson:"withdraw_card_list"`
	CreatedAt                    int64                   `bson:"created_at" json:"created_at"`
	UpdatedAt                    int64                   `bson:"updated_at" json:"updated_at"`
}

type WithdrawCard struct {
	BankCardType string `json:"bank_card_type" bson:"bank_card_type"`
	AccountNo    string `json:"account_no" bson:"account_no"`
	BankCode     string `json:"bank_code" bson:"bank_code"`
	BindId       int    `json:"bind_id" bson:"bind_id"` // 绑卡id
}

// MerchantSubjectInfo 商户主体信息
type MerchantSubjectInfo struct {
	SignType              SignType `json:"sign_type" bson:"sign_type"`                               //  必填  商户签约类型
	LicenceNo             string   `json:"licence_no" bson:"licence_no"`                             //  必填  商户证件编号-统一社会信用代码证编号
	LicenceUrl            string   `json:"licence_url" bson:"licence_url"`                           //  必填  商户证件照片
	SignName              string   `json:"sign_name" bson:"sign_name"`                               //  必填  商户签约名称 与商户证件主体名称一致。
	ShortName             string   `json:"short_name" bson:"short_name"`                             //  必填  商户简称 将在收银台页面或者支付完成页向买家展示。
	OpenAccountLicenceUrl string   `json:"open_account_licence_url" bson:"open_account_licence_url"` //  必填  开户许可证照片 为增加商户入网审核通过率，请上传开户许可证，上传图片前需调用文件上传接口将文件上传至易宝服务器。如果无法提供开户许可证，还可提供基本存款账户信息、印鉴卡、结算账户开户回执单、银行电子回单、企业网银登录截图。当商户类型为个体工商户时，还可以上传结算银行卡照片。
	//HandLicenceUrl        string   `json:"hand_licence_url" bson:"hand_licence_url"`                 //  必填  手持营业执照在经营场所的照片
	LicenseImg FileInfo `json:"license_img" bson:"license_img"` // 必填 营业执照图
	//HandLicenceImg        FileInfo `json:"hand_licence_img" bson:"hand_licence_img"`                 // 必填 手持营业执照图
	EnterpriseOpenImg     FileInfo `json:"enterprise_open_img" bson:"enterprise_open_img"`           // 必填 企业开户信息照片
	IndividualBankCardImg FileInfo `json:"individual_bank_card_img" bson:"individual_bank_card_img"` // 必填 个体工商户银行卡照片
	LicenceValidDateBegin string   `json:"licence_valid_date_begin" bson:"licence_valid_date_begin"`
	LicenceValidDateEnd   string   `json:"licence_valid_date_end" bson:"licence_valid_date_end"`
}

// MerchantCorporationInfo 商户法人信息
type MerchantCorporationInfo struct {
	LegalLicenceType           string   `json:"legal_licence_type" bson:"legal_licence_type"`           //  必填  法人证件类型 ID_CARD:身份证
	LegalName                  string   `json:"legal_name" bson:"legal_name"`                           //  必填  法人姓名
	LegalLicenceNo             string   `json:"legal_licence_no" bson:"legal_licence_no"`               //  必填  法人证件号码
	LegalLicenceFrontUrl       string   `json:"legal_licence_front_url" bson:"legal_licence_front_url"` //  必填  法人证件人像面照片
	LegalLicenceBackUrl        string   `json:"legal_licence_back_url" bson:"legal_licence_back_url"`   //  必填  法人证件非人像面照片
	LegalLicenceFrontImg       FileInfo `json:"legal_licence_front_img" bson:"legal_licence_front_img"` // 必填 法人证件人像面照片
	LegalLicenceBackImg        FileInfo `json:"legal_licence_back_img" bson:"legal_licence_back_img"`   // 必填 法人证件非人像面照片
	LegalLicenceValidDateBegin string   `json:"legal_licence_valid_date_begin" bson:"legal_licence_valid_date_begin"`
	LegalLicenceValidDateEnd   string   `json:"legal_licence_valid_date_end" bson:"legal_licence_valid_date_end"`
}

// MerchantContactInfo 商户联系人
type MerchantContactInfo struct {
	ContactName   string `json:"contact_name" bson:"contact_name"`     //  必填  商户联系人姓名
	ContactMobile string `json:"contact_mobile" bson:"contact_mobile"` //  必填  商户联系人手机号
	ContactEmail  string `json:"contact_email" bson:"contact_email"`   //  必填  商户联系人邮箱
}

// BusinessAddressInfo 经营地址信息
type BusinessAddressInfo struct {
	Province string `json:"province" bson:"province"` //  必填  省编号
	City     string `json:"city" bson:"city"`         //  必填  市编号
	District string `json:"district" bson:"district"` //  必填  区编号
	Address  string `json:"address" bson:"address"`   //  必填  详细地址,不需要再次上送省市区
}

// SettlementAccountInfo 结算账户信息
type SettlementAccountInfo struct {
	BankAccountType BankAccountType `json:"bank_account_type" bson:"bank_account_type"` //  必填  银行账户类型
	BankCardNo      string          `json:"bank_card_no" bson:"bank_card_no"`           //  必填  银行账户号码
	BankCode        string          `json:"bank_code" bson:"bank_code"`                 //  必填  行账户开户总行编码
}
