package warehouseLoadFeeService

import (
	"base/core/xerr"
	"base/dao"
	warehouseLoadFeeDao "base/dao/WarehouseLoadFeeDao"
	"base/global"
	"base/model"
	"base/types"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// ServiceInterface 仓配费
type ServiceInterface interface {
	Upsert(ctx context.Context, warehouseID primitive.ObjectID, feePerKG int) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.WarehouseLoadFee, error)
	GetByWarehouseID(ctx context.Context, warehouseID primitive.ObjectID) (model.WarehouseLoadFee, error)
	List(ctx context.Context) ([]model.WarehouseLoadFee, error)
	CalcWarehouseLoadFee(ctx context.Context, loadFee model.WarehouseLoadFee, list []types.PerProduct) (int, map[primitive.ObjectID]int, error)
}

type warehouseLoadFeeService struct {
	rdb        *redis.Client
	loadFeeDao warehouseLoadFeeDao.DaoInt
}

func NewWarehouseLoadFeeService() ServiceInterface {
	return warehouseLoadFeeService{
		rdb:        global.RDBDefault,
		loadFeeDao: dao.WarehouseLoadDao,
	}
}

// Upsert 创建和更新
func (s warehouseLoadFeeService) Upsert(ctx context.Context, warehouseID primitive.ObjectID, feePerKG int) error {
	if feePerKG < 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "仓配费不能小于0每千克")
	}

	loadFee, err := s.GetByWarehouseID(ctx, warehouseID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	now := time.Now().UnixMilli()
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	 新建
		data := model.WarehouseLoadFee{
			ID:          primitive.NewObjectID(),
			WarehouseID: warehouseID,
			FeePerKG:    feePerKG,
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		err = s.loadFeeDao.Create(ctx, data)
		if err != nil {
			return err
		}
		del(s.rdb, warehouseID)

		return nil
	}

	// 更新
	update := bson.M{
		"fee_per_kg": feePerKG,
		"updated_at": now,
	}
	err = s.loadFeeDao.UpdateOne(bson.M{"_id": loadFee.ID, "warehouse_id": warehouseID}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}
	del(s.rdb, warehouseID)

	return nil
}

func (s warehouseLoadFeeService) GetByID(ctx context.Context, id primitive.ObjectID) (model.WarehouseLoadFee, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.loadFeeDao.Get(ctx, filter)
	if err != nil {
		return model.WarehouseLoadFee{}, err
	}
	return fee, nil
}

func (s warehouseLoadFeeService) GetByWarehouseID(ctx context.Context, warehouseID primitive.ObjectID) (model.WarehouseLoadFee, error) {
	m := get(s.rdb, warehouseID)
	if m.ID == primitive.NilObjectID {
		filter := bson.M{
			"warehouse_id": warehouseID,
		}
		data, err := s.loadFeeDao.Get(ctx, filter)
		if err != nil {
			return model.WarehouseLoadFee{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s warehouseLoadFeeService) List(ctx context.Context) ([]model.WarehouseLoadFee, error) {
	filter := bson.M{}
	list, err := s.loadFeeDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s warehouseLoadFeeService) CalcWarehouseLoadFee(ctx context.Context, loadFee model.WarehouseLoadFee, list []types.PerProduct) (int, map[primitive.ObjectID]int, error) {
	//var total int                         // 总计
	//m := make(map[primitive.ObjectID]int) // 每个商品
	//for _, p := range list {
	//	//weight := p.RoughWeight * p.Num
	//	perFee := BackWarehouseLoadFee(loadFee.FeePerKG, weight)
	//	id, err := util.ConvertToObjectWithCtx(ctx, p.ProductID)
	//	if err != nil {
	//		return 0, nil, err
	//	}
	//	m[id] = perFee
	//	total += perFee
	//}
	//
	//return total, m, nil
	return 0, nil, nil
}

func BackWarehouseLoadFee(unitFee, weight int) int {
	if unitFee == 0 {
		return 0
	}
	kg := decimal.NewFromInt(int64(weight)).Div(decimal.NewFromFloat(1000))
	amount := kg.Mul(decimal.NewFromInt(int64(unitFee))).Round(0)
	intPart := int(amount.IntPart())
	return intPart
}
