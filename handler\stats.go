package handler

import (
	"base/api/stats"
	"github.com/gin-gonic/gin"
)

func statsRouter(r *gin.RouterGroup) {
	r = r.Group("/stats")

	r.POST("/gmv", stats.ListGMV)
	r.POST("/today", stats.GetTodayData)
	//r.POST("/warehouse/ship/total", stats.GetWarehouseShipData)
	r.POST("/warehouse/ship/total/temp", stats.GetWarehouseShipDataTemp)
	//r.POST("/warehouse/ship/list", stats.GetWarehouseShipDataList)
	//r.POST("/warehouse/single/product", stats.ListSingle)

	r.POST("/warehouse/single/product/temp", stats.ListSingleTemp)

	r.POST("/warehouse/single/product/local", stats.ListSingleLocal)
	r.POST("/supplier/all/sale/monthly", stats.AllSupplierSaleMonthly)
	r.POST("/online", stats.OnlineStats)
	r.POST("/online/num", stats.GetOnlineNum)
	r.POST("/sale/supplier", stats.SaleSupplier)

	r.POST("/order", stats.Order)
}
