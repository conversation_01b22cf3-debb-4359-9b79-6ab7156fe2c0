package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderWarehouseService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListHasQuality 已品控
func ListHasQuality(ctx *gin.Context) {
	var req = struct {
		WarehouseID string `json:"warehouse_id" validate:"len=24"`
		//ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp int64 `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//servicePointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListQuality ServicePointID")
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "ListQuality WarehouseID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var servicePointID primitive.ObjectID

	infos, err := orderWarehouseService.NewOrderWarehouseService().ListQuality(ctx, warehouseID, servicePointID, ts, true)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 根据供应商分类
	mSupplier := make(map[primitive.ObjectID][]model.OrderStockUp)
	for _, up := range infos {
		mSupplier[up.SupplierID] = append(mSupplier[up.SupplierID], up)
	}
	var res []qualityBySupplier

	for id, ups := range mSupplier {
		var num int // 商品总数
		for _, up := range ups {
			num += up.StockUpHasNum
		}

		res = append(res, qualityBySupplier{
			SupplierID:     id,
			SupplierName:   ups[0].SupplierName,
			ProductCount:   num,
			ProductTypeNum: len(ups),
			List:           ups,
		})
	}

	xhttp.RespSuccess(ctx, res)
}

type qualityBySupplier struct {
	SupplierID     primitive.ObjectID   `json:"supplier_id"`
	SupplierName   string               `json:"supplier_name"`
	ProductTypeNum int                  `json:"product_type_num"` // 类别
	ProductCount   int                  `json:"product_count"`    // 总数
	List           []model.OrderStockUp `json:"list"`
}
