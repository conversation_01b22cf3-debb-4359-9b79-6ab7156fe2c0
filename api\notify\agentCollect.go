package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/service/messageService"
	"base/service/orderDebtService"
	"base/service/orderService"
	"encoding/json"
	"errors"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// PayNotifyAgentCollect 托管代收
func PayNotifyAgentCollect(ctx *gin.Context) {
	ctx.Set("rid", "agent collect notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := orderService.NewOrderService().NotifyPayStatus(ctx, res)
		if errors.Is(err, mongo.ErrNoDocuments) {
			l.<PERSON>("bizOrderNo：%s支付成功,但是无相关订单", res.BizOrderNo)
			messageService.NewMessageService().SendWarning("13518757974", "agentCollect:"+res.BizOrderNo, "支付成功，无订单")
			xhttp.NotifySuccess(ctx)
			return
		}
		if err != nil {
			l.Errorf("托管代收-回调更新失败%v", err)
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("托管代收-回调未对接：", string(bytes))
	}
}

// PayNotifyAgentCollectDebt 托管代收-补差
func PayNotifyAgentCollectDebt(ctx *gin.Context) {
	ctx.Set("rid", "agent collect debt notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		err := orderDebtService.NewOrderDebtService().NotifyPayStatus(ctx, res)
		if errors.Is(err, mongo.ErrNoDocuments) {
			l.Errorf("bizOrderNo：%s支付成功,但是无相关订单", res.BizOrderNo)
			messageService.NewMessageService().SendWarning("13518757974", "debt agentCollect:"+res.BizOrderNo, "支付成功，无订单")
			xhttp.NotifySuccess(ctx)
			return
		}
		if err != nil {
			l.Errorf("托管代收-补差-回调更新失败")
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("托管代收-补差-回调未对接：", string(bytes))
	}
}

//
//// PayNotifyAgentCollectCoupon 托管代收-优惠券
//func PayNotifyAgentCollectCoupon(ctx *gin.Context) {
//	ctx.Set("rid", "agent collect notify:"+ctx.GetString("rid"))
//	l := global.PayLogger.Sugar()
//	notify := deal(ctx, l)
//	switch notify.NotifyType {
//	case "allinpay.yunst.orderService.pay":
//		// 托管代收---订单成功
//		var res allinpay.NotifyPay
//		parseRes(notify.BizContent, &res)
//		err := orderService.NewOrderService().NotifyPayStatusCoupon(ctx, res)
//		if err != nil {
//			l.Errorf("托管代收-回调更新失败")
//			xhttp.NotifyFail(ctx)
//			return
//		}
//		xhttp.NotifySuccess(ctx)
//		return
//	default:
//		bytes, _ := json.Marshal(notify)
//		zap.S().Error("托管代收-回调未对接：", string(bytes))
//	}
//}
