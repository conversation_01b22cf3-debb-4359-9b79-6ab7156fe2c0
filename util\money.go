package util

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

func DealMoneyToYuan(num int) float64 {
	div := decimal.NewFromInt(int64(num)).Div(decimal.NewFromInt(100))
	f, exact := div.Float64()
	if exact {

	}
	return f
}

func DealMoneyToYuanStr(num int) string {
	div := decimal.NewFromInt(int64(num)).Div(decimal.NewFromInt(100))
	s := div.String()
	return s
}

func DealMoneyToFenInt(num string) int {
	fromString, err := decimal.NewFromString(num)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("订单金额转换严重错误：%s", err.Error())
		return 0
	}

	mul := fromString.Mul(decimal.NewFromInt(100))

	part := int(mul.IntPart())

	return part
}

func DealMoneyFloatToFenInt(num float64) int {
	float := decimal.NewFromFloat(num)

	mul := float.Mul(decimal.NewFromInt(100))

	part := int(mul.IntPart())

	return part
}
