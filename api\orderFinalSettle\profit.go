package orderFinalSettle

import (
	"base/core/xhttp"
	"base/service/profitService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// TransferProfit 利润划转
func TransferProfit(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id"`
		Amount         int    `json:"amount"`
		Remark         string `json:"remark"`
		MonthTimestamp int64  `json:"month_timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, _, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = profitService.NewProfitService().SettleProfit(ctx, supplierID, req.Amount, req.Remark, begin)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ListSettlementBySupplier 结算利润列表
func ListSettlementBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id"`
		MonthTimestamp int64  `json:"month_timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, _, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := profitService.NewProfitService().ListSettlementBySupplier(ctx, supplierID, begin)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}
