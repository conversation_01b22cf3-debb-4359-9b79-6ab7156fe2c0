package adminServicePoint

import (
	"base/core/xhttp"
	"base/service/servicePointService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
		Page       int64 `json:"page"`
		Limit      int64 `json:"limit"`
		OpenStatus int   `json:"open_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
	}

	if req.OpenStatus == 1 {
		filter["is_open"] = false
	}

	if req.OpenStatus == 2 {
		filter["is_open"] = true
	}

	list, count, err := servicePointService.NewServicePointService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}
