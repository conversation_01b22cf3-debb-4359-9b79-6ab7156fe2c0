package brandService

import (
	"base/dao"
	"base/dao/brandDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, name, desc string, avatar model.FileInfo) error
	Update(ctx context.Context, id primitive.ObjectID, name, desc string, avatar model.FileInfo) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Brand, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Brand, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Brand, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type brandService struct {
	brandDao brandDao.DaoInt
}

func NewBrandService() ServiceInterface {
	return brandService{
		brandDao: dao.Brand<PERSON>ao,
	}
}

func (s brandService) Get(ctx context.Context, id primitive.ObjectID) (model.Brand, error) {
	address, err := s.brandDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.Brand{}, err
	}
	return address, nil
}

func (s brandService) Create(ctx context.Context, name, desc string, avatar model.FileInfo) error {
	now := time.Now().UnixMilli()
	data := model.Brand{
		ID:        primitive.NewObjectID(),
		Name:      name,
		Desc:      desc,
		AvatarImg: avatar,
		CreatedAt: now,
	}

	err := s.brandDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s brandService) Update(ctx context.Context, id primitive.ObjectID, name, desc string, avatar model.FileInfo) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"name":       name,
		"desc":       desc,
		"avatar_img": avatar,
		"updated_at": milli,
	}

	err := s.brandDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func (s brandService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Brand, int64, error) {
	list, i, err := s.brandDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s brandService) List(ctx context.Context, filter bson.M) ([]model.Brand, error) {
	list, err := s.brandDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s brandService) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.brandDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return i, nil
}

func (s brandService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.brandDao.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}
