package util

import (
	"errors"
	"os"
)

// IsExistDir 目录是否存在
func IsExistDir(dirPath string) (bool, error) {
	f, err := os.Stat(dirPath)
	if err == nil {
		if f.IsDir() {
			return true, nil
		}
		return false, errors.New(dirPath + " 不是目录")
	}

	if os.IsNotExist(err) {
		return false, nil
	}

	return false, err
}

// FileExist 文件是否存在
func FileExist(filePath string) (bool, error) {
	f, err := os.Stat(filePath)
	if err == nil {
		if f.IsDir() {
			return false, errors.New(filePath + " 不是文件")
		}
		return true, nil
	}

	if os.IsNotExist(err) {
		return false, nil
	}

	return false, err
}

// 创建文件夹
func MkDir(dirPath string) error {
	exist, err := IsExistDir(dirPath)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	err = os.MkdirAll(dirPath, os.ModePerm)
	if err != nil {
		return errors.New("目录创建失败：" + err.Error())
	}

	return nil
}

// 创建文件
func MkFile(filePath string) error {
	exist, err := FileExist(filePath)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	file, err := os.OpenFile(filePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return errors.New("文件创建失败：" + err.Error())
	}
	_ = file.Close()

	return nil
}
