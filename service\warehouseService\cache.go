package warehouseService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

// 缓存
var warehouseCache = "warehouse:"

func get(r *redis.Client, id primitive.ObjectID) model.Warehouse {
	key := warehouseCache + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.Warehouse{}
		}
		var i model.Warehouse
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.Warehouse{}
		}
		return i
	}
	return model.Warehouse{}
}

func set(r *redis.Client, info model.Warehouse) {
	key := warehouseCache + info.ID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().<PERSON>rror("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, time.Hour*72)
}

func del(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), warehouseCache+id.Hex())
}
