package warehouse

import (
	"base/core/xhttp"
	"base/util"
	"github.com/gin-gonic/gin"
)

// ListQuality 品控列表
func ListQuality(ctx *gin.Context) {
	var req = struct {
		WarehouseID    string `json:"warehouse_id" validate:"len=24"`
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		TimeStart      int64  `json:"time_start" validate:"required"`
		TimeEnd        int64  `json:"time_end" validate:"required"`
		Page           int64  `json:"page" validate:"min=1"`
		Limit          int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "warehouse_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = warehouseID
	servicePointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = servicePointID

	xhttp.RespSuccessList(ctx, nil, 0)
}
