package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// Profit 利润
type Profit struct {
	ID                     primitive.ObjectID `json:"id" bson:"_id"`
	OrderID                primitive.ObjectID `json:"order_id" bson:"order_id"`
	BuyerID                primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	SupplierID             primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`
	ProductTotalAmount     int                `json:"product_total_amount" bson:"product_total_amount"`   // 商品销售金额
	ProductAfterSaleAmount int                `json:"product_after_sales" bson:"product_after_sales"`     // 商品售后金额
	ProductDebtAmount      int                `json:"product_debt_amount" bson:"product_debt_amount"`     // 商品补差金额
	ProductProfitAmount    int                `json:"product_profit_amount" bson:"product_profit_amount"` // 商品利润
	OrderCreatedAt         int64              `json:"order_created_at" bson:"order_created_at"`           // 订单创建时间
	StockUpNo              int                `json:"stock_up_no" bson:"stock_up_no"`                     // 备货 批次
	StockUpDayTime         int64              `json:"stock_up_day_time" bson:"stock_up_day_time"`         // 备货 日期
	CreatedAt              int64              `json:"created_at" bson:"created_at"`                       // 创建时间
}

// ProfitSettlement 利润结算
type ProfitSettlement struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	SupplierID     primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`         // 供应商ID
	Amount         int                `json:"amount" bson:"amount"`                   // 结算金额
	Remark         string             `json:"remark" bson:"remark"`                   // 备注
	MonthTimestamp int64              `json:"month_timestamp" bson:"month_timestamp"` // 结算月份
	CreatedAt      int64              `json:"created_at" bson:"created_at"`           // 创建时间
}
