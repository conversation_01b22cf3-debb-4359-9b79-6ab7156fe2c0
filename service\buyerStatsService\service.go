package buyerStatsService

import (
	"base/global"
	"base/model"
	"base/service/buyerService"
	"base/service/cartService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ServiceInterface interface {
	Get(ctx context.Context, id primitive.ObjectID) (model.BuyerStats, error)
	Remove(ctx context.Context, buyerID primitive.ObjectID) error
	Fresh(ctx context.Context, buyerID primitive.ObjectID) error
}

type buyerStatsService struct {
	mdb     *mongo.Database
	rdb     *redis.Client
	buyerS  buyerService.ServiceInterface
	orderS  orderService.ServiceInterface
	refundS orderRefundService.ServiceInterface
}

func NewBuyerStatsService() ServiceInterface {
	return buyerStatsService{
		mdb:     global.MDB,
		rdb:     global.RDBDefault,
		buyerS:  buyerService.NewBuyerService(),
		orderS:  orderService.NewOrderService(),
		refundS: orderRefundService.NewOrderRefundService(),
	}
}

func (s buyerStatsService) Get(ctx context.Context, id primitive.ObjectID) (model.BuyerStats, error) {
	m := get(s.rdb, id)
	if m.BuyerID == primitive.NilObjectID {

		var data model.BuyerStats
		data.BuyerID = id
		// 购买量
		countProduct, orderAmount, err := s.orderS.CountProduct(ctx, id)
		if err != nil {
			return model.BuyerStats{}, err
		}
		data.OrderProductNum = countProduct
		data.OrderAmount = orderAmount

		// 售后次数
		countByBuyer, err := s.refundS.CountByBuyer(ctx, id)
		if err != nil {
			return model.BuyerStats{}, err
		}
		data.AfterSaleOrderNum = countByBuyer

		// 售后率

		totalProduct := decimal.NewFromInt(countProduct)
		totalRefundProduct := decimal.NewFromInt(countByBuyer)
		var rate float64
		if countProduct != 0 && countByBuyer != 0 {
			f, exact := totalRefundProduct.Div(totalProduct).Mul(decimal.NewFromInt(100)).Round(2).Float64()
			_ = exact
			rate = f
		}

		data.AfterSaleRate = rate

		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s buyerStatsService) Remove(ctx context.Context, buyerID primitive.ObjectID) error {
	del(s.rdb, buyerID)

	return nil
}

func (s buyerStatsService) Fresh(ctx context.Context, buyerID primitive.ObjectID) error {
	var data model.BuyerStats
	data.BuyerID = buyerID
	// 购买量
	countProduct, orderAmount, err := s.orderS.CountProduct(ctx, buyerID)
	if err != nil {
		return err
	}
	data.OrderProductNum = countProduct
	data.OrderAmount = orderAmount

	// 售后次数
	countByBuyer, err := s.refundS.CountByBuyer(ctx, buyerID)
	if err != nil {
		return err
	}
	data.AfterSaleOrderNum = countByBuyer

	// 售后率
	totalProduct := decimal.NewFromInt(countProduct)
	totalRefundProduct := decimal.NewFromInt(countByBuyer)
	var rate float64
	if countProduct != 0 && countByBuyer != 0 {
		f, exact := totalRefundProduct.Div(totalProduct).Mul(decimal.NewFromInt(100)).Round(2).Float64()
		_ = exact
		rate = f
	}
	data.AfterSaleRate = rate

	var afterSaleAuditAmount int

	refundFilter := bson.M{
		"buyer_id":     buyerID,
		"refund_type":  model.RefundTypeAfterSale,
		"is_withdraw":  false,
		"audit_status": model.AuditStatusTypePass,
	}
	orderRefunds, err := s.refundS.List(ctx, refundFilter)
	if err != nil {
		return err
	}

	for _, refund := range orderRefunds {
		afterSaleAuditAmount += refund.AuditAmount
	}

	data.AfterSaleAuditAmount = afterSaleAuditAmount

	//LatestOrderTime   int64              `json:"latest_order_time"`
	//LatestVisitTimes  int                `json:"latest_visit_times"`
	//CartProductNum    int                `json:"cart_product_num"`

	cartNum, err := cartService.NewCartService().GetTotalNumByBuyer(ctx, buyerID)
	data.CartProductNum = cartNum

	latestOrderTime, err := s.orderS.GetLatestOrderTime(ctx, buyerID)
	if err != nil {
		return err
	}
	data.LatestOrderTime = latestOrderTime

	set(s.rdb, data)

	return nil
}
