package userAddr

import (
	"base/core/xhttp"
	"base/service/userAddrService"
	"base/types"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	req := &types.UserAddrUpdate{}
	err := xhttp.Parse(ctx, req)
	if err != nil {
		return
	}

	err = userAddrService.NewUserAddrService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

//
//func UpdateAll(ctx *gin.Context) {
//	var req = struct {
//		DeliverFreeBegin int64 `json:"deliver_free_begin"` // 配送限免期-开始
//		DeliverFreeEnd   int64 `json:"deliver_free_end"`   // 配送限免期-结束
//	}{}
//	err := xhttp.Parse(ctx, req)
//	if err != nil {
//		return
//	}
//
//	err = userAddrService.NewUserAddrService().UpdateDeliverFree(ctx, req.DeliverFreeBegin, req.DeliverFreeEnd)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, nil)
//}
