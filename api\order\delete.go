package order

import (
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"time"
)

func Delete(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()
	var req = struct {
		OrderID string `json:"order_id"` // 支付单订单
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	order, err := orderService.NewOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if order.OrderStatus == model.OrderStatusTypeClosed || order.OrderStatus == model.OrderStatusTypeCancel {
		update := bson.M{
			"deleted_at": time.Now().UnixMilli(),
		}
		err = orderService.NewOrderService().UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{"$set": update})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccess(ctx, nil)
}
