package station

import (
	"base/core/xhttp"
	"base/service/stationService"
	"base/service/userService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req types.StationCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	user, err := userService.NewUserService().GetOrCreateUser(ctx, req.ContactMobile, "", "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = stationService.NewStationService().Create(ctx, user.ID, servicePointID, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}
