package adminOrder

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func StatsByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID        string `json:"buyer_id"`
		TimestampStart int64  `json:"timestamp_start"`
		TimestampEnd   int64  `json:"timestamp_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{}

	filter["buyer_id"] = id
	filter["order_status"] = model.OrderStatusTypeFinish

	filter["created_at"] = bson.M{
		"$gte": req.TimestampStart,
		"$lte": req.TimestampEnd,
	}

	findOpt := options.Find()

	findOpt.SetProjection(bson.M{
		"product_list.num":            1,
		"product_list.product_amount": 1,
		"product_list.sort_weight":    1,
	})
	now := time.Now()
	orders, err := orderService.NewOrderService().ListWithOption(ctx, filter, findOpt)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	milliseconds := time.Since(now).Milliseconds()
	zap.S().Errorf("耗时：%d", milliseconds)
	_ = orders

	var r BuyerOrderStats

	for _, order := range orders {
		_ = order
		for _, product := range order.ProductList {
			r.ProductNum += product.Num
			r.ProductWeight += product.SortWeight
			r.ProductAmount += product.ProductAmount
		}
	}

	xhttp.RespSuccess(ctx, r)
}

type BuyerOrderStats struct {
	//StatsPeriodType     string             `json:"stats_period_type"`
	//BuyerID             primitive.ObjectID `json:"buyer_id"`
	ProductNum    int `json:"product_num"`
	ProductWeight int `json:"product_weight"`
	//OrderTimestampStart int64              `json:"order_timestamp_start"`
	//OrderTimestampEnd   int64              `json:"order_timestamp_end"`
	ProductAmount int `json:"product_amount"`
}
