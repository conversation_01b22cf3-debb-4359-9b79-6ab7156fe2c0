package sys

import (
	"base/dao"
	"base/global"
	"base/model"
	"base/service/messageService"
	"base/service/orderAgentPayService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

func testYee(ctx *gin.Context) {

	//id, _ := util.ConvertToObjectWithCtx(ctx, "674bd76e21e7410d23dcd015")
	//
	//queueProduceService.NewQueueProduceService().SendNormalOrderDivide(ctx, id, 0)

	//queueProduceService.NewQueueProduceService().SendDebtOrderDivide(ctx, id)
	//
	//queueProduceService.NewQueueProduceService().SendDivideEndCheck(ctx, id)

	//orderAgentPayService.NewOrderAgentPayService().YeeCheckDivideFlat(ctx, id)
	//yeeMerchantService.NewYeeMerchantService().InitWithDrawCardBind(ctx)

	//id, _ := util.ConvertToObjectWithCtx(ctx, "67492fbc2a244f6f0bc68a7d")
	//orderAgentPayService.NewOrderAgentPayService().ToAgentPayNormalOrder(ctx, id)

	//orderService.NewOrderService().YeeTradeOrderQuery(ctx, id)
	//DoRefundYee()
	//balanceNotify()
	//checkDivideNormalOrder(ctx)

	//migrateYHT(ctx)
	//testBalanceOrder(ctx)
	//testDivideDeliver(ctx)
	//checkPaySuborder(ctx)

	//now := time.Now()
	//
	//addDate := now.AddDate(0, 0, 15)
	//expiredTime := addDate.Format("2006-01-02 15:04:05") // yyyy-MM-dd HH:mm:ss
	//zap.S().Infof("addDate: %s", expiredTime)
}

func checkPaySuborder(ctx *gin.Context) {
	list, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1732695037000,
		},
		"pay_status": model.PayStatusTypePaid,
		"pay_method": "yee_wechat",
	})
	if err != nil {
		return
	}
	_ = list

	for i, parent := range list {

		if len(parent.YeeWechatResult.NotifySubOrderList) == 0 {
			zap.S().Infof("notify:%s", parent.ID.Hex())
			zap.S().Infof("index:%d", i)
			updateNotifySubOrder(ctx, parent.ID)
		}
		if parent.YeeWechatResult.NotifyStatus != "SUCCESS" {
			zap.S().Errorf("err:%s", parent.ID.Hex())
		}
	}
}

func updateNotifySubOrder(ctx context.Context, id primitive.ObjectID) {
	d := dao.ParentOrderDao
	_ = d

	combine, err := orderService.NewOrderService().YeeTradeOrderQueryForCombine(ctx, id)
	if err != nil {
		return
	}
	_ = combine

}

func testBalanceOrder(ctx *gin.Context) {
	list, err := orderService.NewOrderService().List(ctx, bson.M{
		"pay_method": model.PayMethodTypeBalance,
		//"order_status": model.IntegralOrderStatusFinish,
		"created_at": bson.M{
			"$gte": 1728290887000,
			"$lte": 1732697287000,
		},
	})
	_ = err

	m := make(map[string]int)
	for _, order := range list {
		m[order.SupplierName] += order.PaidAmount
	}

	zap.S().Infof("供应商：%v", m)

}

//func migrateYHT(ctx *gin.Context) {
//	filter := bson.M{
//		"user_type": "YHT",
//	}
//	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
//	if err != nil {
//		return
//	}
//	zap.S().Infof("buyers num: %v", len(buyers))
//
//	var buyerIDs []primitive.ObjectID
//	for _, buyer := range buyers {
//		buyerIDs = append(buyerIDs, buyer.ID)
//	}
//
//	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
//		"buyer_id": bson.M{
//			"$in": buyerIDs,
//		},
//	})
//	if err != nil {
//		return
//	}
//	zap.S().Infof("addresses num: %v", len(addresses))
//
//	for _, address := range addresses {
//		if address.UserType != "YHT" {
//			zap.S().Infof("err YHT:%s", address.ID.Hex())
//			continue
//		}
//
//		var buyerName string
//		var createdAt int64
//
//		for _, buyer := range buyers {
//			if address.BuyerID == buyer.ID {
//				buyerName = buyer.BuyerName
//				createdAt = buyer.CreatedAt
//			}
//		}
//
//		r := req{
//			BuyerName:       buyerName,
//			Address:         address.Address,
//			UserName:        address.Contact.Name,
//			Mobile:          address.Contact.Mobile,
//			Longitude:       address.Location.Longitude,
//			Latitude:        address.Location.Latitude,
//			LocationAddress: address.Location.Address,
//			CreatedAt:       createdAt,
//			AddrCreatedAt:   address.CreatedAt,
//		}
//
//		doReq(r)
//
//	}
//
//}
//
//type req struct {
//	BuyerName       string  `json:"buyer_name"`
//	Address         string  `json:"address"`          // 详细地址
//	UserName        string  `json:"user_name"`        // 姓名
//	Mobile          string  `json:"mobile"`           // 手机
//	Longitude       float64 `json:"longitude"`        // 经度
//	Latitude        float64 `json:"latitude"`         // 维度
//	LocationAddress string  `json:"location_address"` // 详细地址
//	CreatedAt       int64   `json:"created_at"`
//	AddrCreatedAt   int64   `json:"addr_created_at"`
//}

//func doReq(r req) {
//
//	url := "http://************:15001/api/sys/test/migrate"
//
//	marshal, err := json.Marshal(r)
//	if err != nil {
//		return
//	}
//
//	var result any
//
//	response, err := util.NewResty().Post(url, marshal, &result)
//	if err != nil {
//		return
//	}
//	_ = response
//
//}

func checkDivideNormalOrder(ctx *gin.Context) {
	var timeBegin int64 = 1732694515000 // 2024-11-27 16:01:55
	var timeEnd int64 = 1732982400000   // 2024-12-01 00:00:00
	filter := bson.M{
		"created_at": bson.M{
			"$gte": timeBegin, // 10-08
			"$lte": timeEnd,   // 2024-11-27 16:10:00
		},
		"order_status":     model.OrderStatusTypeFinish,
		"order_refund_all": false,
	}

	list, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, order := range list {
		m[order.ParentOrderID] = append(m[order.ParentOrderID], order)
	}

	var ids []primitive.ObjectID
	for _, l := range m {
		for i, order := range l {
			_ = i
			//if i > 0 {
			//	time.Sleep(time.Second * 10)
			//}
			byOrder, err := orderAgentPayService.NewOrderAgentPayService().GetOrderByOrder(ctx, order.ID)
			if errors.Is(err, mongo.ErrNoDocuments) {
				ids = append(ids, order.ID)
			}
			_ = byOrder
		}
	}

	for _, id := range ids {
		_ = id
		//queueProduceService.NewQueueProduceService().SendNormalOrderDivide(ctx, id, 0)
	}

}

func balanceNotify() {
	l := []string{"18487070785",
		"13208885522",
		"13888691573",
		"13888265664",
		"15775206337",
		"17787162670",
		"15608717427",
		"13312762915",
		"18213068970",
		"17601486563",
		"15388866206",
		"13987112327",
		"15969555101",
		"15240845245",
		"13888520650",
		"15208750614",
		"15328159586",
		"13638805063",
		"15812030886",
		"15331451108",
		"15200257789",
		"13404970707",
		"13628744705",
		"18087835829",
		"15758001072",
		"18087817582",
		"15228581319"}

	for _, s := range l {
		time.Sleep(time.Second)
		messageService.NewMessageService().SendBalanceNotify(s)
	}

}

func DoRefundYee() {

	refundAmount := 200

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")

	reqRefundRequestId := util.NewUUIDNum()
	yopRequest.AddParam("refundRequestId", reqRefundRequestId) // 商户退款请求号

	parentMerchantNo := "10090765586"
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	merchantNo := "10090789568"
	oriOrderID := "17c95795e82846958aa6917572d8915c"

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmount := refundAmount
	paidAmountStr := util.DealMoneyToYuanStr(paidAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	yopRequest.AddParam("description", "手动退款") // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTradeRefundManual)

	yopResp, err := global.YeePay.DoRequest(yopRequest)
	if nil != err {
		zap.S().Errorf("Error: %s", err)
	}
	_ = yopResp

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		zap.S().Errorf("Error: %s", err)
	}
	zap.S().Infof("退款信息：%s", string(marshal))
}
