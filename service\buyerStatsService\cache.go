package buyerStatsService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

// 缓存
var buyerStatsCache = "buyerStats:"

func get(r *redis.Client, id primitive.ObjectID) model.BuyerStats {
	key := buyerStatsCache + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.BuyerStats{}
		}
		var i model.BuyerStats
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.BuyerStats{}
		}
		return i
	}
	return model.BuyerStats{}
}

func set(r *redis.Client, info model.BuyerStats) {
	key := buyerStatsCache + info.BuyerID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().Error("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, time.Hour*24*30)
}

func del(r *redis.Client, buyerID primitive.ObjectID) {
	r.Del(context.Background(), buyerStatsCache+buyerID.Hex())
}
