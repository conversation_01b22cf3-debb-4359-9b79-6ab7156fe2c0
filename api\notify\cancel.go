package notify

//
//// PayNotifyCancel 取消订单
//func PayNotifyCancel(ctx *gin.Context) {
//	ctx.Set("rid", "cancel notify:"+ctx.GetString("rid"))
//	l := global.PayLogger.Sugar()
//	notify := deal(ctx, l)
//	switch notify.NotifyType {
//	case "allinpay.yunst.orderService.pay":
//		// 托管代收---订单成功
//		var res allinpay.NotifyPay
//		parseRes(notify.BizContent, &res)
//		err := orderRefundService.NewOrderRefundService().NotifyCancelStatus(ctx, res)
//		if err != nil {
//			l.<PERSON>("取消订单-回调更新失败")
//			xhttp.NotifyFail(ctx)
//			return
//		}
//		xhttp.NotifySuccess(ctx)
//		return
//	default:
//		bytes, _ := json.Marshal(notify)
//		zap.S().Error("取消订单-回调未对接：", string(bytes))
//	}
//
//}
