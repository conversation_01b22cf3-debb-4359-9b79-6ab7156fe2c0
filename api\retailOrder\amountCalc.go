package retailOrder

import (
	"base/core/xhttp"
	"base/types"
	"github.com/gin-gonic/gin"
)

// AmountCalc 订单费用计算
func AmountCalc(ctx *gin.Context) {
	var req = struct {
		ProductList []types.RetailProduct `json:"product_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var productAmount int

	for _, p := range req.ProductList {
		price := p.Price

		productAmountPer := price * p.Num

		//totalTransportFeePer := orderService.BackTransportFee(route.FeePerKG, w)
		productAmount += productAmountPer
		//totalTransportFee += totalTransportFeePer
	}

	r := CalcRes{
		TotalProductAmount: productAmount,
	}

	xhttp.RespSuccess(ctx, r)
}

type CalcRes struct {
	TotalProductAmount int `json:"total_product_amount"`
}
