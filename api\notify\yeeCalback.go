package notify

import (
	"base/global"
	"github.com/gin-gonic/gin"
	"github.com/yop-platform/yop-go-sdk/yop/utils"
	"go.uber.org/zap"
	"net/url"
	"strings"
)

func ParseYeeCall(ctx *gin.Context) (string, error) {

	ln := ctx.Request.ContentLength
	// 新建一个字节切片，长度与请求报文的内容长度相同
	body := make([]byte, ln)
	// 读取 r 的请求主体，并将具体内容读入 body 中
	n, err := ctx.Request.Body.Read(body)
	if err != nil {
	}
	_ = n
	parse, err := url.Parse(string(body))
	if err != nil {
	}
	str := parse.Path
	// 移除分隔符
	trim := strings.ReplaceAll(str, ";", "@")

	values, err := url.ParseQuery(trim)
	if err != nil {
		zap.S().Error(err)
	}

	_ = values

	var callback = values["response"][0]
	_ = callback
	//"Ars6jASSiylO70_VJDQ5SFU1zQwaI36kG5WhlSKHjkGdU3fEVEkkbhvAxKjOTUiw9vF7RMnmGKQQWAuV8jCKaOpMNjIEMHehBaPASwTiEE946CcbOeoNILGHf0o20xj2gqqvkQToFXEMNiic7bcYbfi0PxIrR6loBZnW-m5bqzB5RXLibiSjGlmr5CDnxV4tZXmYlkkeN2BcT4msWjfCtuaTMK_fN77WJcCMlW7ffqiN5yIOeqB4QBb5lOnClTRW4DThKPOMkXupAM2AnPxTkDp4n9lh-SK56zLuafk1bQhWUNcS9L4YEKZGJIjP7DY20TAWEr3yXo8w0w0VtB13Ig$Xf6fETKWcLTudBh2HluGSQTqhBRJa6EXHhXlMryWW8Y384RjVwIfpQm19RmTgkoqRc2tNcTWxRIW6itIS62DrzixlqRa099jx21uGqt8FCpvdWwnwlC16SgkeU_5NnrpjA_WQ0XW9RhNxzuQmwfxHGbtnth4vNXWswcSm23j3KQaXFjVP5Ws1uYVCxYSLMxqJE7a56DNWONGcGJJsc0KTCc7cdfr8n24emAaPCNteIG2RM8F17pRxY5yVnguTSZPXmhBlyI25xS7rciWzKZLp2Kfh_JCivABbA-_5Vf3VWPmjITs-TR5HlGVFbnT0eOUMUepXUemjjP8R0f8cBeH2NKej6QjQL99tvlrrxg_QfmezE0WTCITCNDBhpbHiq90lFyLjwlWNDTRo8rhjouSlMA9Ae_b-B4eZorDRVxw3BWywdyo2FzNk-dUDeBVaIth9YsaMGsq9XivGjlnnx3YEVfEtuVSvEm1xBdYsTHcM02nMwZb8Ze2WL1kIFo8IFM0$AES$SHA256"
	var platformPubKey = global.YeePay.Platform
	var isvPriKey = global.YeePay.PrivateKey

	content, err := utils.DecryptCallback(platformPubKey, isvPriKey, callback)
	if nil != err {
		return "", err
	}

	// 验签
	//var pubKey = global.YeePay.PublicKey
	//var signature = ""
	//var data = ""
	////"{\"result\":{\"requestId\":\"requestId\",\"errorMsg\":\"exception.record.not.found.transferDomesticOrder|merchantId:[null],requestId:[requestId]\",\"status\":\"FAILED\"}}"
	//if !utils.VerifySign(data, signature, pubKey, crypto.SHA256) {
	//	//verify failed
	//}

	zap.S().Infof("yee pay notify: %s", content)

	return content, nil
}
