package commentService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/commentDao"
	"base/global"
	"base/model"
	"base/service/orderService"
	"base/types"
	"base/util"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"strings"
	"time"
)

// ServiceInterface 评论
type ServiceInterface interface {
	Audit(ctx context.Context, id primitive.ObjectID, status model.AuditStatusType, auditNote string) error
	Create(ctx context.Context, req types.CommentCreate, buyer model.Buyer) error
	CreateCus(ctx context.Context, req types.CommentCreateCus) error
	Reply(ctx context.Context, id primitive.ObjectID, content string) error
	Delete(ctx context.Context, ids []primitive.ObjectID) error
	DeleteByBuyer(ctx context.Context, id primitive.ObjectID, buyer model.Buyer) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Comment, error)
	GetByOrderProduct(ctx context.Context, orderID, productID primitive.ObjectID) (model.Comment, error)
	ListByProduct(ctx context.Context, productID primitive.ObjectID, page, limit int64) ([]model.Comment, int64, error)
	ListByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Comment, int64, error)
	ListBySupplier(ctx context.Context, supplierID primitive.ObjectID, status model.AuditStatusType, page, limit int64) ([]model.Comment, int64, error)
	ListByWeb(ctx context.Context, status model.AuditStatusType, page, limit int64) ([]model.Comment, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	AutoComment(ctx context.Context, content string) error
}

type commentService struct {
	mdb        *mongo.Database
	commentDao commentDao.DaoInt
	orderS     orderService.ServiceInterface
}

func NewCommentService() ServiceInterface {
	return commentService{
		mdb:        global.MDB,
		commentDao: dao.CommentDao,
		orderS:     orderService.NewOrderService(),
	}
}

func (s commentService) Audit(ctx context.Context, id primitive.ObjectID, status model.AuditStatusType, auditNote string) error {
	byID, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	filter := bson.M{
		"_id": byID.ID,
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"audit_status":        status,
		"supplier_audit_note": auditNote,
		"audit_at":            now,
		"updated_at":          now,
	}
	err = s.commentDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func check(req types.CommentCreate) error {
	if len(req.OrderID) != 24 {
		return xerr.NewErr(xerr.ErrParamError, nil, "参数订单ID缺失")
	}
	if len(req.ProductID) != 24 {
		return xerr.NewErr(xerr.ErrParamError, nil, "参数商品ID缺失")
	}
	if strings.TrimSpace(req.Content) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "参数内容缺失")
	}
	return nil
}

func (s commentService) Create(ctx context.Context, req types.CommentCreate, buyer model.Buyer) error {
	err := check(req)
	if err != nil {
		return err
	}

	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "order_id")
	if err != nil {
		return err
	}

	pID, err := util.ConvertToObjectWithNote(req.ProductID, "product_id")
	if err != nil {
		return err
	}

	r, err := s.GetByOrderProduct(ctx, orderID, pID)
	if !errors.Is(err, mongo.ErrNoDocuments) && r.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "该订单商品已评价，请刷新")
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return xerr.NewErr(xerr.ErrParamError, err, "订单不存在")
		}
		return xerr.NewErr(xerr.ErrSysBusy, err, "订单查询错误")
	}

	// 订单状态不为待评价状态
	if order.OrderStatus != model.OrderStatusTypeFinish || order.BuyerID != buyer.ID {
		return xerr.NewErr(xerr.ErrParamError, err, "订单不能评价")
	}
	now := time.Now().UnixMilli()

	var p model.ProductOrder
	for _, productOrder := range order.ProductList {
		if productOrder.ProductID == pID {
			p = productOrder
		}
	}

	comment := model.Comment{
		ID:           primitive.NewObjectID(),
		UserID:       buyer.UserID,
		BuyerID:      order.BuyerID,
		BuyerName:    order.BuyerName,
		BuyerAvatar:  buyer.AvatarImg,
		OrderID:      order.ID,
		SupplierID:   order.SupplierID,
		SupplierName: order.SupplierName,
		ProductID:    pID,
		ProductTitle: p.ProductTitle,
		ProductCover: p.ProductCoverImg,
		DeliverType:  order.DeliverType,
		LogisticStar: req.LogisticStar,
		DeliverStar:  req.DeliverStar,
		ProductStar:  req.ProductStar,
		SupplierStar: req.SupplierStar,
		Content:      req.Content,
		Video:        req.Video,
		ImgList:      req.ImgList,
		AuditStatus:  model.AuditStatusTypeDoing,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		err = s.commentDao.Create(ctx, comment)
		if err != nil {
			return nil, err
		}
		err = s.updateOrder(ctx, order, pID)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})

	return nil
}

func (s commentService) CreateCus(ctx context.Context, req types.CommentCreateCus) error {
	pID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		return err
	}
	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		return err
	}

	comment := model.Comment{
		ID:           primitive.NewObjectID(),
		UserID:       primitive.NilObjectID,
		BuyerID:      primitive.NilObjectID,
		BuyerName:    req.BuyerName,
		BuyerAvatar:  req.AvatarImg,
		OrderID:      primitive.NilObjectID,
		SupplierID:   supplierID,
		SupplierName: req.SupplierName,
		ProductID:    pID,
		ProductTitle: req.ProductTitle,
		ProductCover: req.ProductCoverImg,
		DeliverType:  model.DeliverTypeDoor,
		LogisticStar: req.LogisticStar,
		DeliverStar:  req.DeliverStar,
		ProductStar:  req.ProductStar,
		SupplierStar: req.SupplierStar,
		Content:      req.Content,
		Video:        req.Video,
		ImgList:      req.ImgList,
		AuditStatus:  model.AuditStatusTypePass,
		CreatedAt:    req.CreatedAt,
		UpdatedAt:    req.CreatedAt,
	}

	err = s.commentDao.Create(ctx, comment)
	if err != nil {
		return err
	}

	return nil
}

func (s commentService) Reply(ctx context.Context, id primitive.ObjectID, content string) error {
	now := time.Now().UnixMilli()
	update := bson.M{
		"reply_content": content,
		"reply_at":      now,
		"updated_at":    now,
	}
	err := s.commentDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s commentService) DeleteByBuyer(ctx context.Context, id primitive.ObjectID, buyer model.Buyer) error {
	byID, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if byID.BuyerID != buyer.ID {
		return xerr.NewErr(xerr.ErrParamError, err, "无权限")
	}
	now := time.Now().UnixMilli()
	update := bson.M{
		"updated_at": now,
		"deleted_at": now,
	}
	err = s.commentDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func (s commentService) updateOrder(ctx context.Context, order model.Order, productID primitive.ObjectID) error {
	filter := bson.M{
		"_id":                     order.ID,
		"product_list.product_id": productID,
	}
	now := time.Now().UnixMilli()
	commentAll := true

	for _, productOrder := range order.ProductList {
		if !productOrder.HasComment && productOrder.ProductID != productID {
			commentAll = false
			break
		}
	}

	update := bson.M{
		"product_list.$.has_comment": true,
		"updated_at":                 now,
	}
	if commentAll {
		update["has_comment"] = true
	}
	err := s.orderS.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func (s commentService) ListByProduct(ctx context.Context, productID primitive.ObjectID, page, limit int64) ([]model.Comment, int64, error) {
	filter := bson.M{
		"product_id":   productID,
		"audit_status": model.AuditStatusTypePass,
		"deleted_at":   0,
	}
	list, count, err := s.commentDao.ListByPage(ctx, filter, page, limit)

	return list, count, err
}

func (s commentService) ListBySupplier(ctx context.Context, supplierID primitive.ObjectID, status model.AuditStatusType, page, limit int64) ([]model.Comment, int64, error) {
	filter := bson.M{
		"supplier_id":  supplierID,
		"audit_status": status,
		"deleted_at":   0,
	}

	list, count, err := s.commentDao.ListByPage(ctx, filter, page, limit)

	return list, count, err
}

func (s commentService) ListByWeb(ctx context.Context, status model.AuditStatusType, page, limit int64) ([]model.Comment, int64, error) {
	// over 24h
	milli := time.Now().Add(-time.Hour * 24).UnixMilli()
	filter := bson.M{
		"created_at":   bson.M{"$lte": milli},
		"audit_status": status,
		"deleted_at":   0,
	}
	list, count, err := s.commentDao.ListByPage(ctx, filter, page, limit)

	return list, count, err
}

func (s commentService) GetByOrderProduct(ctx context.Context, orderID, productID primitive.ObjectID) (model.Comment, error) {
	filter := bson.M{
		"product_id": productID,
		"order_id":   orderID,
	}
	data, err := s.commentDao.Get(ctx, filter)
	if err != nil {
		return model.Comment{}, err
	}

	return data, nil
}
func (s commentService) GetByID(ctx context.Context, id primitive.ObjectID) (model.Comment, error) {
	filter := bson.M{
		"_id": id,
	}
	data, err := s.commentDao.Get(ctx, filter)
	if err != nil {
		return model.Comment{}, err
	}

	return data, nil
}

func (s commentService) ListByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Comment, int64, error) {
	filter := bson.M{
		"buyer_id":   buyerID,
		"deleted_at": 0,
	}
	list, count, err := s.commentDao.ListByPage(ctx, filter, page, limit)

	return list, count, err
}

func (s commentService) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.commentDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s commentService) Delete(ctx context.Context, ids []primitive.ObjectID) error {
	if len(ids) < 1 {
		return nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}

	now := time.Now().UnixMilli()
	update := bson.M{
		"deleted_at": now,
	}

	err := s.commentDao.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

type MNS struct {
	OrderID string `json:"order_id"`
}

func (s commentService) AutoComment(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("AutoComment error:%v", err)
			return
		}
	}()

	var data MNS
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	if len(data.OrderID) < 1 {
		return nil
	}

	orderID, err := util.ConvertToObjectWithNote(data.OrderID, "")
	if err != nil {
		return err
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}
	if order.HasComment {
		zap.S().Infof("订单已全评论，跳过订单：%s", data.OrderID)
		return nil
	}

	var pIDs []primitive.ObjectID
	for _, p := range order.ProductList {
		if !p.HasComment {
			pIDs = append(pIDs, p.ProductID)
		}
	}

	filter := bson.M{
		"_id": order.ID,
		"product_list.product_id": bson.M{
			"$in": pIDs,
		},
	}
	now := time.Now().UnixMilli()

	update := bson.M{
		"product_list.$.has_comment":     true,
		"product_list.$.is_auto_comment": true,
		"updated_at":                     now,
		"has_comment":                    true,
	}

	err = s.orderS.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}
