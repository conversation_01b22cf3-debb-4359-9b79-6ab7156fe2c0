package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/service/buyerBalanceOrderService"
	"encoding/json"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PayNotifyDepositBuyerBalance 会员余额
func PayNotifyDepositBuyerBalance(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 订单结果通知
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		buyerBalanceOrderService.NewBuyerBalanceOrderService().NotifyPayStatus(ctx, res)
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("余额充值-回调未对接：", string(bytes))
	}

}
