package orderRefundService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderRefundDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/deliverFeeRuleService"
	"base/service/miniService"
	"base/service/orderDebtService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/service/payAccountService"
	"base/service/productCommissionService"
	"base/service/productImageService"
	"base/service/productService"
	"base/service/routeService"
	"base/service/servicePointCommissionService"
	"base/service/servicePointService"
	"base/service/supplierService"
	"base/service/warehouseService"
	"base/service/yeeMerchantService"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	_ "github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 退款订单
type ServiceInterface interface {
	BackShipSettle(ctx context.Context, order model.Order) ([]model.ProductSettle, error)
	CreateRefund(ctx context.Context, req types.OrderRefundReq) (model.OrderRefund, error) // 售后
	ReSubmitRefund(ctx context.Context, req types.OrderRefundReq) error                    // 二次提交售后
	WithdrawRefund(ctx context.Context, id primitive.ObjectID) error                       // 撤销
	GetByID(ctx context.Context, id primitive.ObjectID) (model.OrderRefund, error)
	GetByProductID(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error)
	GetByProductIDForAfterSale(ctx context.Context, orderID, productID primitive.ObjectID, skuIDCode string) (model.OrderRefund, error)
	GetByProductIDForQuality(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error)
	DoPayRefundBalance(ctx context.Context, order model.OrderRefund) error
	DoPayRefundJSAPI(ctx context.Context, order model.OrderRefund) error

	//DoPayRefundRefresh(ctx context.Context, order model.OrderRefund) error

	Audit(ctx context.Context, refund model.OrderRefund, auditStatus model.AuditStatusType, auditAmount int, auditNote string, auditorType model.AuditorType) error
	ConfirmRefund(ctx context.Context, id primitive.ObjectID, confirmType model.ConfirmType, auditObjection string) error
	//UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error
	UpdatePayResult(ctx context.Context, id primitive.ObjectID, ps model.RefundResult) error
	UpdateParentOrderID(ctx context.Context, id, parentOrderID primitive.ObjectID) error
	// CheckShippedExistDebtOrRefund 检查发货后是否存在补差和退款
	CheckShippedExistDebtOrRefund(order model.Order) error

	CancelOrder(ctx context.Context, orderID primitive.ObjectID) error

	YeeCancelOrder(ctx context.Context, order model.Order) error
	DoRefundYee(ctx context.Context, order model.OrderRefund) error

	YeeRefundDeliver(ctx context.Context, id primitive.ObjectID) error

	//CancelOrderCoupon(ctx context.Context, orderID string) error  // 取消订单代金券
	RefundDeliver(ctx context.Context, parentOrderID string) error
	//RefundPartDeliverManual(ctx context.Context, parentOrderID primitive.ObjectID) error
	ListAllRefundByOrder(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error) // 订单的所有退款
	ListByPage(ctx context.Context, refundType model.RefundType, auditStatus model.AuditStatusType, page, limit int64, withdrawStatus int) ([]model.OrderRefund, int64, error)
	ListByPageYHT(ctx context.Context, refundType model.RefundType, auditStatus model.AuditStatusType, page, limit int64, withdrawStatus int) ([]model.OrderRefund, int64, error)
	ListRetailByPage(ctx context.Context, pointID primitive.ObjectID, auditStatus model.AuditStatusType, page, limit int64, withdrawStatus int) ([]model.OrderRefund, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	ListByPageCus(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderRefund, int64, error)
	CountAfterSaleMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int64, error)
	CountByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int64, error)
	CountNum(ctx context.Context, buyerID primitive.ObjectID) (int64, error)
	CountByProduct(ctx context.Context, productID primitive.ObjectID) (int64, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderRefund, error)
	ListByBuyer(ctx context.Context, buyerID primitive.ObjectID, refundType model.RefundType, page, limit int64) ([]model.OrderRefund, int64, error)
	ListByProductID(ctx context.Context, orderID, productID primitive.ObjectID) ([]model.OrderRefund, error)
	GetShipRefundByProductID(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error)
	GetAfterSaleRefundByProductID(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error)
	ListAfterSaleRefund(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error)
	ListShipRefundRefund(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error)
	ListByOrder(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error)
	GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.OrderRefund, error)
	GetByOutRefundNo(ctx context.Context, outRefundNo string) (model.OrderRefund, error)

	ActiveCheckRefund(ctx context.Context, orderID string) error // 主动查询订单支付状态
	//DoAfterShipRefund(ctx context.Context, content string) error // 执行发货退款
	SyncRefundData(ctx context.Context, orderID string) error        // 同步订单的退款数据
	SyncQualityRefundData(ctx context.Context, orderID string) error // 同步订单的退款数据

	ChangeRefundAuditor(ctx context.Context, content string) error
	CompleteRefund(ctx context.Context, content string) error

	CheckOrderHasRefundAll(ctx context.Context, orderID string) error

	NotifyRefundStatus(ctx context.Context, res allinpay.NotifyPay) error //  异步通知

	NotifyRefund(ctx context.Context, content *model.RefundNotify) error

	NotifyCancel(ctx context.Context, content *model.RefundNotify) error

	YeeNotifyCancel(ctx context.Context, notify model.YeeTradeRefundNotify) error
	YeeNotifyRefundAfterSale(ctx context.Context, notify model.YeeTradeRefundNotify) error
	YeeNotifyRefundQuality(ctx context.Context, notify model.YeeTradeRefundNotify) error
	YeeNotifyRefundDeliver(ctx context.Context, notify model.YeeTradeRefundNotify) error
}

type orderRefundService struct {
	mdb *mongo.Database
	rdb *redis.Client
	l   *zap.SugaredLogger

	yeePay       *global.YeePayInfo
	yeeMerchantS yeeMerchantService.ServiceInterface

	orderS                  orderService.ServiceInterface
	productS                productService.ServiceInterface
	routeFeeS               routeService.ServiceInterface
	servicePointS           servicePointService.ServiceInterface
	warehouseS              warehouseService.ServiceInterface
	servicePointCommissionS servicePointCommissionService.ServiceInterface
	productImageS           productImageService.ServiceInterface
	productCommissionS      productCommissionService.ServiceInterface
	//供应商
	supplierS supplierService.ServiceInterface

	authenticationS authenticationService.ServiceInterface

	mini miniService.ServiceInterface

	//	费率
	// 支付公司
	PayCompanyCommission float64

	// 平台营销账户余额
	payAccountS payAccountService.ServiceInterface

	allInPayOrderS payModule.OrderService
	// 父单
	parentOrderS parentOrderService.ServiceInterface

	orderRefundDao orderRefundDao.DaoInt // 退款

	// 订单补差
	orderDebtS orderDebtService.ServiceInterface

	afterSaleHour int64 // 售后期

	deliverFeeRuleS deliverFeeRuleService.ServiceInterface // 配送费
}

// NewOrderRefundService 创建退款订单服务
func NewOrderRefundService() ServiceInterface {
	return orderRefundService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
		l:   global.OrderLogger.Sugar(),

		yeePay:       global.YeePay,
		yeeMerchantS: yeeMerchantService.NewYeeMerchantService(),

		orderS:                  orderService.NewOrderService(),
		productS:                productService.NewProductService(),
		routeFeeS:               routeService.NewTransportFeeService(),
		servicePointS:           servicePointService.NewServicePointService(),
		warehouseS:              warehouseService.NewWarehouseServiceService(),
		servicePointCommissionS: servicePointCommissionService.NewPartnerCommissionService(),
		productImageS:           productImageService.NewProductImageService(),
		productCommissionS:      productCommissionService.NewProductCommissionService(),
		supplierS:               supplierService.NewSupplierService(),

		mini: miniService.NewMiniService(),

		payAccountS: payAccountService.NewPayAccountService(),

		allInPayOrderS: payModule.NewOrderS(),
		parentOrderS:   parentOrderService.NewParentOrderService(),

		orderRefundDao: dao.OrderRefundDao,

		afterSaleHour: global.OrderAfterSaleHour,

		deliverFeeRuleS: deliverFeeRuleService.NewDeliverFeeRuleService(),

		orderDebtS: orderDebtService.NewOrderDebtService(),
	}
}

// CreateRefund 售后
func (s orderRefundService) CreateRefund(ctx context.Context, req types.OrderRefundReq) (model.OrderRefund, error) {
	now := time.Now().UnixMilli()

	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "CreateRefund OrderID")
	if err != nil {
		return model.OrderRefund{}, err
	}
	productID, err := util.ConvertToObjectWithNote(req.ProductID, "CreateRefund ProductID")
	if err != nil {
		return model.OrderRefund{}, err
	}

	byProduct, err := s.GetByProductIDForAfterSale(ctx, orderID, productID, req.SkuIDCode)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.OrderRefund{}, err
	}
	if byProduct.ID != primitive.NilObjectID {
		s.l.Errorf("该订单%s此商品%s已有售后订单，请勿重复申请", req.OrderID, req.ProductID)
		return model.OrderRefund{}, xerr.NewErr(xerr.ErrParamError, nil, "该订单此商品已有售后订单，请勿重复申请")
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return model.OrderRefund{}, err
	}

	if order.OrderStatus != model.OrderStatusTypeFinish {
		return model.OrderRefund{}, xerr.NewErr(xerr.ErrParamError, nil, "无法申请退款，订单尚未完成")
	}

	// 结算退款
	debt, err := s.orderDebtS.GetByOrderID(ctx, orderID)
	if err != nil {
		return model.OrderRefund{}, err
	}

	var settle model.ProductSettle
	for _, quality := range debt.SettleProductList {
		if quality.ProductID == productID && quality.SkuIDCode == req.SkuIDCode {
			settle = quality
		}
	}

	var qualityRefundAmount int
	if settle.SettleResultType == model.SettleResultTypeRefund && settle.DiffProductAmount > 0 {
		qualityRefundAmount = settle.DiffProductAmount
	}

	p, err := s.checkAfterSaleRefund(order, productID, req.SkuIDCode, req.Amount, qualityRefundAmount)
	if err != nil {
		return model.OrderRefund{}, err
	}

	parentOrder, err := s.parentOrderS.Get(ctx, order.ParentOrderID)
	if err != nil {
		return model.OrderRefund{}, err
	}

	var yeeRefund model.YeeRefundResult
	if order.PayMethod == model.PayMethodTypeYeeWechat {
		merchant, err := s.yeeMerchantS.GetBySupplier(ctx, order.SupplierID)
		if err != nil {
			return model.OrderRefund{}, err
		}
		var merchantNo string
		var oriOrderID string
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == merchant.MerchantNo {
				merchantNo = sub.MerchantNo
				oriOrderID = sub.OrderId
				break
			}
		}
		yeeRefund.ParentMerchantNo = parentOrder.YeeWechatResult.ParentMerchantNo
		yeeRefund.MerchantNo = merchantNo
		yeeRefund.OrderId = oriOrderID
	}

	if order.PayMethod == model.PayMethodTypeYeeBalance {
		yeeRefund.ParentMerchantNo = parentOrder.YeeWechatResult.ParentMerchantNo
		yeeRefund.MerchantNo = parentOrder.YeeWechatResult.ParentMerchantNo
		yeeRefund.OrderId = parentOrder.YeeWechatResult.OrderID
	}

	data := model.OrderRefund{
		ID:                            primitive.NewObjectID(),
		BuyerID:                       order.BuyerID,
		UserID:                        order.UserID,
		BuyerName:                     order.BuyerName,
		SupplierID:                    order.SupplierID,
		SupplierName:                  order.SupplierName,
		OrderID:                       order.ID,
		PayMethod:                     order.PayMethod,
		OrderIDNum:                    order.IDNum,
		OrderType:                     order.OrderType,
		UserType:                      order.UserType,
		ServicePointID:                order.ServicePointID,
		ParentOrderID:                 parentOrder.ID,
		BizOrderNo:                    util.NewUUID(),
		OriPayBizUserID:               order.PayBizUserID,
		OriReceiverBizUserID:          order.ReceiverBizUserID,
		OriBizOrderNo:                 parentOrder.BizOrderNo,
		OriOrderNo:                    parentOrder.BizOrderNoResult.OrderNo,
		OriTransactionID:              parentOrder.WXPayResult.TransactionID,
		OriTotalAmount:                parentOrder.WXPayResult.TotalAmount,
		RefundType:                    model.RefundTypeAfterSale, // 售后
		RefundBackType:                model.RefundBackTypeMini,  // 退款返回路径
		RefundReasonType:              req.RefundReasonType,
		Reason:                        req.Reason,
		ProductID:                     p.ProductID,
		SkuIDCode:                     req.SkuIDCode,
		SkuName:                       p.SkuName,
		ProductImageID:                p.ProductImageID,
		ProductTitle:                  p.ProductTitle,
		ProductCover:                  p.ProductCoverImg,
		IsCheckWeight:                 p.IsCheckWeight,
		ImageList:                     req.ImageList,
		ImageListOne:                  req.ImageListOne,
		ImageListTwo:                  req.ImageListTwo,
		ImageListThree:                req.ImageListThree,
		Video:                         req.Video,
		Price:                         p.Price,
		Num:                           req.RefundNum,
		RoughWeight:                   p.RoughWeight,
		OutWeight:                     p.OutWeight,
		NetWeight:                     p.NetWeight,
		ProductRoughWeightUnitPriceKG: p.ProductRoughWeightUnitPriceKG,
		UserPayRoughWeightUnitPriceKG: p.UserPayRoughWeightUnitPriceKG,
		CouponRoughWeightUnitPriceKG:  p.CouponRoughWeightUnitPriceKG,
		RefundWeight:                  req.RefundWeight,
		Amount:                        req.Amount,
		AuditAmount:                   req.Amount,
		AuditNote:                     "",
		AuditStatus:                   model.AuditStatusTypeDoing,
		AuditorType:                   model.AuditorTypeSupplier,
		IsComplete:                    false,
		CreatedAt:                     now,
		IsRefundTransport:             false,
		YeeRefundResult:               yeeRefund,
	}

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return model.OrderRefund{}, err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 创建
		err = s.orderRefundDao.Create(sessCtx, data)
		if err != nil {
			return nil, err
		}

		// 更新订单状态和订单商品状态
		filter := bson.M{
			"_id":                      orderID,
			"product_list.product_id":  p.ProductID,
			"product_list.sku_id_code": req.SkuIDCode,
		}
		update := bson.M{
			//"pay_status":                       model.PayStatusTypePaidButRefund, // 支付成功但是发生退款
			"product_list.$.after_sale_status": model.AfterSaleStatusTypePending, //  售后退款
			"updated_at":                       time.Now().UnixMilli(),
		}
		err = s.orderS.UpdateOne(sessCtx, filter, bson.M{"$set": update})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return model.OrderRefund{}, err
	}
	return data, nil
}

// ReSubmitRefund 二次申请售后
func (s orderRefundService) ReSubmitRefund(ctx context.Context, req types.OrderRefundReq) error {
	now := time.Now().UnixMilli()

	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "CreateRefund OrderID")
	if err != nil {
		return err
	}
	productID, err := util.ConvertToObjectWithNote(req.ProductID, "CreateRefund ProductID")
	if err != nil {
		return err
	}

	byOrderProduct, err := s.GetByProductIDForAfterSale(ctx, orderID, productID, req.SkuIDCode)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}

	debt, err := s.orderDebtS.GetByOrderID(ctx, orderID)
	if err != nil {
		return err
	}

	var settle model.ProductSettle
	for _, quality := range debt.SettleProductList {
		if quality.ProductID == productID && quality.SkuIDCode == req.SkuIDCode {
			settle = quality
		}
	}

	var qualityRefundAmount int
	if settle.SettleResultType == model.SettleResultTypeRefund && settle.DiffProductAmount > 0 {
		qualityRefundAmount = settle.DiffProductAmount
	}

	p, err := s.checkAfterSaleRefund(order, productID, req.SkuIDCode, req.Amount, qualityRefundAmount)
	if err != nil {
		return err
	}

	byOrderProduct.RefundReasonType = req.RefundReasonType
	byOrderProduct.ImageList = req.ImageList
	byOrderProduct.ImageListOne = req.ImageListOne
	byOrderProduct.ImageListTwo = req.ImageListTwo
	byOrderProduct.ImageListThree = req.ImageListThree
	byOrderProduct.Video = req.Video
	byOrderProduct.RefundWeight = req.RefundWeight
	byOrderProduct.Reason = req.Reason
	byOrderProduct.Amount = req.Amount
	byOrderProduct.AuditAmount = req.Amount
	byOrderProduct.UpdatedAt = now
	byOrderProduct.IsWithdraw = false

	// 创建
	err = s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": byOrderProduct.ID}, bson.M{"$set": byOrderProduct})
	if err != nil {
		return err
	}

	// 更新订单状态和订单商品状态
	filter := bson.M{
		"_id":                      orderID,
		"product_list.product_id":  p.ProductID,
		"product_list.sku_id_code": req.SkuIDCode,
	}
	update := bson.M{
		"product_list.$.after_sale_status": model.AfterSaleStatusTypePending, //  售后退款
		"updated_at":                       time.Now().UnixMilli(),
	}
	err = s.orderS.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s orderRefundService) WithdrawRefund(ctx context.Context, id primitive.ObjectID) error {
	refund, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if refund.IsWithdraw {
		return xerr.NewErr(xerr.ErrParamError, nil, "申请已撤销，请刷新")
	}
	filter := bson.M{
		"_id": id,
	}
	now := time.Now().UnixMilli()
	update := bson.M{
		"is_withdraw": true,
		"is_complete": true,
		"updated_at":  now,
	}
	err = s.orderRefundDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	filterOrder := bson.M{
		"_id":                      refund.OrderID,
		"product_list.product_id":  refund.ProductID,
		"product_list.sku_id_code": refund.SkuIDCode,
	}
	updateOrder := bson.M{
		"$set": bson.M{
			"product_list.$.after_sale_status": model.AfterSaleStatusTypeFinish,
			"updated_at":                       now,
		},
	}

	s.orderS.UpdateOne(ctx, filterOrder, updateOrder)

	mnsSendService.NewMNSClient().SendRemoveProductStats(refund.ProductID)
	mnsSendService.NewMNSClient().SendRemoveBuyerStats(refund.BuyerID)

	return nil
}

func (s orderRefundService) ConfirmRefund(ctx context.Context, id primitive.ObjectID, confirmType model.ConfirmType, auditObjection string) error {
	refund, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if refund.IsComplete {
		return xerr.NewErr(xerr.ErrParamError, nil, "售后已完结，请刷新")
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"updated_at": now,
	}

	if confirmType == model.ConfirmTypeDisagree {
		// 异议内容
		update["audit_objection"] = auditObjection
		update["auditor_type"] = model.AuditorTypePlatform
		update["audit_status"] = model.AuditStatusTypeDoing
	}

	if confirmType == model.ConfirmTypeAgree {
		update["is_complete"] = true
	}

	filter := bson.M{
		"_id": id,
	}
	err = s.orderRefundDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	if confirmType == model.ConfirmTypeAgree {
		//	 完结通知退款,主动同意
		mnsSendService.NewMNSClient().SendRefundComplete(refund.ID, 1)
	}

	return nil
}

func (s orderRefundService) ChangeRefundAuditor(ctx context.Context, content string) error {
	// 用于超时
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ChangeRefundAuditor error:%v", err)
			return
		}
	}()
	var data model.MNSRefund
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	refund, err := s.orderRefundDao.Get(ctx, bson.M{"_id": data.RefundID})
	if err != nil {
		return err
	}

	if refund.AuditStatus == model.AuditStatusTypePass {
		return nil
	}

	if refund.IsComplete || refund.AuditorType == model.AuditorTypePlatform {
		return nil
	}

	update := bson.M{
		"auditor_type":           model.AuditorTypePlatform,
		"supplier_audit_timeout": true,
	}
	err = s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": data.RefundID}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s orderRefundService) CompleteRefund(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("CompleteRefund error:%v", err)
			return
		}
	}()

	var data model.MNSRefund
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	refund, err := s.GetByID(ctx, data.RefundID)
	if err != nil {
		return err
	}

	if refund.YeeRefundResult.RefundRequestId != "" {
		zap.S().Warnf("CompleteRefund error:售后已处理:%s", refund.ID.Hex())
		return nil
	}

	now := time.Now()

	filter := bson.M{
		"_id":                      refund.OrderID,
		"product_list.product_id":  refund.ProductID,
		"product_list.sku_id_code": refund.SkuIDCode,
	}
	updateOrder := bson.M{
		"$set": bson.M{
			"product_list.$.after_sale_status": model.AfterSaleStatusTypeFinish,
			"updated_at":                       now.UnixMilli(),
		},
	}

	err = s.orderS.UpdateOne(ctx, filter, updateOrder)
	if err != nil {
		return err
	}

	if refund.PayMethod == model.PayMethodTypeYeeWechat || refund.PayMethod == model.PayMethodTypeYeeBalance {
		// 易宝
		err := s.DoRefundYee(ctx, refund)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s orderRefundService) UpdatePayResult(ctx context.Context, id primitive.ObjectID, ps model.RefundResult) error {
	update := bson.M{
		"refund_result": ps,
	}
	err := s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s orderRefundService) UpdateParentOrderID(ctx context.Context, id, parentOrderID primitive.ObjectID) error {
	update := bson.M{
		"parent_order_id": parentOrderID,
	}
	err := s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s orderRefundService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.OrderRefund, error) {
	refund, err := s.orderRefundDao.Get(ctx, bson.M{"biz_order_no": bizOrderNo})
	if err != nil {
		return model.OrderRefund{}, err
	}
	return refund, nil
}

func (s orderRefundService) GetByOutRefundNo(ctx context.Context, outRefundNo string) (model.OrderRefund, error) {
	filter := bson.M{
		"wx_refund_result.out_refund_no": outRefundNo,
	}
	refund, err := s.orderRefundDao.Get(ctx, filter)
	if err != nil {
		return model.OrderRefund{}, err
	}
	return refund, nil
}

func (s orderRefundService) GetByID(ctx context.Context, id primitive.ObjectID) (model.OrderRefund, error) {
	refund, err := s.orderRefundDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.OrderRefund{}, err
	}
	return refund, nil
}

func (s orderRefundService) GetByProductID(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error) {
	refund, err := s.orderRefundDao.Get(ctx, bson.M{"order_id": orderID, "product_id": productID})
	if err != nil {
		return model.OrderRefund{}, err
	}
	return refund, nil
}

func (s orderRefundService) GetByProductIDForAfterSale(ctx context.Context, orderID, productID primitive.ObjectID, skuIDCode string) (model.OrderRefund, error) {
	refund, err := s.orderRefundDao.Get(ctx, bson.M{
		"order_id":    orderID,
		"product_id":  productID,
		"refund_type": model.RefundTypeAfterSale,
		"sku_id_code": skuIDCode,
	})
	if err != nil {
		return model.OrderRefund{}, err
	}
	return refund, nil
}

func (s orderRefundService) GetByProductIDForQuality(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error) {
	refund, err := s.orderRefundDao.Get(ctx, bson.M{
		"order_id":    orderID,
		"product_id":  productID,
		"refund_type": model.RefundTypeQuality,
	})
	if err != nil {
		return model.OrderRefund{}, err
	}
	return refund, nil
}

func (s orderRefundService) List(ctx context.Context, filter bson.M) ([]model.OrderRefund, error) {
	list, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderRefundService) ListByPage(ctx context.Context, refundType model.RefundType, auditStatus model.AuditStatusType, page, limit int64, withdrawStatus int) ([]model.OrderRefund, int64, error) {
	filter := bson.M{
		"audit_status": auditStatus,
		"refund_type":  refundType,
		"order_type":   model.OrderTypeWholeSale,
	}
	if withdrawStatus != 0 {
		filter["is_withdraw"] = false
		if withdrawStatus == 2 {
			filter["is_withdraw"] = true
		}
	}

	list, count, err := s.orderRefundDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}
func (s orderRefundService) ListByPageYHT(ctx context.Context, refundType model.RefundType, auditStatus model.AuditStatusType, page, limit int64, withdrawStatus int) ([]model.OrderRefund, int64, error) {
	filter := bson.M{
		"audit_status": auditStatus,
		"refund_type":  refundType,
		"order_type":   model.OrderTypeWholeSale,
		"user_type":    "YHT",
	}
	if withdrawStatus != 0 {
		filter["is_withdraw"] = false
		if withdrawStatus == 2 {
			filter["is_withdraw"] = true
		}
	}

	list, count, err := s.orderRefundDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s orderRefundService) ListRetailByPage(ctx context.Context, pointID primitive.ObjectID, auditStatus model.AuditStatusType, page, limit int64, withdrawStatus int) ([]model.OrderRefund, int64, error) {
	filter := bson.M{
		"audit_status":     auditStatus,
		"service_point_id": pointID,
		"order_type":       model.OrderTypeRetail,
	}
	if withdrawStatus != 0 {
		filter["is_withdraw"] = false
		if withdrawStatus == 2 {
			filter["is_withdraw"] = true
		}
	}

	list, count, err := s.orderRefundDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s orderRefundService) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.orderRefundDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderRefundService) ListByPageCus(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderRefund, int64, error) {
	list, count, err := s.orderRefundDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s orderRefundService) ListByBuyer(ctx context.Context, buyerID primitive.ObjectID, refundType model.RefundType, page, limit int64) ([]model.OrderRefund, int64, error) {
	filter := bson.M{
		"buyer_id":    buyerID,
		"refund_type": refundType,
	}
	list, count, err := s.orderRefundDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s orderRefundService) ListByProductID(ctx context.Context, orderID, productID primitive.ObjectID) ([]model.OrderRefund, error) {
	filter := bson.M{
		"order_id":   orderID,
		"product_id": productID,
	}
	list, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderRefundService) GetShipRefundByProductID(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error) {
	filter := bson.M{
		"order_id":    orderID,
		"product_id":  productID,
		"refund_type": model.RefundTypeQuality,
	}
	data, err := s.orderRefundDao.Get(ctx, filter)
	if err != nil {
		return model.OrderRefund{}, err
	}
	return data, nil
}

func (s orderRefundService) GetAfterSaleRefundByProductID(ctx context.Context, orderID, productID primitive.ObjectID) (model.OrderRefund, error) {
	filter := bson.M{
		"order_id":    orderID,
		"product_id":  productID,
		"refund_type": model.RefundTypeAfterSale,
	}
	data, err := s.orderRefundDao.Get(ctx, filter)
	if err != nil {
		return model.OrderRefund{}, err
	}
	return data, nil
}

func (s orderRefundService) ListAfterSaleRefund(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error) {
	filter := bson.M{
		"order_id":    orderID,
		"refund_type": model.RefundTypeAfterSale,
	}
	data, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s orderRefundService) ListByOrder(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error) {
	filter := bson.M{
		"order_id": orderID,
	}
	data, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s orderRefundService) ListShipRefundRefund(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error) {
	filter := bson.M{
		"order_id":    orderID,
		"refund_type": model.RefundTypeQuality,
	}
	data, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// Cancel 取消订单-已付款
//func (s orderRefundService) Cancel(ctx context.Context, orderID primitive.ObjectID) error {
//	order, err := s.orderS.Get(ctx, orderID)
//	if err != nil {
//		return err
//	}
//
//	now := time.Now()
//	created := time.UnixMilli(order.CreatedAt)
//	if now.Sub(created).Seconds() < 60 {
//		d := 60 - now.Sub(created).Seconds()
//		return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("请%d秒后取消", int(d)))
//	}
//
//	parentOrder, err := s.parentOrderS.Get(ctx, order.ParentOrderID)
//	if err != nil {
//		return err
//	}
//	if order.OrderStatus > model.OrderStatusTypeToStockUp {
//		//已经备货了
//		return xerr.NewErr(xerr.ErrParamError, nil, "取消失败，订单已备货")
//	}
//	if order.OrderStatus == model.OrderStatusTypeCancel || order.OrderStatus == model.OrderStatusTypeClosed {
//		return xerr.NewErr(xerr.ErrParamError, nil, "订单已关闭，请刷新")
//	}
//
//	orders, err := s.orderS.ListByParentOrderID(ctx, parentOrder.ID)
//	if err != nil {
//		return err
//	}
//	refundDeliverFee := true
//	for _, o := range orders {
//		if o.OrderStatus != model.OrderStatusTypeCancel && o.ID != orderID {
//			refundDeliverFee = false
//			break
//		}
//	}
//
//	amount := order.PaidAmount
//
//	var rList []pays.RefundItem
//	rList = append(rList, pays.RefundItem{
//		//AccountSetNo:  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）
//		BizUserId: order.ReceiverBizUserID,
//		Amount:    amount,
//	})
//	req := pays.RefundReq{
//		BizOrderNo:    util.NewUUID(),
//		OriBizOrderNo: parentOrder.BizOrderNo,
//		OriOrderNo:    parentOrder.BizOrderNoResult.OrderNo,
//		BizUserId:     order.PayBizUserID,
//		RefundType:    "D0",
//		RefundList:    rList,
//		BackUrl:       global.BackHost + global.BackUrlCancel,
//		Amount:        amount,
//		//CouponAmount:  order.PaidAmount,
//		CouponAmount:  0,
//		FeeAmount:     0,
//		RefundAccount: "TLT",
//		ExtendInfo:    "取消订单" + orderID.Hex(),
//	}
//	res, err := s.allInPayOrderS.RefundS(req)
//	if err != nil {
//		s.l.Errorf("执行取消订单退款请求错误%v", err)
//		return err
//	}
//
//	ps := model.RefundResult{
//		PayStatus:      res.PayStatus,
//		PayFailMessage: res.PayFailMessage,
//		OrderNo:        res.OrderNo,
//		BizOrderNo:     res.BizOrderNo,
//		Amount:         res.Amount,
//		CouponAmount:   res.CouponAmount,
//		FeeAmount:      res.FeeAmount,
//		ExtendInfo:     res.ExtendInfo,
//	}
//
//	if res.PayStatus == "fail" {
//		return xerr.NewErr(xerr.ErrOrder, nil, "退款失败，请联系客服")
//	}
//
//	err = s.orderS.CancelOrderResult(ctx, order, ps)
//	if err != nil {
//		s.l.Errorf("执行取消订单更新请求结果错误%v", err)
//		return err
//	}
//
//	queryReq := pays.GetOrderStatusReq{
//		BizOrderNo: req.BizOrderNo,
//	}
//	queryRes, err := s.allInPayOrderS.GetOrderStatusS(queryReq)
//	if errors.Is(err, xerr.XerrPayNoOrder) {
//		s.l.Infof("订单取消退款查询支付单，无记录,忽略，orderID:%s,orderNo%s,bizOrderNo:%s", orderID, res.OrderNo, res.BizOrderNo)
//		return nil
//	}
//	s.l.Infof("-------------------%v,%v,%v", queryRes.OrderStatus, queryRes.IsAccountSuccess, "===========================")
//	if queryRes.OrderStatus == 4 && queryRes.IsAccountSuccess == 1 {
//
//		if refundDeliverFee && parentOrder.DeliverFeeRes.FinalDeliverFee != 0 {
//			// 配送费-单独判断处理，单独退
//			s.l.Infof("单独退配送费%s", parentOrder.ID)
//			mnsSendService.NewMNSClient().SendRefundDeliverAlone(parentOrder.ID.Hex())
//		}
//
//		update := bson.M{
//			"$set": bson.M{
//				"pay_status":   model.PayStatusTypePaid,
//				"order_status": model.OrderStatusTypeCancel,
//				"updated_at":   time.Now().UnixMilli(),
//			},
//		}
//		err = s.orderS.UpdateOne(ctx, bson.M{"_id": orderID}, update)
//		if err != nil {
//			s.l.Errorf("执行取消订单更新请求结果错误%v", err)
//			return err
//		}
//
//		if refundDeliverFee {
//			// 代金券
//			if parentOrder.CouponAccountID != primitive.NilObjectID {
//				err = s.couponAccountS.RecoverCoupon(ctx, parentOrder.CouponAccountID)
//				if err != nil {
//					return err
//				}
//			}
//		}
//		if order.CouponAmount > 0 {
//			//	退代金券
//			mnsSendService.NewMNSClient().SendCancelOrderCoupon(order.ID.Hex())
//		}
//
//		//	 检查是否需要激活
//		if order.CouponAccount.CouponID.Hex() == model.NewUserInviteCouponID {
//			mnsSendService.NewMNSClient().SendUpdateInviteStatus(order.UserID, model.InviteStatusFailed)
//		}
//
//		if order.OrderType != model.OrderTypeRetail {
//			mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderCancel, order.UserID, parentOrder.ID, order.ProductTotalAmount/100)
//		}
//
//		if order.PayMethod == model.PayMethodTypeBalance {
//			mnsSendService.NewMNSClient().SendCreateRecord(model.BuyerBalanceRecordTypeOrderCancel, order.BuyerID, order.ID, order.PaidAmount)
//		}
//
//		return nil
//	}
//	mnsSendService.NewMNSClient().SendCancel(orderID.Hex())
//
//	return nil
//}

func (s orderRefundService) YeeCancelOrder(ctx context.Context, order model.Order) error {
	now := time.Now()
	created := time.UnixMilli(order.CreatedAt)
	if now.Sub(created).Seconds() < 60 {
		d := 60 - now.Sub(created).Seconds()
		return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("请%d秒后取消", int(d)))
	}

	parentOrder, err := s.parentOrderS.Get(ctx, order.ParentOrderID)
	if err != nil {
		return err
	}

	if order.OrderStatus > model.OrderStatusTypeToStockUp {
		//已经备货了
		return xerr.NewErr(xerr.ErrParamError, nil, "取消失败，订单已备货")
	}
	if order.OrderStatus == model.OrderStatusTypeCancel || order.OrderStatus == model.OrderStatusTypeClosed {
		return xerr.NewErr(xerr.ErrParamError, nil, "订单已关闭，请刷新")
	}

	if order.YeeRefundResult.OrderId != "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "退款中，请稍等")
	}

	milli := now.UnixMilli()

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")

	//reqOrderID := util.NewUUIDNum()
	reqRefundRequestId := util.NewUUIDNum()

	yopRequest.AddParam("refundRequestId", reqRefundRequestId) // 商户退款请求号

	parentMerchantNo := parentOrder.YeeWechatResult.ParentMerchantNo
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	var merchantNo string
	var oriOrderID string

	if parentOrder.PayMethod == model.PayMethodTypeYeeWechat {
		merchant, err := s.yeeMerchantS.GetBySupplier(ctx, order.SupplierID)
		if err != nil {
			return err
		}
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == merchant.MerchantNo {
				merchantNo = sub.MerchantNo
				oriOrderID = sub.OrderId
				break
			}
		}
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		merchantNo = parentMerchantNo
		oriOrderID = parentOrder.YeeWechatResult.OrderID
	}

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmount := order.PaidAmount
	paidAmountStr := util.DealMoneyToYuanStr(paidAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	yopRequest.AddParam("description", "订单取消") // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTradeRefundCancel) // 接收支付结果的通知地址

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}
	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("退款信息：%s", string(marshal))

	var r model.YeeRefundRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	res := model.YeeRefundResult{
		OrderId:          oriOrderID,
		RefundRequestId:  reqRefundRequestId,
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		RefundAmount:     order.PaidAmount,
		Status:           "PROCESSING",
	}

	err = s.orderS.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"yee_refund_result": res,
			"order_status":      model.OrderStatusTypeCancel,
			"updated_at":        milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

// DoRefundYee 退款-易宝
func (s orderRefundService) DoRefundYee(ctx context.Context, order model.OrderRefund) error {
	// 以审核金额为准
	if order.IsWithdraw {
		s.l.Warnf("该售后订单%s已被撤销，取消退款", order.ID.Hex())
		return nil
	}
	milli := time.Now().UnixMilli()
	refundAmount := order.AuditAmount + order.TotalServiceFee + order.TotalWarehouseLoadFee

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	reqRefundRequestId := util.NewUUIDNum()
	yopRequest.AddParam("refundRequestId", reqRefundRequestId) // 商户退款请求号

	parentMerchantNo := order.YeeRefundResult.ParentMerchantNo
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	merchantNo := order.YeeRefundResult.MerchantNo
	oriOrderID := order.YeeRefundResult.OrderId

	if order.PayMethod == model.PayMethodTypeYeeBalance {
		merchantNo = parentMerchantNo
		oriOrderID = order.YeeRefundResult.OrderId
	}

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmount := refundAmount
	paidAmountStr := util.DealMoneyToYuanStr(paidAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	description := "品控退款"
	notifyUrl := global.NotifyUrlYeePayTradeRefundQuality

	if order.RefundType == model.RefundTypeAfterSale {
		description = "售后退款"
		notifyUrl = global.NotifyUrlYeePayTradeRefundAfterSale
	}

	yopRequest.AddParam("description", description) // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+notifyUrl) // 接收支付结果的通知地址

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return err
	}
	_ = yopResp

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("退款信息：%s", string(marshal))

	var r model.YeeRefundRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		zap.S().Errorf("退款信息：%s", string(yopResp.Content))
		return nil
	}

	res := model.YeeRefundResult{
		OrderId:          oriOrderID,
		RefundRequestId:  reqRefundRequestId,
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		RefundAmount:     refundAmount,
		Status:           "PROCESSING",
	}

	err = s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"yee_refund_result": res,
			"updated_at":        milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s orderRefundService) CancelOrder(ctx context.Context, orderID primitive.ObjectID) error {
	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}

	now := time.Now()
	created := time.UnixMilli(order.CreatedAt)
	if now.Sub(created).Seconds() < 60 {
		d := 60 - now.Sub(created).Seconds()
		return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("请%d秒后取消", int(d)))
	}

	parentOrder, err := s.parentOrderS.Get(ctx, order.ParentOrderID)
	if err != nil {
		return err
	}

	if order.OrderStatus > model.OrderStatusTypeToStockUp {
		//已经备货了
		return xerr.NewErr(xerr.ErrParamError, nil, "取消失败，订单已备货")
	}
	if order.OrderStatus == model.OrderStatusTypeCancel || order.OrderStatus == model.OrderStatusTypeClosed {
		return xerr.NewErr(xerr.ErrParamError, nil, "订单已关闭，请刷新")
	}

	orders, err := s.orderS.ListByParentOrderID(ctx, parentOrder.ID)
	if err != nil {
		return err
	}

	refundDeliverFee := true
	for _, o := range orders {
		if o.OrderStatus != model.OrderStatusTypeCancel && o.ID != orderID {
			refundDeliverFee = false
			break
		}
	}

	if refundDeliverFee {
		zap.S().Errorf("需要退配送费：订单：%s", order.ID.Hex())
	}

	if order.YeeRefundResult.OrderId != "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "退款中，请稍等")
	}

	milli := now.UnixMilli()

	svc := global.PayRefund

	tradeNo := util.NewUUIDNum()

	req := refunddomestic.CreateRequest{
		TransactionId: core.String(parentOrder.WXPayResult.TransactionID),
		//OutTradeNo:  core.String(tradeNo), // 与上 二选一
		OutRefundNo: core.String(tradeNo),
		//Reason:      core.String(""),
		//FundsAccount:      core.String(""), // 若传递此参数则使用对应的资金账户退款，否则默认使用未结算资金退款（仅对老资金流商户适用） 枚举值： AVAILABLE：可用余额账户
		NotifyUrl: core.String(global.BackHost + global.NotifyUrlRefundNormalOrderCancel),
		Amount: &refunddomestic.AmountReq{
			Total:    core.Int64(int64(parentOrder.WXPayResult.PayerAmount)), // 原订单
			Refund:   core.Int64(int64(order.PaidAmount)),
			Currency: core.String("CNY"),
		},
	}
	resp, apiResult, err := svc.Create(ctx, req)
	if err != nil {
		return err
	}
	_ = apiResult

	tempAmount := *resp.Amount

	var from []model.RefundFrom

	for _, item := range tempAmount.From {
		from = append(from, model.RefundFrom{
			Account: string(*item.Account),
			Amount:  int(*item.Amount),
		})
	}

	amount := model.RefundAmount{
		Total:            int(*tempAmount.Total),
		Refund:           int(*tempAmount.Refund),
		PayerTotal:       int(*tempAmount.PayerTotal),
		PayerRefund:      int(*tempAmount.PayerRefund),
		SettlementRefund: int(*tempAmount.SettlementRefund),
		SettlementTotal:  int(*tempAmount.SettlementTotal),
		DiscountRefund:   int(*tempAmount.DiscountRefund),
		Currency:         *tempAmount.Currency,
		//RefundFee: int(*tempAmount.),
		From: from,
	}

	cancelResult := model.WXRefundResult{
		RefundID:            *resp.RefundId,
		OutRefundNo:         *resp.OutRefundNo,
		TransactionId:       *resp.TransactionId,
		OutTradeNo:          *resp.OutTradeNo,
		Channel:             *resp.Channel,
		UserReceivedAccount: *resp.UserReceivedAccount,
		//SuccessTime:     *resp.SuccessTime,
		//CreateTime:     *resp.CreateTime,
		Status:       *resp.Status,
		FundsAccount: *resp.FundsAccount,
		Amount:       amount,
	}

	respMarshal, err := json.Marshal(resp)
	if err != nil {
		return err
	}

	zap.S().Warnf("退款信息：%s", string(respMarshal))

	err = s.orderS.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"wx_cancel_result": cancelResult,
			"order_status":     model.OrderStatusTypeCancel,
			"updated_at":       milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s orderRefundService) RefundDeliver(ctx context.Context, parentOrderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("RefundDeliver error:%v", err)
			return
		}
	}()
	id, err := util.ConvertToObjectWithNote(parentOrderID, "")
	if err != nil {
		return err
	}

	parentOrder, err := s.parentOrderS.Get(ctx, id)
	if err != nil {
		return err
	}

	if parentOrder.DeliverFeeRes.FinalDeliverFee == 0 {
		s.l.Infof("订单%s，配送费为0，跳过退配送费", parentOrderID)
		return nil
	}

	if parentOrderID == "662ec4a1026f8efadf5c1c0a" {
		return nil
	}

	//var deliverAmount int

	//orderDetailS, err := s.allInPayOrderS.GetOrderDetailS(pays.GetOrderDetailReq{
	//	BizOrderNo: parentOrder.BizOrderNo,
	//})
	//if err != nil {
	//	s.l.Errorf("查询订单详情错误%v", err.Error())
	//	return err
	//}
	//
	//orders, err := s.orderS.ListByParentOrderID(ctx, parentOrder.ID)
	//if err != nil {
	//	return err
	//}

	//var receiverBizUserID string
	//split := strings.Split(orderDetailS.ExtendInfo, "deliver")
	//if len(split) == 2 {
	//	receiverBizUserID = split[1]
	//	// 配送费
	//	deliverAmount =
	//}

	amount := parentOrder.DeliverFeeRes.FinalDeliverFee

	var rList []pays.RefundItem
	rList = append(rList, pays.RefundItem{
		//AccountSetNo:  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）
		BizUserId: parentOrder.DeliverReceiveBizUserID,
		Amount:    amount,
	})
	req := pays.RefundReq{
		BizOrderNo:    util.NewUUID(),
		OriBizOrderNo: parentOrder.BizOrderNo,
		//OriOrderNo:    parentOrder.BizOrderNoResult.OrderNo,
		BizUserId:     parentOrder.BizOrderNoResult.BizUserId,
		RefundType:    "D0",
		RefundList:    rList,
		BackUrl:       global.BackHost + global.BackUrlRefundDeliver,
		Amount:        amount,
		CouponAmount:  0,
		FeeAmount:     0,
		RefundAccount: "TLT",
		ExtendInfo:    "单独退配送费" + parentOrderID,
	}
	res, err := s.allInPayOrderS.RefundS(req)
	if err != nil {
		s.l.Errorf("执行取消订单退款请求错误%v", err)
		return err
	}

	ps := model.RefundResult{
		PayStatus:      res.PayStatus,
		PayFailMessage: res.PayFailMessage,
		OrderNo:        res.OrderNo,
		BizOrderNo:     res.BizOrderNo,
		Amount:         res.Amount,
		CouponAmount:   res.CouponAmount,
		FeeAmount:      res.FeeAmount,
		ExtendInfo:     res.ExtendInfo,
	}

	s.l.Infof("退配送费------>")
	reqMarshall, _ := json.Marshal(req)
	resMarshall, _ := json.Marshal(res)
	s.l.Infof("调用支付信息，订单：%s,请求：%s，响应：%s", parentOrderID, string(reqMarshall), string(resMarshall))
	s.l.Infof("<--------退配送费")

	if res.PayStatus == "fail" {
		s.l.Info(ps)
		s.l.Errorf("退款失败%v", res.PayFailMessage)
		return xerr.NewErr(xerr.ErrOrder, nil, "退款失败，请联系客服")
	}

	if parentOrder.PayMethod == model.PayMethodTypeBalance {
		//mnsSendService.NewMNSClient().SendCreateRecord(model.BuyerBalanceRecordTypeDeliverFee, parentOrder.BuyerID, parentOrder.ID, res.Amount)
	}

	return nil
}

func (s orderRefundService) YeeRefundDeliver(ctx context.Context, parentOrderID primitive.ObjectID) error {
	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
	if err != nil {
		return err
	}

	orders, err := s.orderS.ListByParentOrderID(ctx, parentOrder.ID)
	if err != nil {
		return err
	}

	refundDeliverFee := true
	for _, o := range orders {
		if o.OrderStatus != model.OrderStatusTypeFinish && o.OrderStatus != model.OrderStatusTypeCancel {
			//未取消
			refundDeliverFee = false
			break
		}
	}

	if !refundDeliverFee {
		return nil
	}

	if parentOrder.DeliverFeeRes.FinalDeliverFee == 0 {
		s.l.Infof("订单%s，配送费为0，跳过退配送费", parentOrderID.Hex())
		return nil
	}

	amount := parentOrder.DeliverFeeRes.FinalDeliverFee

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")

	reqRefundRequestId := util.NewUUIDNum()

	yopRequest.AddParam("refundRequestId", reqRefundRequestId) // 商户退款请求号

	parentMerchantNo := parentOrder.YeeWechatResult.ParentMerchantNo
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	var merchantNo string
	var oriOrderID string

	if parentOrder.PayMethod == model.PayMethodTypeYeeWechat {
		merchant, err := s.yeeMerchantS.GetYeeByPoint(ctx, parentOrder.ServicePointID)
		if err != nil {
			return err
		}
		_ = merchant
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == merchant.MerchantNo {
				//if sub.MerchantNo == "10090781432" {
				merchantNo = sub.MerchantNo
				oriOrderID = sub.OrderId
				break
			}
		}
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		merchantNo = parentMerchantNo
		oriOrderID = parentOrder.YeeWechatResult.OrderID
	}

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmount := amount
	paidAmountStr := util.DealMoneyToYuanStr(paidAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	yopRequest.AddParam("description", "配送费退款") // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTradeRefundDeliverFee) // 接收支付结果的通知地址

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}
	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("退款信息：%s", string(marshal))

	var r model.YeeRefundRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	res := model.YeeRefundResult{
		OrderId:          oriOrderID,
		RefundRequestId:  reqRefundRequestId,
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		RefundAmount:     amount,
		Status:           "PROCESSING",
	}

	milli := time.Now().UnixMilli()
	err = s.parentOrderS.UpdateOne(ctx, bson.M{"_id": parentOrderID}, bson.M{
		"$set": bson.M{
			"yee_refund_deliver_result": res,
			"updated_at":                milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

//
//// RefundPartDeliverManual 手动退还部分配送费
//func (s orderRefundService) RefundPartDeliverManual(ctx context.Context, parentOrderID primitive.ObjectID) error {
//	//id, err := util.ConvertToObjectWithNote(parentOrderID, "")
//	//if err != nil {
//	//	return err
//	//}
//
//	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
//	if err != nil {
//		return err
//	}
//
//	if parentOrder.DeliverFeeRes.IsSubsidy {
//		return xerr.NewErr(xerr.ErrParamError, nil, "已经补贴，无法手动减免配送费")
//	}
//
//	if parentOrder.PayStatus != model.PayStatusTypePaid {
//		return xerr.NewErr(xerr.ErrParamError, nil, "订单未支付，无法手动减免")
//	}
//
//	orders, err := s.orderS.ListByParentOrderID(ctx, parentOrder.ID)
//	if err != nil {
//		return err
//	}
//
//	orderDetailS, err := s.allInPayOrderS.GetOrderDetailS(pays.GetOrderDetailReq{
//		BizOrderNo: parentOrder.BizOrderNo,
//	})
//	if err != nil {
//		s.l.Errorf("查询订单详情错误%v", err.Error())
//		return err
//	}
//
//	// 配送费
//	deliverAmount := parentOrder.DeliverFeeRes.FinalDeliverFee
//	//finalDeliverAmount := parentOrder.DeliverFeeRes.TotalDeliverFee - parentOrder.DeliverFeeRes.SubsidyDeliverFee
//
//	var receiverBizUserID string
//	split := strings.Split(orderDetailS.ExtendInfo, "deliver")
//	if len(split) == 2 {
//		receiverBizUserID = split[1]
//
//	}
//
//	amount := deliverAmount
//
//	var rList []pays.RefundItem
//	rList = append(rList, pays.RefundItem{
//		//AccountSetNo:  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）
//		BizUserId: receiverBizUserID,
//		Amount:    amount,
//	})
//	req := pays.RefundReq{
//		BizOrderNo:    util.NewUUID(),
//		OriBizOrderNo: parentOrder.BizOrderNo,
//		//OriOrderNo:    parentOrder.BizOrderNoResult.OrderNo,
//		BizUserId:     orders[0].PayBizUserID,
//		RefundType:    "D0",
//		RefundList:    rList,
//		BackUrl:       global.BackHost + global.BackUrlRefundDeliver,
//		Amount:        amount,
//		CouponAmount:  0,
//		FeeAmount:     0,
//		RefundAccount: "TLT",
//		ExtendInfo:    "收到退退配送费" + parentOrder.ID.Hex(),
//	}
//	res, err := s.allInPayOrderS.RefundS(req)
//	if err != nil {
//		s.l.Errorf("执行取消订单退款请求错误%v", err)
//		return err
//	}
//
//	ps := model.RefundResult{
//		PayStatus:      res.PayStatus,
//		PayFailMessage: res.PayFailMessage,
//		OrderNo:        res.OrderNo,
//		BizOrderNo:     res.BizOrderNo,
//		Amount:         res.Amount,
//		CouponAmount:   res.CouponAmount,
//		FeeAmount:      res.FeeAmount,
//		ExtendInfo:     res.ExtendInfo,
//	}
//
//	s.l.Infof("手动退配送费------>")
//	reqMarshall, _ := json.Marshal(req)
//	resMarshall, _ := json.Marshal(res)
//	s.l.Infof("调用支付信息，订单：%s,请求：%s，响应：%s", parentOrderID, string(reqMarshall), string(resMarshall))
//	s.l.Infof("<--------手动退配送费")
//
//	if res.PayStatus == "fail" {
//		s.l.Info(ps)
//		s.l.Errorf("退款失败%v", res.PayFailMessage)
//		return xerr.NewErr(xerr.ErrOrder, nil, "退款失败，请联系客服")
//	}
//
//	// 更新订单为已享受补贴
//	//update := bson.M{
//	//	"deliver_fee_res.is_subsidy":        true,
//	//	"deliver_fee_res.final_deliver_fee": finalDeliverAmount,
//	//	"updated_at":                        time.Now().UnixMilli(),
//	//}
//	//
//	//err = s.parentOrderS.UpdateOne(ctx, bson.M{"_id": parentOrder.ID}, bson.M{"$set": update})
//	//if err != nil {
//	//	return err
//	//}
//	//
//	//for _, order := range orders {
//	//
//	//	err = s.orderS.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{"$set": update})
//	//	if err != nil {
//	//		return err
//	//	}
//	//}
//
//	return nil
//}

// Audit 审核
func (s orderRefundService) Audit(ctx context.Context, refund model.OrderRefund, auditStatus model.AuditStatusType, auditAmount int, auditNote string, auditorType model.AuditorType) error {
	if auditStatus == model.AuditStatusTypePass && auditAmount < 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写退款价格")
	}

	now := time.Now().UnixMilli()

	refund.AuditAmount = auditAmount

	update := bson.M{
		"audit_status": auditStatus,
		"audit_amount": auditAmount,
		"audit_note":   auditNote,
		"updated_at":   now,
		"auditor_type": auditorType,
	}

	if auditorType == model.AuditorTypeSupplier {
		update["supplier_audit_at"] = now
		update["supplier_audit_content"] = auditNote
		if auditStatus == model.AuditStatusTypePass {
			update["supplier_audit_amount"] = auditAmount
		}
	}

	if auditorType == model.AuditorTypePlatform {
		update["platform_audit_at"] = now
		update["is_complete"] = true
	}

	err := s.orderRefundDao.UpdateOne(ctx, bson.M{
		"_id":         refund.ID,
		"is_withdraw": false,
	}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	//	审核通过
	if auditorType == model.AuditorTypePlatform && auditStatus == model.AuditStatusTypePass {
		seconds := 5
		mnsSendService.NewMNSClient().SendRefundComplete(refund.ID, int64(seconds))
	}

	return err
}

// DoPayRefundBalance 执行支付退款
func (s orderRefundService) DoPayRefundBalance(ctx context.Context, order model.OrderRefund) error {
	// 以审核金额为准
	if order.IsWithdraw {
		s.l.Warnf("该售后订单%s已被撤销，取消退款", order.ID.Hex())
		return nil
	}
	var amount int

	amount = order.AuditAmount + order.TotalServiceFee + order.TotalWarehouseLoadFee

	var rList []pays.RefundItem
	rList = append(rList, pays.RefundItem{
		//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
		BizUserId: order.OriReceiverBizUserID,
		Amount:    amount,
	})
	req := pays.RefundReq{
		BizOrderNo:    order.BizOrderNo,
		OriBizOrderNo: order.OriBizOrderNo,
		OriOrderNo:    order.OriOrderNo,
		BizUserId:     order.OriPayBizUserID, // 收款人-原订单的付款方
		RefundType:    "D0",
		RefundList:    rList,
		BackUrl:       global.BackHost + global.BackUrlRefund,
		Amount:        amount,
		//CouponAmount:  0,
		FeeAmount:     0,
		RefundAccount: "TLT",
		ExtendInfo:    "退款单id：" + order.ID.Hex(),
	}

	res, err := s.allInPayOrderS.RefundS(req)
	if errors.Is(err, xerr.XerrPayOrderProcessing) {
		s.l.Infof("退款订单%s，原订单%s，原商户订单号%s处理中，执行下次退款", order.ID.Hex(), order.OrderID.Hex(), order.OriBizOrderNo)
		return xerr.XerrPayOrderProcessing
	}
	if err != nil {
		s.l.Errorf("s.allInPayOrderS.RefundS(req) 错误%s", err.Error())
		return err
	}
	if res.PayStatus == "fail" {
		s.l.Errorf("调起退款失败fail%s，跳过本次", res.PayFailMessage)
		return nil
	}

	ps := model.RefundResult{
		PayStatus:      res.PayStatus,
		PayFailMessage: res.PayFailMessage,
		OrderNo:        res.OrderNo,
		BizOrderNo:     res.BizOrderNo,
		Amount:         res.Amount,
		CouponAmount:   res.CouponAmount,
		FeeAmount:      res.FeeAmount,
		ExtendInfo:     res.ExtendInfo,
	}
	err = s.UpdatePayResult(ctx, order.ID, ps)
	if err != nil {
		s.l.Errorf("更新调用退款接口错误%v", err)
		return err
	}
	return nil
}

// DoPayRefundJSAPI 执行支付退款
func (s orderRefundService) DoPayRefundJSAPI(ctx context.Context, order model.OrderRefund) error {
	// 以审核金额为准
	if order.IsWithdraw {
		s.l.Warnf("该售后订单%s已被撤销，取消退款", order.ID.Hex())
		return nil
	}
	refundAmount := order.AuditAmount + order.TotalServiceFee + order.TotalWarehouseLoadFee

	milli := time.Now().UnixMilli()

	svc := global.PayRefund

	tradeNo := util.NewUUIDNum()

	req := refunddomestic.CreateRequest{
		TransactionId: core.String(order.OriTransactionID),
		//OutTradeNo:  core.String(tradeNo), // 与上 二选一
		OutRefundNo: core.String(tradeNo),
		//Reason:      core.String(""),
		//FundsAccount:      core.String(""), // 若传递此参数则使用对应的资金账户退款，否则默认使用未结算资金退款（仅对老资金流商户适用） 枚举值： AVAILABLE：可用余额账户
		NotifyUrl: core.String(global.BackHost + global.NotifyUrlRefundNormalOrderRefund),
		Amount: &refunddomestic.AmountReq{
			Total:    core.Int64(int64(order.OriTotalAmount)), // 原订单
			Refund:   core.Int64(int64(refundAmount)),
			Currency: core.String("CNY"),
		},
	}
	resp, apiResult, err := svc.Create(ctx, req)
	if err != nil {
		return err
	}
	_ = apiResult

	tempAmount := *resp.Amount

	var from []model.RefundFrom

	for _, item := range tempAmount.From {
		from = append(from, model.RefundFrom{
			Account: string(*item.Account),
			Amount:  int(*item.Amount),
		})
	}

	amount := model.RefundAmount{
		Total:            int(*tempAmount.Total),
		Refund:           int(*tempAmount.Refund),
		PayerTotal:       int(*tempAmount.PayerTotal),
		PayerRefund:      int(*tempAmount.PayerRefund),
		SettlementRefund: int(*tempAmount.SettlementRefund),
		SettlementTotal:  int(*tempAmount.SettlementTotal),
		DiscountRefund:   int(*tempAmount.DiscountRefund),
		Currency:         *tempAmount.Currency,
		//RefundFee: int(*tempAmount.),
		From: from,
	}

	cancelResult := model.WXRefundResult{
		RefundID:            *resp.RefundId,
		OutRefundNo:         *resp.OutRefundNo,
		TransactionId:       *resp.TransactionId,
		OutTradeNo:          *resp.OutTradeNo,
		Channel:             *resp.Channel,
		UserReceivedAccount: *resp.UserReceivedAccount,
		//SuccessTime:     *resp.SuccessTime,
		//CreateTime:     *resp.CreateTime,
		Status:       *resp.Status,
		FundsAccount: *resp.FundsAccount,
		Amount:       amount,
	}

	respMarshal, err := json.Marshal(resp)
	if err != nil {
		return err
	}

	zap.S().Warnf("退款信息：%s", string(respMarshal))

	err = s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"wx_refund_result": cancelResult,
			"updated_at":       milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

//
//// DoPayRefundRefresh 刷新执行支付退款-贷方余额不足
//func (s orderRefundService) DoPayRefundRefresh(ctx context.Context, order model.OrderRefund) error {
//	s.l.Warnf("刷新执行支付退款-贷方余额不足，biz_order_no：%v,退款单ID：%s", order.OriBizOrderNo, order.ID.Hex())
//
//	// 更新bizOrderNo
//	order.BizOrderNo = util.NewUUID()
//	err := s.orderRefundDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
//		"$set": bson.M{
//			"biz_order_no": order.BizOrderNo,
//		},
//	})
//	if err != nil {
//		s.l.Errorf("更新biz_order_no错误%v", err)
//		return err
//	}
//
//	amount := order.AuditAmount + order.TotalTransportFee
//	var rList []pays.RefundItem
//	rList = append(rList, pays.RefundItem{
//		//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
//		BizUserId: order.OriReceiverBizUserID,
//		Amount:    amount,
//	})
//	req := pays.RefundReq{
//		BizOrderNo:    order.BizOrderNo,
//		OriBizOrderNo: order.OriBizOrderNo,
//		OriOrderNo:    order.OriOrderNo,
//		BizUserId:     order.OriPayBizUserID, // 收款人-原订单的付款方
//		RefundType:    "D0",
//		RefundList:    rList,
//		BackUrl:       global.BackHost + global.BackUrlRefund,
//		Amount:        amount,
//		CouponAmount:  0,
//		FeeAmount:     0,
//		RefundAccount: "TLT",
//		ExtendInfo:    "退款：" + order.Reason,
//	}
//	res, err := s.allInPayOrderS.RefundS(req)
//	if errors.Is(err, xerr.XerrPayOrderProcessing) {
//		s.l.Infof("刷新执行支付退款 退款订单%s，原订单%s，原商户订单号%s处理中，执行下次退款", order.ID.Hex(), order.OrderID.Hex(), order.OriBizOrderNo)
//		return xerr.XerrPayOrderProcessing
//	}
//	if err != nil {
//		s.l.Errorf("刷新执行支付退款 s.allInPayOrderS.RefundS(req) 错误%s", err.Error())
//		return err
//	}
//	if res.PayStatus == "fail" {
//		s.l.Errorf("刷新执行支付退款 调起退款失败fail%s", res.PayFailMessage)
//		return xerr.NewErr(xerr.ErrParamError, nil, res.PayFailMessage)
//	}
//
//	ps := model.RefundResult{
//		PayStatus:      res.PayStatus,
//		PayFailMessage: res.PayFailMessage,
//		OrderNo:        res.OrderNo,
//		BizOrderNo:     res.BizOrderNo,
//		Amount:         res.Amount,
//		CouponAmount:   res.CouponAmount,
//		FeeAmount:      res.FeeAmount,
//		ExtendInfo:     res.ExtendInfo,
//	}
//	err = s.UpdatePayResult(ctx, order.ID, ps)
//	if err != nil {
//		s.l.Errorf("刷新执行支付退款 更新调用退款接口错误%v", err)
//		return err
//	}
//	return nil
//}

// BackShipSettle   发货结算
func (s orderRefundService) BackShipSettle(ctx context.Context, order model.Order) ([]model.ProductSettle, error) {
	_ = ctx
	var list []model.ProductSettle

	for _, p := range order.ProductList {
		refund := backSettleData(order, p)
		list = append(list, refund)
	}

	marshal, _ := json.Marshal(list)
	zap.S().Infof("结算单列表%v", string(marshal))

	return list, nil
}

func backQualityRefundServiceFee(finalAmount, productAmount, totalServiceFee int) int {
	if totalServiceFee == 0 {
		return 0
	}
	finalAmountDe := decimal.NewFromInt(int64(finalAmount))
	productAmountDe := decimal.NewFromInt(int64(productAmount))
	totalServiceFeeDe := decimal.NewFromInt(int64(totalServiceFee))

	percent := finalAmountDe.Div(productAmountDe)

	amount := totalServiceFeeDe.Mul(percent).Round(0)

	return int(amount.IntPart())
}

func toDec(num int) decimal.Decimal {
	return decimal.NewFromInt(int64(num))
}

// ActiveCheckRefund 主动查询订单退款状态
func (s orderRefundService) ActiveCheckRefund(ctx context.Context, orderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ActiveCheckRefund error:%v", err)
			return
		}
	}()

	id, err := util.ConvertToObjectWithNote(orderID, "ActiveCheckRefund orderID")
	if err != nil {
		return err
	}
	order, err := s.orderS.Get(ctx, id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil
	}
	if err != nil {
		return err
	}

	if order.OrderStatus == model.OrderStatusTypeCancel {
		s.l.Infof("订单已经取消，忽略%s", orderID)
		return nil
	}

	req := pays.GetOrderStatusReq{
		OrderNo:    order.CancelResult.OrderNo,
		BizOrderNo: order.CancelResult.BizOrderNo,
	}
	res, err := s.allInPayOrderS.GetOrderStatusS(req)
	if errors.Is(err, xerr.XerrPayNoOrder) {
		s.l.Errorf("订单取消退款查询支付单，无记录，orderID:%s,orderNo%s,bizOrderNo:%s", orderID, req.OrderNo, req.BizOrderNo)
		return nil
	}
	// 4 成功
	payStatus1 := model.PayStatusTypePaidButRefund
	orderStatus := model.OrderStatusTypeCancel

	if res.OrderStatus == 3 {
		s.l.Errorf("主动检查订单是否支付(退款，返回交易发生错误%v", res.ErrorMessage)
		// 交易发生错误
		payStatus1 = model.PayStatusTypeFail
		orderStatus = model.OrderStatusTypeCancel
	}

	s.l.Errorf("主动检查订单是否支付(退款，更新%v", res.OrderStatus)
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		err = s.orderS.UpdateOrderStatus(sessCtx, id, payStatus1, orderStatus)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

// NotifyRefundStatus 回调
func (s orderRefundService) NotifyRefundStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知
	if res.Status == "OK" {
		//“OK”标识支付成功；
		refund, err := s.GetByBizOrderNo(ctx, res.BizOrderNo)
		if err != nil {
			return err
		}
		filter := bson.M{"biz_order_no": res.BizOrderNo}
		err = s.orderRefundDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
			"refund_result.pay_status":                 "success",
			"refund_result.pay_interface_out_trade_no": res.PayInterfaceOutTradeNo,
			"refund_result.pay_datetime":               res.PayDatetime,
			"updated_at":                               time.Now().UnixMilli(),
		}})
		if err != nil {
			s.l.Error("更新退款订单状态错误:", err)
			return err
		}

		mnsSendService.NewMNSClient().SendSyncRefundData(refund.OrderID.Hex())

		if refund.RefundType == model.RefundTypeAfterSale {
		}

		if refund.RefundType == model.RefundTypeQuality || refund.RefundType == model.RefundTypeAfterSale {
			if refund.RefundType == model.RefundTypeQuality {
				mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderQuality, refund.BuyerID, refund.ParentOrderID, (refund.AuditAmount)/100)
			}
			if refund.RefundType == model.RefundTypeAfterSale {
				mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderAfterSale, refund.BuyerID, refund.ParentOrderID, (refund.AuditAmount)/100)
			}

			if refund.PayMethod == model.PayMethodTypeBalance {
				// 余额
				if refund.RefundType == model.RefundTypeQuality {
					// 品控
					//mnsSendService.NewMNSClient().SendCreateRecord(model.BuyerBalanceRecordTypeOrderQuality, refund.BuyerID, refund.ID, res.Amount)
				}
				if refund.RefundType == model.RefundTypeAfterSale {
					// 售后
					//mnsSendService.NewMNSClient().SendCreateRecord(model.BuyerBalanceRecordTypeOrderAfterSale, refund.BuyerID, refund.ID, res.Amount)
				}

			}
		}

	}
	return nil
}

func (s orderRefundService) NotifyCancel(ctx context.Context, content *model.RefundNotify) error {
	filter := bson.M{
		"wx_cancel_result.out_trade_no": content.OutTradeNo,
	}

	order, err := s.orderS.GetByCus(ctx, filter)
	if err != nil {
		return err
	}

	if order.WXCancelResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"wx_cancel_result.transaction_id":        content.TransactionId,
		"wx_cancel_result.refund_id":             content.RefundId,
		"wx_cancel_result.status":                content.RefundStatus,
		"wx_cancel_result.user_received_account": content.UserReceivedAccount,
		"wx_cancel_result.success_time":          content.SuccessTime,
		"wx_cancel_result.out_refund_no":         content.OutRefundNo,
		"wx_cancel_result.total":                 content.Amount.Total,
		"wx_cancel_result.refund":                content.Amount.Refund,
		"wx_cancel_result.payer_total":           content.Amount.PayerTotal,
		"wx_cancel_result.payer_refund":          content.Amount.PayerRefund,
		"wx_cancel_result.source":                content,
	}

	if content.RefundStatus == "SUCCESS" {
		milli := time.Now().UnixMilli()

		update["order_status"] = model.OrderStatusTypeCancel
		update["updated_at"] = milli
	}

	err = s.orderS.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s orderRefundService) YeeNotifyCancel(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	filter := bson.M{
		"yee_refund_result.refund_request_id": notify.RefundRequestId,
	}
	order, err := s.orderS.GetByCus(ctx, filter)
	if err != nil {
		return err
	}

	if order.YeeRefundResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"yee_refund_result.notify_payment_method":      notify.PaymentMethod,
		"yee_refund_result.notify_refund_success_date": notify.RefundSuccessDate,
		"yee_refund_result.status":                     notify.Status,
		"yee_refund_result.notify_error_message":       notify.ErrorMessage,
		"yee_refund_result.notify_unique_refund_no":    notify.UniqueOrderNo,
	}

	if notify.Status == "SUCCESS" {
		milli := time.Now().UnixMilli()
		update["order_status"] = model.OrderStatusTypeCancel
		update["updated_at"] = milli
	}

	err = s.orderS.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if order.PayMethod == model.PayMethodTypeYeeBalance {
		// 取消订单
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeOrderCancel, order.BuyerID, order.ID, order.PaidAmount)
	}

	// 检查是否退配送费
	err = s.YeeRefundDeliver(ctx, order.ParentOrderID)
	if err != nil {
		zap.S().Errorf("检查订单退配送费异常：%s", err.Error())
	}

	deliverFeeRuleService.NewDeliverFeeRuleService().RemoveDeliverRecord(ctx, order.Address.AddressID, order.ID)

	return nil
}

func (s orderRefundService) YeeNotifyRefundDeliver(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	parentOrder, err := s.parentOrderS.GetByDeliverRefundReqID(ctx, notify.RefundRequestId)
	if err != nil {
		return err
	}

	if parentOrder.YeeRefundDeliverResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"yee_refund_deliver_result.notify_payment_method":      notify.PaymentMethod,
		"yee_refund_deliver_result.notify_refund_success_date": notify.RefundSuccessDate,
		"yee_refund_deliver_result.status":                     notify.Status,
		"yee_refund_deliver_result.notify_error_message":       notify.ErrorMessage,
		"yee_refund_deliver_result.notify_unique_refund_no":    notify.UniqueOrderNo,
	}

	if notify.Status == "SUCCESS" {
		milli := time.Now().UnixMilli()
		update["updated_at"] = milli
	}

	err = s.parentOrderS.UpdateOne(ctx, bson.M{
		"_id": parentOrder.ID,
	}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		amount := parentOrder.DeliverFeeRes.FinalDeliverFee
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeDeliverFee, parentOrder.BuyerID, parentOrder.ID, amount)
	}

	return nil
}

func (s orderRefundService) YeeNotifyRefundAfterSale(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	filter := bson.M{
		"yee_refund_result.refund_request_id": notify.RefundRequestId,
	}
	refund, err := s.orderRefundDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if refund.YeeRefundResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"yee_refund_result.notify_payment_method":      notify.PaymentMethod,
		"yee_refund_result.notify_refund_success_date": notify.RefundSuccessDate,
		"yee_refund_result.status":                     notify.Status,
		"yee_refund_result.notify_error_message":       notify.ErrorMessage,
		"yee_refund_result.notify_unique_refund_no":    notify.UniqueOrderNo,
	}

	if notify.Status == "SUCCESS" {
		milli := time.Now().UnixMilli()
		update["updated_at"] = milli
	}

	err = s.orderRefundDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if refund.PayMethod == model.PayMethodTypeYeeBalance {
		// 取消订单
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeOrderAfterSale, refund.BuyerID, refund.ID, refund.AuditAmount)
	}

	mnsSendService.NewMNSClient().SendSyncRefundData(refund.OrderID.Hex())

	return nil
}

func (s orderRefundService) YeeNotifyRefundQuality(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	filter := bson.M{
		"yee_refund_result.refund_request_id": notify.RefundRequestId,
	}
	refund, err := s.orderRefundDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if refund.YeeRefundResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"yee_refund_result.notify_payment_method":      notify.PaymentMethod,
		"yee_refund_result.notify_refund_success_date": notify.RefundSuccessDate,
		"yee_refund_result.status":                     notify.Status,
		"yee_refund_result.notify_error_message":       notify.ErrorMessage,
		"yee_refund_result.notify_unique_refund_no":    notify.UniqueOrderNo,
	}

	if notify.Status == "SUCCESS" {
		milli := time.Now().UnixMilli()
		update["updated_at"] = milli
	}

	err = s.orderRefundDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})

	if err != nil {
		return err
	}

	if refund.PayMethod == model.PayMethodTypeYeeBalance {
		// 取消订单
		amount := refund.AuditAmount + refund.TotalServiceFee + refund.TotalWarehouseLoadFee
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeOrderQuality, refund.BuyerID, refund.ID, amount)
	}

	mnsSendService.NewMNSClient().SendSyncRefundData(refund.OrderID.Hex())

	return nil
}

func (s orderRefundService) NotifyRefund(ctx context.Context, content *model.RefundNotify) error {
	refund, err := s.GetByOutRefundNo(ctx, content.OutRefundNo)
	if err != nil {
		return err
	}

	if refund.WXRefundResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"wx_refund_result.transaction_id":        content.TransactionId,
		"wx_refund_result.refund_id":             content.RefundId,
		"wx_refund_result.status":                content.RefundStatus,
		"wx_refund_result.user_received_account": content.UserReceivedAccount,
		"wx_refund_result.success_time":          content.SuccessTime,
		"wx_refund_result.out_refund_no":         content.OutRefundNo,
		"wx_refund_result.total":                 content.Amount.Total,
		"wx_refund_result.refund":                content.Amount.Refund,
		"wx_refund_result.payer_total":           content.Amount.PayerTotal,
		"wx_refund_result.payer_refund":          content.Amount.PayerRefund,
		"wx_refund_result.source":                content,
	}

	milli := time.Now().UnixMilli()
	update["updated_at"] = milli

	filter := bson.M{
		"wx_refund_result.out_refund_no": content.OutRefundNo,
	}

	err = s.orderRefundDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	mnsSendService.NewMNSClient().SendSyncRefundData(refund.OrderID.Hex())

	if refund.RefundType == model.RefundTypeAfterSale {
	}

	if refund.RefundType == model.RefundTypeQuality || refund.RefundType == model.RefundTypeAfterSale {
		if refund.RefundType == model.RefundTypeQuality {
			mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderQuality, refund.BuyerID, refund.ParentOrderID, (refund.AuditAmount)/100)
		}
		if refund.RefundType == model.RefundTypeAfterSale {
			mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeOrderAfterSale, refund.BuyerID, refund.ParentOrderID, (refund.AuditAmount)/100)
		}
	}

	return nil
}

func (s orderRefundService) CountAfterSaleMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int64, error) {
	filter := bson.M{
		"supplier_id": supplierID,
		"deleted_at":  0,
		//"pay_status":   model.PayStatusTypePaid,
		//"order_status": model.OrderStatusTypeFinish,
		"refund_type": model.RefundTypeAfterSale,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	count, err := s.orderRefundDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderRefundService) CountByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"buyer_id":    buyerID,
		"deleted_at":  0,
		"refund_type": 1,
	}

	count, err := s.orderRefundDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderRefundService) CountNum(ctx context.Context, buyerID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"buyer_id":    buyerID,
		"is_complete": false,
		"is_withdraw": false,
		"refund_type": 1,
	}

	count, err := s.orderRefundDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderRefundService) CountByProduct(ctx context.Context, productID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"product_id":  productID,
		"deleted_at":  0,
		"refund_type": 1,
	}

	count, err := s.orderRefundDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

var refundLock sync.Mutex

// SyncRefundData 同步订单的退款数据---品控退款和售后退款
func (s orderRefundService) SyncRefundData(ctx context.Context, orderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("SyncRefundData error:%v", err)
			return
		}
	}()

	return nil
}

// SyncQualityRefundData 同步订单的退款数据---品控退款
func (s orderRefundService) SyncQualityRefundData(ctx context.Context, orderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("SyncRefundData error:%v", err)
			return
		}
	}()
	oid, err := util.ConvertToObjectWithCtx(ctx, orderID)
	if err != nil {
		return err
	}
	order, err := s.orderS.Get(ctx, oid)
	if err != nil {
		return err
	}
	//mp := make(map[primitive.ObjectID][]model.OrderRefund)
	//for _, refund := range list {
	//	if refund.PayMethod == model.PayMethodTypeBalance {
	//		if refund.RefundResult.PayStatus == "success" {
	//			mp[refund.ProductID] = append(mp[refund.ProductID], refund)
	//		}
	//	}
	//
	//	if refund.PayMethod == model.PayMethodTypeWechat {
	//		if refund.WXRefundResult.Status == "SUCCESS" {
	//			mp[refund.ProductID] = append(mp[refund.ProductID], refund)
	//		}
	//	}
	//}

	// 配送费
	parentOrderID := order.ParentOrderID
	orders, err := s.orderS.ListByParentOrderID(ctx, parentOrderID)
	if err != nil {
		s.l.Errorf("查询订单%s错误", parentOrderID.Hex())
		return err
	}

	var i int
	for _, o := range orders {
		if o.OrderRefundAll {
			i++
		}
	}

	if i == len(orders) {
		//	 全退-退运费
		s.l.Infof("CheckOrderHasRefundAll 子订单已全退，现在执行退运费")
		err = s.YeeRefundDeliver(ctx, parentOrderID)
		if err != nil {
			zap.S().Errorf("SyncRefundData 配送费:%s，parentOrderID：%s", err.Error(), parentOrderID.Hex())
		}
	}
	//mnsSendService.NewMNSClient().SendCheckOrderHasRefundAll(orderID)

	return nil
}

// CheckOrderHasRefundAll 检查订单是否全退检查
func (s orderRefundService) CheckOrderHasRefundAll(ctx context.Context, orderID string) error {
	//defer func() {
	//	if err := recover(); err != nil {
	//		zap.S().Errorf("CheckOrderHasRefundAll error:%v", err)
	//		return
	//	}
	//}()
	//oid, err := util.ConvertToObjectWithNote(orderID, "CheckOrderHasRefundAll orderID")
	//if err != nil {
	//	return err
	//}
	//
	//order, err := s.orderS.Get(ctx, oid)
	//if err != nil {
	//	s.l.Errorf("检查订单是否全退检查 查询订单错误%v", err)
	//	return err
	//}
	//
	//f := true
	//for _, i := range order.ProductList {
	//	if i.IsShipRefundAll == false {
	//		if f {
	//			f = false
	//		}
	//	}
	//
	//}
	//
	//if f {
	//	now := time.Now().UnixMilli()
	//	filter := bson.M{
	//		"_id": oid,
	//	}
	//
	//	update := bson.M{
	//		"order_status_record.arrive_time": 0,
	//		"order_status":                    model.OrderStatusTypeFinish,
	//		"order_refund_all":                true,
	//		"updated_at":                      now,
	//	}
	//	s.l.Infof("检查订单是否全退检查 更新为订单已完成，filter::%v,update::::%v", filter, update)
	//
	//	err = s.orderS.UpdateOne(ctx, filter, bson.M{"$set": update})
	//	if err != nil {
	//		s.l.Errorf("检查订单是否全退检查，错误%v", err)
	//		return err
	//	}
	//
	//}
	//
	//if f {
	//	orders, err := s.orderS.ListByParentOrderID(ctx, order.ParentOrderID)
	//	if err != nil {
	//		s.l.Errorf("查询订单%s错误", order.ParentOrderID.Hex())
	//		return err
	//	}
	//
	//	var i int
	//	for _, o := range orders {
	//		if o.OrderStatusRecord.ArriveTime == 0 && o.OrderStatus == model.OrderStatusTypeFinish {
	//			i++
	//		}
	//	}
	//
	//	if i == len(orders) {
	//		//	 全退-退运费
	//		s.l.Infof("CheckOrderHasRefundAll 子订单已全退，现在执行退运费")
	//		mnsSendService.NewMNSClient().SendRefundDeliverAlone(order.ParentOrderID.Hex())
	//	}
	//}

	return nil
}
