package stationService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/stationDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/allInPayUserService"
	"base/service/authenticationService"
	"base/service/bankAccountService"
	"base/service/entityService"
	"base/service/messageService"
	"base/service/routeService"
	"base/service/servicePointCommissionService"
	"base/types"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, userID, servicePointID primitive.ObjectID, req types.StationCreateReq) error
	GetByUser(ctx context.Context, userID primitive.ObjectID) (model.Station, error)
	ListByLocation(ctx context.Context, longitude, latitude float64) ([]model.Station, error)
	ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, isOpen bool) ([]model.Station, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.Station, error)
	UpdateOpen(ctx context.Context, id primitive.ObjectID, status model.OpenStatus) error
	UpdateCommissionRate(ctx context.Context, id primitive.ObjectID, rate int) error
	Identity(ctx context.Context, id primitive.ObjectID, name, number string) error
	BankBind(ctx context.Context, id primitive.ObjectID, mobile, carNo string) error
	UpdateServiceFee(ctx context.Context, id primitive.ObjectID, supplierServiceFee, deliverServiceFee float64) error
	UpdateHeadImg(ctx context.Context, id primitive.ObjectID, shopHeadImg model.FileInfo) error
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Station, int64, error)
	ListCus(ctx context.Context, filter bson.M) ([]model.Station, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Station, error)
	CheckOneExist(ctx context.Context, id primitive.ObjectID) error
}

type stationService struct {
	mdb                     *mongo.Database
	rdb                     *redis.Client
	stationDao              stationDao.DaoInt
	msg                     messageService.ServiceInterface
	servicePointCommissions servicePointCommissionService.ServiceInterface
	routeService            routeService.ServiceInterface
	entityService           entityService.ServiceInterface
	bankAccountS            bankAccountService.ServiceInterface
	authenticationS         authenticationService.ServiceInterface

	AllInPayS     payModule.MemberService
	AllInPayUserS allInPayUserService.ServiceInterface
}

func NewStationService() ServiceInterface {
	return stationService{
		mdb:                     global.MDB,
		rdb:                     global.RDBDefault,
		stationDao:              dao.StationDao,
		msg:                     messageService.NewMessageService(),
		servicePointCommissions: servicePointCommissionService.NewPartnerCommissionService(),
		routeService:            routeService.NewTransportFeeService(),
		entityService:           entityService.NewEntityService(),
		bankAccountS:            bankAccountService.NewBankCardService(),
		authenticationS:         authenticationService.NewAuthenticationService(),
		//warehouseS:              warehouseService.NewWarehouseServiceService(),

		AllInPayS:     payModule.NewMember(),
		AllInPayUserS: allInPayUserService.NewAllInPayUserService(),
	}
}

func (s stationService) Create(ctx context.Context, userID, servicePointID primitive.ObjectID, req types.StationCreateReq) error {
	byUser, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if byUser.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在信息，请勿重复申请")
	}

	now := time.Now().UnixMilli()

	data := model.Station{
		ID:             primitive.NewObjectID(),
		ServicePointID: servicePointID,
		UserID:         userID,
		Name:           req.Name,
		DeliverType:    req.DeliverType,
		ContactUser:    req.ContactUser,
		ContactMobile:  req.ContactMobile,
		Location:       req.Location,
		Address:        req.Address,
		OpenStatus:     model.OpenStatusClosed,
		CommissionRate: req.CommissionRate,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	err = s.stationDao.Create(ctx, data)
	if err != nil {
		return err
	}

	err = s.authenticationS.CreateByStation(ctx, data.ContactMobile, data.UserID, data.ID)
	if err != nil {
		return err
	}

	return nil
}

func (s stationService) UpdateOpen(ctx context.Context, id primitive.ObjectID, status model.OpenStatus) error {
	//station, err := s.Get(ctx, id)
	//if err != nil {
	//	return err
	//}

	//if station.IdentityStatus != model.IdentityStatusYes {
	//	return xerr.NewErr(xerr.ErrParamError, nil, "不可更新，尚未实名认证")
	//}
	err := s.stationDao.Update(ctx, bson.M{"_id": id}, bson.M{
		"$set": bson.M{
			"open_status": status,
		}})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

func (s stationService) UpdateCommissionRate(ctx context.Context, id primitive.ObjectID, rate int) error {
	err := s.stationDao.Update(ctx, bson.M{"_id": id}, bson.M{
		"$set": bson.M{
			"commission_rate": rate,
		}})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

func (s stationService) Identity(ctx context.Context, id primitive.ObjectID, name, number string) error {
	err := s.authenticationS.SetRealNameByStation(ctx, id, name, number)
	if err != nil {
		return err
	}
	//err = s.stationDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"$set": bson.M{
	//		"identity_status": model.IdentityStatusYes,
	//	}})
	//if err != nil {
	//	return err
	//}
	del(s.rdb, id)

	return nil
}

func (s stationService) BankBind(ctx context.Context, id primitive.ObjectID, mobile, carNo string) error {
	err := s.authenticationS.BindBankByStation(ctx, id, mobile, carNo)
	if err != nil {
		return err
	}
	//err = s.stationDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"$set": bson.M{
	//		"bank_bind_status": model.BankBindStatusYes,
	//	}})
	//if err != nil {
	//	return err
	//}
	del(s.rdb, id)

	return nil
}

func (s stationService) UpdateServiceFee(ctx context.Context, id primitive.ObjectID, supplierServiceFee, deliverServiceFee float64) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"supplier_service_fee": supplierServiceFee,
		"deliver_service_fee":  deliverServiceFee,
		"updated_at":           milli,
	}
	err := s.stationDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

func (s stationService) UpdateHeadImg(ctx context.Context, id primitive.ObjectID, shopHeadImg model.FileInfo) error {
	now := time.Now().UnixMilli()
	update := bson.M{
		"updated_at":    now,
		"shop_head_img": shopHeadImg,
	}
	err := s.stationDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

func (s stationService) CheckOneExist(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}
	count, err := s.stationDao.Count(ctx, filter)
	if err != nil {
		return err
	}
	if count != 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "供应商不存在")
	}
	return nil
}

func (s stationService) ListByLocation(ctx context.Context, longitude, latitude float64) ([]model.Station, error) {
	list, err := s.stationDao.List(ctx, bson.M{
		"is_open":    true,
		"deleted_at": 0,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s stationService) ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, isOpen bool) ([]model.Station, error) {
	list, err := s.stationDao.List(ctx, bson.M{
		"warehouse_id": warehouseID,
		"is_open":      isOpen,
		"deleted_at":   0,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s stationService) Get(ctx context.Context, id primitive.ObjectID) (model.Station, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		data, err := s.stationDao.Get(ctx, id)
		if err != nil {
			return model.Station{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s stationService) GetByUser(ctx context.Context, userID primitive.ObjectID) (model.Station, error) {
	i, err := s.stationDao.GetByUserID(ctx, userID)
	if err != nil {
		return model.Station{}, err
	}
	return i, nil
}

func (s stationService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Station, int64, error) {
	list, i, err := s.stationDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s stationService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Station, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"deleted_at": 0,
	}
	list, err := s.stationDao.List(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s stationService) ListCus(ctx context.Context, filter bson.M) ([]model.Station, error) {
	list, err := s.stationDao.List(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}
