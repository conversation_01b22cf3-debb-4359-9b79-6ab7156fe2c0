package couponUserService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/couponUserDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/buyerService"
	"base/service/couponStockService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 优惠券
type ServiceInterface interface {
	Create(ctx context.Context, couponStockID primitive.ObjectID, buyerID primitive.ObjectID, remark string) error
	CreateNewUser(ctx context.Context, userID primitive.ObjectID) (model.CouponUser, error)
	GetByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.CouponUser, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.CouponUser, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M) ([]model.CouponUser, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponUser, int64, error)
	// BackValidCouponForOrder(list []model.CouponAccount, productAmount int) []model.CouponForOrder
	ListByUserID(ctx context.Context, userID primitive.ObjectID) ([]model.CouponUser, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UseCoupon(ctx context.Context, id primitive.ObjectID) error
	RecoverCoupon(ctx context.Context, id primitive.ObjectID) error

	CheckCouponUser(ctx context.Context) error
	Refresh(ctx context.Context, content string) error
	Restore(ctx context.Context, content string) error
	SendCouponUserUsed(ctx context.Context, content string) error
}

type service struct {
	mdb           *mongo.Database
	rdb           *redis.Client
	couponStockS  couponStockService.ServiceInterface
	buyerS        buyerService.ServiceInterface
	couponUserDao couponUserDao.DaoInt
}

// NewCouponUserService 创建优惠券账户服务
func NewCouponUserService() ServiceInterface {
	return service{
		mdb:           global.MDB,
		rdb:           global.RDBDefault,
		couponStockS:  couponStockService.NewService(),
		buyerS:        buyerService.NewBuyerService(),
		couponUserDao: dao.CouponUserDao,
	}
}

// Get 获取优惠券账户
func (s service) Get(ctx context.Context, id primitive.ObjectID) (model.CouponUser, error) {
	filter := bson.M{
		"_id": id,
	}
	coupon, err := s.couponUserDao.Get(ctx, filter)
	if err != nil {
		return model.CouponUser{}, err
	}
	return coupon, nil
}

// Create 创建优惠券账户
func (s service) Create(ctx context.Context, couponStockID primitive.ObjectID, buyerID primitive.ObjectID, remark string) error {
	var err error

	if remark == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "备注不能为空")
	}

	buyer, err := s.buyerS.GetByID(ctx, buyerID)
	if err != nil {
		return err
	}
	_ = buyer

	couponStock, err := s.couponStockS.Get(ctx, couponStockID)
	if err != nil {
		return err
	}

	// 检查单用户是否超发
	filter := bson.M{
		"buyer_id":        buyerID,
		"coupon_stock_id": couponStockID,
	}
	count, err := s.couponUserDao.Count(ctx, filter)
	if err != nil {
		return err
	}

	// 如果已经有相同的优惠券，则不允许再次发放
	if count >= int64(couponStock.MaxPerUserNum) {
		return xerr.NewErr(xerr.ErrParamError, nil, "该用户已领取过此优惠券")
	}

	now := time.Now()

	serialNumber, err := s.getSendCouponSerialNumber(ctx)
	if err != nil {
		return err
	}
	serialNo := time.Now().Format("********") + serialNumber

	//  获取now 的今日零点
	zeroTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	beginTime := zeroTime.UnixMilli()
	endTime := zeroTime.AddDate(0, 0, couponStock.ValidDays).UnixMilli()
	endTime = endTime - 1

	data := model.CouponUser{
		ID:                 primitive.NewObjectID(),
		SerialNo:           serialNo,
		BuyerID:            buyerID,
		BuyerName:          buyer.BuyerName,
		CouponStockID:      couponStockID,
		CouponStockType:    couponStock.CouponStockType,
		AvailableBeginTime: beginTime,
		AvailableEndTime:   endTime,
		Title:              couponStock.Title,
		Description:        couponStock.Description,
		CouponAmount:       couponStock.CouponAmount,
		MinAmount:          couponStock.MinAmount,
		CouponStatus:       model.CouponStatusTypeValid,
		Remark:             remark,
		CreatedAt:          now.UnixMilli(),
		UpdatedAt:          now.UnixMilli(),
	}

	err = s.couponStockS.UpdateSendedNum(ctx, couponStockID, 1, true)
	if err != nil {
		return err
	}
	err = s.couponUserDao.Create(ctx, data)
	if err != nil {
		return err
	}

	//mnsSendService.NewMNSClient().SendCouponCheckAccountValid(data)

	return nil
}

var sendCouponSerialNumberKey = "send_coupon_serial_number:"

// 当日发券流水号
func (s service) getSendCouponSerialNumber(ctx context.Context) (string, error) {
	// 获取当日发券流水号,存在就+1,不存在就设置为1
	now := time.Now()
	date := now.Format("********")
	key := fmt.Sprintf("%s%s", sendCouponSerialNumberKey, date)

	serialNumber, err := s.rdb.Incr(ctx, key).Result()
	if err != nil {
		zap.S().Errorf("获取当日发券流水号失败: %v", err)
		return "", err
	}

	if serialNumber == 1 {
		err = s.rdb.Expire(ctx, key, time.Hour*25).Err()
		if err != nil {
			zap.S().Errorf("设置当日发券流水号过期时间失败: %v", err)
			return "", err
		}
	}

	//  返回6位流水号
	serialNumberStr := fmt.Sprintf("%06d", serialNumber)

	return serialNumberStr, nil
}

// CheckNewUserCoupon 检查新人券
func (s service) CheckNewUserCoupon(ctx context.Context, userID primitive.ObjectID, couponActiveID primitive.ObjectID) (bool, error) {
	//	受邀新人券和主动邀请券只能其一
	couponInviteID, err := util.ConvertToObjectWithNote(model.NewUserInviteCouponID, "coupon_id")
	if err != nil {
		return false, err
	}
	filter := bson.M{
		"user_id": userID,
		"coupon_id": bson.M{
			"$in": []primitive.ObjectID{
				couponInviteID,
				couponActiveID,
			},
		},
	}
	count, err := s.Count(ctx, filter)
	if err != nil {
		return false, err
	}
	if count > 0 {
		return true, nil
	}

	return false, nil
}

// CreateNewUser 主动创建新人券
func (s service) CreateNewUser(ctx context.Context, userID primitive.ObjectID) (model.CouponUser, error) {
	// // 新人
	// couponActiveID, err := util.ConvertToObjectWithNote(model.NewUserActiveCouponID, "coupon_id")
	// if err != nil {
	// 	return model.CouponUser{}, err
	// }

	// coupon, err := s.couponStockS.Get(ctx, couponActiveID)
	// if err != nil {
	// 	return model.CouponUser{}, err
	// }

	// // 判断重复
	// checkNewUserCoupon, err := s.CheckNewUserCoupon(ctx, userID, couponActiveID)
	// if err != nil {
	// 	return model.CouponUser{}, err
	// }
	// if checkNewUserCoupon {
	// 	return model.CouponUser{}, xerr.ErrExistNewUserCouponAccount
	// }

	// now := time.Now()
	// nowMilli := now.UnixMilli()

	// err = checkCoupon(coupon, nowMilli)
	// if err != nil {
	// 	zap.S().Warnf("checkCoupon error: %v", err.Error())
	// 	return model.CouponUser{}, nil
	// }

	// // var validUseBegin, validUseEnd int64
	// // switch coupon.ValidRuleType {
	// // case model.ValidRuleTypeInterval:
	// // 	validUseBegin = coupon.ValidUseBegin
	// // 	validUseEnd = coupon.ValidUseEnd
	// // 	break
	// // case model.ValidRuleTypeWhenGet:
	// // 	begin := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, time.Local)
	// // 	end := begin.Add(time.Duration(coupon.ValidDurationHour) * time.Hour)
	// // 	validUseBegin = begin.UnixMilli()
	// // 	validUseEnd = end.UnixMilli()
	// // 	break
	// // default:
	// // 	return model.CouponAccount{}, xerr.NewErr(xerr.ErrParamError, nil, "优惠券异常，请联系平台")
	// // }

	// data := model.CouponUser{
	// 	// ID:               primitive.NewObjectID(),
	// 	// UserID:           userID,
	// 	// CouponID:         coupon.ID,
	// 	// CouponType:       coupon.CouponType,
	// 	// ValidRuleType:    coupon.ValidRuleType,
	// 	// ValidUseBegin:    validUseBegin,
	// 	// ValidUseEnd:      validUseEnd,
	// 	// Num:              1,
	// 	// Title:            coupon.Title,
	// 	// DescList:         coupon.DescList,
	// 	// Amount:           coupon.Amount,
	// 	// ConditionAmount:  coupon.ConditionAmount,
	// 	// CouponStatusType: model.CouponStatusTypeValid,
	// 	// Note:             "新用户代金券",
	// 	// CreatedAt:        nowMilli,
	// 	// UpdatedAt:        nowMilli,
	// }

	// session, err := s.mdb.Client().StartSession()
	// if err != nil {
	// 	return model.CouponUser{}, err
	// }
	// defer session.EndSession(ctx)
	// _, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
	// 	err = s.couponStockS.UpdateSendedNum(sessCtx, coupon.ID, 1, true)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	err = s.couponUserDao.Create(sessCtx, data)
	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	return nil, nil
	// })
	// if err != nil {
	// 	return model.CouponUser{}, err
	// }

	// //mnsSendService.NewMNSClient().SendCouponCheckAccountValid(data)

	// return data, nil
	return model.CouponUser{}, nil
}

func (s service) backRewardCoupon(ctx context.Context, inviterUserID, activeCouponAccountID primitive.ObjectID, now time.Time, nowMilli int64) (model.CouponUser, error) {
	// 奖励券
	couponID, err := util.ConvertToObjectWithNote(model.NewUserRewardCouponID, "coupon_id")
	if err != nil {
		return model.CouponUser{}, err
	}
	coupon, err := s.couponStockS.Get(ctx, couponID)
	if err != nil {
		return model.CouponUser{}, err
	}
	_ = coupon

	// var validUseBegin, validUseEnd int64
	// switch coupon.ValidRuleType {
	// case model.ValidRuleTypeInterval:
	// 	validUseBegin = coupon.ValidUseBegin
	// 	validUseEnd = coupon.ValidUseEnd
	// 	break
	// case model.ValidRuleTypeWhenGet:
	// 	end := now.Add(time.Duration(coupon.ValidDurationHour) * time.Hour)
	// 	validUseBegin = nowMilli
	// 	validUseEnd = end.UnixMilli()
	// 	break
	// default:
	// 	return model.CouponAccount{}, xerr.NewErr(xerr.ErrParamError, nil, "优惠券异常，请联系平台")
	// }

	data := model.CouponUser{
		// ID:                      primitive.NewObjectID(),
		// UserID:                  inviterUserID,
		// CouponID:                coupon.ID,
		// CouponType:              coupon.CouponType,
		// ValidRuleType:           coupon.ValidRuleType,
		// ValidUseBegin:           validUseBegin,
		// ValidUseEnd:             validUseEnd,
		// Num:                     1,
		// Amount:                  coupon.Amount,
		// ConditionAmount:         coupon.ConditionAmount,
		// CouponStatusType:        model.CouponStatusTypeToActive,
		// ActiveByCouponAccountID: activeCouponAccountID,
		// Title:                   coupon.Title,
		// DescList:                coupon.DescList,
		// Note:                    "邀请者奖励券",
		// CreatedAt:               nowMilli,
		// UpdatedAt:               nowMilli,
	}

	return data, nil

}

func (s service) GetByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.CouponUser, error) {
	filter := bson.M{
		"buyer_id":    buyerID,
		"coupon_type": "newUser",
	}
	get, err := s.couponUserDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.CouponUser{}, err
	}
	return get, nil
}

func (s service) List(ctx context.Context, filter bson.M) ([]model.CouponUser, error) {
	list, err := s.couponUserDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s service) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponUser, int64, error) {
	list, count, err := s.couponUserDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

// func (s service) BackValidCouponForOrder(list []model.CouponAccount, productAmount int) []model.CouponForOrder {
// 	now := time.Now().UnixMilli()
// 	resList := make([]model.CouponForOrder, 0)
// 	for _, c := range list {
// 		//	金额符合的
// 		item := model.CouponForOrder{
// 			CouponAccount: c,
// 		}
// 		if productAmount >= c.ConditionAmount && c.CouponStatusType == model.CouponStatusTypeValid {
// 			if c.ValidUseBegin <= now && c.ValidUseEnd >= now {
// 				//	 符合
// 				item.Optional = true
// 			}
// 			if c.ValidUseBegin > now {
// 				//	 未到期
// 				item.CouponStatusType = model.CouponStatusTypeNotYet
// 			}
// 		}
// 		resList = append(resList, item)
// 	}
// 	return resList
// }

func (s service) ListByUserID(ctx context.Context, userID primitive.ObjectID) ([]model.CouponUser, error) {
	filter := bson.M{
		"user_id":    userID,
		"deleted_at": 0,
	}

	list, err := s.couponUserDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s service) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.couponUserDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s service) Delete(ctx context.Context, id primitive.ObjectID) error {
	err := s.couponUserDao.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}
	return nil
}

func (s service) UseCoupon(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}
	update := bson.M{
		"coupon_status_type": model.CouponStatusTypeUsed,
		"updated_at":         time.Now().UnixMilli(),
	}

	err := s.couponUserDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}
func (s service) RecoverCoupon(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}
	update := bson.M{
		"coupon_status_type": model.CouponStatusTypeValid,
		"updated_at":         time.Now().UnixMilli(),
	}

	err := s.couponUserDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

// Refresh 刷新用户优惠券状态
func (s service) Refresh(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("Refresh %v", err)
		}
	}()

	var data model.MNSCouponUser
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	marshal, err := json.Marshal(data)
	if err != nil {
		return err
	}
	zap.S().Infof("Refresh : %s", string(marshal))

	couponUserID, err := util.ConvertToObjectWithCtx(ctx, data.CouponUserID)
	if err != nil {
		return err
	}

	couponUser, err := s.Get(ctx, couponUserID)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	if couponUser.CouponStatus == model.CouponStatusTypeValid && couponUser.AvailableEndTime < now {
		// 过期
		filter := bson.M{"_id": couponUserID}
		update := bson.M{
			"coupon_status": model.CouponStatusTypeExpired,
			"updated_at":    time.Now().UnixMilli(),
		}
		err = s.couponUserDao.Update(ctx, filter, bson.M{"$set": update})
		if err != nil {
			return err
		}
	}

	return nil
}

// Restore 恢复优惠券
func (s service) Restore(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("Restore %v", err)
		}
	}()

	var data model.MNSCouponUser
	err := util.DecodeMNSContent(content, &data)

	couponUserID, err := util.ConvertToObjectWithCtx(ctx, data.CouponUserID)
	if err != nil {
		return err
	}
	couponUser, err := s.Get(ctx, couponUserID)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	if couponUser.CouponStatus == model.CouponStatusTypeUsed && couponUser.AvailableEndTime > now {
		// 恢复
		filter := bson.M{"_id": couponUserID}
		update := bson.M{
			"coupon_status": model.CouponStatusTypeValid,
			"updated_at":    time.Now().UnixMilli(),
		}
		err = s.couponUserDao.Update(ctx, filter, bson.M{"$set": update})
		if err != nil {
			return err
		}
	}

	return nil
}

// SendCouponUserUsed 发送优惠券已使用消息
func (s service) SendCouponUserUsed(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("SendCouponUserUsed %v", err)
		}
	}()

	var data model.MNSCouponUser
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}
	marshal, err := json.Marshal(data)
	if err != nil {
		return err
	}
	zap.S().Infof("SendCouponUserUsed : %s", string(marshal))

	couponUserID, err := util.ConvertToObjectWithCtx(ctx, data.CouponUserID)
	if err != nil {
		return err
	}

	parentOrderID, err := util.ConvertToObjectWithCtx(ctx, data.ParentOrderID)
	if err != nil {
		return err
	}

	couponUser, err := s.Get(ctx, couponUserID)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	if couponUser.CouponStatus != model.CouponStatusTypeUsed {
		// 恢复
		filter := bson.M{"_id": couponUserID}
		update := bson.M{
			"coupon_status":   model.CouponStatusTypeUsed,
			"parent_order_id": parentOrderID,
			"updated_at":      now,
		}
		err = s.couponUserDao.Update(ctx, filter, bson.M{"$set": update})
		if err != nil {
			return err
		}
	}

	return nil
}

// CheckCouponUser 检查用户优惠券状态
func (s service) CheckCouponUser(ctx context.Context) error {
	// 筛选状态为有效的优惠券，并且可用时间低于最近24小时
	now := time.Now()
	last24Hours := now.Add(24 * time.Hour).UnixMilli()

	filter := bson.M{
		"coupon_status":      model.CouponStatusTypeValid,
		"available_end_time": bson.M{"$lte": last24Hours},
	}

	couponUserList, err := s.List(ctx, filter)
	if err != nil {
		return err
	}

	jsonMarshal, err := json.Marshal(couponUserList)
	if err != nil {
		return err
	}

	zap.S().Infof("检查用户优惠券状态: %v", string(jsonMarshal))

	for _, couponUser := range couponUserList {
		// 检查是否已检查过
		if s.isCheckedCouponUserStatus(ctx, couponUser.ID) {
			continue
		}

		delaySecond := couponUser.AvailableEndTime - now.UnixMilli()
		seconds := delaySecond / 1000
		mnsSendService.NewMNSClient().SendCouponUserRefresh(couponUser.ID.Hex(), seconds)

		// 设置已检查
		err = s.setCheckedCouponUserStatus(ctx, couponUser.ID)
		if err != nil {
			return err
		}
	}

	return nil
}

// 缓存-已检查用户优惠券状态
var checkedCouponUserStatusKey = "checked_coupon_user_status:"

func (s service) setCheckedCouponUserStatus(ctx context.Context, couponUserID primitive.ObjectID) error {
	now := time.Now().UnixMilli()
	key := fmt.Sprintf("%s%s", checkedCouponUserStatusKey, couponUserID.Hex())
	err := s.rdb.Set(ctx, key, now, time.Hour*25).Err()
	if err != nil {
		return err
	}
	return nil
}

func (s service) isCheckedCouponUserStatus(ctx context.Context, couponUserID primitive.ObjectID) bool {
	key := fmt.Sprintf("%s%s", checkedCouponUserStatusKey, couponUserID.Hex())
	exists, err := s.rdb.Exists(ctx, key).Result()
	if err != nil {
		return false
	}
	return exists == 1
}
