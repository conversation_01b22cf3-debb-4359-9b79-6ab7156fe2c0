package sys

import (
	"base/util"
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"hash"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"time"
)

type ConvertParam struct {
	SrcUri     string `json:"SrcUri"`
	FileName   string `json:"FileName"`
	ExportType string `json:"ExportType"`
	Callback   string `json:"CallBack"`
	TaskId     string `json:"TaskId"`
}

type UrlParam struct {
	Url  string `json:"url"`
	Code string `json:"Code"`
}

type OutPut struct {
	RequestId string `json:"RequestId"`
	Code      string `json:"Code"`
	Message   string `json:"Message"`
}

type CallBack struct {
	TaskId string `json:"TaskId"`
	Code   string `json:"Code"`
}

var AppId = "AK20230904XROBWK"
var AccessKeySecret = "7304dec4680c30fb6ba431fbe92d907f"
var HTTPHeaderAuthorization = "Authorization"
var HTTPHeaderDate = "Date"
var HTTPHeaderContentType = "Content-Type"
var HTTPHeaderContentMD5 = "Content-MD5"

var docUrl = "/api/doc/notify"

func convertToPDF() {
	if len(os.Args) != 2 {
		fmt.Println("参数请加任务ID")
	}
	TaskId := util.NewUUID()
	param := &ConvertParam{
		SrcUri:     "https://image.guoshut.top/deliverNote/20230901T224509.xlsx",
		FileName:   "temp0904.png",
		ExportType: "png",
		Callback:   docUrl,
		TaskId:     TaskId,
	}

	data, _ := json.Marshal(param)
	r := bytes.NewReader(data)
	req, err := http.NewRequest(http.MethodPost, "https://dhs.open.wps.cn/pre/v1/convert", r)
	if err != nil {
		fmt.Println(err)
		return
	}
	req.Close = true
	SignHeader(req, AppId, AccessKeySecret, data)
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	output := new(OutPut)
	body, _ := ioutil.ReadAll(resp.Body)
	if err := json.Unmarshal(body, output); err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("HTTP状态码:%v 返回结果体:%v\n", resp.StatusCode, output)
	fmt.Println("开始等待接口回调")

}

func PDFNotify(ctx *gin.Context) {

}

func SignHeader(req *http.Request, appId, secretKey string, data []byte) {
	date := time.Now().UTC().Format("Mon, 02 Jan 2006 15:04:05 GMT")
	md5Ctx := md5.New()
	md5Ctx.Write(data)
	md5Data := hex.EncodeToString(md5Ctx.Sum(nil))
	req.Header.Add(HTTPHeaderDate, date)
	req.Header.Add(HTTPHeaderContentType, "application/json")
	req.Header.Add(HTTPHeaderContentMD5, md5Data)
	authorizationStr := "WPS " + appId + ":" + getHeaderSingedStr(req, secretKey)
	req.Header.Set(HTTPHeaderAuthorization, authorizationStr)
}

func getHeaderSingedStr(req *http.Request, secretKey string) string {
	date := req.Header.Get(HTTPHeaderDate)
	contentType := req.Header.Get(HTTPHeaderContentType)
	contentMd5 := req.Header.Get(HTTPHeaderContentMD5)
	canonicalizedResource := req.URL.RequestURI()
	signStr := req.Method + "\n" + contentMd5 + "\n" + contentType + "\n" + date + "\n" + canonicalizedResource

	h := hmac.New(func() hash.Hash { return sha1.New() }, []byte(secretKey))
	io.WriteString(h, signStr)
	signedStr := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signedStr
}
