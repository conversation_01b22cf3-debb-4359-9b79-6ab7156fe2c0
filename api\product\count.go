package product

import (
	"base/core/xhttp"
	"base/service/productAuditService"
	"base/service/productService"

	"github.com/gin-gonic/gin"
)

// CountAudit 审核数量
func CountAudit(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	count, err := productAuditService.NewProductAuditService().CountAudit(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 下架审核
	countOffline, err := productService.NewProductService().CountOffLineAudit(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	total := count + int64(countOffline)

	xhttp.RespSuccess(ctx, total)
}
