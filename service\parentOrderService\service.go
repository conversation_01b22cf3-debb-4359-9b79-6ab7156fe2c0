package parentOrderService

import (
	"base/dao"
	"base/dao/parentOrderDao"
	"base/global"
	"base/model"
	"base/util"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/cnbattle/allinpay"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 支付父单
type ServiceInterface interface {
	Create(ctx context.Context, data model.ParentOrder) error
	Get(ctx context.Context, id primitive.ObjectID) (model.ParentOrder, error)
	GetByOutTradeNo(ctx context.Context, outTradeNo string) (model.ParentOrder, error)
	GetByTradeOrderID(ctx context.Context, orderID string) (model.ParentOrder, error)
	GetByDeliverRefundReqID(ctx context.Context, refundReqID string) (model.ParentOrder, error)
	List(ctx context.Context, filter bson.M) ([]model.ParentOrder, error)
	GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.ParentOrder, error)
	UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error
	UpdatePayStatusByID(ctx context.Context, id primitive.ObjectID, payStatus model.PayStatusType) error
	UpdateCreatePayBizOrderNO(ctx context.Context, parentOrderID primitive.ObjectID, payMethod model.PayMethodType) (string, error) // 更新和创建父单支付订单号
	UpdateCreatePayBizOrderCouponNO(ctx context.Context, parentOrderID primitive.ObjectID) (string, error)                          //  更新和创建父单支付订单号-代金券
	UpdateBizOrderNoResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error
	UpdateBizOrderNoCouponResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ParentOrder, int64, error)
}
type parentOrderService struct {
	mdb            *mongo.Database
	rdb            *redis.Client
	parentOrderDao parentOrderDao.DaoInt
	l              *zap.SugaredLogger
}

func NewParentOrderService() ServiceInterface {
	return parentOrderService{
		mdb:            global.MDB,
		rdb:            global.RDBDefault,
		parentOrderDao: dao.ParentOrderDao,
		l:              global.OrderLogger.Sugar(),
	}
}

func (s parentOrderService) UpdateBizOrderNoResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"biz_order_no": bizOrderNo}
		err = s.parentOrderDao.UpdateOne(sessCtx, filter, bson.M{"$set": bson.M{"biz_order_no_result": ps}})
		if err != nil {
			s.l.Error("更新父单状态错误:", err)
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s parentOrderService) UpdateBizOrderNoCouponResult(ctx context.Context, bizOrderNo string, ps model.PayResult) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"biz_order_coupon_no": bizOrderNo}
		err = s.parentOrderDao.UpdateOne(sessCtx, filter, bson.M{"$set": bson.M{"biz_order_coupon_no_result": ps}})
		if err != nil {
			s.l.Error("更新父单代金券状态错误:", err)
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s parentOrderService) UpdateCreatePayBizOrderNO(ctx context.Context, parentOrderID primitive.ObjectID, payMethod model.PayMethodType) (string, error) {
	filter := bson.M{
		"_id": parentOrderID,
	}

	no := util.NewUUID()
	update := bson.M{
		"biz_order_no": no,
		"pay_method":   payMethod,
		"pay_status":   model.PayStatusTypePending,
		"updated_at":   time.Now().UnixMilli(),
	}
	err := s.parentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return "", err
	}

	return no, nil
}

func (s parentOrderService) UpdateCreatePayBizOrderCouponNO(ctx context.Context, parentOrderID primitive.ObjectID) (string, error) {
	filter := bson.M{
		"_id": parentOrderID,
	}
	no := util.NewUUID()
	update := bson.M{
		"biz_order_coupon_no": no,
		"updated_at":          time.Now().UnixMilli(),
	}
	err := s.parentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return "", err
	}
	return no, nil
}

func (s parentOrderService) UpdatePayStatusByID(ctx context.Context, id primitive.ObjectID, payStatus model.PayStatusType) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"pay_status": payStatus,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.parentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s parentOrderService) UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	update := bson.M{
		"pay_status": payStatus,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.parentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s parentOrderService) UpdatePayStatusNotifyCoupon(ctx context.Context, res allinpay.NotifyPay) error {
	filter := bson.M{
		"biz_order_coupon_no": res.BizOrderNo,
	}

	update := bson.M{
		"biz_order_coupon_no_result.amount":       res.Amount,
		"biz_order_coupon_no_result.biz_order_no": res.BizOrderNo,
		"biz_order_coupon_no_result.extendInfo":   res.ExtendInfo,
		"biz_order_coupon_no_result.pay_datetime": res.PayDatetime,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.parentOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s parentOrderService) Create(ctx context.Context, data model.ParentOrder) error {
	err := s.parentOrderDao.Create(ctx, data)

	return err
}

func (s parentOrderService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.ParentOrder, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	data, err := s.parentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.ParentOrder{}, err
	}
	return data, nil
}

func (s parentOrderService) Get(ctx context.Context, id primitive.ObjectID) (model.ParentOrder, error) {
	filter := bson.M{
		"_id": id,
	}
	payOrder, err := s.parentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.ParentOrder{}, err
	}
	return payOrder, nil
}

func (s parentOrderService) GetByOutTradeNo(ctx context.Context, outTradeNo string) (model.ParentOrder, error) {
	filter := bson.M{
		"wx_pay_result.out_trade_no": outTradeNo,
	}
	payOrder, err := s.parentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.ParentOrder{}, err
	}
	return payOrder, nil
}

func (s parentOrderService) GetByTradeOrderID(ctx context.Context, orderID string) (model.ParentOrder, error) {
	filter := bson.M{
		"yee_wechat_result.order_id": orderID,
	}
	payOrder, err := s.parentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.ParentOrder{}, err
	}
	return payOrder, nil
}

func (s parentOrderService) GetByDeliverRefundReqID(ctx context.Context, refundReqID string) (model.ParentOrder, error) {
	filter := bson.M{
		"yee_refund_deliver_result.refund_request_id": refundReqID,
	}
	payOrder, err := s.parentOrderDao.Get(ctx, filter)
	if err != nil {
		return model.ParentOrder{}, err
	}
	return payOrder, nil
}

func (s parentOrderService) List(ctx context.Context, filter bson.M) ([]model.ParentOrder, error) {
	list, err := s.parentOrderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s parentOrderService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ParentOrder, int64, error) {
	list, count, err := s.parentOrderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s parentOrderService) UpdateOne(ctx context.Context, filter, update bson.M) error {
	err := s.parentOrderDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
