package global

import (
	"base/core/config"
	"base/util"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"log"
)

var MDB *mongo.Database
var RDBDefault *redis.Client

func InitMongo(conf config.Mongo) *mongo.Database {
	op := options.Client().ApplyURI(conf.Url)

	c, err := util.MongoInit(op)
	if err != nil {
		zap.S().Error("连接失败")
		zap.S().Fatalln("err:", err)
	}
	database := c.Database(conf.Database)
	return database
}

func InitRedis(c config.Redis) *redis.Client {
	rOpt := redis.Options{
		Addr:         c.Host + ":" + c.Port,
		Password:     c.Password,
		DB:           1,
		PoolSize:     50,
		MinIdleConns: 10,
	}

	rdb, err := util.RedisInit(rOpt)
	if err != nil {
		log.Fatalln(err)
	}
	return rdb
}
