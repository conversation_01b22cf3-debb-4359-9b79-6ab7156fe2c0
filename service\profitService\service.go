package profitService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/profitDao"
	"base/dao/profitSettlementDao"
	"base/model"
	"base/service/orderAgentPayService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/productBuyPriceService"
	"base/service/supplierService"
	"base/types"
	"context"
	"errors"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 利润服务接口
type ServiceInterface interface {
	List(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) ([]model.Profit, error)
	GetProfitRes(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (types.ProfitRes, error)
	CalcOrderProfit(ctx context.Context, orderID primitive.ObjectID) error
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.Profit, error)

	SettleProfit(ctx context.Context, supplierID primitive.ObjectID, amount int, remark string, monthTimestamp int64) error
	ListSettlement(ctx context.Context, monthTimestamp int64, page, limit int64) ([]model.ProfitSettlement, int64, error)
	ListSettlementBySupplier(ctx context.Context, supplierID primitive.ObjectID, monthTimestamp int64) ([]model.ProfitSettlement, error)
}

type profitService struct {
	profitDao              profitDao.DaoInt
	profitSettlementDao    profitSettlementDao.DaoInt
	orderService           orderService.ServiceInterface
	refundService          orderRefundService.ServiceInterface
	debtService            orderDebtService.ServiceInterface
	orderAgentPayService   orderAgentPayService.ServiceInterface
	productBuyPriceService productBuyPriceService.ServiceInterface
	supplierService        supplierService.ServiceInterface
}

// NewProfitService 创建利润服务
func NewProfitService() ServiceInterface {
	return profitService{
		profitDao:              dao.ProfitDao,
		profitSettlementDao:    dao.ProfitSettlementDao,
		orderService:           orderService.NewOrderService(),
		refundService:          orderRefundService.NewOrderRefundService(),
		debtService:            orderDebtService.NewOrderDebtService(),
		orderAgentPayService:   orderAgentPayService.NewOrderAgentPayService(),
		productBuyPriceService: productBuyPriceService.NewProductBuyPriceService(),
		supplierService:        supplierService.NewSupplierService(),
	}
}

// GetByOrderID 根据订单ID获取利润
func (s profitService) GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.Profit, error) {
	profit, err := s.profitDao.Get(ctx, bson.M{
		"order_id": orderID,
	})
	if err != nil {
		return model.Profit{}, err
	}

	return profit, nil
}

// CalcOrderProfit 计算订单利润
func (s profitService) CalcOrderProfit(ctx context.Context, orderID primitive.ObjectID) error {
	order, err := s.orderService.Get(ctx, orderID)
	if err != nil {
		return err
	}

	profit, err := s.GetByOrderID(ctx, orderID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if profit.ID != primitive.NilObjectID {
		zap.S().Warnf("订单已存在利润记录，无需重新计算,订单ID: %s", orderID.Hex())
		return nil
		// 直接删除
		// err = s.profitDao.Delete(ctx, bson.M{
		// 	"order_id": orderID,
		// })
		// if err != nil {
		// 	return err
		// }
	}

	// 售后退款
	refunds, err := orderRefundService.NewOrderRefundService().ListAfterSaleRefund(ctx, order.ID)
	if err != nil {
		return err
	}
	_ = refunds

	// 结算
	debt, err := orderDebtService.NewOrderDebtService().GetByOrderID(ctx, order.ID)
	if err != nil {
		return err
	}
	_ = debt

	filterBuyPrice := bson.M{
		"order_list.order_id": order.ID,
	}
	buyPrices, err := s.productBuyPriceService.List(ctx, filterBuyPrice)
	if err != nil {
		return err
	}
	_ = buyPrices

	//  订单利润
	var totalProfitAmount int

	for _, buyPrice := range buyPrices {
		unitPrice := buyPrice.AverageUnitPrice
		purchaseUnitPrice := buyPrice.AverageBuyUnitPrice

		diffUnitPrice := unitPrice - purchaseUnitPrice

		var sortNum, sortWeight int
		var f bool
		for _, product := range order.ProductList {
			if product.ProductID == buyPrice.ProductID {
				sortNum = product.SortNum
				sortWeight = product.SortWeight
				f = true
				break
			}
		}

		if !f {
			zap.S().Errorf("订单商品不存在采购价,订单ID: %s", orderID.Hex())
			return nil
		}

		var perProfitAmount int

		if buyPrice.IsCheckWeight {
			// 计重
			diffUnitPriceDecimal := decimal.NewFromInt(int64(diffUnitPrice))
			sortWeightDecimal := decimal.NewFromInt(int64(sortWeight)).Div(decimal.NewFromInt(1000))
			perProfitAmount = int(diffUnitPriceDecimal.Mul(sortWeightDecimal).Round(0).IntPart())
		}

		if !buyPrice.IsCheckWeight {
			// 计件
			perProfitAmount = diffUnitPrice * sortNum
		}

		totalProfitAmount += perProfitAmount

	}

	// 售后金额
	var afterSaleRefundAmount int
	for _, refund := range refunds {
		if !refund.IsWithdraw && refund.AuditStatus == model.AuditStatusTypePass && refund.IsComplete {
			// 已完结
			afterSaleRefundAmount += refund.AuditAmount
		}
	}

	debtTotalAmount := debt.TotalProductAmount

	milli := time.Now().UnixMilli()

	data := model.Profit{
		ID:                     primitive.NewObjectID(),
		OrderID:                order.ID,
		BuyerID:                order.BuyerID,
		SupplierID:             order.SupplierID,
		ProductTotalAmount:     order.TotalAmount,
		ProductAfterSaleAmount: afterSaleRefundAmount,
		ProductDebtAmount:      debtTotalAmount,
		ProductProfitAmount:    totalProfitAmount,
		OrderCreatedAt:         order.CreatedAt,
		StockUpNo:              order.StockUpNo,      // 备货 批次
		StockUpDayTime:         order.StockUpDayTime, // 备货 日期
		CreatedAt:              milli,
	}

	err = s.profitDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

// List 利润列表
func (s profitService) List(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) ([]model.Profit, error) {
	filter := bson.M{
		"supplier_id": supplierID,
		"order_created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	profits, err := s.profitDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return profits, nil
}

// GetProfitRes 利润统计
func (s profitService) GetProfitRes(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (types.ProfitRes, error) {
	filter := bson.M{
		"supplier_id": supplierID,
		"order_created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	profits, err := s.profitDao.List(ctx, filter)
	if err != nil {
		return types.ProfitRes{}, err
	}

	var totalAmount int
	var afterSaleAmount int
	var debtAmount int
	var profitAmount int

	for _, profit := range profits {
		totalAmount += profit.ProductTotalAmount
		afterSaleAmount += profit.ProductAfterSaleAmount
		debtAmount += profit.ProductDebtAmount
		profitAmount += profit.ProductProfitAmount
	}

	data := types.ProfitRes{
		ProductTotalAmount:     totalAmount,
		ProductAfterSaleAmount: afterSaleAmount,
		ProductDebtAmount:      debtAmount,
		ProductProfitAmount:    profitAmount,
	}

	return data, nil
}

// SettleProfit 结算利润
func (s profitService) SettleProfit(ctx context.Context, supplierID primitive.ObjectID, amount int, remark string, monthTimestamp int64) error {
	// 判断是否存在当月
	count, err := s.profitSettlementDao.Count(ctx, bson.M{
		"supplier_id":     supplierID,
		"month_timestamp": monthTimestamp,
	})
	if err != nil {
		return err
	}
	if count > 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "当月已结算")
	}

	if amount <= 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "结算金额不能小于0")
	}

	remark = strings.TrimSpace(remark)

	if remark == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "结算备注不能为空")
	}

	err = s.supplierService.UnfreezeFrozenBalanceAmount(ctx, supplierID, amount)
	if err != nil {
		return err
	}

	profitSettlement := model.ProfitSettlement{
		ID:             primitive.NewObjectID(),
		SupplierID:     supplierID,
		Amount:         amount,
		Remark:         remark,
		MonthTimestamp: monthTimestamp,
		CreatedAt:      time.Now().UnixMilli(),
	}

	err = s.profitSettlementDao.Create(ctx, profitSettlement)
	if err != nil {
		return err
	}

	return nil
}

// ListSettlement 结算利润列表
func (s profitService) ListSettlement(ctx context.Context, monthTimestamp int64, page, limit int64) ([]model.ProfitSettlement, int64, error) {
	filter := bson.M{
		"month_timestamp": monthTimestamp,
	}

	settlements, total, err := s.profitSettlementDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}

	return settlements, total, nil
}

// ListSettlementBySupplier 结算利润列表
func (s profitService) ListSettlementBySupplier(ctx context.Context, supplierID primitive.ObjectID, monthTimestamp int64) ([]model.ProfitSettlement, error) {
	filter := bson.M{
		"supplier_id":     supplierID,
		"month_timestamp": monthTimestamp,
	}

	settlements, err := s.profitSettlementDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return settlements, nil
}
