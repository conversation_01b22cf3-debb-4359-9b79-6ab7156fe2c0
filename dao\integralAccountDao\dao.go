package integralAccountDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.IntegralAccount) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.IntegralAccount, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralAccount, int64, error)
	Get(ctx context.Context, filter bson.M) (model.IntegralAccount, error)
}

type integralAccountDao struct {
	db *mongo.Collection
}

func NewIntegralAccountDao(collect string) DaoInt {
	return integralAccountDao{
		db: global.MDB.Collection(collect),
	}
}

func (s integralAccountDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralAccount, int64, error) {
	var list []model.IntegralAccount
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "num", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s integralAccountDao) Get(ctx context.Context, filter bson.M) (model.IntegralAccount, error) {
	var data model.IntegralAccount
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IntegralAccount{}, err
	}
	return data, nil
}

func (s integralAccountDao) List(ctx context.Context, filter bson.M) ([]model.IntegralAccount, error) {
	opts := options.Find()

	sort := bson.D{
		bson.E{Key: "num", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	var list []model.IntegralAccount
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s integralAccountDao) Create(ctx context.Context, data model.IntegralAccount) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralAccountDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s integralAccountDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}
