package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// DeliverType 配送方式
type DeliverType int

const (
	DeliverTypeDoor           DeliverType = 1 // 送货到店
	DeliverTypeSelfPickUp     DeliverType = 2 // 自提
	DeliverTypeLogistics      DeliverType = 3 // 物流
	DeliverTypeInstantDeliver DeliverType = 4 // 即时配送
)

var DeliverTypeMsg = map[DeliverType]string{
	DeliverTypeDoor:           "自提",
	DeliverTypeSelfPickUp:     "配送",
	DeliverTypeLogistics:      "物流",
	DeliverTypeInstantDeliver: "即时配送",
}

// PayMethodType 支付方式
type PayMethodType string

const (
	PayMethodTypeWechat     PayMethodType = "wechat"      // 微信   改为jsapi
	PayMethodTypeBalance    PayMethodType = "balance"     // 余额
	PayMethodTypeYeeWechat  PayMethodType = "yee_wechat"  // 易宝-微信   改为jsapi
	PayMethodTypeYeeBalance PayMethodType = "yee_balance" // 易宝-余额
)

type OrderType string

const (
	OrderTypeRetail    OrderType = "retail"
	OrderTypeWholeSale OrderType = "wholeSale"
)

//type DeliverLogisticsFromType int
//
//const (
//	DeliverLogisticsFromTypePlatform    DeliverLogisticsFromType = 1 // 平台指派
//	DeliverLogisticsFromTypeSelfContact DeliverLogisticsFromType = 2 // 自主联系
//)

// ParentOrder  支付父单
type ParentOrder struct {
	ID                       primitive.ObjectID `json:"id" bson:"_id"`
	UserID                   primitive.ObjectID `json:"user_id" bson:"user_id"`                                         // 用户ID
	BuyerID                  primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                                       // 采购商ID
	ServicePointID           primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`                       // 服务仓ID
	SecondPointID            primitive.ObjectID `json:"second_point_id" bson:"second_point_id"`                         // 服务仓ID
	PayMethod                PayMethodType      `json:"pay_method" bson:"pay_method"`                                   // 支付方式
	BizOrderNo               string             `json:"biz_order_no" bson:"biz_order_no"`                               // 支付订单号（云商通
	BizOrderNoResult         PayResult          `json:"biz_order_no_result" bson:"biz_order_no_result"`                 // 微信支付结果
	BizOrderCouponNo         string             `json:"biz_order_coupon_no" bson:"biz_order_coupon_no"`                 // 代金券（云商通，未使用代金券时为空
	BizOrderCouponNoResult   PayResult          `json:"biz_order_coupon_no_result" bson:"biz_order_coupon_no_result"`   // 代金券支付结果
	TotalAmount              int                `json:"total_amount" bson:"total_amount"`                               // 总金额
	ProductTotalAmount       int                `json:"product_total_amount" bson:"product_total_amount"`               // 商品总金额
	OriginProductTotalAmount int                `json:"origin_product_total_amount" bson:"origin_product_total_amount"` // 原商品总金额
	PaidAmount               int                `json:"paid_amount" bson:"paid_amount"`                                 // 实付金额
	OrderType                OrderType          `json:"order_type" bson:"order_type"`                                   // 订单类型
	DeliverType              DeliverType        `json:"deliver_type" bson:"deliver_type"`                               // 配送方式
	InstantDeliverType       int                `json:"instant_deliver_type" bson:"instant_deliver_type"`               // 配送方式
	InstantDeliverName       string             `json:"instant_deliver_name" bson:"instant_deliver_name"`               // 配送方式
	DeliverFeeRes            DeliverFeeRes      `json:"deliver_fee_res" bson:"deliver_fee_res"`                         // 配送费
	DeliverReceiveBizUserID  string             `json:"deliver_receive_biz_user_id" bson:"deliver_receive_biz_user_id"` // 配送费收款人
	OrderNote                string             `json:"order_note" bson:"order_note"`                                   // 订单备注
	PayStatus                PayStatusType      `json:"pay_status" bson:"pay_status"`                                   // 支付状态
	Expire                   int64              `json:"expire" bson:"expire"`                                           // 订单超时
	TotalServiceFee          int                `json:"total_service_fee" bson:"total_service_fee"`                     // 服务费
	TotalTransportFee        int                `json:"total_transport_fee" bson:"total_transport_fee"`                 // 干线费
	TransportFeePerKG        int                `json:"transport_fee_per_kg" bson:"transport_fee_per_kg"`               // 干线费单价-分/kg
	UnitWarehouseLoadFee     int                `json:"unit_warehouse_load_fee" bson:"unit_warehouse_load_fee"`         // 仓配费单价
	TotalWarehouseLoadFee    int                `json:"total_warehouse_load_fee" bson:"total_warehouse_load_fee"`       // 仓配费总价
	PayResult                PayResult          `json:"pay_result" bson:"pay_result"`                                   // 代收支付结果
	InvoiceStatus            InvoiceStatusType  `json:"invoice_status" bson:"invoice_status"`                           // 发票状态
	CouponUserID             primitive.ObjectID `json:"coupon_user_id" bson:"coupon_user_id"`                           // 用户优惠券ID
	CouponAmount             int                `json:"coupon_amount" bson:"coupon_amount"`                             // 用户优惠券使用金额
	CouponMinAmount          int                `json:"coupon_min_amount" bson:"coupon_min_amount"`                     // 优惠券-门槛金额
	CouponTitle              string             `json:"coupon_title" bson:"coupon_title"`                               // 优惠券-标题
	LogisticsName            string             `json:"logistics_name" bson:"logistics_name"`                           // 物流名称
	WXPayResult              WXPayResult        `json:"wx_pay_result" bson:"wx_pay_result"`                             // 支付结果
	YeeWechatResult          YeeWechatResult    `json:"yee_wechat_result" bson:"yee_wechat_result"`                     // 易宝支付-微信
	YeeRefundDeliverResult   YeeRefundResult    `json:"yee_refund_deliver_result" bson:"yee_refund_deliver_result"`     // 配送费-退款
	CreatedAt                int64              `bson:"created_at" json:"created_at"`
	UpdatedAt                int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt                int64              `bson:"deleted_at" json:"deleted_at"`
}

//WXCancelResult           WXRefundResult     `json:"wx_cancel_result" bson:"wx_cancel_result"`                       // 取消订单

type Discount struct {
	DiscountType int                `json:"discount_type" bson:"discount_type"` // 优惠类型 1 优惠券
	ObjectID     primitive.ObjectID `json:"object_id" bson:"object_id"`         // 对应的优惠券账户ID   CouponAccount IDs
}

// SupplierOrder 供应商订单
type SupplierOrder struct {
	SupplierID        primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`                 // 供应商ID
	SupplierName      string             `json:"supplier_name" bson:"supplier_name"`             // 供应商名称
	TotalAmount       int                `json:"total_amount" bson:"total_amount"`               // 总金额
	UnitTransportFee  int                `json:"unit_transport_fee" bson:"unit_transport_fee"`   // 运费单价
	TotalTransportFee int                `json:"total_transport_fee" bson:"total_transport_fee"` // 总运费
	TotalWeight       int                `json:"total_weight" bson:"total_weight"`               // 总重量
	PaidAmount        int                `json:"paid_amount" bson:"paid_amount"`                 // 实付
	CashCouponAmount  int                `json:"cash_coupon_amount" bson:"cash_coupon_amount"`   // 优惠金额
	ProductList       []ProductOrder     `json:"product_list" bson:"product_list"`               // 产品列表
}

type PayResult struct {
	Status                 string                 `json:"status" bson:"status"`                     // 支付状态  OK
	PayStatus              string                 `json:"pay_status" bson:"pay_status"`             // 支付状态  仅交易验证方式为“0”时返回  成功：success  进行中：pending  失败：fail
	PayOpenID              string                 `json:"pay_open_id" bson:"pay_open_id"`           // 支付的openID
	PayFailMessage         string                 `json:"pay_fail_message" bson:"pay_fail_message"` // 支付失败信息	仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId              string                 `json:"biz_user_id" bson:"biz_user_id"`           // 仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
	Amount                 int                    `json:"amount" bson:"amount"`
	OrderNo                string                 `json:"order_no" bson:"order_no"`                                     // 云商通订单号
	BizOrderNo             string                 `json:"biz_order_no" bson:"biz_order_no"`                             // 商户订单号（支付订单）
	ReqPayInterfaceNo      string                 `json:"req_pay_interface_no" bson:"req_pay_interface_no"`             // 请求渠道流水号
	PayInterfaceOutTradeNo string                 `json:"pay_interface_out_trade_no" bson:"pay_interface_out_trade_no"` // 渠道交易流水号  针对收银宝相关支付渠道返回，对应收银宝交易单号trxid字段： 微信小程序支付（单、集团）
	PayInterfacetrxcode    string                 `json:"pay_interfacetrxcode" bson:"pay_interfacetrxcode"`             // 通道交易类型  仅收银宝-付款码支付方式返回，对应收银宝接口字段trxcode
	Acct                   string                 `json:"acct" bson:"acct"`                                             // 支付人帐号  仅收银宝-付款码支付方式返回， 微信支付的openid
	ChannelFee             string                 `json:"channel_fee" bson:"channel_fee"`                               // 渠道手续费	仅收银宝支付方式返回
	Chnldata               string                 `json:"chnldata" bson:"chnldata"`                                     // 收银宝渠道信息  透传渠道活动参数，目前返回云闪付/微信/支付宝的活动参数；
	ChannelPaytime         string                 `json:"channel_paytime" bson:"channel_paytime"`                       // 渠道交易完成时间	取值为收银宝接口交易完成时间 格式：yyyyMMddHHmmss
	Cusid                  string                 `json:"cusid" bson:"cusid"`                                           // 	渠道商户号	收银宝商户号
	TradeNo                string                 `json:"trade_no" bson:"trade_no"`                                     // 交易编号
	PayDatetime            string                 `json:"pay_datetime" bson:"pay_datetime"`
	PayInfo                map[string]interface{} `json:"pay_info" bson:"pay_info"`                             // 扫码支付信息/ JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息    收银宝微信小程序支付参数/微信原生小程序支付参数必传 注：有效时间60分钟
	ValidationType         int                    `json:"validation_type" bson:"validation_type"`               // 交易验证方式	当支付方式为收银宝快捷且需验证短信验证码时才返回
	MiniprogrampayinfoVsp  map[string]interface{} `json:"miniprogramPayInfo_VSP" bson:"miniprogramPayInfo_VSP"` // 小程序收银台支付参数
	ExtendInfo             string                 `json:"extend_info" bson:"extend_info"`                       // 扩展信息
	Termrefnum             string                 `json:"termrefnum" bson:"termrefnum"`                         // 扩展信息
	Termauthno             string                 `json:"termauthno" bson:"termauthno"`                         // 扩展信息
	Chnltrxid              string                 `json:"chnltrxid" bson:"chnltrxid"`                           // 扩展信息
}

type PayCloseResult struct {
	OrderStatus   int    `json:"order_status" bson:"order_status"`     // 	订单状态	1-未支付 3-交易失败 4-交易完成 5-交易完成（发生退款） 6-关闭 99-进行中
	ErrorMessage  string `json:"error_message" bson:"error_message"`   // 	关闭失败原因
	CloseDatetime string `json:"close_datetime" bson:"close_datetime"` // 订单关闭完成时间	yyyy-MM-dd HH:mm:ss
	OrderNo       string `json:"order_no" bson:"order_no"`             // 云商通订单号
	BizOrderNo    string `json:"biz_order_no" bson:"biz_order_no"`     // 商户订单号（支付订单）
	CloseResult   int    `json:"close_result" bson:"close_result"`     // 关闭结果	1-关闭成功 2-关闭失败
}

// WithdrawPayResult 提现申请
type WithdrawPayResult struct {
	Status                 string                 `json:"status" bson:"status"`                     //
	PayStatus              string                 `json:"pay_status" bson:"pay_status"`             // 支付状态  仅交易验证方式为“0”时返回  成功：success  进行中：pending  失败：fail
	PayFailMessage         string                 `json:"pay_fail_message" bson:"pay_fail_message"` // 支付失败信息	仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId              string                 `json:"biz_user_id" bson:"biz_user_id"`           // 仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
	OrderNo                string                 `json:"order_no" bson:"order_no"`                 // 云商通订单号
	BizOrderNo             string                 `json:"biz_order_no" bson:"biz_order_no"`         // 商户订单号（支付订单）
	ChannelExtendInfo      map[string]interface{} `json:"channel_extend_info" bson:"channel_extend_info"`
	PayInterfaceOutTradeNo string                 `json:"pay_interface_out_trade_no" bson:"pay_interface_out_trade_no"`
	ExtendInfo             string                 `json:"extend_info" bson:"extend_info"` // 扩展信息
	PayDateTime            string                 `json:"pay_date_time" bson:"pay_date_time"`
	Acct                   string                 `json:"acct" bson:"acct"`
}
