package topicService

import (
	"base/dao"
	"base/dao/topicDao"
	"base/model"
	"base/service/productHistoryService"
	"base/types"
	"base/util"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, req types.TopicCreateReq) (primitive.ObjectID, error)
	Update(ctx context.Context, req types.TopicUpdateReq) error
	UpdateProduct(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error
	DownProduct(ctx context.Context, productID primitive.ObjectID, isDel bool) error
	UpProduct(ctx context.Context, productID primitive.ObjectID) error
	List(ctx context.Context, visible bool) ([]model.Topic, error)
	ListALl(ctx context.Context) ([]model.Topic, error)
	ListVisible(ctx context.Context) ([]model.Topic, error)
	Del(ctx context.Context, Id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Topic, error)
}

type topicService struct {
	db              topicDao.DaoInt
	productHistoryS productHistoryService.ServiceInterface
}

func (s topicService) Get(ctx context.Context, id primitive.ObjectID) (model.Topic, error) {
	get, err := s.db.Get(ctx, bson.M{"_id": id})
	return get, err
}

func NewTopicService() ServiceInterface {
	return topicService{
		db:              dao.TopicDao,
		productHistoryS: productHistoryService.NewProductHistoryService(),
	}
}

func (s topicService) DownProduct(ctx context.Context, productID primitive.ObjectID, isdel bool) error {
	filter := bson.M{
		"product_list": bson.M{
			"$in": bson.A{productID},
		},
	}

	list, err := s.db.ListByCus(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"$pull": bson.M{
			"product_list": bson.M{
				"$in": bson.A{productID},
			},
		},
	}

	err = s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	if isdel {
		return nil
	}
	for _, v := range list {
		s.productHistoryS.NotifyDownSale(ctx, model.HistoryTypeTopic, v.ID, productID)
	}

	return nil
}

func (s topicService) UpProduct(ctx context.Context, productID primitive.ObjectID) error {
	list, err := s.productHistoryS.ListUpSale(ctx, model.HistoryTypeTopic, productID)
	if err != nil {
		return err
	}

	for _, v := range list {
		update := bson.M{
			"$addToSet": bson.M{
				"product_list": productID,
			},
		}
		zap.S().Infof(v.ID.Hex(), "up--------------------", productID.Hex())
		err = s.db.UpdateInfo(ctx, bson.M{"_id": v.ObjectID}, update)
		if err != nil {
			zap.S().Errorf("topicService UpProduct错误%v", err)
		}
	}
	return nil
}

func (s topicService) Create(ctx context.Context, req types.TopicCreateReq) (primitive.ObjectID, error) {
	now := time.Now().UnixMilli()
	topic := model.Topic{
		ID:          primitive.NewObjectID(),
		Sort:        req.Sort,
		Visible:     req.Visible,
		Page:        req.Page,
		TopImg:      req.TopImg,
		ProductList: nil,
		Img:         req.Img,
		CreatedAt:   now,
	}

	topic.ProductList = make([]primitive.ObjectID, len(req.ProductList))
	for i, productId := range req.ProductList {
		id, err := primitive.ObjectIDFromHex(productId)
		if err != nil {
			return [12]byte{}, err
		}
		topic.ProductList[i] = id
	}

	err := s.db.Create(ctx, topic)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return topic.ID, nil
}

func (s topicService) Update(ctx context.Context, req types.TopicUpdateReq) error {
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		return err
	}

	productList := make([]primitive.ObjectID, len(req.ProductList))
	for i, productId := range req.ProductList {
		productList[i], err = primitive.ObjectIDFromHex(productId)
		if err != nil {
			return err
		}
	}

	return s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
		"sort":         req.Sort,
		"visible":      req.Visible,
		"page":         req.Page,
		"top_img":      req.TopImg,
		"product_list": productList,
		"img":          req.Img,
		"updated_at":   time.Now().UnixMilli(),
	}})
}

func (s topicService) UpdateProduct(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error {
	return s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
		"product_list": pIDs,
		"updated_at":   time.Now().UnixMilli(),
	}})
}

func (s topicService) ListALl(ctx context.Context) ([]model.Topic, error) {
	list, err := s.db.ListByCus(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s topicService) ListVisible(ctx context.Context) ([]model.Topic, error) {
	list, err := s.db.ListByCus(ctx, bson.M{
		"visible": true,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s topicService) List(ctx context.Context, visible bool) ([]model.Topic, error) {
	list, err := s.db.ListByCus(ctx, bson.M{
		"visible": visible,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s topicService) Del(ctx context.Context, Id primitive.ObjectID) error {
	return s.db.Delete(ctx, Id)
}
