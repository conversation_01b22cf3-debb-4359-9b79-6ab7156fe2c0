package sys

import (
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/buyerService"
	"base/service/integralAccountService"
	"base/service/integralRecordService"
	"base/service/orderQualityService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/productService"
	"base/service/userService"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	pays "github.com/cnbattle/allinpay/service"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/signintech/gopdf"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func Test(ctx *gin.Context) {
	var req = struct {
		P1 string  `json:"p1"`
		P2 string  `json:"p2"`
		P3 int     `json:"p3"`
		P4 int64   `json:"p4"`
		P5 float64 `json:"p5"`
		P6 float64 `json:"p6"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//id, err := primitive.ObjectIDFromHex("657e9035456d1d5073e40aad")
	//if err != nil {
	//	return
	//}
	//note, err := deliverNoteService.NewDeliverNoteService().GetByID(ctx, id)
	//if err != nil {
	//	return
	//}
	//buyerList := []model.MNSBuyer{{
	//	BuyerID:     note.BuyerID.Hex(),
	//	DeliverType: model.DeliverTypeLogistics,
	//}}
	//genData := model.MNSGenDeliverNote{
	//	BuyerList: buyerList,
	//	Timestamp: note.DayAt,
	//}
	//
	//mnsSendService.NewMNSClient().SendDeliverNoteGenerate(genData)
	//
	//return

	//var buyerList []model.MNSBuyer
	//for _, order := range orderList {
	//	var exist bool
	//	for _, b := range buyerList {
	//		if b.BuyerID == order.BuyerID.Hex() && b.DeliverType == order.DeliverType {
	//			exist = true
	//			break
	//		}
	//	}
	//	if !exist {
	//		buyerList = append(buyerList, model.MNSBuyer{
	//			BuyerID:     order.BuyerID.Hex(),
	//			DeliverType: order.DeliverType,
	//		})
	//	}
	//}
	//
	//genData := model.MNSGenDeliverNote{
	//	BuyerList: buyerList,
	//	Timestamp: zeroTimestamp,
	//}
	//
	//mnsSendService.NewMNSClient().SendDeliverNoteGenerate(genData)

	//supplierStatService.NewSupplierStatService().CalcDaily(ctx)

	//mnsSendService.NewMNSClient().SendConsumeIntegral(model.RecordTypeComment, primitive.NewObjectID(), primitive.NewObjectID(), 1)

	//now := time.Now()
	//rdb := global.RDBDefault
	//key := "orderIDNum:" + now.Format("20060102")
	//
	//val := rdb.Incr(ctx, key).Val()
	//
	//rdb.Expire(ctx, key, time.Hour*24)
	//orderNum := fmt.Sprintf("%04d", val)
	//
	//t := now.Format("20060102150405")
	//
	//id := t + orderNum
	//fmt.Printf("id:%s\n", id)

	//id, err := util.ConvertToObjectWithCtx(ctx, req.P1)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//_ = id
	//
	//mnsSendService.NewMNSClient().SendAgentPay(id.Hex())
	//
	//orders, _ := orderService.NewOrderService().List(ctx, bson.M{
	//	"_id": bson.M{
	//		"$in": bson.A{id},
	//	},
	//})
	//
	//var orderRefundAllIDList []primitive.ObjectID
	//for _, order := range orders {
	//	refundAll := true
	//	for _, i := range order.ProductList {
	//		if i.IsShipRefundAll == false {
	//			refundAll = false
	//			break
	//		}
	//	}
	//	if refundAll {
	//		orderRefundAllIDList = append(orderRefundAllIDList, order.ID)
	//	}
	//}
	//refundAll, _ := json.Marshal(orderRefundAllIDList)
	//fmt.Printf("订单全退列表::%s\n", string(refundAll))

	//orderAgentPayService.NewOrderAgentPayService().SingleOrderAgentPay(ctx, id)
	//supplierStatService.NewSupplierStatService().CalcDaily(ctx)

	//orderAgentPayService.NewOrderAgentPayService().SingleOrderAgentPayDeliverPay(ctx, id)

	//sendSMS(ctx)

	//fmt.Println(util.NewUUID())

	//refundNotSplit(context.Background())
	//manualGenIntegral(ctx)

	//integralRecordService.NewIntegralRecordService().DelMany(ctx, bson.M{"note": "历史订单积分"})
	//calcAllIntegral(ctx)

	//refundParentOrderID(ctx)

	//dealPriceListToPrice(ctx)

	//dealDiscountList(ctx)
	//orderCheck(ctx)

	//dealOrderCreateTime(ctx)
	//doAgentPay(ctx, req.P1)

	//id, _ := primitive.ObjectIDFromHex("6456032ef99212289203a497")
	//products, err := productService.NewProductService().List(ctx, bson.M{
	//	"supplier_id": id,
	//})
	//newID, _ := primitive.ObjectIDFromHex("657d2cd3609aa6dc440afe64")
	//for _, product := range products {
	//	productService.NewProductService().UpdateCus(ctx, product.ID, bson.M{
	//		"supplier_id": newID,
	//	})
	//}

	//refundDeposit2(ctx)
	//firstOrder(ctx)
	//statBuyer(ctx)

	//id, _ := primitive.ObjectIDFromHex("65729e39ad4572cf83eb4c70")
	//num, _, err := dao.OrderDao.CountProductNum(ctx, id)
	//if err != nil {
	//	return
	//}
	//_ = num

}

func migrateQualityPhoto(ctx context.Context) {
	filter := bson.M{
		"stock_up_day_time": bson.M{
			"$gte": 1704038400000, // 2024-01-01 00:00:00
		},
	}
	list, err := orderQualityService.NewOrderQualityService().List(ctx, filter)
	if err != nil {
		return
	}
	for _, quality := range list {
		for _, temp := range quality.OrderList {
			if temp.Photo.Name != "" {
				//	 直接迁移至订单

				var pList []model.FileInfo
				pList = append(pList, model.FileInfo{
					Type: "image",
					Name: temp.Photo.Name,
				})

				update := bson.M{
					"product_list.$.photo_list": pList,
				}
				_ = update
				zap.S().Infof("更新：%v,%d", update, quality.StockUpDayTime)
				//err = orderService.NewOrderService().UpdateOne(ctx, bson.M{"_id": temp.OrderID, "product_list.product_id": quality.ProductID}, bson.M{
				//	"$set": update,
				//})
				//if err != nil {
				//	zap.S().Errorf("err:%s", err.Error())
				//	return
				//}
			}
		}
	}
}
func refundDeposit2(ctx context.Context) {
	amount := 50000
	var rList []pays.RefundItem
	rList = append(rList, pays.RefundItem{
		//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
		//BizUserId: "0f047ccd-544a-4413-ab38-c37de24fefc2",
		//AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
		BizUserId: "d88807f6-3beb-4661-8e30-502bb5c4164d",
		Amount:    amount,
	})

	tlt := "TLT"
	payReq := pays.RefundReq{
		BizOrderNo:    util.NewUUID(),
		OriBizOrderNo: "f5de5755-2544-4ba3-b93a-378d71dc9021",
		OriOrderNo:    "1742526132512976896",
		BizUserId:     "d88807f6-3beb-4661-8e30-502bb5c4164d", // 收款人-原订单的付款方  lgl
		RefundType:    "D0",
		RefundList:    rList,
		BackUrl:       global.BackHost + global.BackUrlRefundManual,
		Amount:        amount,
		CouponAmount:  0,
		FeeAmount:     0,
		RefundAccount: tlt,
		ExtendInfo:    "充值退款：手动",
	}
	res, err := payModule.NewOrderS().RefundS(payReq)
	if err != nil {
		log.Println(err)
		return
	}
	if res.PayStatus == "fail" {
		log.Println(err)
		return
	}
	_ = res
	log.Println(res)
}

func checkMobile(ctx context.Context) {
	listAll, err := authenticationService.NewAuthenticationService().ListAll(ctx)
	if err != nil {
		return
	}

	var userIDs []primitive.ObjectID
	mAuth := make(map[primitive.ObjectID]string)
	for _, auth := range listAll {
		userIDs = append(userIDs, auth.UserID)
		mAuth[auth.UserID] = auth.Mobile
	}

	users, err := userService.NewUserService().ListByIDs(ctx, userIDs)
	if err != nil {
		return
	}

	for id, mobile := range mAuth {
		for _, user := range users {
			if user.ID == id {
				if user.Mobile != mobile {
					buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, user.ID)
					if err != nil {
						return
					}
					_ = buyer
					zap.S().Infof("不一致，用户id:%s,认证id:%s,用户手机号：%s，认证手机号：%s,会员:%s", user.ID.Hex(), id.Hex(), user.Mobile, mobile, buyer.BuyerName)
				}
			}
		}
	}
}

// 统计
func statBuyer(ctx context.Context) {
	list, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1709222400000, // 2023-12-01 00:00:00
			"$lte": 1709913540000, // 2024-12-31 23:59:00
		},
	})
	if err != nil {
		return
	}

	//	 入驻打印会员
	var i int
	for _, buyer := range list {

		//if buyer.BusinessLicenseImg.Name != "" && buyer.AuditStatus == 2 {
		//	i++
		//}
		format := time.UnixMilli(buyer.CreatedAt).Format("2006-01-02 15:04:05")
		fmt.Printf("%s,%s\n", buyer.BuyerName, format)
	}

	zap.S().Infof("总计营业执照:%d", i)

}

// 统计-首次下单
func firstOrder(ctx context.Context) {
	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1704038400000, // 2023-12-01 00:00:00
			"$lte": 1704643200000, // 2
		},
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	})
	if err != nil {
		return
	}

	m := make(map[primitive.ObjectID]int)
	for _, order := range orders {
		m[order.BuyerID] = 0
	}

	mOrder := make(map[primitive.ObjectID]int)
	for buyerID, _ := range m {
		count, err := orderService.NewOrderService().Count(ctx, bson.M{
			"buyer_id": buyerID,
			"created_at": bson.M{
				"$lt": 1704038400000, // 2023-12-01 00:00:00
				//"$gt": 1703433600000,
			},
			//"order_status": bson.M{
			//	"$gte": model.OrderStatusTypeToStockUp,
			//},
		})
		if err != nil {
			return
		}
		if count == 0 {
			mOrder[buyerID] = 0
		}
	}
	//
	zap.S().Infof("首次下单：%d", len(mOrder))
	//
	mFirst := make(map[string]string)
	//
	for _, order := range orders {
		if _, ok := mOrder[order.BuyerID]; ok {
			format := time.UnixMilli(order.CreatedAt).Format("2006-01-02 15:04:05")
			//fmt.Printf("%s,%s\n", order.BuyerName, format)
			if _, ok2 := mFirst[order.BuyerName]; !ok2 {
				mFirst[order.BuyerName] = format
			}
		}
	}
	for s, s2 := range mFirst {
		fmt.Printf("%s,%s\n", s, s2)
	}
}

func refundDeposit(ctx context.Context) {
	//list, _ := buyerBalanceOrderService.NewBuyerBalanceOrderService().List(ctx, bson.M{
	//	"pay_status": 4,
	//})
	//for _, order := range list {
	//	amount := order.Amount
	//	var rList []pays.RefundItem
	//	rList = append(rList, pays.RefundItem{
	//		//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
	//		//BizUserId: "0f047ccd-544a-4413-ab38-c37de24fefc2",
	//		//AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	//		BizUserId: "901bd8fc-27dc-465d-8c81-f0c9c89ab838",
	//		Amount:    amount,
	//	})
	//
	//	tlt := "TLT"
	//	payReq := pays.RefundReq{
	//		BizOrderNo:    util.NewUUID(),
	//		OriBizOrderNo: order.PayResult.BizOrderNo,
	//		OriOrderNo:    order.PayResult.OrderNo,
	//		BizUserId:     order.PayResult.BizUserId, // 收款人-原订单的付款方  lgl
	//		RefundType:    "D0",
	//		RefundList:    rList,
	//		BackUrl:       global.BackHost + global.BackUrlRefundManual,
	//		Amount:        amount,
	//		CouponAmount:  0,
	//		FeeAmount:     0,
	//		RefundAccount: tlt,
	//		ExtendInfo:    "重置-退款：手动",
	//	}
	//	res, err := payModule.NewOrderS().RefundS(payReq)
	//	if err != nil {
	//		log.Println(err)
	//		return
	//	}
	//	if res.PayStatus == "fail" {
	//		log.Println(err)
	//		return
	//	}
	//	_ = res
	//	log.Println(res)
	//}
}

func doAgentPay(ctx context.Context) {
	//list := []string{"6567fe761c18c14214c33141", "653b9c6877d46a48231fb0eb", "6545a9d1c9b3b227f6aba055", "654847abc9b3b227f6abaa64", "654f95c41fcb0d2bbc3eb3d7"}
	//_ = list
	//
	//for _, s := range list {
	//	orderAgentPayService.NewOrderAgentPayService().ToAgentPay(ctx, s)
	//}

}

func checkRefundAll(ctx context.Context) {
	orderList, err := orderService.NewOrderService().List(ctx, bson.M{
		"product_list.is_ship_refund_all": true,
	})
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, order := range orderList {
		ids = append(ids, order.ID)
	}

	refundList, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		return
	}

	for _, order := range orderList {
		var f bool
		for _, refund := range refundList {
			if order.ID == refund.OrderID {
				f = true
			}
		}
		if !f {
			//	不存在
			zap.S().Errorf("order_id: %s 订单不存在", order.ID.Hex())
		}
	}

}

func dealOrderCreateTime(ctx context.Context) {
	list, err := orderService.NewOrderService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1699321031000,
			"$lte": 1701913031000, // 2023-12-07 09:37:11
		},
		"has_agent_pay":    false,
		"order_refund_all": false,
		"order_status":     model.OrderStatusTypeFinish,
	})
	if err != nil {
		return
	}

	id, _ := primitive.ObjectIDFromHex("647e9d95b1fd9a6008654e69")
	_ = id
	for i, order := range list {
		//if order.SupplierID != id {
		//	continue
		//}

		_ = i
		format := time.UnixMilli(order.CreatedAt).Format(time.RFC3339)
		zap.S().Infof("时间：%s，订单id：%s,供应商：%s,供应商id:%s", format, order.ID.Hex(), order.SupplierName, order.SupplierID.Hex())

		//mnsSendService.NewMNSClient().SendAgentPay(order.ID.Hex())
		//
		//time.Sleep(time.Second * 5)

		//for index, order := range orders {
		//	createdAt := order.CreatedAt + int64(index)
		//	orderService.NewOrderService().UpdateOne(ctx, bson.M{
		//		"_id": order.ID,
		//	}, bson.M{
		//		"$set": bson.M{
		//			"created_at": createdAt,
		//		},
		//	})
		//}

	}
}

func statOrderCreateTime(ctx context.Context) {
	id, _ := primitive.ObjectIDFromHex("6460a64ded085912072576e7")

	list, err := orderService.NewOrderService().List(ctx, bson.M{
		"buyer_id": id,
	})
	if err != nil {
		return
	}

	m := make(map[int64][]model.Order)
	for _, order := range list {
		m[order.CreatedAt] = append(m[order.CreatedAt], order)
	}

	for i, orders := range m {
		zap.S().Infof("时间：%d，订单数：%d", i, len(orders))
	}

}

func orderCheck(ctx context.Context) {
	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": 1696734614000,
		},
		"order_refund_all": true,
	})
	if err != nil {
		zap.S().Error(err)
		return
	}

	var ids []primitive.ObjectID
	for _, order := range orders {
		ids = append(ids, order.ID)
	}

	refunds, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		zap.S().Error(err)
		return
	}

	for _, order := range orders {
		var f bool
		for _, refund := range refunds {
			if refund.OrderID == order.ID {
				f = true
				break
			}
		}
		if !f {
			zap.S().Errorf("------------------------:%s", order.ID.Hex())
		}
	}

}

func dealPriceListToPrice(ctx context.Context) {
	var limit int64 = 300
	for i := 1; i <= 10; i++ {
		page := int64(i)
		products, _, err := productService.NewProductService().ListByCus(ctx, bson.M{
			"sale": false,
		}, page, limit)
		if err != nil {
			zap.S().Error("查询商品列表错误")
		}

		for index, product := range products {
			price := product.PriceList[0].Price
			originPrice := 0
			if len(product.OriginPriceList) > 0 {
				originPrice = product.OriginPriceList[0].Price
			}
			update := bson.M{
				"price":        price,
				"origin_price": originPrice,
			}
			_ = update
			//if product.ID.Hex() == "653c740f77d46a48231fb3e8" {
			//err = productService.NewProductService().UpdateCus(ctx, product.ID, update)
			//if err != nil {
			//	return
			//}

			//return
			zap.S().Infof("商品[%d]处理完成", index)
		}
		zap.S().Infof("%d页商品列表处理完成", page)
	}

}

func dealDiscountList(ctx context.Context) {
	var limit int64 = 300
	for i := 1; i <= 10; i++ {
		page := int64(i)
		products, _, err := productService.NewProductService().ListByCus(ctx, bson.M{
			"sale": false,
		}, page, limit)
		if err != nil {
			zap.S().Error("查询商品列表错误")
		}

		for index, product := range products {

			if len(product.PriceList) <= 1 {
				continue
			}

			//	 阶梯价

			var discountList []model.ProductDiscount

			price := product.PriceList[0].Price

			for j, per := range product.PriceList {
				if j == 0 {
					continue
				}

				priceDe := decimal.NewFromInt(int64(price))
				pDe := decimal.NewFromInt(int64(per.Price))

				discountValue := pDe.Div(priceDe).Mul(decimal.NewFromInt(100)).RoundCeil(0).IntPart()
				if discountValue == 100 {
					continue
				}

				discountList = append(discountList, model.ProductDiscount{
					Num:   per.Num,
					Value: int(discountValue),
				})
			}
			if len(discountList) == 0 {
				continue
			}

			update := bson.M{
				"discount_list": discountList,
			}
			_ = update
			zap.S().Infof("update  %s ", update)
			//return

			//if product.ID.Hex() == "653c740f77d46a48231fb3e8" {
			err = productService.NewProductService().UpdateCus(ctx, product.ID, update)
			if err != nil {
				return
			}
			zap.S().Infof("商品[%d]处理完成", index)
		}
		zap.S().Infof("%d页商品列表处理完成", page)
	}

}

func refundParentOrderID(ctx context.Context) {
	//accounts, _, _ := integralAccountService.NewIntegralAccountService().ListByPage(ctx, bson.M{}, 1, 300)
	refunds, _ := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"created_at": bson.M{
			"$gte": *************,
		},
		"parent_order_id": primitive.NilObjectID,
	})

	for _, refund := range refunds {

		_ = refund

		//order, err := orderService.NewOrderService().Get(ctx, refund.OrderID)
		//if err != nil {
		//	return
		//}
		//
		//orderRefundService.NewOrderRefundService().UpdateParentOrderID(ctx, refund.ID, order.ParentOrderID)

	}

	fmt.Println(len(refunds))
}

func calcAllIntegral(ctx context.Context) {
	//accounts, _, _ := integralAccountService.NewIntegralAccountService().ListByPage(ctx, bson.M{}, 1, 300)
	records, _ := integralRecordService.NewIntegralRecordService().List(ctx, bson.M{})

	for _, record := range records {
		if record.ChangeNum == 0 {
			marshal, _ := json.Marshal(record)
			fmt.Printf("等于0:%s\n", string(marshal))
			//integralRecordService.NewIntegralRecordService().DelMany(ctx, bson.M{"_id": record.ID})
		}
	}

	return

	mRecord := make(map[primitive.ObjectID][]model.IntegralRecord)
	for _, record := range records {
		mRecord[record.IntegralAccountID] = append(mRecord[record.IntegralAccountID], record)
	}

	for accountID, integralRecords := range mRecord {
		var num int
		for _, record := range integralRecords {
			if record.ChangeType == 1 {
				num += record.ChangeNum
			}
			if record.ChangeType == 0 {
				num -= record.ChangeNum
			}
		}
		integralAccountService.NewIntegralAccountService().UpdateNum(ctx, accountID, num)
	}

}

func transfer(supplierID primitive.ObjectID, amount int) {
	auth, err := authenticationService.NewAuthenticationService().GetBySupplier(context.Background(), supplierID)
	if err != nil {
		return
	}

	req := pays.ApplicationTransferReq{
		BizTransferNo:      util.NewUUID(),
		SourceAccountSetNo: global.AllInPayAccountSetInfo.StandardBalanceNo, // 标准余额
		TargetBizUserId:    auth.PayBizUserId,                               // 供应商bizUserID
		TargetAccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,      // 托管账户集
		Amount:             amount,
		ExtendInfo:         "平台转账供应商",
	}
	transferRes, err := payModule.NewOrderS().ApplicationTransferS(req)
	if err != nil {
		zap.S().Errorf("transfer %s", err.Error())
		return
	}
	_ = transferRes
}

type ApplicationTransferReq struct {
	BizTransferNo      string `json:"bizTransferNo"`      // 必填    商户系统转账订单号，商户系统唯一	全局唯一，不可重复，商户侧务必保障此订单号全局唯一，不可重复，如订单号重复，则影响订单退款。
	SourceAccountSetNo string `json:"sourceAccountSetNo"` // 必填    源账户集编号	云商通统一的标准账户集合的编号。详细
	TargetBizUserId    string `json:"targetBizUserId"`    // 必填    目标商户系统用户标识，商户系统中唯一编号。	收款会员的BizUserId
	TargetAccountSetNo string `json:"targetAccountSetNo"` // 必填    目标账户集编号	云商通分配的托管专用账户集的编号
	Amount             int    `json:"amount"`             // 必填    金额	单位：分。
	ExtendInfo         string `json:"extendInfo"`         //  扩展信息
}

func refundNotSplit(ctx context.Context) {
	amount := 4228
	var rList []pays.RefundItem
	rList = append(rList, pays.RefundItem{
		//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
		//BizUserId: "0f047ccd-544a-4413-ab38-c37de24fefc2",
		//AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
		BizUserId: "70ec9f15-e938-4f68-a4dc-594f5a182630",
		Amount:    amount,
	})

	tlt := "TLT"
	payReq := pays.RefundReq{
		BizOrderNo:    "b427b697-6d71-4cf9-a126-9e5f6c185f94",
		OriBizOrderNo: "633c03ca-440c-42d8-b905-e6fa22c90f1c",
		OriOrderNo:    "1669331439306416128",
		BizUserId:     "c4b3f16e-a174-44a6-977f-1cb5ec10b014", // 收款人-原订单的付款方  lgl
		RefundType:    "D0",
		RefundList:    rList,
		BackUrl:       global.BackHost + global.BackUrlRefund,
		Amount:        amount,
		CouponAmount:  0,
		FeeAmount:     0,
		RefundAccount: tlt,
		ExtendInfo:    "退款：手动",
	}
	res, err := payModule.NewOrderS().RefundS(payReq)
	if err != nil {
		log.Println(err)
		return
	}
	if res.PayStatus == "fail" {
		log.Println(err)
		return
	}
	_ = res
	log.Println(res)
}

// 中秋国庆短信
func sendSMS(ctx context.Context) {
	//users, _, err := userService.NewUserService().List(ctx, 1, 2500)
	//if err != nil {
	//	zap.S().Errorf("查询用户：%s", err.Error())
	//}
	//for i, user := range users {
	//	mobile := user.Mobile
	//	err := messageService.NewCaptchaService().SendMarketingMessage(mobile)
	//	if err != nil {
	//		zap.S().Errorf("发送短信：%s", err.Error())
	//	}
	//	global.RDBDefault.ZAdd(context.Background(), "message-marketing", &redis.Z{Member: mobile, Score: float64(time.Now().UnixMilli())})
	//	zap.S().Infof("发送index:%d", i)
	//}

	//messageService.NewCaptchaService().SendMarketingMessage("***********")

}

//id1, _ := primitive.ObjectIDFromHex("6514eda94450856bc700070f")
//err = orderPointService.NewOrderPointService().DoReceive(ctx, id1, []model.FileInfo{}, time.Now().UnixMilli())

//"6514467ca6648027bd2f6eb2","6514467ca6648027bd2f6eb3"
//orderIDs := "6514482a68df4dc59f60184a"
//mnsSendService.NewMNSClient().SendCheckAfterShip(orderIDs)
//id1, _ := primitive.ObjectIDFromHex("65144bd57368fb8c871e997e")
//id2, _ := primitive.ObjectIDFromHex("65144bd57368fb8c871e997f")
//var ids []primitive.ObjectID
//ids = append(ids, id1)
//ids = append(ids, id2)
//
//mnsSendService.NewMNSClient().SendDoAfterShipRefund(ids)

//uuid := util.NewUUID()
//
//xhttp.RespSuccess(ctx, uuid)
//id, err := util.ConvertToObjectWithNote(req.P1, "")
//if err != nil {
//	xhttp.RespErr(ctx, err)
//	return
//}

//accessToken, err := miniService.NewMiniService(model.ObjectTypeBuyer).GetAccessToken()
//if err != nil {
//	log.Println(err)
//}
//xhttp.RespSuccess(ctx, accessToken)

//tempRefundStats(ctx)

//var data allinpay.NotifyPay
//data.BizOrderNo = req.P2
//data.Status = "OK"
//err = orderRefundService.NewOrderRefundService().NotifyRefundStatus(ctx, data)
//if err != nil {
//	zap.S().Error(err)
//	return
//}

func p() {
	pdf := gopdf.GoPdf{}
	pdf.Start(gopdf.Config{PageSize: *gopdf.PageSizeA4})
	//if err := pdf.SetFont("loma", "", 14); err != nil {
	//	log.Panic(err.Error())
	//}

	//pdf.AddHeader(func() {
	//	pdf.SetY(5)
	//	pdf.Cell(nil, "header")
	//})
	//pdf.AddFooter(func() {
	//	pdf.SetY(825)
	//	pdf.Cell(nil, "footer")
	//})
	pdf.AddPage()
	err := pdf.Cell(nil, "您好")
	if err != nil {

	}
	pdf.WritePdf("./api/sys/" + "hello.pdf")
}

func attr(name string) {
	origin, err := excelize.OpenFile("./api/orderDeliverNote/" + name)
	if err != nil {
		zap.S().Error(err)
	}
	f, err := excelize.OpenFile("./api/orderDeliverNote/3.xlsx")
	if err != nil {
		zap.S().Error(err)
	}

	sheetName := "sheet1"

	//originProps, err := origin.GetSheetProps(sheetName)
	//fProps, err := f.GetSheetProps(sheetName)
	//
	//_ = originProps
	//_ = fProps

	//originView, err := origin.GetSheetView(sheetName, 1)
	//fView, err := f.GetSheetView(sheetName, 1)
	//
	//_ = originView
	//_ = fView

	//originLay, err := origin.GetPageLayout(sheetName)
	//fLay, err := f.GetPageLayout(sheetName)
	//
	//_ = originLay
	//_ = fLay

	//originWorkbook, err := origin.GetWorkbookProps()
	//fWorkbook, err := f.GetWorkbookProps()
	//
	//_ = originWorkbook
	//_ = fWorkbook

	//originGetAppProps, err := origin.GetAppProps()
	//fGetAppProps, err := f.GetAppProps()
	//
	//_ = originGetAppProps
	//_ = fGetAppProps

	_ = sheetName

	originGetDocProps, err := origin.GetDocProps()
	fGetDocProps, err := f.GetDocProps()

	_ = originGetDocProps
	_ = fGetDocProps

	zap.S().Info()

}

func tempRefundStats(ctx *gin.Context) {
	filter := bson.M{}
	filter["order_status"] = model.OrderStatusTypeFinish
	filter["pay_status"] = model.PayStatusTypePaid

	filter["created_at"] = bson.M{
		"$gte": 1688140800000,
		//"$lte": 1692258842000,
		"$lte": 1690819199000,
	}

	list, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, order := range list {
		orderIDs = append(orderIDs, order.ID)
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	var totalShipRefundAmount int
	var totalAfterSalePassAmount int
	var totalAfterSaleApplyAmount int
	var afterSaleNum int

	mBuyer := make(map[primitive.ObjectID][]model.OrderRefund)
	for _, r := range refunds {
		if r.RefundType == model.RefundTypeQuality {
			totalShipRefundAmount += r.AuditAmount + r.TotalTransportFee
		}
		if r.RefundType == model.RefundTypeAfterSale {
			totalAfterSaleApplyAmount += r.Amount
			afterSaleNum++
			mBuyer[r.BuyerID] = append(mBuyer[r.BuyerID], r)
		}
		if r.RefundType == model.RefundTypeAfterSale && r.AuditStatus == model.AuditStatusTypePass {
			totalAfterSalePassAmount += r.AuditAmount
			if r.RefundResult.PayStatus != "success" {
				marshal, _ := json.Marshal(r)
				zap.S().Error(string(marshal))
			}
		}
	}
	zap.S().Errorf("总退款单数:%d", len(refunds))
	zap.S().Errorf("售后退款单数:%d", afterSaleNum)
	zap.S().Infof("总发货退款：%.2f", float64(totalShipRefundAmount)/100)
	zap.S().Infof("总售后申请额：%.2f", float64(totalAfterSaleApplyAmount)/100)
	zap.S().Infof("总售后退款额：%.2f", float64(totalAfterSalePassAmount)/100)

	i := 1
	for _, rList := range mBuyer {
		var totalAfterSaleApplyAmountBuyer int
		var totalAfterSalePassAmountBuyer int
		var afterSaleNumBuyer int
		var name string
		for _, r := range rList {
			if r.RefundType == model.RefundTypeAfterSale {
				totalAfterSaleApplyAmountBuyer += r.Amount
				afterSaleNumBuyer++
			}
			if r.RefundType == model.RefundTypeAfterSale && r.AuditStatus == model.AuditStatusTypePass {
				totalAfterSalePassAmountBuyer += r.AuditAmount
			}
			if name == "" {
				name = r.BuyerName
			}
		}

		//zap.S().Infof("%d, 采购商：%s，申请数：%d，申请额：%.2f，通过额%.2f", i, name, afterSaleNumBuyer, float64(totalAfterSaleApplyAmountBuyer)/100, float64(totalAfterSalePassAmountBuyer)/100)
		i++
	}
}

func refundHasSplit(ctx *gin.Context) {
	//amount := 1378
	//var rList []pays.RefundItem

	// 服务点
	//rList = append(rList, pays.RefundItem{
	//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
	//AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	//BizUserId:    "95be6a71-f96e-4058-bf13-4a8c09d2a085",
	//Amount:       100,
	//})
	// 平台
	//rList = append(rList, pays.RefundItem{
	//	//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
	//	AccountSetNo: global.AllInPayAccountSetInfo.StandardBalanceNo,
	//	BizUserId:    "#yunBizUserId_B2C#",
	//	Amount:       6,
	//})
	// 供应商
	//supplierAmount := amount - 1600 - 220 - 300
	//rList = append(rList, pays.RefundItem{
	//AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
	//AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	//BizUserId:    "35a34d40-33e0-4bc2-8fc1-7fb2ae3ca334",
	//Amount:       1,
	//})

	//// 集中仓
	//rList = append(rList, pays.RefundItem{
	//	//	AccountSetNo  账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
	//	AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	//	BizUserId:    "********-012d-436a-aad8-77e71ad8fc97",
	//	Amount:       1378,
	//})
	//
	//tlt := "TLT"
	//payReq := pays.RefundReq{
	//	BizOrderNo:    util.NewUUID(),
	//	OriBizOrderNo: "4063f2cb-9eaf-4d16-93e4-38c9933a73a3",
	//	//OriBizOrderNo: "d8633d33-7f46-442a-9d33-3f0373ded70c",
	//	OriOrderNo: "1663808833660665856",
	//	//OriOrderNo:    "1663809385899499520",
	//	BizUserId:     "901bd8fc-27dc-465d-8c81-f0c9c89ab838", // 收款人-原订单的付款方  lgl
	//	RefundType:    "D0",
	//	RefundList:    rList,
	//	BackUrl:       global.BackHost + global.BackUrlRefund,
	//	Amount:        amount,
	//	CouponAmount:  0,
	//	FeeAmount:     0,
	//	RefundAccount: tlt,
	//	ExtendInfo:    "退款：手动",
	//}
	//res, err := payModule.NewOrderS().RefundS(payReq)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//if res.PayStatus == "fail" {
	//	err = xerr.NewErr(xerr.ErrParamError, nil, res.PayFailMessage)
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//_ = res
	//log.Println(res)

}

func dealPayBuyer(ctx *gin.Context) {
	//list, _, err := buyerService.NewBuyerService().List(bson.M{}, 1, 999)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//for _, b := range list {
	//	auth, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, b.ID)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//
	//	if auth.PayBizUserId == "901bd8fc-27dc-465d-8c81-f0c9c89ab838" {
	//		zap.S().Debug("跳过")
	//		continue
	//	}
	//	if auth.PayBizUserId == "befa6566-3c5d-4553-a79e-15bbb6bd4784" {
	//		zap.S().Debug("跳过")
	//		continue
	//	}
	//
	//	get, err := payModule.NewMember().GetMemberInfoForIndividualS(pays.GetMemberInfoReq{BizUserId: auth.PayBizUserId})
	//	if err != nil {
	//		log.Println(err)
	//		return
	//	}
	//	continue
	//	res, err := payModule.NewMember().CreateMemberS(pays.CreateMemberReq{
	//		BizUserId:  util.NewUUID(),
	//		MemberType: auth.MemberType,
	//		Source:     pays.SourceMobile,
	//		ExtendParam: map[string]interface{}{
	//			"object_id":   auth.ObjectID.Hex(),
	//			"object_type": auth.ObjectType,
	//		}})
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	filter := bson.M{
	//		"_id": auth.ID,
	//	}
	//	update := bson.M{
	//		"$set": bson.M{
	//			"pay_user_id":     res.UserID,
	//			"pay_biz_user_id": res.BizUserId,
	//			"updated_at":      time.Now().UnixMilli(),
	//		},
	//	}
	//	err = authenticationService.NewAuthenticationService().UpdateCus(ctx, filter, update)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
	//return
}

//22217
//
//befa6566-3c5d-4553-a79e-15bbb6bd4784
//
//
//
//buyer  7974
//
//901bd8fc-27dc-465d-8c81-f0c9c89ab838
//

//天天鲜果  供应商
//6456032af99212289203a483
//35a34d40-33e0-4bc2-8fc1-7fb2ae3ca334
//35a34d40-33e0-4bc2-8fc1-7fb2ae3ca334       fe7cc9d8-07d1-4703-b0bd-7e7d8725eaa8

//集中仓
//64707f215d56e144ecbdb10a
//********-012d-436a-aad8-77e71ad8fc97     userid     eb7c67c1-cd67-42b2-ab8e-1f3812d67cd8

//服务点
//6470b814c794a83c9840e6a2
//95be6a71-f96e-4058-bf13-4a8c09d2a085   userid   ee5fe497-9b4f-467f-ae2b-af1ae42bf660

//已经付款
//901bd8fc-27dc-465d-8c81-f0c9c89ab838
//
//befa6566-3c5d-4553-a79e-15bbb6bd4784

func dealPaySupplier(ctx *gin.Context) {
	//list, _, err := supplierService.NewSupplierService().List(bson.M{}, 1, 999)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//var exist []string
	////6456032af99212289203a483
	//exist = append(exist, "35a34d40-33e0-4bc2-8fc1-7fb2ae3ca334")
	//exist = append(exist, "95be6a71-f96e-4058-bf13-4a8c09d2a085")
	//exist = append(exist, "********-012d-436a-aad8-77e71ad8fc97")
	////
	//list, err := authenticationService.NewAuthenticationService().ListAll(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//for _, auth := range list {
	//	var f bool
	//	for _, s := range exist {
	//		if auth.PayBizUserId == s {
	//			//	 exist
	//			f = true
	//		}
	//	}
	//	if !f && auth.ObjectType == model.ObjectTypeSupplier {
	//		//	注册
	//		res, err := payModule.NewMember().CreateMemberS(pays.CreateMemberReq{
	//			BizUserId:  util.NewUUID(),
	//			MemberType: auth.MemberType,
	//			Source:     pays.SourceMobile,
	//			ExtendParam: map[string]interface{}{
	//				"object_id":   auth.ObjectID.Hex(),
	//				"object_type": auth.ObjectType,
	//			}})
	//		if err != nil {
	//			xhttp.RespErr(ctx, err)
	//			return
	//		}
	//		filter := bson.M{
	//			"_id": auth.ID,
	//		}
	//		update := bson.M{
	//			"$set": bson.M{
	//				"pay_user_id":     res.UserID,
	//				"pay_biz_user_id": res.BizUserId,
	//				"updated_at":      time.Now().UnixMilli(),
	//			},
	//		}
	//		err = authenticationService.NewAuthenticationService().UpdateCus(ctx, filter, update)
	//		if err != nil {
	//			xhttp.RespErr(ctx, err)
	//			return
	//		}
	//	}
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
	//return
}

/*
	err = authenticationService.NewAuthenticationService().UpdateCus(ctx, bson.M{},
		bson.M{"$rename": bson.M{
			"bank_account.accounttype":        "bank_account.account_type",
			"bank_account.cardnumber":         "bank_account.card_number",
			"bank_account.parentbankname":     "bank_account.parent_bank_name",
			"bank_account.bankname":           "bank_account.bank_name",
			"bank_account.unionbank":          "bank_account.union_bank",
			"bank_account.bankreservedmobile": "bank_account.bank_reserved_mobile",
		}})
	if err != nil {
		log.Println(err)
	}
*/
