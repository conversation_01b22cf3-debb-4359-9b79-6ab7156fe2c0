package productBuyPriceDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.ProductBuyPrice) error
	Find(ctx context.Context, filter bson.M) ([]model.ProductBuyPrice, error)
	Get(ctx context.Context, filter bson.M) (model.ProductBuyPrice, error)
	Update(ctx context.Context, filter, update bson.M) error
}

type productBuyPriceDao struct {
	db *mongo.Collection
}

func (p productBuyPriceDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := p.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (p productBuyPriceDao) Get(ctx context.Context, filter bson.M) (model.ProductBuyPrice, error) {
	var data model.ProductBuyPrice
	err := p.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductBuyPrice{}, err
	}
	return data, nil
}

func (p productBuyPriceDao) Create(ctx context.Context, data model.ProductBuyPrice) error {
	res, err := p.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (p productBuyPriceDao) Find(ctx context.Context, filter bson.M) ([]model.ProductBuyPrice, error) {
	var list []model.ProductBuyPrice

	cursor, err := p.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func NewProductBuyPriceDao(collect string) DaoInt {
	return productBuyPriceDao{
		db: global.MDB.Collection(collect),
	}
}
