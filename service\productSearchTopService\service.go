package productSearchTopService

import (
	"base/global"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, list []string) error
	List(ctx context.Context) ([]redis.Z, error)
}

type productSearchTopService struct {
	mdb *mongo.Database
	rdb *redis.Client
}

func (s productSearchTopService) Upsert(ctx context.Context, list []string) error {
	set(s.rdb, list)

	return nil
}

func (s productSearchTopService) List(ctx context.Context) ([]redis.Z, error) {
	list := get(s.rdb)

	return list, nil
}

func NewProductSearchTopService() ServiceInterface {
	return productSearchTopService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
	}
}
