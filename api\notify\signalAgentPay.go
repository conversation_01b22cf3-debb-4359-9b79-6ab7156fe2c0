package notify

import (
	"base/core/xhttp"
	"base/global"
	"encoding/json"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func PayNotifySignalAgentPayTemp(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)

		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("单笔代付-回调未对接：", string(bytes))
	}
}

//
//// PayNotifySignalAgentPayDebt 单笔代付
//func PayNotifySignalAgentPayDebt(ctx *gin.Context) {
//	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
//	l := global.PayLogger.Sugar()
//	notify := deal(ctx, l)
//	switch notify.NotifyType {
//	case "allinpay.yunst.orderService.pay":
//		// 托管代收---订单成功
//		var res allinpay.NotifyPay
//		if res.BizOrderNo == "d8633d33-7f46-442a-9d33-3f0373ded70c" {
//			xhttp.NotifySuccess(ctx)
//			return
//		}
//		parseRes(notify.BizContent, &res)
//		err := orderAgentPayService.NewOrderAgentPayService().NotifyPaySignalAgentPayStatusDebt(ctx, res)
//		if err != nil {
//			l.Errorf("单笔代付-回调更新失败")
//			xhttp.NotifyFail(ctx)
//			return
//		}
//		xhttp.NotifySuccess(ctx)
//		return
//	default:
//		bytes, _ := json.Marshal(notify)
//		zap.S().Error("单笔代付-回调未对接：", string(bytes))
//	}

/*
		代金券
		{"buyerBizUserId":"b6120808-60c2-4111-b21d-e0f75c0c11b7",
	"amount":1,"orderNo":"1655848777845579776",
	"extendInfo":"微信支付，支付者b6120808-60c2-4111-b21d-e0f75c0c11b7",
	"payDatetime":"2023-05-09 16:14:47",
	"bizOrderNo":"586b78be-1325-4254-872c-2429b320c261","status":"OK"}
*/

//}
