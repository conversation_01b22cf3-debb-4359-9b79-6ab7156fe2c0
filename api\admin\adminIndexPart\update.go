package adminIndexPart

import (
	"base/core/xhttp"
	"base/model"
	"base/service/indexPartService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Update(ctx *gin.Context) {
	var req = struct {
		ID         string         `json:"id" validate:"-"`
		Title      string         `json:"title" validate:"required"`
		TopImg     model.FileInfo `json:"top_img" validate:"required"`
		Style      string         `json:"style" validate:"required"`
		Sort       int            `json:"sort" validate:"-"`
		DisplayNum int            `json:"display_num" validate:"-"`
		Visible    bool           `json:"visible" validate:"-"`
		Condition  []string       `json:"condition"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var id primitive.ObjectID
	if len(req.ID) == 24 {
		id, err = util.ConvertToObjectWithCtx(ctx, req.ID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	err = indexPartService.NewIndexPartService().Update(ctx, id, req.Title, req.Style, req.Sort, req.Visible, req.DisplayNum, req.TopImg, req.Condition)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
