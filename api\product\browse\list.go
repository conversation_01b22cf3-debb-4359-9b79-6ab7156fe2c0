package browse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/browseService"
	"base/service/fruitClassService"
	"base/service/productService"
	"base/service/productUnitService"
	"base/service/supplierService"
	"base/util"
	"context"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=10"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.BuyerID, "buyer_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	statsList, count, err := browseService.NewBrowseService().ListForBuyer(ctx, id, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, collect := range statsList {
		ids = append(ids, collect.ProductID)
	}

	products, err := productService.NewProductService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]res, 0, len(products))

	for _, v := range products {
		supplier, err := supplierService.NewSupplierService().Get(ctx, v.SupplierID)
		if err != nil {
			zap.S().Error("查询供应商信息错误", err)
		}
		v.NonStandardAttr.FruitClassName = backFruitClassName(v.NonStandardAttr.FruitClassID)

		list = append(list, res{
			Product:             v,
			ProductUnitTypeName: backProductUnitName(v.ProductUnitID),
			SupplierTagListInfo: supplier.TagList,
			SupplierName:        supplier.ShopSimpleName,
		})
	}

	xhttp.RespSuccessList(ctx, list, count)
}

type res struct {
	model.Product
	SupplierName        string              `json:"supplier_name"`          // 供应商名称
	ProductUnitTypeName string              `json:"product_unit_type_name"` // 产品单位名称
	SupplierTagListInfo []model.SupplierTag `json:"supplier_tag_list_info"` // 商家标签
	CartNum             int                 `json:"cart_num"`               // 购物车数量
	HasCollect          bool                `json:"has_collect"`            // 是否收藏
}

func backProductUnitName(id primitive.ObjectID) string {
	unit, err := productUnitService.NewProductUnitService().Get(context.Background(), id)
	if err != nil {
		zap.S().Error("查询商品单位错误")
		return ""
	}
	return unit.Name
}

func backFruitClassName(id primitive.ObjectID) string {
	if id == primitive.NilObjectID {
		return ""
	}
	data, err := fruitClassService.NewFruitClassService().Get(context.Background(), id)
	if err != nil {
		zap.S().Error("查询水果等级错误")
		return ""
	}
	return data.Name
}
