package model

type FeedbackStatusType int

const (
	FeedbackStatusDoing FeedbackStatusType = 1 + iota
	FeedbackStatusDone
)

type QuestionStatus int

const (
	QuestionStatusOpening QuestionStatus = 1 + iota
	QuestionStatusDuplicated
	QuestionStatusClosed
)

var QuestionStatusMap = map[QuestionStatus]string{
	QuestionStatusOpening:    "开放中",
	QuestionStatusDuplicated: "重复",
	QuestionStatusClosed:     "已关闭",
}

type VoteType int

const (
	VoteTypeNone VoteType = 1 + iota
	VoteTypeUp
	VoteTypeDown
)

var VoteTypeMap = map[VoteType]string{
	VoteTypeNone: "none",
	VoteTypeUp:   "up",
	VoteTypeDown: "down",
}

type VoteObjectType int

const (
	VoteObjectTypeAsk VoteObjectType = 1 + iota
	VoteObjectTypeAnswer
)
