package adminBuyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/buyerStatsService"
	"base/service/orderService"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
		//ServicePointID  string `json:"service_point_id"`
		//AuditStatus     int   `json:"audit_status" validate:"required"`
		//LicenseStatus   int   `json:"license_status"` // 0 全部 1 无 2  有
		TimeBegin int64 `json:"time_begin"`
		TimeEnd   int64 `json:"time_end"`
		Page      int64 `json:"page" validate:"min=1"`
		Limit     int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.AuditStatus)]; !ok {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误"))
	//}

	filter := bson.M{
		//"audit_status": req.AuditStatus,
	}

	//if len(req.ServicePointID) == 24 {
	//	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	filter["service_point_id"] = pointID
	//}

	//if req.LicenseStatus != 0 {
	//	filter["license_status"] = req.LicenseStatus
	//}

	if req.TimeBegin > 0 {
		filter["created_at"] = bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		}
	}

	buyers, count, err := buyerService.NewBuyerService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]buyerRes, 0, len(buyers))
	for _, buyer := range buyers {
		stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyer.ID)
		if err != nil {
			zap.S().Errorf("查询会员统计异常:%s", err.Error())
			return
		}
		item := buyerRes{
			Buyer:      buyer,
			BuyerStats: stats,
		}
		list = append(list, item)
	}

	xhttp.RespSuccessList(ctx, list, count)
	return
}

// CountOrderBuyer 查询下单人数
func CountOrderBuyer(ctx *gin.Context) {
	var req = struct {
		TimeBegin int64 `json:"time_begin"`
		TimeEnd   int64 `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filterBuyer := bson.M{
		"created_at": bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		},
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, filterBuyer)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var buyerIDs []primitive.ObjectID
	for _, buyer := range buyers {
		buyerIDs = append(buyerIDs, buyer.ID)
	}

	if len(buyerIDs) == 0 {
		xhttp.RespSuccess(ctx, 0)
		return
	}

	filter := bson.M{
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
		"created_at": bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		},
	}

	findOpt := options.Find()

	findOpt.SetProjection(bson.M{
		"buyer_id": 1,
	})
	orders, err := orderService.NewOrderService().ListWithOption(ctx, filter, findOpt)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mBuyer := make(map[primitive.ObjectID]int)
	for _, order := range orders {
		mBuyer[order.BuyerID] = 0
	}

	xhttp.RespSuccess(ctx, len(mBuyer))
}

type buyerRes struct {
	model.Buyer
	BuyerStats model.BuyerStats `json:"buyer_stats"`
}

// ListAuditing 查询审核中
func ListAuditing(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"audit_status": model.AuditStatusTypeDoing,
	}

	if len(req.ServicePointID) == 24 {
		pointID, _ := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
		filter["service_point_id"] = pointID
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]auditRes, 0)

	// 会员
	for _, buyer := range buyers {
		list = append(list, auditRes{
			Buyer:    buyer,
			InfoType: "buyer",
		})
	}

	// 地址
	var ids []primitive.ObjectID
	for _, address := range addresses {
		ids = append(ids, address.UserID)
	}

	var buyersForAddr []model.Buyer

	if len(ids) > 0 {
		buyersForAddr, err = buyerService.NewBuyerService().ListByCus(ctx, bson.M{
			"user_id": bson.M{
				"$in": ids,
			},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	for _, address := range addresses {
		var bName string
		for _, buyer := range buyersForAddr {
			if address.BuyerID == buyer.ID {
				bName = buyer.BuyerName
				break
			}
		}

		list = append(list, auditRes{
			Address:   address,
			BuyerName: bName,
			InfoType:  "address",
		})
	}

	xhttp.RespSuccess(ctx, list)

}

type auditRes struct {
	Buyer     model.Buyer   `json:"buyer"`
	Address   model.Address `json:"address"`
	BuyerName string        `json:"buyer_name"`
	InfoType  string        `json:"info_type"`
}
