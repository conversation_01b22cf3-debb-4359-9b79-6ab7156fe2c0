package middleware

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/jwtService"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CheckToken(ctx *gin.Context) {
	auth := ctx.GetHeader("Authorization")
	auth = strings.TrimSpace(auth)
	if len(auth) < 1 {
		p := ctx.Request.RequestURI

		fullPath := ctx.FullPath()

		zap.S().Warnf("未登录,uri:%s,fullPath:%s", p, fullPath)
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.Abort()
		return
	}

	myClaims, err := jwtService.NewJwtService().ParseToken(auth)
	if err != nil {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.Abort()
		return
	}
	_ = myClaims

	ctx.Set("user_id", myClaims.ID)

	ctx.Next()
}
