package adminDeposiSet

import (
	"base/core/xhttp"
	"base/model"
	"base/service/depositSetService"
	"github.com/gin-gonic/gin"
)

// List 查询
func List(ctx *gin.Context) {
	list, err := depositSetService.NewDepositSetService().List(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	resList := make([]res, 0, len(list))
	for _, v := range list {
		resList = append(resList,
			res{
				DepositSet:     v,
				ObjectTypeName: model.ObjectTypeMsg[v.ObjectType],
			},
		)
	}

	xhttp.RespSuccess(ctx, resList)
}

type res struct {
	model.DepositSet
	ObjectTypeName string `json:"object_type_name"`
}
