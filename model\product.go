package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProductParamType int

const (
	ProductParamTypeFruit ProductParamType = 1 // 水果类
	ProductParamTypeOther ProductParamType = 2 // 其他类
)

type ProductUnit struct {
	ID   primitive.ObjectID `json:"id" bson:"_id"`
	Name string             `json:"name" bson:"name"`
}

// ProductOriginType 产品来源-国内/国外
type ProductOriginType string

const (
	// ProductOriginTypeDomestic 国内
	ProductOriginTypeDomestic ProductOriginType = "domestic"
	// ProductOriginTypeForeign 国外
	ProductOriginTypeForeign = "foreign"
)

// Product 商品表
type Product struct {
	ID                     primitive.ObjectID   `bson:"_id" json:"id"`
	OriginID               string               `bson:"origin_id" json:"origin_id"`                             // 原数据商品ID
	SupplierID             primitive.ObjectID   `bson:"supplier_id" json:"supplier_id"`                         // 供应商ID
	ServicePointID         primitive.ObjectID   `bson:"service_point_id" json:"service_point_id"`               // 服务仓ID
	SupplierSimpleName     string               `bson:"supplier_simple_name" json:"supplier_simple_name"`       // 供应商简称
	ProductParamType       ProductParamType     `json:"product_param_type" bson:"product_param_type"`           // 规格参数类型
	ProductUnitID          primitive.ObjectID   `json:"product_unit_id" bson:"product_unit_id"`                 // 单位类型
	ProductUnitTypeName    string               `json:"product_unit_type_name" bson:"product_unit_type_name"`   // 单位名称
	HasParam               bool                 `json:"has_param" bson:"has_param"`                             // 规格参数 有/无
	IsCheckWeight          bool                 `json:"is_check_weight" bson:"is_check_weight"`                 // 分拣检查重量  是/否
	WarehouseID            primitive.ObjectID   `bson:"warehouse_id" json:"warehouse_id"`                       // 集中仓ID-用户选择
	CategoryIDs            []primitive.ObjectID `bson:"category_ids" json:"category_ids"`                       // 商品分类信息
	Title                  string               `bson:"title" json:"title"`                                     // 商品名称
	Desc                   string               `bson:"desc" json:"desc"`                                       // 商品详情
	VideoFile              FileInfo             `bson:"video_file" json:"video_file"`                           // 商品视频
	CoverImg               FileInfo             `bson:"cover_img" json:"cover_img"`                             // 商品封面图片
	DisplayFile            []FileInfo           `bson:"display_file" json:"display_file"`                       // 轮播展示图(视频)
	DescImg                []FileInfo           `bson:"desc_img" json:"desc_img"`                               // 商品详情图
	Sale                   bool                 `bson:"sale" json:"sale"`                                       // 商品上下架状态
	Stock                  int                  `bson:"stock" json:"stock"`                                     // 库存
	BuyMinLimit            int                  `bson:"buy_min_limit" json:"buy_min_limit"`                     // 起购 0 不限制
	BuyMaxLimit            int                  `bson:"buy_max_limit" json:"buy_max_limit"`                     // 限购 0 不限制
	Price                  int                  `json:"price" bson:"price"`                                     // 价格
	OriginPrice            int                  `json:"origin_price" bson:"origin_price"`                       // 原价格
	CostPrice              int                  `json:"cost_price" bson:"cost_price"`                           // 成本价
	SupplyPrice            int                  `json:"supply_price" bson:"supply_price"`                       // 成本价
	MarketWholesalePrice   int                  `json:"market_wholesale_price" bson:"market_wholesale_price"`   // 市场批发价
	EstimatePurchasePrice  int                  `json:"estimate_purchase_price" bson:"estimate_purchase_price"` // 预估采购价
	PriceList              []PerPrice           `bson:"price_list" json:"price_list"`                           // 价格列表
	OriginPriceList        []PerPrice           `bson:"origin_price_list" json:"origin_price_list"`             // 原始价格列表
	CustomTagList          []CustomTag          `json:"custom_tag_list" bson:"custom_tag_list"`                 // 自定义标签
	SearchTagList          []string             `json:"search_tag_list" bson:"search_tag_list"`                 // 搜索标签
	Weight                 Weight               `bson:"weight" json:"weight"`                                   // 重量
	SoldCount              int                  `bson:"sold_count" json:"sold_count"`                           // 商品销量
	AttrInfo               []FieldInfo          `bson:"attr_info" json:"attr_info"`                             // 商品参数
	StandardAttr           StandardAttr         `json:"standard_attr" bson:"standard_attr"`                     // 标品参数
	NonStandardAttr        NonStandardAttr      `json:"non_standard_attr" bson:"non_standard_attr"`             // 非标品参数
	TagList                []ProductTag         `json:"tag_list" bson:"tag_list"`                               // 产品标签ID
	WordTagList            []ProductTag         `json:"word_tag_list" bson:"word_tag_list"`                       // 文字标签
	CoverTag               ProductTag           `json:"cover_tag" bson:"cover_tag"`                                 // 封面标签
	CommissionPercent      int                  `bson:"commission_percent" json:"commission_percent"`               // 商品抽成
	Version                int64                `bson:"version" json:"version"`                                     // 版本-时间戳--审核时更新
	AuditStatus            AuditStatusType      `bson:"audit_status" json:"audit_status"`                           // 审核状态
	FailReason             string               `bson:"fail_reason" json:"fail_reason"`                             // 审核失败原因
	PurchaseNote           string               `json:"purchase_note" bson:"purchase_note"`                         // 采购说明-采购门店
	LinkBrandStatus        int                  `json:"link_brand_status" bson:"link_brand_status"`                 // 关联品牌状态  2 关联
	LinkBrandID            primitive.ObjectID   `json:"link_brand_id" bson:"link_brand_id"`                         // 关联品牌ID
	LinkBrandName          string               `json:"link_brand_name" bson:"link_brand_name"`                     // 关联品牌名称
	IsExternalSale         bool                 `json:"is_external_sale" bson:"is_external_sale"`                   // 外部销售
	LinkProductStatus      int                  `json:"link_product_status" bson:"link_product_status"`             // 关联商品状态  1 2
	LinkProductID          primitive.ObjectID   `json:"link_product_id" bson:"link_product_id"`                     // 关联商品ID
	LinkProductSupplierID  primitive.ObjectID   `json:"link_product_supplier_id" bson:"link_product_supplier_id"`   // 关联商品ID
	LinkProductPriceChange int                  `json:"link_product_price_change" bson:"link_product_price_change"` // 关联商品价格浮动
	LinkProductPrice       int                  `json:"link_product_price" bson:"link_product_price"`               // 关联原商品价格
	LinkProductOriginPrice int                  `json:"link_product_origin_price" bson:"link_product_origin_price"` // 关联原商品价格
	RecommendProductList   []primitive.ObjectID `json:"recommend_product_list" bson:"recommend_product_list"`       // 推荐关联
	UserType               UserType             `json:"user_type" bson:"user_type"`                                 // 类型
	ProductOriginType      ProductOriginType    `json:"product_origin_type" bson:"product_origin_type"`             // 产品来源-国内/国外
	StartPrice             int                  `json:"start_price" bson:"start_price"`                             // 起售价
	StartWeightUnitPrice   int                  `json:"start_weight_unit_price" bson:"start_weight_unit_price"`     // 起售重量单价
	SkuList                []Sku                `json:"sku_list" bson:"sku_list"`                                   // sku列表
	CreatedAt              int64                `bson:"created_at" json:"created_at"`
	UpdatedAt              int64                `bson:"updated_at" json:"updated_at"`
	DeletedAt              int64                `bson:"deleted_at" json:"deleted_at"`
}

// ProductDiscount 商品折扣
type ProductDiscount struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Num       int                `json:"num" bson:"num"`     // 折扣数量
	Value     int                `json:"value" bson:"value"` // 折扣值
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
}

// DiscountPrice 商品折扣价
type DiscountPrice struct {
	ID    primitive.ObjectID `json:"id" bson:"_id"`
	Num   int                `json:"num" bson:"num"`     // 数量
	Price int                `json:"price" bson:"price"` // 价格
}

// CustomTag 自定义标签
type CustomTag struct {
	Key   string `json:"key" bson:"key"`     // 标题
	Value string `json:"value" bson:"value"` // 值
}

// Sku 规格
type Sku struct {
	IDCode                string `json:"id_code" bson:"id_code"`                                 // 编号，系统自动生成
	Name                  string `json:"name" bson:"name"`                                       // 名称
	Description           string `json:"description" bson:"description"`                         // 描述
	Cover                 string `json:"cover" bson:"cover"`                                     // 封面
	Price                 int    `json:"price" bson:"price"`                                     // 销售价
	RoughWeight           int    `json:"rough_weight" bson:"rough_weight"`                       // 毛重
	OutWeight             int    `json:"out_weight" bson:"out_weight"`                           // 皮重
	NetWeight             int    `json:"net_weight" bson:"net_weight"`                           // 净重
	MarketWholesalePrice  int    `json:"market_wholesale_price" bson:"market_wholesale_price"`   // 市场批发价
	EstimatePurchasePrice int    `json:"estimate_purchase_price" bson:"estimate_purchase_price"` // 预估采购价
	Stock                 int    `json:"stock" bson:"stock"`                                     // 库存
}

// Stock                 int    `json:"stock" bson:"stock"`                                     // 库存

// StandardAttr 标品参数
type StandardAttr struct {
	IncludedNum int    `json:"included_num" bson:"included_num"` // 内含数量
	UnitName    string `json:"unit_name" bson:"unit_name"`       // 数量单位名称
}

// NonStandardAttr 非标品参数
type NonStandardAttr struct {
	FruitClassID   primitive.ObjectID `json:"fruit_class_id" bson:"fruit_class_id"`     // 等级
	FruitClassName string             `json:"fruit_class_name" bson:"fruit_class_name"` // 等级名称
	Width          int                `json:"width" bson:"width"`                       // 果径-mm
	Size           string             `json:"size" bson:"size"`                         // 规格  large medium small
}

// Weight 重量
type Weight struct {
	RoughWeight int `bson:"rough_weight" json:"rough_weight"` // 毛重
	OutWeight   int `bson:"out_weight" json:"out_weight"`     // 皮重
	NetWeight   int `bson:"net_weight" json:"net_weight"`     // 净重
}

// 果径

// PerPrice 阶梯价
type PerPrice struct {
	Num   int `json:"num" bson:"num"`
	Price int `json:"price" bson:"price"`
}

// Cart 购物车
type Cart struct {
	ID         primitive.ObjectID `bson:"_id" json:"id"`
	BuyerID    primitive.ObjectID `bson:"buyer_id" json:"buyer_id"`       // 采购商ID
	ProductID  primitive.ObjectID `bson:"product_id" json:"product_id"`   // 所属商品ID
	SupplierID primitive.ObjectID `bson:"supplier_id" json:"supplier_id"` // 供应商ID
	SkuIDCode  string             `bson:"sku_id_code" json:"sku_id_code"` // sku编号
	Count      int                `bson:"count" json:"count"`             // 商品数量
	CreatedAt  int64              `bson:"created_at" json:"created_at"`
}

// ProductStats 商品统计
type ProductStats struct {
	BuyerID           primitive.ObjectID `json:"buyer_id"`
	TotalNum          int64              `json:"order_product_num"`    // 总数
	TotalBuyerNum     int64              `json:"total_buyer_num"`      // 发货客户数
	TotalOrderNum     int64              `json:"total_order_num"`      // 订单数
	AfterSaleOrderNum int64              `json:"after_sale_order_num"` // 售后次数
	AfterSaleRate     float64            `json:"after_sale_rate"`      // 售后率
}
