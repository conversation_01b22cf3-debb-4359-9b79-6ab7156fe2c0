package officialAccountUserService

import (
	"base/dao"
	"base/dao/officialAccountUserDao"
	"base/model"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.OfficialAccountUser) error
	GetByOpenID(ctx context.Context, openID string) (model.OfficialAccountUser, error)
	GetByUnionID(ctx context.Context, unionID string) (model.OfficialAccountUser, error)
}

type officialAccountUserService struct {
	officialAccountUserDao officialAccountUserDao.DaoInt
}

func NewOfficialAccountUserService() ServiceInterface {
	return officialAccountUserService{
		officialAccountUserDao: dao.OfficialAccountUserDao,
	}
}

func (s officialAccountUserService) Create(ctx context.Context, data model.OfficialAccountUser) error {
	user, err := s.GetByOpenID(ctx, data.OpenID)
	if !errors.Is(err, mongo.ErrNoDocuments) {
		// 已存在
		return nil
	}
	_ = user
	err = s.officialAccountUserDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s officialAccountUserService) GetByOpenID(ctx context.Context, openID string) (model.OfficialAccountUser, error) {
	user, err := s.officialAccountUserDao.Get(ctx, bson.M{"open_id": openID})
	if err != nil {
		return model.OfficialAccountUser{}, err
	}
	return user, nil
}

func (s officialAccountUserService) GetByUnionID(ctx context.Context, unionID string) (model.OfficialAccountUser, error) {
	user, err := s.officialAccountUserDao.Get(ctx, bson.M{"union_id": unionID})
	if err != nil {
		return model.OfficialAccountUser{}, err
	}
	return user, nil
}
