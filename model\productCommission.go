package model

import "go.mongodb.org/mongo-driver/bson/primitive"

/*
产品佣金，在审核商品时确定，目前定为三挡：2%,3%,5%
*/

// ProductCommission 产品佣金
type ProductCommission struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	ProductID primitive.ObjectID `json:"product_id" bson:"product_id"` // 产品ID
	Percent   int                `json:"percent" bson:"percent"`       // 百分比
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
}
