package deliverNoteService

import (
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/ossService"
	"base/service/parentOrderService"
	"base/util"
	"bytes"
	"context"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"
	"unicode/utf8"

	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s deliverNoteService) gen(ctx context.Context, buyerID primitive.ObjectID, zeroTimestamp int64, deliverType model.DeliverType) error {
	begin, end, _ := util.DayScopeTimestamp(zeroTimestamp)

	filter := bson.M{
		"buyer_id": buyerID,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"deliver_type": deliverType,
	}
	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		return err
	}

	if len(orders) < 1 {
		zap.S().Errorf("生成配送单异常，订单列表为空,请求:%s", ctx.Value("req_id"))
		return nil
	}

	var orderIDs []primitive.ObjectID
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
	}

	zap.S().Infof("----------------------------")
	zap.S().Infof("-----------gen-------------buyerID:%s:%d", buyerID.Hex(), len(orderIDs))

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			return err
		}
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
			//"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			return err
		}
	}

	var parentOrderIDs []primitive.ObjectID
	for _, order := range orders {
		parentOrderIDs = append(parentOrderIDs, order.ParentOrderID)
	}

	parentOrders, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{"_id": bson.M{"$in": parentOrderIDs}})
	if err != nil {
		return err
	}

	beginAt, endAt, orderLists, calcRes := CalcFunc(orders, refunds, debts)

	bufferFile, err := toExcel(orderLists, parentOrders, calcRes)
	if err != nil {
		return err
	}

	// TODO: 测试
	// return nil

	_ = beginAt
	_ = endAt
	_ = bufferFile
	now := time.Now()

	dir := "deliverNote"

	milli := now.UnixMilli()
	nowStr := strconv.Itoa(int(milli))
	pathSuffix := nowStr + "@" + buyerID.Hex()

	fileName := pathSuffix + ".xlsx"
	objectName := dir + "/" + fileName

	zap.S().Infof("执行上传配送单：%s,buyerID:%s,timestamp:%d", objectName, buyerID.Hex(), zeroTimestamp)

	err = ossService.NewOssService().UploadDeliverNote(objectName, bufferFile)
	if err != nil {
		zap.S().Errorf("上传oss失败：%v", err.Error())
		return err
	}

	remark := ""

	err = s.Upsert(ctx, now, buyerID, objectName, remark, beginAt, endAt, zeroTimestamp, deliverType)
	if err != nil {
		return err
	}

	return nil
}

type CalcRes struct {
	TotalOrderPaidAmount   int   `json:"total_order_paid_amount"`
	OrderTimeBegin         int64 `json:"order_time_begin"`
	OrderTimeEnd           int64 `json:"order_time_end"`
	ExistAfterSaleAuditing bool  `json:"exist_after_sale_auditing"`
	ExistDebtNotPaid       bool  `json:"exist_debt_not_paid"`
}

type OrderList struct {
	Order      model.Order         `json:"order"`
	Debt       model.OrderDebt     `json:"debt"`
	RefundList []model.OrderRefund `json:"refund_list"`
}

func CalcFunc(orders []model.Order, refunds []model.OrderRefund, debts []model.OrderDebt) (int64, int64, []OrderList, CalcRes) {
	var orderTimeBegin int64
	var orderTimeEnd int64

	var totalAmount int

	var existAfterSaleAuditing bool
	var existDebtNotPaid bool

	for _, order := range orders {
		totalAmount += order.PaidAmount

		if orderTimeBegin == 0 {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd == 0 {
			orderTimeEnd = order.CreatedAt
		}

		if orderTimeBegin > order.CreatedAt {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd < order.CreatedAt {
			orderTimeEnd = order.CreatedAt
		}
	}

	mDebt := make(map[primitive.ObjectID]model.OrderDebt)

	for _, debt := range debts {
		//if debt.PayStatus == model.PayStatusTypePaid {
		//	totalDebtPaidAmount += debt.TotalProductAmount
		//} else {
		//	totalDebtNotPaidAmount += debt.TotalProductAmount
		//}
		mDebt[debt.OrderID] = debt
		if debt.PayStatus != model.PayStatusTypePaid {
			existDebtNotPaid = true
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)

	for _, re := range refunds {
		_ = re
		//if re.RefundType == model.RefundTypeQuality {
		//	totalShipRefundAmount += re.AuditAmount + re.TotalTransportFee + re.TotalWarehouseLoadFee + re.TotalServiceFee
		//}
		//if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypePass {
		//	totalAfterSalePassAmount += re.AuditAmount
		//}
		//mRefund[re.OrderID] = append(mRefund[re.OrderID], re)
		//
		//if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypeDoing {
		//	existAfterSaleAuditing = true
		//}
	}

	resList := make([]OrderList, 0)
	for _, order := range orders {
		item := OrderList{
			Order:      order,
			Debt:       mDebt[order.ID],
			RefundList: mRefund[order.ID],
		}
		resList = append(resList, item)
	}

	//totalFinalAmount := totalAmount - totalShipRefundAmount - totalAfterSalePassAmount + totalDebtPaidAmount + totalDebtNotPaidAmount
	//totalFinalAmount := totalAmount
	//+ totalDebtPaidAmount + totalDebtNotPaidAmount

	res := CalcRes{
		TotalOrderPaidAmount:   totalAmount,
		OrderTimeBegin:         orderTimeBegin,
		OrderTimeEnd:           orderTimeEnd,
		ExistAfterSaleAuditing: existAfterSaleAuditing,
		ExistDebtNotPaid:       existDebtNotPaid,
	}
	return orderTimeBegin, orderTimeEnd, resList, res

}

type parentOrderSort []model.ParentOrder

func (array parentOrderSort) Len() int {
	return len(array)
}

func (array parentOrderSort) Less(i, j int) bool {
	return array[i].CreatedAt < array[j].CreatedAt //从小到大， 若为大于号，则从大到小
}

func (array parentOrderSort) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

func toExcel(list []OrderList, parentOrders []model.ParentOrder, res CalcRes) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	title(f, sheetName, i)

	buyerPart(f, sheetName, list[0].Order.BuyerName, i+1)

	orderTime(f, sheetName, i+2, res.OrderTimeBegin, res.OrderTimeEnd)
	createTime(f, sheetName, i+3)

	category(f, sheetName, i+5)

	i = 8

	mParent := make(map[primitive.ObjectID][]OrderList)
	for _, orderList := range list {
		mParent[orderList.Order.ParentOrderID] = append(mParent[orderList.Order.ParentOrderID], orderList)
	}

	sort.Sort(parentOrderSort(parentOrders))

	var totalProductAmount int
	var pAllFinalAmount int
	var totalDeliverFeeAll int
	var totalDeliverFeeSubsidy int
	var totalDeliverFee int
	var totalServiceFee int

	var totalDebtAmount int
	var totalShipRefundAmount int

	var orderWeight, sortWeight int
	var orderNum, sortNum int

	var weightOffsetListPositive []int
	var weightOffsetListNegative []int

	var refundCellList []int
	var debtCellList []int

	for pI, pOrder := range parentOrders {
		if allList, ok := mParent[pOrder.ID]; ok {
			deliverMergeBegin := i
			var paidAmountPerOrder int
			for k, v := range allList {
				productNum := len(v.Order.ProductList)
				if productNum > 1 {
					err = f.MergeCell(sheetName, "A"+strconv.Itoa(i), "A"+strconv.Itoa(i+productNum-1))
					err = f.MergeCell(sheetName, "B"+strconv.Itoa(i), "B"+strconv.Itoa(i+productNum-1))
				}

				_ = k
				//createTime1 := time.UnixMilli(v.Order.CreatedAt).Format("2006-01-02 15:04:05")
				//no := fmt.Sprintf("%s\n[%s]", v.Order.SupplierName, createTime1)
				err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i+productNum-1), v.Order.SupplierName)

				for j, p := range v.Order.ProductList {

					productTitle := fmt.Sprintf("%s[%s]", p.ProductTitle, p.SkuName)
					err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i+j), productTitle)
					// 单价

					saleUnitPriceFmt := formatAmount(dealMoney(p.Price)) + "/件"
					if p.IsCheckWeight {
						saleUnitPriceFmt = formatAmount(dealMoney(p.ProductRoughWeightUnitPriceKG)) + "元/kg"
					}

					err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+j), saleUnitPriceFmt)

					err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i+j), p.Num)

					err = f.SetCellValue(sheetName, "J"+strconv.Itoa(i+j), formatAmount(dealMoney(p.TotalServiceFee)))

					err = f.SetCellValue(sheetName, "K"+strconv.Itoa(i+j), p.SortNum)

					runeLen := utf8.RuneCountInString(productTitle)
					//zap.S().Info(runeLen)
					//l := float64(runeLen) / 10
					if runeLen < 30 {
						runeLen = 30
					}

					err = f.SetRowHeight(sheetName, i+j, float64(runeLen))
					//if productNum > 1 {
					//
					//
					//} else {
					//	//err = f.SetRowHeight(sheetName, i+j, 34)
					//}
					err = f.SetCellValue(sheetName, "H"+strconv.Itoa(i+j), formatAmount(dealMoney(p.ProductAmount)))

					// 优惠券
					if p.CouponSplitAmount > 0 {
						err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i+j), formatAmount(dealMoney(p.CouponSplitAmount)))
					}

					orderWeight += p.TotalWeight
					sortWeight += p.SortWeight

					orderNum += p.Num
					sortNum += p.SortNum

					if p.IsCheckWeight {
						err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+j), formatWeight(dealWeight(p.TotalWeight)))
						err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i+j), formatWeight(dealWeight(p.SortWeight)))
						// 重量误差
						weightOffset := p.SortWeight - p.TotalWeight
						if weightOffset > 0 {
							wo := dealWeight(weightOffset)
							woStr := strconv.FormatFloat(wo, 'f', 1, 64)
							err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i+j), "+"+woStr)
							weightOffsetListPositive = append(weightOffsetListPositive, i+j)
						}
						if weightOffset < 0 {
							err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i+j), formatWeight(dealWeight(weightOffset)))
							weightOffsetListNegative = append(weightOffsetListNegative, i+j)
						}

					} else {
						err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+j), formatWeight(dealWeight(p.TotalWeight)))
						err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i+j), formatWeight(dealWeight(p.SortWeight)))
					}

					//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i+j), formatAmount(dealMoney(p.TotalWarehouseLoadFee)))

					saleWay := "按件"
					if p.IsCheckWeight {
						saleWay = "称重"
					}
					err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i+j), saleWay)

					// 品控退款
					var sortRefund int

					for _, d1 := range v.Debt.SettleProductList {
						if d1.ProductID == p.ProductID && d1.SkuIDCode == p.SkuIDCode && d1.SettleResultType == model.SettleResultTypeRefund {
							sortRefund += d1.DiffProductAmount + d1.TotalTransportFee + d1.TotalServiceFee
							break
						}
					}
					if sortRefund > 0 {
						err = f.SetCellValue(sheetName, "O"+strconv.Itoa(i+j), formatAmount(-1*dealMoney(sortRefund)))
						refundCellList = append(refundCellList, i+j)
						totalShipRefundAmount += sortRefund
					}

					settleUnit := p.Price
					if p.IsCheckWeight {
						settleUnit = p.ProductRoughWeightUnitPriceKG
					}

					// 结算单价

					settleUnitFmt := formatAmount(dealMoney(settleUnit)) + "/件"
					if p.IsCheckWeight {
						settleUnitFmt = formatAmount(dealMoney(settleUnit)) + "元/kg"
					}

					err = f.SetCellValue(sheetName, "N"+strconv.Itoa(i+j), settleUnitFmt)

					var debtAmount int
					for _, d1 := range v.Debt.SettleProductList {
						if d1.ProductID == p.ProductID && d1.SkuIDCode == p.SkuIDCode && d1.SettleResultType == model.SettleResultTypeDebt {
							debtAmount += d1.DiffProductAmount
							break
						}
					}

					if debtAmount > 0 {
						err = f.SetCellValue(sheetName, "P"+strconv.Itoa(i+j), formatAmount(dealMoney(debtAmount)))
						debtCellList = append(debtCellList, i+j)
						totalDebtAmount += debtAmount
					}

					// 商品小计 商品金额+仓配费-品控退款+补差金额
					pFinalAmount := p.ProductAmount + p.TotalWarehouseLoadFee - sortRefund + debtAmount + p.TotalServiceFee + p.TotalTransportFee - p.CouponSplitAmount
					pAllFinalAmount += pFinalAmount

					err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(i+j), formatAmount(dealMoney(pFinalAmount)))
				}
				i += productNum

				totalServiceFee += v.Order.TotalServiceFee
				//totalLoadFee += v.Order.TotalWarehouseLoadFee
				totalProductAmount += v.Order.ProductTotalAmount

				paidAmountPerOrder += v.Order.PaidAmount
			}

			beginStr := strconv.Itoa(deliverMergeBegin)
			err = f.MergeCell(sheetName, "A"+strconv.Itoa(deliverMergeBegin), "A"+strconv.Itoa(i-1))
			err = f.SetCellValue(sheetName, "A"+beginStr, pI+1)

			totalDeliverFee += pOrder.DeliverFeeRes.FinalDeliverFee
			totalDeliverFeeAll += pOrder.DeliverFeeRes.TotalDeliverFee
			if pOrder.DeliverFeeRes.SubsidyDeliverFee > 0 {
				totalDeliverFeeSubsidy += pOrder.DeliverFeeRes.SubsidyDeliverFee
			}

		}

	}

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	content2 := strconv.Itoa(i)
	err = f.SetCellStyle(sheetName, "A8", "Q"+content2, style)
	//err = f.SetRowStyle(sheetName, 6, i, style)

	setProductStyle(f, sheetName, 8, i)

	setRefundStyle(f, sheetName, 6, i)

	setOrderStyle(f, sheetName, 6, i)

	setShipStyle(f, sheetName, 6, i)

	setWeightOffsetStyle(f, sheetName, weightOffsetListPositive, weightOffsetListNegative)

	setRefundDebtStyle(f, sheetName, refundCellList, debtCellList)

	// 小计
	resForCol(f, sheetName, i, res, totalProductAmount, totalDebtAmount, totalShipRefundAmount, totalServiceFee, pAllFinalAmount, orderWeight, sortWeight, orderNum, sortNum)

	setOther(f, sheetName, i+1, totalProductAmount, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount)

	// 汇总表
	i += 8

	//i += 5

	note(f, sheetName, i)

	f.SetActiveSheet(index)

	//f.SaveAs("./api/sys/a.xlsx")

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	return toBuffer, nil
}

func formatAmount(amount float64) string {
	return strconv.FormatFloat(amount, 'f', 2, 64)
}

func formatWeight(num float64) string {
	return strconv.FormatFloat(num, 'f', 1, 64)
}

func setWeightOffsetStyle(f *excelize.File, sheetName string, weightOffsetListPositive, weightOffsetListNegative []int) {
	var err error
	_ = err
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
			Color:  "ff0000",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#fef2cb"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	for _, i := range weightOffsetListPositive {
		str := strconv.Itoa(i)
		hCell := "M" + str
		vCell := "M" + str
		err = f.SetCellStyle(sheetName, hCell, vCell, style)
	}

	styleNegative, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
			Color:  "#0070c0",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#fef2cb"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	for _, i := range weightOffsetListNegative {
		str := strconv.Itoa(i)
		hCell := "M" + str
		vCell := "M" + str
		err = f.SetCellStyle(sheetName, hCell, vCell, styleNegative)
	}

}

func setRefundDebtStyle(f *excelize.File, sheetName string, refundCellList, debtCellList []int) {
	var err error
	_ = err
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
			Color:  "0070c0",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	for _, i := range refundCellList {
		str := strconv.Itoa(i)
		hCell := "O" + str
		vCell := "O" + str
		err = f.SetCellStyle(sheetName, hCell, vCell, style)
	}

	styleDebt, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
			Color:  "#ff0000",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	for _, i := range debtCellList {
		str := strconv.Itoa(i)
		hCell := "P" + str
		vCell := "P" + str
		err = f.SetCellStyle(sheetName, hCell, vCell, styleDebt)
	}

}

// 其他
func setOther(f *excelize.File, sheetName string, index, totalProductAmount, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount int) {
	originIndex := index
	row := strconv.Itoa(index)
	var err error

	err = f.SetCellValue(sheetName, "B"+row, "其他")

	// 配送费
	err = f.MergeCell(sheetName, "C"+row, "C"+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index), "配送费")
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+1), "配送费补贴")
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+2), "配送费实付")
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+3), "订单支付金额")
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+4), "实际结算金额")

	err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(index), formatAmount(dealMoney(totalDeliverFeeAll)))
	err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(index+1), formatAmount(dealMoney(totalDeliverFeeSubsidy)))
	err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(index+2), formatAmount(dealMoney(totalDeliverFee)))
	err = f.SetCellValue(sheetName, "H"+strconv.Itoa(index+3), formatAmount(dealMoney(totalProductAmount+totalServiceFee+totalDeliverFee)))

	err = f.MergeCell(sheetName, "H"+strconv.Itoa(index+3), "J"+strconv.Itoa(index+3))

	// 服务费
	index += 4

	//index += 1
	err = f.MergeCell(sheetName, "B"+strconv.Itoa(originIndex), "B"+strconv.Itoa(index))
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	//err = f.MergeCell(sheetName, "B"+strconv.Itoa(index+1), "B"+strconv.Itoa(index))
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}

	//index += 1
	finalAmount := pAllFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(index), dealMoney(finalAmount))
	//
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(originIndex), "Q"+strconv.Itoa(index), style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "C"+strconv.Itoa(index), "Q"+strconv.Itoa(index), style2)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "C"+cell1, "C"+cell2, style)
}

func setOrderStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#d9e1f2"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "E"+cell1, "J"+cell2, style)
}

func setShipStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#fef2cb"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "K"+cell1, "M"+cell2, style)

	styleTitle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#fef2cb"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "K"+cell1, "M"+cell1, styleTitle)

}

func setRefundStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "N"+cell1, "P"+cell2, style)

	styleTitle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "N"+cell1, "P"+cell1, styleTitle)

}

// 小计
func resForCol(f *excelize.File, sheetName string, index int, res CalcRes, totalProductAmount, totalDebtAmount, totalShipRefundAmount, totalServiceFee,
	pAllFinalAmount, orderWeight, sortWeight, orderNum, sortNum int) {
	row := strconv.Itoa(index)
	//row2 := strconv.Itoa(index + 1)

	var err error
	_ = err

	err = f.SetRowHeight(sheetName, index, 26)
	err = f.SetCellValue(sheetName, "B"+row, "小计")
	err = f.SetCellValue(sheetName, "H"+row, formatAmount(dealMoney(totalProductAmount)))
	//err = f.SetCellValue(sheetName, "I"+row, formatAmount(dealMoney(totalLoadFee)))
	err = f.SetCellValue(sheetName, "J"+row, formatAmount(dealMoney(totalServiceFee)))

	err = f.SetCellValue(sheetName, "O"+row, formatAmount(-1*dealMoney(totalShipRefundAmount)))
	err = f.SetCellValue(sheetName, "P"+row, formatAmount(dealMoney(totalDebtAmount)))

	err = f.SetCellValue(sheetName, "Q"+row, formatAmount(dealMoney(pAllFinalAmount)))

	// 订单重量
	err = f.SetCellValue(sheetName, "F"+row, formatWeight(dealWeight(orderWeight)))
	// 发货重量
	err = f.SetCellValue(sheetName, "L"+row, formatWeight(dealWeight(sortWeight)))

	err = f.SetCellValue(sheetName, "E"+row, orderNum)
	err = f.SetCellValue(sheetName, "K"+row, sortNum)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, "Q"+row, style)

	err = f.SetRowHeight(sheetName, index, 20)

	//	订单
	styleOrder, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#d9e1f2"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "E"+row, "J"+row, styleOrder)

	//	发货
	styleShip, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#fef2cb"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "K"+row, "M"+row, styleShip)

	//	退款
	styleRefund, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "N"+row, "P"+row, styleRefund)

	//	退款
	styleRefundAmount, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
			Color:  "0070c0",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "O"+row, "O"+row, styleRefundAmount)

	//	退款
	styleDebtAmount, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
			Color:  "ff0000",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e5f0df"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "P"+row, "P"+row, styleDebtAmount)
}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.23),
		Right:  float64Ptr(0.23),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func note(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "备注说明：")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+1), "M"+strconv.Itoa(index+1))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+1), "双方理解生鲜产品订单与实际发货中存在一定的误差，存在多退少补的情况，双方同意按照实际发货情况进行多退少补；")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+2), "M"+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+2), "该订单中未包含长途远距离运费，若有发生由买家向运输方单独支付；")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "C"+strconv.Itoa(index+2), style)

}

func orderTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func title(f *excelize.File, sheetName string, index int) {
	_ = index
	err := f.MergeCell(sheetName, "A1", "Q1")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetRowHeight(sheetName, 1, 30)

	err = f.SetCellValue(sheetName, "A1", "发货单")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   24,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {

	}
	err = f.SetCellStyle(sheetName, "A1", "A1", titleStyle)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
}

func buyerPart(f *excelize.File, sheetName, buyerName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "会员："+buyerName)
	err = f.SetRowHeight(sheetName, 2, 24)
	timeStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   18,
		},
	})
	_ = err

	err = f.SetCellStyle(sheetName, "B"+row, "C"+row, timeStyle)

}

func buyerBottom(f *excelize.File, sheetName, buyerName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "会员："+buyerName)
}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	err = f.MergeCell(sheetName, "A"+row, "A"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "A"+row, "")

	// 供应商
	err = f.MergeCell(sheetName, "B"+row, "B"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "B"+row, "供应商")

	// 商品
	err = f.MergeCell(sheetName, "C"+row, "C"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "C"+row, "商品")

	// 计价方式
	err = f.MergeCell(sheetName, "D"+row, "D"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "D"+row, "销售\n方式")

	// 订单信息
	err = f.MergeCell(sheetName, "E"+row, "J"+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "E"+row, "订单信息")
	err = f.SetCellValue(sheetName, "G"+row2, "单价\n（元）")
	err = f.SetCellValue(sheetName, "E"+row2, "订单数量\n（件）")
	err = f.SetCellValue(sheetName, "H"+row2, "商品金额\n（元）")
	err = f.SetCellValue(sheetName, "F"+row2, "订单重量\n（kg）")
	err = f.SetCellValue(sheetName, "I"+row2, "优惠券\n（元）")
	err = f.SetCellValue(sheetName, "J"+row2, "服务费\n（元）")

	// 发货信息
	err = f.MergeCell(sheetName, "K"+row, "M"+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "K"+row, "发货信息")
	err = f.SetCellValue(sheetName, "K"+row2, "发货数量\n（件）")
	err = f.SetCellValue(sheetName, "L"+row2, "发货重量\n（kg）")
	err = f.SetCellValue(sheetName, "M"+row2, "重量误差\n（kg）")

	// 退款补差
	err = f.MergeCell(sheetName, "N"+row, "P"+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "N"+row, "结算信息")
	err = f.SetCellValue(sheetName, "N"+row2, "结算单价\n")
	err = f.SetCellValue(sheetName, "O"+row2, "结算退款\n（元）")
	err = f.SetCellValue(sheetName, "P"+row2, "结算补差\n（元）")

	// 金额小计
	err = f.MergeCell(sheetName, "Q"+row, "Q"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "Q"+row, "金额小计\n（元）")

	err = f.SetColWidth(sheetName, "A", "A", 4)
	err = f.SetColWidth(sheetName, "B", "B", 10)
	err = f.SetColWidth(sheetName, "C", "C", 32)
	err = f.SetColWidth(sheetName, "G", "G", 12)
	err = f.SetColWidth(sheetName, "H", "H", 14)
	err = f.SetColWidth(sheetName, "N", "N", 14)
	err = f.SetColWidth(sheetName, "O", "O", 14)
	err = f.SetColWidth(sheetName, "P", "P", 14)
	err = f.SetColWidth(sheetName, "Q", "Q", 14)

	//err = f.SetSheetRow(sheetName, "A"+row, &[]interface{}{"", "供应商", "商品", "计价\n方式", "订单\n数量", "发货\n数量", "商品\n金额", "订单\n重量", "发货\n重量", "仓配\n费", "配送\n费", "支付\n金额", "发货\n退款", "补差\n金额", "小计"})
	//_ = err
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, "Q"+row2, style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "E"+row2, "Q"+row2, style2)

	err = f.SetRowHeight(sheetName, index, 20)
	err = f.SetRowHeight(sheetName, index+1, 26)
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}
