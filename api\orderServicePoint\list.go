package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderPointService"
	"base/service/servicePointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListToArrive  待到货
func ListToArrive(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListToArrive service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderPointService.NewOrderPointService().ListToArrive(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

// ListToDeliver  待配送
func ListToDeliver(ctx *gin.Context) {
	//var req = struct {
	//	Timestamp int64 `json:"timestamp" validate:"required"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	userID, err := xhttp.UserID(ctx)
	if err != nil {
		return
	}

	point, err := servicePointService.NewServicePointService().GetByUser(ctx, userID, model.ServicePointLevelFirst)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListToDeliver(ctx, point.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//var resList []res
	//for _, datum := range data {
	//	l := datum.Address.Location
	//	resList = append(resList, res{
	//		Order:    datum,
	//		Distance: backDistance(point, l.Longitude, l.Latitude),
	//	})
	//}
	if len(list) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, o := range list {
		m[o.ID] = append(m[o.ID], o)
	}
	var resList []res
	for id, orders := range m {
		l := orders[0].Address.Location
		item := res{
			BuyerId:   id,
			BuyerName: orders[0].BuyerName,
			Address:   orders[0].Address,
			Distance:  backDistance(point, l.Longitude, l.Latitude),
			OrderList: orders,
		}
		resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, resList)
}

type res struct {
	BuyerId   primitive.ObjectID `json:"buyer_id"`
	BuyerName string             `json:"buyer_name"`
	Address   model.OrderAddress `json:"address"`
	Distance  int                `json:"distance"`
	OrderList []model.Order      `json:"order_list"`
}

func backDistance(point model.ServicePoint, lon, lat float64) int {
	if lon == 0 || lat == 0 {
		return 0
	}
	return int(util.LatitudeLongitudeDistance(point.Location.Longitude, point.Location.Latitude, lon, lat))
}

func ListOrderForPoint(ctx *gin.Context) {
	var req = struct {
		Begin         int64  `json:"begin"`
		End           int64  `json:"end"`
		SecondPointID string `json:"second_point_id"`
		Page          int64  `json:"page" validate:"min=1"`
		Limit         int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.SecondPointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, count, err := orderPointService.NewOrderPointService().ListOrders(ctx, id, req.Page, req.Limit, req.Begin, req.End)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, data, count)
}
