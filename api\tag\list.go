package tag

import (
	"base/core/xhttp"
	"base/service/tagService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		CategoryID string `uri:"category_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	categoryID, err := util.ConvertToObject(req.CategoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := tagService.NewTagService().List(ctx, categoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}
