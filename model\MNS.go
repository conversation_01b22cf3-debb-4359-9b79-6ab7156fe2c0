package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const (
	NewUserInviteCouponID string = "650a58a44ea2d10787de239b"
	NewUserActiveCouponID string = "6510edddf575f174aa503322" // 主动登录
	NewUserRewardCouponID string = "650ab98a701de77579bde5b1"
)

const (
	MNSSyncSortData          string = "syncSortData"
	MNSCheckAfterShip        string = "checkAfterShip"
	MNSSyncRefundData        string = "syncRefundData"
	MNSSyncQualityRefundData string = "syncQualityRefundData"
	MNSOrderAutoReceive      string = "orderAutoReceive"
	MNSAgentPayDeliver       string = "agentPayDeliver"
)

const (
	// MNSCouponStockRefresh 优惠券批次刷新
	MNSCouponStockRefresh string = "couponStockRefresh"
	// MNSCouponUserUsed 优惠券已使用
	MNSCouponUserUsed string = "couponUserUsed"
	// MNSCouponUserRestore 优惠券恢复
	MNSCouponUserRestore string = "couponUserRestore"
	// MNSCouponUserRefresh 优惠券刷新
	MNSCouponUserRefresh string = "couponUserRefresh"
	// MNSCheckPromotionSubsidy 检查营销补贴
	MNSCheckPromotionSubsidy string = "checkPromotionSubsidy"
)

const (
	MNSOrderDivideNormal    string = "divideNormalOrder"
	MNSOrderDivideFlatCheck string = "divideFlatCheck"
	MNSOrderDivideEndCheck  string = "divideEndCheck"
	MNSOrderDivideDeliver   string = "divideDeliver"
	MNSOrderDivideDebt      string = "divideDebtOrder"
	MNSOrderCalcProfit      string = "calcProfit"
	MNSOrderCloseParent     string = "closeParent"
	MNSOrderShipSettle      string = "shipSettle"
	MNSOrderFinalSettle     string = "orderFinalSettle"
)

// 邀请

const (
// MNSInviteUpdateStatus string = "inviteUpdateStatus"
)

// 积分
const (
	MNSIntegralGenerate string = "integralGenerate"
	MNSIntegralConsume  string = "integralConsume"
	MNSIntegralAgentPay string = "integralAgentPay"
)

// 配送单

const (
	MNSDeliverNoteGenerate string = "deliverNoteGenerate"
)

const (
	MNSProductUp   string = "productUp"
	MNSProductDown string = "productDown"
)

const MNSCloseIntegralOrder string = "closeIntegralOrder"

const MNSBalanceCreateRecord string = "balanceCreateRecord"

// 统计

const (
	MNSRemoveBuyerStats       string = "removeBuyerStats"
	MNSRemoveProductStats     string = "removeProductStats"
	MNSProductSaleStatsExport string = "productSaleStatsExport"
)

const MNSCheckOverWeight string = "checkOverWeight"

const MNSRefundChangeAuditor string = "refundChangeAuditor"
const MNSRefundComplete string = "refundComplete"
const MNSOrderAdjustSettleRefund string = "orderAdjustSettleRefund"

const MNSProductOfflineCheck string = "productOfflineCheck"

const MNSDeliverFeeDetailGenerate string = "deliverFeeDetailGenerate"

// 供应商冻结金额
const MNSSupplierFreezeAmount string = "supplierFreezeAmount"

type MNSGenDeliverNote struct {
	BuyerList []MNSBuyer `json:"buyer_list"`
	Timestamp int64      `json:"timestamp"`
}

type MNSBuyer struct {
	BuyerID     string      `json:"buyer_id"`
	DeliverType DeliverType `json:"deliver_type"`
}

type MNSBuyerInfo struct {
	BuyerID string `json:"buyer_id"`
}

type MNSID struct {
	ID string `json:"id"`
}

type MNSBizOrderNo struct {
	BizOrderNo string `json:"biz_order_no"`
}

type MNSOrder struct {
	OrderID string `json:"order_id"`
}

type MNSOrderList struct {
	OrderIDList []string `json:"order_id_list"`
}

type MNSBalanceRecord struct {
	BuyerID                string                 `json:"buyer_id"`
	ObjectID               string                 `json:"object_id"`
	BuyerBalanceRecordType BuyerBalanceRecordType `json:"buyer_balance_record_type"`
	Amount                 int                    `json:"amount"`
}

type MNSAgentPay struct {
	AgentPayID string `json:"agentPayID"`
}

type MNSParentOrder struct {
	ParentOrderID string `json:"parent_order_id"`
}

type MNSDebtOrder struct {
	DebtOrderID string `json:"debt_order_id"`
}
type MNSIntegralAgentPayOrder struct {
	OrderID string `json:"order_id"`
}

type MNSOverWeight struct {
	OrderIDList []string `json:"order_id_list"`
}

type MNSRefund struct {
	RefundID primitive.ObjectID `json:"refund_id"`
}

type MNSOrderAdjustSettle struct {
	AdjustSettleID primitive.ObjectID `json:"adjust_settle_id"`
}

type MNSProduct struct {
	ProductID primitive.ObjectID `json:"product_id"`
	OfflineAt int64              `json:"offline_at"`
}

// MNSCouponStock 优惠券批次
type MNSCouponStock struct {
	CouponStockID string `json:"coupon_stock_id"`
}

// MNSPromotionSubsidy 检查营销补贴
type MNSPromotionSubsidy struct {
	OrderID string `json:"order_id"`
}

// MNSCouponUser 优惠券用户
type MNSCouponUser struct {
	CouponUserID  string `json:"coupon_user_id"`
	ParentOrderID string `json:"parent_order_id"`
}

// MNSProductSaleStatsExportTime 商品销售统计导出时间
type MNSProductSaleStatsExportTime struct {
	BeginTime int64 `json:"begin_time"`
	EndTime   int64 `json:"end_time"`
}

// MNSDeliverFeeDetail 配送费明细生成
type MNSDeliverFeeDetail struct {
	ParentOrderID string `json:"parent_order_id"`
}

// MNSSupplierFreezeAmountInfo 供应商冻结金额
type MNSSupplierFreezeAmountInfo struct {
	SupplierID string `json:"supplier_id"`
	Amount     int    `json:"amount"`
}
