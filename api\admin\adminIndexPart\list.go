package adminIndexPart

import (
	"base/core/xhttp"
	"base/service/indexPartService"

	"github.com/gin-gonic/gin"
)

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
		VisibleType int `json:"visible_type"` // 0 1 true  2  false
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, err := indexPartService.NewIndexPartService().List(ctx, req.VisibleType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}
