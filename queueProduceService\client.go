package queueProduceService

import (
	"base/global"
	"base/model"
	"encoding/base64"
	"encoding/json"
	_ "net/http/pprof"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type QueueProduceService struct {
	queueKey           string
	rdb                *redis.Client
	orderAfterSaleHour int64
}

func NewQueueProduceService() *QueueProduceService {
	return &QueueProduceService{
		queueKey:           global.QueueName,
		rdb:                global.RDBDefault,
		orderAfterSaleHour: global.OrderAfterSaleHour,
	}
}

// SendDivideFlatCheck 分账检查
//func (s *QueueProduceService) SendDivideFlatCheck(ctx context.Context, agentPayID primitive.ObjectID) {
//	var t int64
//
//	addSeconds := (time.Second * 10).Seconds()
//
//	t = int64(addSeconds)
//
//	data := model.QueueMessage{
//		MessageID:   util.NewUUID(),
//		Event:       model.QueueEventDivideFlatCheck,
//		Value:       agentPayID.Hex(),
//		DelaySecond: t,
//	}
//	s.send(ctx, data)
//}

// SendDivideEndCheck 分账检查-完结
//func (s *QueueProduceService) SendDivideEndCheck(ctx context.Context, agentPayID primitive.ObjectID) {
//	var t int64
//
//	addSeconds := (time.Second * 10).Seconds()
//
//	t = int64(addSeconds)
//
//	data := model.QueueMessage{
//		MessageID:   util.NewUUID(),
//		Event:       model.QueueEventDivideEndCheck,
//		Value:       agentPayID.Hex(),
//		DelaySecond: t,
//	}
//	s.send(ctx, data)
//}
//
//// SendCreateBalanceRecord 余额记录
//func (s *QueueProduceService) SendCreateBalanceRecord(ctx context.Context, buyerBalanceRecordType model.BuyerBalanceRecordType, buyerID, objectID primitive.ObjectID, amount int) {
//	d := model.QueueValueBalanceRecord{
//		BuyerID:                buyerID.Hex(),
//		ObjectID:               objectID.Hex(),
//		BuyerBalanceRecordType: buyerBalanceRecordType,
//		Amount:                 amount,
//	}
//
//	content := encodeContentStruct(d)
//
//	data := model.QueueMessage{
//		MessageID:   util.NewUUID(),
//		Event:       model.QueueEventBalanceCreateRecord,
//		Value:       content,
//		DelaySecond: 0,
//	}
//
//	s.send(ctx, data)
//}

func encodeContentStruct(data any) string {
	marshal, err := json.Marshal(data)
	if err != nil {
		zap.S().Errorf("encodeContentStruct,内容:%s，err:%v", data, err)
		return ""
	}

	toString := base64.StdEncoding.EncodeToString(marshal)

	zap.S().Infof("延迟消息value:%s,内容：%s", toString, string(marshal))
	return toString
}

func (s *QueueProduceService) send(ctx context.Context, msg model.QueueMessage) {
	//toString := base64.StdEncoding.EncodeToString([]byte(msg))

	key := s.queueKey

	now := time.Now()

	add := now.Add(time.Second * time.Duration(msg.DelaySecond))

	marshal, _ := json.Marshal(msg)
	toString := base64.StdEncoding.EncodeToString(marshal)

	member := toString

	data := redis.Z{
		Score:  float64(add.UnixMilli()),
		Member: member,
	}
	s.rdb.ZAdd(ctx, key, &data)

	zap.S().Infof("延迟队列：%s", string(marshal))
	return
}
