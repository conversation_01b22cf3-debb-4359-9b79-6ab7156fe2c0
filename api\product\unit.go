package product

import (
	"base/core/xhttp"
	"base/service/productUnitService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateUnit 单位
func CreateUnit(ctx *gin.Context) {
	var req = struct {
		ID   string `json:"id" validate:"-"`
		Name string `json:"name" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	var id primitive.ObjectID
	if len(req.ID) == 24 {
		id, err = util.ConvertToObjectWithNote(req.ID, "id")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	err = productUnitService.NewProductUnitService().Upsert(ctx, id, req.Name)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// ListUnit 单位
func ListUnit(ctx *gin.Context) {
	list, err := productUnitService.NewProductUnitService().List(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}
