package xhttp

import (
	"base/core/xerr"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"net/http"
)

type Resp struct {
	Code int         `json:"code"`
	Msg  string      `json:"message"`
	Data interface{} `json:"data,omitempty"`
}

func NotifySuccess(ctx *gin.Context) {
	ctx.String(http.StatusOK, "success")
}

func NotifyYeeSuccess(ctx *gin.Context) {
	ctx.String(http.StatusOK, "Success")
}

func NotifyYeeRefundSuccess(ctx *gin.Context) {
	ctx.String(http.StatusOK, "SUCCESS")
}

func NotifyFail(ctx *gin.Context) {
	ctx.String(http.StatusInternalServerError, "fail")
}

func RespSuccess(ctx *gin.Context, data interface{}) {
	ctx.JSON(0, Resp{Code: 0, Msg: "ok", Data: data})
}

func RespSuccessList(ctx *gin.Context, data interface{}, count int64) {
	ctx.JSON(0, Resp{Code: 0, Msg: "ok", Data: struct {
		List  interface{} `json:"list"`
		Count int64       `json:"count"`
	}{
		List:  data,
		Count: count,
	}})
}

func RespNoExist(ctx *gin.Context) {
	back(ctx, xerr.ErrNoDocument, "记录不存在")
	return
}

func RespEnvError(ctx *gin.Context) {
	back(ctx, xerr.ErrEnvValue, "X-Env错误")
	return
}

func RespErr(ctx *gin.Context, err error) {
	if errors.Is(err, mongo.ErrNoDocuments) {
		// 记录不存在
		RespNoExist(ctx)
		return
	}

	e, ok := err.(*xerr.Err)
	if !ok {
		zap.S().Error("系统错误：", err)
		back(ctx, http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError))
		return
	}
	back(ctx, e.Code, e.Msg)
	return
}

func back(ctx *gin.Context, code int, msg string) {
	if len(msg) < 1 {
		if v, ok := xerr.ErrCodeMsg[code]; ok {
			msg = v
		}
	}
	ctx.JSON(0, Resp{Code: code, Msg: msg})
	ctx.Abort()
	return
}
