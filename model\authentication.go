package model

import (
	pays "github.com/cnbattle/allinpay/service"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Authentication 认证信息-个人和企业
type Authentication struct {
	ID                           primitive.ObjectID `json:"id" bson:"_id"`
	UserID                       primitive.ObjectID `json:"user_id" bson:"user_id"`                                                 // 用户ID
	ObjectType                   ObjectType         `json:"object_type" bson:"object_type"`                                         // 对象类型
	ObjectID                     primitive.ObjectID `json:"object_id" bson:"object_id"`                                             // 对象ID
	Mobile                       string             `json:"mobile" bson:"mobile"`                                                   // 手机号
	IsMobileVerify               bool               `json:"is_mobile_verify" bson:"is_mobile_verify"`                               // 手机号-支付是否验证
	PayMiniAccList               []string           `json:"pay_mini_acc_list" bson:"pay_mini_acc_list"`                             // 小程序-支付账户用户标识列表
	IsPayMiniAcc                 bool               `json:"is_pay_mini_acc" bson:"is_pay_mini_acc"`                                 // 是否绑定小程序-支付账户用户标识
	MemberType                   pays.MemberType    `json:"member_type" bson:"member_type"`                                         // 会员类型  // 个人会员 3  企业会员 2
	PayUserID                    string             `json:"pay_user_id" bson:"pay_user_id"`                                         // 云商通用户唯一标识
	PayBizUserId                 string             `json:"pay_biz_user_id" bson:"pay_biz_user_id"`                                 // 商户系统用户标识，商户系统中唯一编号。
	Company                      Company            `json:"company" bson:"company"`                                                 // 企业信息
	BankAccount                  BankAccount        `json:"bank_account" bson:"bank_account"`                                       // 银行账户
	HasSetCompanyInfo            bool               `json:"has_set_company_info" bson:"has_set_company_info"`                       // 是否已经设置企业信息
	Result                       int                `json:"result" bson:"result"`                                                   // 审核结果 2：审核成功。 3：审核失败。
	AccountSetResult             int                `json:"account_set_result" bson:"account_set_result"`                           // 对私银行账户认证结果  2：认证成功  3：认证失败。 注：个体工商户的对私银行账户四要素认证结果
	CheckTime                    string             `json:"check_time" bson:"check_time"`                                           // 审核时间
	FailReason                   string             `json:"fail_reason" bson:"fail_reason"`                                         // 设置失败理由
	Remark                       string             `json:"remark" bson:"remark"`                                                   // 备注
	OcrRegnumComparisonResult    int                `json:"ocr_regnum_comparison_result" bson:"ocr_regnum_comparison_result"`       // OCR识别与企业工商认证信息是否一致   0-否 1-是
	OcrIdcardComparisonResult    int                `json:"ocr_idcard_comparison_result" bson:"ocr_idcard_comparison_result"`       // OCR识别与企业法人实名信息是否一致   0-否 1-是
	ResultInfo                   string             `json:"result_info" bson:"result_info"`                                         // 比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
	ReqSerialNo                  string             `json:"req_serial_no" bson:"req_serial_no"`                                     // 请求流水号  【影印件采集（文件上传模式）】接口上送的reqSerialNo字段
	AcctProtocolNo               string             `json:"acct_protocol_no" bson:"acct_protocol_no"`                               //  必填  账户提现协议编号 商户端需保存
	SignAcctName                 string             `json:"sign_acct_name" bson:"sign_acct_name"`                                   //  必填  签约户名
	SignResult                   string             `json:"sign_result" bson:"sign_result"`                                         //  必填  签订结果	成功：ok ，失败：error
	RealName                     string             `json:"real_name" bson:"real_name"`                                             // 个人会员 实名
	IdentityNo                   string             `json:"identity_no" bson:"identity_no"`                                         // 个人会员 身份证
	IndividualBankcardNo         string             `json:"individual_bankcard_no" bson:"individual_bankcard_no"`                   // 个人银行卡 卡号 绑定成功才存在
	IndividualBankReservedMobile string             `json:"individual_bank_reserved_mobile" bson:"individual_bank_reserved_mobile"` // 个人银行卡 银行预留手机号
	IndividualBankcardResult     string             `json:"individual_bankcard_result" bson:"individual_bankcard_result"`           // ok为绑定
	IndividualAcctProtocolNo     string             `json:"individual_acct_protocol_no" bson:"individual_acct_protocol_no"`         //  必填  个人会员-账户提现协议编号 商户端需保存
	IndividualSignAcctName       string             `json:"individual_sign_acct_name" bson:"individual_sign_acct_name"`             //  必填  个人会员-签约户名
	IndividualSignResult         string             `json:"individual_sign_result" bson:"individual_sign_result"`                   //  必填  个人会员-签订结果	成功：ok ，失败：error
	PersonalBankAccount       BankAccount        `json:"personal_bank_account" bson:"personal_bank_account"`               // 个人银行账户，类型为公司
	SignPersonal              SignPersonal       `json:"sign_personal" bson:"sign_personal"`                               // 公司个人银行提现签约
	CreatedAt                 int64              `bson:"created_at" json:"created_at"`
	UpdatedAt                 int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt                 int64              `bson:"deleted_at" json:"deleted_at"`
}

// SignPersonal 公司个人银行提现签约
type SignPersonal struct {
	AcctProtocolNo string `json:"acct_protocol_no" bson:"acct_protocol_no"` //  必填  账户提现协议编号 商户端需保存
	CardNumber     string `json:"card_number" bson:"card_number"`           // 账号
	SignAcctName   string `json:"sign_acct_name" bson:"sign_acct_name"`     //  必填  签约户名
	SignResult     string `json:"sign_result" bson:"sign_result"`           //  必填  签订结果	成功：ok ，失败：error
}

// Company 企业信息
type Company struct {
	CompanyType            CompanyType     `json:"company_type" bson:"company_type"`                           // 企业类型
	CompanyName            string          `json:"company_name" bson:"company_name"`                           // 企业名称
	CreditCode             string          `json:"credit_code" bson:"credit_code"`                             // 统一社会信用代码
	BusinessLicenseValidTo string          `json:"business_license_valid_to" bson:"business_license_valid_to"` // 营业执照有效期 --不需要
	BusinessLicenseImg     FileInfo        `json:"business_license_img" bson:"business_license_img"`           // 营业执照图
	BusinessLicense        BusinessLicense `json:"business_license" bson:"business_license"`
	HasOcrRegnumComparison bool            `json:"has_ocr_regnum_comparison" bson:"has_ocr_regnum_comparison"` // 是否提交ocr营业执照
	Legal                  Legal           `json:"legal" bson:"legal"`
}

// Legal 法人
type Legal struct {
	LegalName                   string   `json:"legal_name" bson:"legal_name"`                                           // 法人姓名
	IdentityType                int      `json:"identity_type" bson:"identity_type"`                                     // 法人证件类型  1 身份证
	LegalIds                    string   `json:"legal_ids" bson:"legal_ids"`                                             // 法人证件号码
	LegalPhone                  string   `json:"legal_phone" bson:"legal_phone"`                                         // 法人手机号
	IDCard                      IDCard   `json:"id_card" bson:"id_card"`                                                 // 身份证信息
	IdCardFrontImg              FileInfo `bson:"id_card_front_img" json:"id_card_front_img"`                             // 身份证-正面
	IdCardBackImg               FileInfo `bson:"id_card_back_img" json:"id_card_back_img"`                               // 身份证-背面
	HasOcrIdcardComparisonFront bool     `json:"has_ocr_idcard_comparison_front" bson:"has_ocr_idcard_comparison_front"` // 提交人像面ocr
	HasOcrIdcardComparisonBack  bool     `json:"has_ocr_idcard_comparison_back" bson:"has_ocr_idcard_comparison_back"`   // 提交国徽面ocr
	//IdCardWithHandImg FileInfo `bson:"id_card_with_hand_img" json:"id_card_with_hand_img"` // 身份证-手持
}

// BusinessLicense 营业执照
type BusinessLicense struct {
	RegistrationNumber  string `json:"registration_number" bson:"registration_number"`   // 注册码，社会统一信用码
	Name                string `json:"name"  bson:"name"`                                // 企业名称
	Type                string `json:"type" bson:"type"`                                 // 企业类型
	Address             string `json:"address" bson:"address"`                           // 地址
	LegalRepresentative string `json:"legal_representative" bson:"legal_representative"` // 法人
	RegisteredCapital   string `json:"registered_capital" bson:"registered_capital"`     // 注册资金
	FoundDate           string `json:"found_date" bson:"found_date"`                     // 注册时间
	BusinessTerm        string `json:"business_term" bson:"business_term"`               // 有效期
	BusinessScope       string `json:"business_scope" bson:"business_scope"`             // 经营范围
}

// IDCard 身份证
type IDCard struct {
	Name            string `json:"name" bson:"name"`                         // 姓名
	Sex             string `json:"sex"  bson:"sex"`                          // 性别
	Ethnicity       string `json:"ethnicity" bson:"ethnicity"`               // 民族
	Birth           string `json:"birth" bson:"birth"`                       // 生日
	Address         string `json:"address" bson:"address"`                   // 地址
	CertificateType string `json:"certificate_type" bson:"certificate_type"` // 证件类型-默认：身份证
	IdCardNumber    string `json:"id_card_number" bson:"id_card_number"`     // 身份证号码
	ValidFrom       string `json:"valid_from" bson:"valid_from"`             // 有效期开始
	ValidTo         string `json:"valid_to" bson:"valid_to"`                 // 有效期截止
	Issue           string `json:"issue" bson:"issue"`                       // 签发机构
}

// IDCardOcr 身份证
type IDCardOcr struct {
	Name      string `json:"name" bson:"name"`             // 姓名
	Sex       string `json:"sex"  bson:"sex"`              // 性别
	Ethnicity string `json:"ethnicity" bson:"ethnicity"`   // 民族
	Birth     string `json:"birth" bson:"birth"`           // 生日
	Address   string `json:"address" bson:"address"`       // 地址
	Number    string `json:"number" bson:"number"`         // 身份证号码
	ValidFrom string `json:"valid_from" bson:"valid_from"` // 有效期开始
	ValidTo   string `json:"valid_to" bson:"valid_to"`     // 有效期截止
	Issue     string `json:"issue" bson:"issue"`           // 签发机构
}

type AccountType int

const (
	AccountTypePublic AccountType = 1
	AccountTypeSelf   AccountType = 0
)

// BankAccount 银行账户
type BankAccount struct {
	AccountType AccountType `json:"account_type" bson:"account_type"` // 银行账户类型-1对公/0对私
	CardNumber  string      `json:"card_number" bson:"card_number"`   // 账号

	// 对公
	ParentBankName string `json:"parent_bank_name" bson:"parent_bank_name"` // 开户银行名称
	BankName       string `json:"bank_name" bson:"bank_name"`               // 开户行支行名称
	UnionBank      string `json:"union_bank" bson:"union_bank"`             // 支付行号，12位数字

	// 对私
	BankReservedMobile string   `json:"bank_reserved_mobile" bson:"bank_reserved_mobile"` // 银行预留手机号
	AccountSetResult   int      `json:"account_set_result" bson:"account_set_result"`     // 对私银行账户认证结果  2：认证成功。 3：认证失败。
	BankcardImg        FileInfo `json:"bankcard_img" bson:"bankcard_img"`                 // 银行卡图片
	Bankcard           Bankcard `json:"bankcard_basic" bson:"bankcard_basic"`             // 银行识别
}

// Bankcard 银行卡
type Bankcard struct {
	BankName      string `json:"bank_name" bson:"bank_name"`             // 发卡行名称
	BankCode      string `json:"bank_code" bson:"bank_code"`             // 	发卡行代码
	CardTypeLabel string `json:"card_type_label" bson:"card_type_label"` // 	卡种名称
	CardType      int    `json:"card_type" bson:"card_type"`             // 	卡种
	CardBin       string `json:"card_bin" bson:"card_bin"`               // 卡bin
	CardName      string `json:"card_name" bson:"card_name"`             // 卡名
	CardLenth     int    `json:"card_lenth" bson:"card_lenth"`           // 	卡片长度
	CardState     int    `json:"card_state" bson:"card_state"`           // 状态（1：有效；0：无效）
}

// BankCardOcr 银行卡
type BankCardOcr struct {
	BankName   string `json:"bank_name" bson:"bank_name"`
	CardNumber string `json:"card_number" bson:"card_number"`
	IssueDate  string `json:"issue_date" bson:"issue_date"`
	ExpiryDate string `json:"expiry_date" bson:"expiry_date"`
	Type       string `json:"type" bson:"type"`
}
