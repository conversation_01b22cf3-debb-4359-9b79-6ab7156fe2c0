package model

import (
	pays "github.com/cnbattle/allinpay/service"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LicenseStatus int

const (
	LicenseStatusNo  LicenseStatus = 1
	LicenseStatusHas LicenseStatus = 2
)

type UserType string

const (
	UserTypeNormal UserType = "normal"
	UserTypeYHT    UserType = "YHT"
)

type ServiceFeeType string

const (
	ServiceFeeTypeNone ServiceFeeType = "none"
	ServiceFeeTypeOne  ServiceFeeType = "one"
	ServiceFeeTypeTwo  ServiceFeeType = "two"
)

// Buyer 采购商
type Buyer struct {
	ID        primitive.ObjectID `bson:"_id" json:"id"`
	UserID    primitive.ObjectID `bson:"user_id" json:"user_id"`       // 用户ID
	BuyerName string             `bson:"buyer_name" json:"buyer_name"` // 采购商名称
	//BuyerManagerUserID primitive.ObjectID `json:"buyer_manager_user_id" bson:"buyer_manager_user_id"` // 关联的采购商ID
	//BuyerManagerUserName string             `json:"buyer_manager_user_name" bson:"buyer_manager_user_name"`

	ManagerBuyerID  primitive.ObjectID `json:"manager_buyer_id" bson:"manager_buyer_id"`
	ManagerUserName string             `json:"manager_user_name" bson:"manager_user_name"`

	Mobile                  string             `json:"mobile" bson:"mobile"`
	OpenID                  string             `json:"open_id" bson:"open_id"`
	UnionID                 string             `json:"union_id" bson:"union_id"`
	Password                string             `bson:"password" json:"password"`             // 密码
	BuyerCode               string             `json:"buyer_code" bson:"buyer_code"`         // 会员码
	BuyerType               BuyerType          `bson:"buyer_type" json:"buyer_type"`         // 类型
	MemberType              pays.MemberType    `json:"member_type" bson:"member_type"`       // 会员类型
	ContactUser             string             `bson:"contact_user" json:"contact_user"`     // 收货联系人
	ContactMobile           string             `json:"contact_mobile" bson:"contact_mobile"` // 收货手机号
	ApplyReason             string             `json:"apply_reason" bson:"apply_reason"`     // 申请说明
	Entity                  int                `json:"entity" bson:"entity"`                 // 1 单位组织 2 个人
	Location                Location           `bson:"location" json:"location"`             // 定位地址
	Address                 string             `bson:"address" json:"address"`               // 详细地址
	AvatarImg               FileInfo           `bson:"avatar_img" json:"avatar_img"`         // 头像
	AvatarFile              string             `json:"avatar_file" bson:"avatar_file"`
	ShopHeadImg             FileInfo           `bson:"shop_head_img" json:"shop_head_img"`                     // 门头照-图片
	BusinessLicenseImg      FileInfo           `bson:"business_license_img" json:"business_license_img"`       // 营业执照-图片
	AuditStatus             AuditStatusType    `bson:"audit_status" json:"audit_status"`                       // 认证审核状态
	AuditFailReason         string             `json:"audit_fail_reason" bson:"audit_fail_reason"`             // 认证审核未通过原因
	AccountStatus           AccountStatusType  `bson:"account_status" json:"account_status"`                   // 账号状态
	ActiveExpire            int64              `bson:"active_expire" json:"active_expire"`                     // 活跃过期时间/秒-审核后
	OriginID                string             `bson:"origin_id" json:"origin_id"`                             // 旧数据原始ID
	IsNew                   bool               `bson:"is_new" json:"is_new"`                                   // 是否新人
	Note                    string             `json:"note" bson:"note"`                                       // 备注
	LicenseStatus           LicenseStatus      `json:"license_status" bson:"license_status"`                   // 营业执照
	CreditCode              string             `json:"credit_code" bson:"credit_code"`                         // 营业执照code
	IsAssignServicePoint    bool               `bson:"is_assign_service_point" json:"is_assign_service_point"` // 分配服务仓情况
	ServicePointID          primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`               // 服务仓ID
	ServicePointName        string             `bson:"service_point_name" json:"service_point_name"`           // 服务仓名称
	StationID               primitive.ObjectID `bson:"station_id" json:"station_id"`
	StationName             string             `bson:"station_name" json:"station_name"`             // 服务仓名称
	DeliverType             []DeliverType      `json:"deliver_type" bson:"deliver_type"`             // 配送方式
	DeliverFee              int                `json:"deliver_fee" bson:"deliver_fee"`               // 配送费
	SubsidyAmount           int                `json:"subsidy_amount" bson:"subsidy_amount"`         // 配送费补贴规则-门槛
	SubsidyPercent          int                `json:"subsidy_percent" bson:"subsidy_percent"`       // 配送费补贴规则-比例
	DeliverFreeBegin        int64              `json:"deliver_free_begin" bson:"deliver_free_begin"` // 配送限免期-开始
	DeliverFreeEnd          int64              `json:"deliver_free_end" bson:"deliver_free_end"`     // 配送限免期-结束
	AddressNote             string             `json:"address_note" bson:"address_note"`             // 地址备注
	LogisticsNote           string             `json:"logistics_note" bson:"logistics_note"`         // 物流备注
	LogisticsUnitFee        int                `json:"logistics_unit_fee" bson:"logistics_unit_fee"` // 物流费单价
	InstantDeliver          []InstantDeliver   `json:"instant_deliver" bson:"instant_deliver"`       // 即时配送
	PriceLevel              int                `json:"price_level" bson:"price_level"`               // 价格等级（加价） +0 +5 +10
	ServiceFee              int                `json:"service_fee" bson:"service_fee"`               // 服务费-额外
	ServiceFeeType          ServiceFeeType     `json:"service_fee_type" bson:"service_fee_type"`
	ServiceFeeRebatePercent int                `json:"service_fee_rebate_percent" bson:"service_fee_rebate_percent"` // 服务费-返利
	InvoiceAuthStatus       int                `json:"invoice_auth_status" bson:"invoice_auth_status"`               // 发票授权状态   0/1 未授权  2 已授权
	UserType                UserType           `json:"user_type" bson:"user_type"`                                   // normal  YHT
	CustomerManager         bool               `json:"customer_manager" bson:"customer_manager"`                     // 客户经理
	CreatedAt               int64              `bson:"created_at" json:"created_at"`
	UpdatedAt               int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt               int64              `bson:"deleted_at" json:"deleted_at"`
}

type InstantDeliver struct {
	ID     int    `json:"id" bson:"id"`
	Name   string `json:"name" bson:"name"`     // 名称
	Amount int    `json:"amount" bson:"amount"` // 金额
}

// BuyerGroup 交流群
type BuyerGroup struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	Title         string             `json:"title" bson:"title"`                   // 标题
	Img           FileInfo           `json:"img" bson:"img"`                       // 图片
	Sort          int                `json:"sort" bson:"sort"`                     // 排序
	ConditionType ConditionType      `json:"condition_type" bson:"condition_type"` // 加群条件
	CreatedAt     int64              `bson:"created_at" json:"created_at"`
	UpdatedAt     int64              `bson:"updated_at" json:"updated_at"`
}

// BuyerStats 会员统计
type BuyerStats struct {
	BuyerID              primitive.ObjectID `json:"buyer_id"`
	OrderProductNum      int64              `json:"order_product_num"` // 购买量
	OrderAmount          int64              `json:"order_amount"`      // 订单总金额
	AfterSaleAuditAmount int                `json:"after_sale_audit_amount"`
	AfterSaleOrderNum    int64              `json:"after_sale_order_num"` // 售后次数
	AfterSaleRate        float64            `json:"after_sale_rate"`      // 售后率
	LatestOrderTime      int64              `json:"latest_order_time"`
	LatestVisitTimes     int                `json:"latest_visit_times"`
	CartProductNum       int                `json:"cart_product_num"`
}

type ConditionType int

const (
	ConditionTypeNotBuyer    ConditionType = 1
	ConditionTypeBuyerPassed ConditionType = 2
)
