package payAccount

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/payAccountService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetNormalUserBalance 查询普通用户余额
func GetNormalUserBalance(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id"`
		WarehouseID    string `json:"warehouse_id"`
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	env := xhttp.GetEnv(ctx)

	var id primitive.ObjectID
	switch env {
	case model.ObjectTypeSupplier:
		id, err = util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeWarehouse:
		id, err = util.ConvertToObjectWithCtx(ctx, req.WarehouseID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeServicePoint:
		id, err = util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	default:
		break
	}

	if id == primitive.NilObjectID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误"))
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByObject(ctx, id, env)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := payAccountService.NewPayAccountService().GetNormalUserBalance(ctx, auth.PayBizUserId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetBuyerBalance 查询会员余额
func GetBuyerBalance(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := payAccountService.NewPayAccountService().GetNormalUserBalance(ctx, auth.PayBizUserId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetBuyerBalanceCache 查询会员余额
func GetBuyerBalanceCache(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := payAccountService.NewPayAccountService().GetNormalUserBalanceCache(ctx, auth.PayBizUserId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

func GetStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	station, err := authenticationService.NewAuthenticationService().GetByStation(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := payAccountService.NewPayAccountService().GetNormalUserBalanceCache(ctx, station.PayBizUserId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}
